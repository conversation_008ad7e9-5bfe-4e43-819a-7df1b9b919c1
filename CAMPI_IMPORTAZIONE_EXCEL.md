# Campi per Importazione Excel - Sistema CMS

Questo documento elenca **esattamente** i campi richiesti per l'importazione Excel dei cavi.

## 📋 **CAMPI OBBLIGATORI per Importazione Cavi**

### **<PERSON><PERSON><PERSON> (case-insensitive)**

| Campo | Nome Colonna | Tipo | Obbligatorio | Descrizione |
|-------|--------------|------|--------------|-------------|
| **ID Cavo** | `id_cavo` | Testo | ✅ **SÌ** | Identificativo univoco del cavo |
| **Utility** | `utility` | Testo | ✅ **SÌ** | Tipo di utility (es. "Energia", "Telecom") |
| **Tipologia** | `tipologia` | Testo | ✅ **SÌ** | Tipologia del cavo (es. "MT", "BT") |
| **Metri Teorici** | `metri_teorici` | Numero | ✅ **SÌ** | Lunghezza teorica del cavo |

### **Colonne Opzionali (ma Consigliate)**

| Campo | Nome Colonna | Tipo | Descrizione |
|-------|--------------|------|-------------|
| **Formazione** | `formazione` o `sezione` | Testo | Formazione del cavo (es. "3x2.5", "4x1.5") |
| **Sistema** | `sistema` | Testo | Sistema di appartenenza |
| **Colore Cavo** | `colore_cavo` | Testo | Colore del cavo |
| **Ubicazione Partenza** | `ubicazione_partenza` | Testo | Punto di partenza |
| **Utenza Partenza** | `utenza_partenza` | Testo | Utenza di partenza |
| **Descrizione Utenza Partenza** | `descrizione_utenza_partenza` | Testo | Descrizione dettagliata |
| **Ubicazione Arrivo** | `ubicazione_arrivo` | Testo | Punto di arrivo |
| **Utenza Arrivo** | `utenza_arrivo` | Testo | Utenza di arrivo |
| **Descrizione Utenza Arrivo** | `descrizione_utenza_arrivo` | Testo | Descrizione dettagliata |
| **Responsabile Posa** | `responsabile_posa` | Testo | Chi è responsabile della posa |

### **Campi Spare (Legacy - Non Più Utilizzati)**

| Campo | Nome Colonna | Tipo | Descrizione |
|-------|--------------|------|-------------|
| **N. Conduttori** | `n_conduttori` | Testo | ⚠️ Campo legacy - non più utilizzato nel sistema |
| **SH** | `sh` | Testo | ⚠️ Campo legacy - non più utilizzato nel sistema |

## 🔧 **Regole di Validazione**

### **1. Campo "formazione" (ex "sezione")**
- ✅ **Campo principale**: `formazione` è ora il campo principale per la sezione del cavo
- ✅ **Compatibilità legacy**: `sezione` viene automaticamente mappato a `formazione`
- ✅ **Esempi validi**: "3x2.5", "4x1.5", "2x4", "1x10"
- ✅ **Valore di default**: Se vuoto viene impostato a "TBD"
- ✅ **Opzionale**: Non più obbligatorio per l'importazione

### **2. Campi Spare (n_conduttori e SH)**
- ⚠️ **Non più utilizzati**: Questi campi sono legacy e non più attivi nel sistema
- ✅ **Gestione automatica**: Se presenti vengono ignorati, se assenti vengono impostati a valori di default
- ✅ **Nessuna derivazione**: Non viene più derivato automaticamente n_conduttori da formazione

### **3. Campi con Valori Predefiniti**
Se non specificati, vengono impostati automaticamente a "TBD":
- `formazione` → "TBD"
- `sistema` → "TBD"
- `colore_cavo` → "TBD"
- `ubicazione_partenza` → "TBD"
- `utenza_partenza` → "TBD"
- `descrizione_utenza_partenza` → "TBD"
- `ubicazione_arrivo` → "TBD"
- `utenza_arrivo` → "TBD"
- `descrizione_utenza_arrivo` → "TBD"
- `responsabile_posa` → "TBD"

## 📊 **Esempio di File Excel Corretto**

### **Versione Minima (Solo Campi Obbligatori)**
```
id_cavo     | utility | tipologia | metri_teorici
C001        | Energia | MT        | 100
C002        | Telecom | BT        | 150
C003        | Energia | MT        | 200
```

### **Versione Completa (Con Campi Opzionali)**
```
id_cavo | utility | tipologia | metri_teorici | formazione | sistema | ubicazione_partenza | ubicazione_arrivo
C001    | Energia | MT        | 100          | 3x2.5      | SIS1    | Cabina A           | Cabina B
C002    | Telecom | BT        | 150          | 4x1.5      | SIS2    | Palo 1             | Palo 2
```

## ⚠️ **Errori Comuni da Evitare**

### **1. Nomi Colonne Errati**
❌ **SBAGLIATO**: `ID_Cavo`, `UTILITY`, `Tipologia_Cavo`
✅ **CORRETTO**: `id_cavo`, `utility`, `tipologia`

### **2. Campi Vuoti Obbligatori**
❌ **SBAGLIATO**: Lasciare vuoti `id_cavo`, `utility`, `tipologia`, `metri_teorici`
✅ **CORRETTO**: Compilare tutti i campi obbligatori (formazione è ora opzionale)

### **3. Formato Metri Teorici**
❌ **SBAGLIATO**: "100m", "150 metri", "duecento"
✅ **CORRETTO**: 100, 150, 200 (solo numeri)

### **4. Formato Formazione**
❌ **SBAGLIATO**: "3 x 2.5", "tre per due e mezzo"
✅ **CORRETTO**: "3x2.5", "4x1.5", "2x4"

## 🛠️ **Come Creare un Template Corretto**

### **Opzione 1: Scarica Template dalla Webapp**
1. Vai su **Gestione Excel** nella webapp
2. Clicca **"Crea Template Excel per cavi"**
3. Scarica il file generato automaticamente

### **Opzione 2: Crea Manualmente**
1. Apri Excel
2. Nella prima riga inserisci le intestazioni minime:
   ```
   id_cavo | utility | tipologia | metri_teorici
   ```
   Oppure con campi opzionali:
   ```
   id_cavo | utility | tipologia | metri_teorici | formazione | sistema
   ```
3. Compila i dati nelle righe successive

## 🔍 **Debug Errori di Importazione**

### **Errore: "Colonne obbligatorie mancanti"**
- ✅ Verifica che tutte le 4 colonne obbligatorie siano presenti: `id_cavo`, `utility`, `tipologia`, `metri_teorici`
- ✅ Controlla che i nomi siano scritti correttamente (minuscolo, underscore)

### **Errore: "File vuoto o non valido"**
- ✅ Verifica che il file contenga dati (non solo intestazioni)
- ✅ Controlla che il formato sia .xlsx o .xls

### **Errore: "Nessun dato valido da importare"**
- ✅ Verifica che almeno una riga abbia tutti i campi obbligatori compilati
- ✅ Controlla che `metri_teorici` sia un numero valido

## 📞 **Supporto**

Se continui ad avere problemi:
1. Verifica che il file rispetti esattamente questa struttura
2. Usa il template generato dalla webapp
3. Controlla i log del backend per errori specifici

---

**Nota**: I nomi delle colonne sono **case-insensitive** (maiuscolo/minuscolo non importa) e gli spazi vengono automaticamente convertiti in underscore.
