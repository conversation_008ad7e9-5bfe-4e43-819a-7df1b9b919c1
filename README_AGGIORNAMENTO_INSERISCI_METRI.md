# Aggiornamento del Componente "Inserisci Metri Posati"

Questo documento descrive le modifiche apportate al componente "Inserisci Metri Posati" per adattarlo alla nuova configurazione che utilizza solo "tipologia" e "formazione" (precedentemente "sezione"), rimuovendo i riferimenti a "n_conduttori" e "SH".

## Modifiche Effettuate

### 1. Componente InserisciMetriForm.js

1. **Visualizzazione dei dettagli del cavo**:
   - Rimosso il campo "n_conduttori"
   - Rinominato "sezione" a "Formazione"

2. **Logica di compatibilità tra cavo e bobina**:
   - Rimosso il controllo sul campo "n_conduttori"
   - La compatibilità ora si basa solo su "tipologia" e "formazione" (sezione)

3. **Visualizzazione delle bobine compatibili**:
   - Rimosso il riferimento a "n_conduttori" nel conteggio delle bobine compatibili
   - Aggiornato il filtro per considerare solo "tipologia" e "formazione"

4. **Visualizzazione dei risultati di ricerca**:
   - R<PERSON><PERSON> il campo "n_conduttori" dalla tabella dei risultati
   - Rinominato l'intestazione "Conduttori" a "Formazione"

5. **Logica di compatibilità nella selezione manuale della bobina**:
   - Rimosso il controllo sul campo "n_conduttori"
   - Aggiornati i log di debug per mostrare solo "tipologia" e "formazione"

6. **Visualizzazione delle bobine nel menu a tendina**:
   - Rimosso il riferimento a "n_conduttori" nella visualizzazione delle bobine
   - Mostrato solo il campo "formazione" (sezione)

7. **Logica di compatibilità nella visualizzazione delle bobine**:
   - Rimosso il controllo sul campo "n_conduttori" per evidenziare le bobine compatibili
   - Aggiornato lo stile delle bobine compatibili per considerare solo "tipologia" e "formazione"

### 2. Componente IncompatibleReelDialog.js

1. **Logica di incompatibilità**:
   - Rimosso il controllo sul campo "n_conduttori"
   - Rimossa la visualizzazione delle incompatibilità relative a "n_conduttori"
   - Rinominato "sezione" a "Formazione" nella tabella delle incompatibilità

### 3. Servizio parcoCaviService.js

1. **Funzione getBobineCompatibili**:
   - Rimossa la logica di gestione del formato "X x Y" per "n_conduttori"
   - Impostato un valore fisso '0' per il parametro "n_conduttori" per compatibilità con il backend
   - Aggiornati i log di debug per mostrare solo "tipologia" e "formazione"

## Note Importanti

1. **Compatibilità con il Backend**: Il parametro "n_conduttori" viene ancora inviato al backend con un valore fisso '0' per mantenere la compatibilità con l'API esistente.

2. **Visualizzazione**: Tutti i riferimenti a "n_conduttori" sono stati rimossi dall'interfaccia utente.

3. **Logica di Business**: La logica di compatibilità tra cavi e bobine ora si basa solo su "tipologia" e "formazione" (sezione).

4. **Terminologia**: Il campo "sezione" è stato rinominato a "Formazione" in tutta l'interfaccia utente per maggiore chiarezza.
