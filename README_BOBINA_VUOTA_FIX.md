# Risoluzione Problema "BOBINA_VUOTA" durante l'Importazione

## Problema
Durante l'importazione di file Excel generati con `genera_file_test.py`, il sistema stava ancora impostando "BOBINA_VUOTA" come valore per il campo `id_bobina`, anche se era stato precedentemente modificato per utilizzare una stringa vuota.

## Causa
Dopo un'analisi approfondita, è stato identificato che il problema era nella funzione `genera_cavo_specifico` in `genera_file_test.py`. Questa funzione stava impostando "BOBINA VUOTA" (con uno spazio) come valore per `id_bobina`:

```python
"id_bobina": "BOBINA VUOTA",
```

Questa inconsistenza causava problemi durante l'importazione, poiché il sistema trattava "BOBINA VUOTA" come un valore diverso da "BOBINA_VUOTA" (con un underscore) o da una stringa vuota.

## Soluzione
La funzione `genera_cavo_specifico` è stata modificata per impostare `id_bobina` a una stringa vuota:

```python
"id_bobina": "",
```

Questo garantisce che quando un file di test viene generato con i dati specifici del cavo, il campo `id_bobina` sarà vuoto, in linea con il comportamento atteso durante l'importazione.

## Come Testare

1. **Generare un file di test con il cavo specifico**:
   ```
   python genera_file_test.py cavo_specifico
   ```

2. **Verificare che il file generato abbia `id_bobina` vuoto**:
   Aprire il file Excel generato e controllare che il campo `id_bobina` sia vuoto.

3. **Importare il file generato**:
   ```
   python test_import_excel.py
   ```
   Oppure utilizzare l'interfaccia web per importare il file.

4. **Verificare che il cavo importato abbia `id_bobina` vuoto**:
   Controllare nel database che il cavo importato abbia `id_bobina` vuoto, non "BOBINA_VUOTA".

## Note Tecniche

- La modifica è stata apportata solo alla funzione `genera_cavo_specifico` in `genera_file_test.py`.
- Non sono state necessarie modifiche al processo di importazione, poiché era già stato configurato per gestire correttamente i valori vuoti per `id_bobina`.
- Questa modifica garantisce la coerenza con il resto del codice, che utilizza stringhe vuote per `id_bobina` quando non è specificato.

## Conclusione

Questa modifica risolve il problema dell'impostazione di "BOBINA_VUOTA" durante l'importazione. Ora, quando un file di test viene generato e importato, il campo `id_bobina` rimarrà vuoto, come previsto.