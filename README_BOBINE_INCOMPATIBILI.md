# Implementazione della Funzionalità di Gestione Bobine Incompatibili

Questo documento descrive l'implementazione della funzionalità che permette all'utente di utilizzare una bobina non compatibile con un cavo, modificando automaticamente le caratteristiche del cavo per renderlo compatibile con la bobina selezionata.

## Problema

Nella CLI originale, quando un utente selezionava una bobina non compatibile con un cavo, il sistema offriva la possibilità di modificare le caratteristiche del cavo per renderlo compatibile con la bobina selezionata. Questa funzionalità non era stata implementata nella versione web.

## Soluzione

Abbiamo implementato la funzionalità seguendo la logica della CLI originale:

1. Quando un utente seleziona una bobina non compatibile con un cavo, il sistema mostra un dialogo che evidenzia le incompatibilità
2. L'utente può scegliere di:
   - Aggiornare le caratteristiche del cavo per renderlo compatibile con la bobina
   - Selezionare un'altra bobina
   - Annullare l'operazione

### Implementazione Backend

1. Abbiamo aggiunto un nuovo endpoint nell'API:
   - `POST /cavi/{cantiere_id}/{cavo_id}/update-for-compatibility`
   - Questo endpoint aggiorna le caratteristiche del cavo (tipologia, n_conduttori, sezione) per renderlo compatibile con la bobina selezionata

### Implementazione Frontend

1. Abbiamo aggiunto un nuovo metodo nel servizio `caviService`:
   - `updateCavoForCompatibility`: Chiama il nuovo endpoint per aggiornare le caratteristiche del cavo

2. Abbiamo modificato il componente `InserisciMetriForm`:
   - Aggiunto il controllo di compatibilità quando l'utente seleziona una bobina
   - Implementato il dialogo di incompatibilità che mostra le differenze tra cavo e bobina
   - Aggiunto il gestore per l'aggiornamento delle caratteristiche del cavo

3. Abbiamo utilizzato il componente `IncompatibleReelDialog` esistente per mostrare le incompatibilità e le opzioni disponibili

## Flusso di Utilizzo

1. L'utente seleziona un cavo
2. L'utente seleziona una bobina non compatibile con il cavo
3. Il sistema mostra un dialogo che evidenzia le incompatibilità
4. L'utente può scegliere di:
   - Aggiornare le caratteristiche del cavo (tipologia, n_conduttori, sezione) per renderlo compatibile con la bobina
   - Selezionare un'altra bobina
   - Annullare l'operazione
5. Se l'utente sceglie di aggiornare le caratteristiche del cavo:
   - Il sistema aggiorna il cavo nel database
   - Il sistema associa la bobina al cavo
   - Il sistema mostra un messaggio di successo

## Note Importanti

1. La funzionalità è disponibile solo per cavi non ancora installati
2. L'aggiornamento delle caratteristiche del cavo è irreversibile
3. La funzionalità mantiene la compatibilità con la logica della CLI originale
