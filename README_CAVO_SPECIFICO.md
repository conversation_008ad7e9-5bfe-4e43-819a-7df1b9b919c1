# Generazione di File Excel con Cavo Specifico

## Descrizione

Questo documento descrive le modifiche apportate al file `genera_file_test.py` per supportare la generazione di un file Excel contenente un cavo specifico con i dati forniti nell'esempio:

```
C001 Lighting Signal FG16OR16 1X240MM2 71.5 0 Da installare BOBINA VUOTA 26/05/2025, 23:05
```

## Funzionalità Aggiunte

1. **Nuova funzione `genera_cavo_specifico`**: Genera un file Excel contenente un singolo cavo con i dati specifici dell'esempio.

2. **Opzione da linea di comando**: È possibile generare il file con il cavo specifico utilizzando il comando:
   ```
   python genera_file_test.py cavo_specifico [directory] [prefisso]
   ```

3. **Opzione nel menu interattivo**: È stata aggiunta l'opzione "6. Cavo specifico dell'esempio" al menu interattivo.

## Dati del Cavo Specifico

Il cavo generato contiene i seguenti dati:

- **id_cavo**: "C001"
- **sistema**: "Lighting"
- **utility**: "Signal"
- **tipologia**: "FG16OR16"
- **formazione**: "1X240MM2"
- **metri_teorici**: 71.5
- **collegamenti**: 0
- **stato_installazione**: "Da installare"
- **id_bobina**: "BOBINA VUOTA"
- **timestamp**: "26/05/2025, 23:05"

Per i campi non specificati nell'esempio, viene utilizzato il valore predefinito "TBD".

## Come Utilizzare

### Da Linea di Comando

```bash
# Genera il file con il cavo specifico nella directory predefinita
python genera_file_test.py cavo_specifico

# Genera il file con il cavo specifico in una directory specifica
python genera_file_test.py cavo_specifico ./mia_directory

# Genera il file con il cavo specifico con un prefisso personalizzato
python genera_file_test.py cavo_specifico . mio_prefisso
```

### Dal Menu Interattivo

1. Esegui il programma senza argomenti:
   ```bash
   python genera_file_test.py
   ```

2. Seleziona l'opzione "6. Cavo specifico dell'esempio"

3. Inserisci opzionalmente una directory di output e un prefisso per il nome del file

## Verifica dell'Importazione

Per verificare che il file generato possa essere importato correttamente nel sistema:

1. Genera il file con il cavo specifico:
   ```bash
   python genera_file_test.py cavo_specifico
   ```

2. Importa il file generato utilizzando il modulo di importazione Excel:
   ```bash
   python test_import_excel.py
   ```
   Oppure utilizza l'interfaccia web per importare il file.

3. Verifica che il cavo sia stato importato correttamente nel database con tutti i campi specificati.

## Note Tecniche

- Il file generato contiene una riga di titolo e una riga di intestazioni, seguendo lo stesso formato dei file generati dalle altre funzioni.
- I campi come "collegamenti" e "stato_installazione" sono inclusi esplicitamente nel file, anche se non sono presenti nei file generati dalla funzione `genera_cavi_test`.
- Il campo "id_bobina" è impostato a "BOBINA VUOTA" come specificato nell'esempio, anche se il valore predefinito nel sistema è una stringa vuota.