# Risoluzione del problema di apertura dei file Excel

## Problema
Gli utenti riscontravano problemi nell'apertura dei file Excel generati dall'applicazione. Quando tentavano di aprire i file in Microsoft Excel, veniva mostrato un errore di file corrotto o estensione non valida.

## Causa
Il problema era causato dal modo in cui i file Excel venivano serviti attraverso l'API. In particolare:

1. I file Excel venivano generati correttamente con openpyxl
2. I file venivano salvati in una directory statica
3. L'API restituiva un URL al file statico
4. Quando l'utente scaricava il file tramite questo URL, il browser non riceveva le corrette intestazioni HTTP per i file Excel
5. Microsoft Excel non riconosceva il file come un file Excel valido a causa della mancanza di intestazioni HTTP appropriate

## Soluzione
La soluzione implementata consiste nel modificare gli endpoint API per servire i file Excel direttamente come download utilizzando `FileResponse` di FastAPI invece di restituire URL a file statici. Questo garantisce che:

1. Il file venga servito con il MIME type corretto (`application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`)
2. Le intestazioni HTTP appropriate vengano impostate (come `Content-Disposition: attachment`)
3. Il browser gestisca il file come un download diretto

### Modifiche apportate
Sono stati modificati i seguenti endpoint API:

1. `/api/excel/template-cavi` - Generazione template cavi
2. `/api/excel/template-parco-bobine` - Generazione template parco bobine
3. `/api/excel/{cantiere_id}/export-cavi` - Esportazione cavi
4. `/api/excel/{cantiere_id}/export-parco-bobine` - Esportazione parco bobine

Per ciascun endpoint, è stato modificato il tipo di risposta da `Dict[str, Any]` a `FileResponse` e il file viene ora servito direttamente come download invece di restituire un URL.

### Esempio di modifica
Prima:
```python
@router.get("/template-cavi", response_model=Dict[str, Any])
async def create_cavi_template(...):
    # ...
    file_url = get_file_url(static_path)
    return {"success": True, "file_url": file_url}
```

Dopo:
```python
@router.get("/template-cavi", response_class=FileResponse)
async def create_cavi_template(...):
    # ...
    return FileResponse(
        path=template_path,
        filename=template_filename,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    )
```

## Come testare
Per verificare che la soluzione funzioni correttamente:

1. Avvia il server FastAPI
2. Accedi all'interfaccia utente dell'applicazione
3. Prova a generare un template Excel (cavi o parco bobine)
4. Verifica che il file venga scaricato automaticamente
5. Apri il file scaricato in Microsoft Excel
6. Verifica che il file si apra correttamente senza errori

In alternativa, puoi utilizzare lo script `test_api_template.py` per testare direttamente gli endpoint API:

```bash
python test_api_template.py
```

Questo script testerà tutti gli endpoint API relativi ai file Excel e verificherà che:
- La risposta abbia il Content-Type corretto
- La risposta abbia l'intestazione Content-Disposition impostata per il download
- Il file possa essere scaricato correttamente

## Note tecniche
Utilizzando `FileResponse` invece di restituire un URL a un file statico, FastAPI:

1. Imposta automaticamente l'intestazione `Content-Disposition` per indicare che il file deve essere scaricato
2. Imposta il MIME type corretto (`application/vnd.openxmlformats-officedocument.spreadsheetml.sheet` per i file .xlsx)
3. Gestisce correttamente la lettura e l'invio del file al client

Questo approccio è più robusto e garantisce che i file Excel vengano riconosciuti correttamente da Microsoft Excel.