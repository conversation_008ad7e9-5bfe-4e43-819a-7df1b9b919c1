# Modifiche ai Campi del Database

Questo documento descrive le modifiche apportate ai campi del database per semplificare il sistema.

## Modifiche Effettuate

### 1. Campo "n_conduttori"
- Il campo è stato mantenuto nel database ma è stato reso "spare" (non utilizzato)
- È stato rimosso dalle interfacce utente (form e visualizzazioni)
- I dati esistenti sono conservati nel database per compatibilità con il sistema precedente

### 2. Campo "sezione"
- Il campo è stato rinominato a "Formazione" nell'interfaccia utente
- La struttura del database è rimasta invariata
- Questo campo ora rappresenta la formazione completa del cavo

### 3. Campo "SH" (schermato)
- Il campo è stato mantenuto nel database ma è stato reso "spare" (non utilizzato)
- È stato rimosso dalle interfacce utente (form e visualizzazioni)
- I dati esistenti sono conservati nel database per compatibilità con il sistema precedente

## Componenti Modificati

I seguenti componenti sono stati aggiornati per riflettere queste modifiche:

1. **CavoForm.js** - Rimossi i campi n_conduttori e SH, rinominato sezione a Formazione
2. **CavoDetailsView.js** - Rimossi i campi n_conduttori e SH, rinominato sezione a Formazione
3. **ParcoCavi.js** - Rimosso il campo n_conduttori, rinominato sezione a Formazione
4. **VisualizzaCaviPage.js** - Rimossi i campi n_conduttori e SH, rinominato sezione a Formazione
5. **InserisciMetriForm.js** - Aggiornata la visualizzazione dei cavi per rimuovere n_conduttori
6. **IncompatibleReelDialog.js** - Rinominato sezione a Formazione nella tabella di incompatibilità
7. **BobineFilterableTable.js** - Rimosso il campo n_conduttori, rinominato sezione a Formazione
8. **CaviFilterableTable.js** - Rimosso il campo n_conduttori, rinominato sezione a Formazione

## Note Importanti

1. **Compatibilità con il Database**: Nessuna modifica è stata apportata alla struttura del database. I campi sono stati mantenuti ma non vengono più utilizzati nell'interfaccia utente.

2. **Dati Esistenti**: I dati esistenti nei campi n_conduttori e SH sono conservati nel database per compatibilità con il sistema precedente.

3. **Logica di Business**: La logica di business che utilizzava questi campi è stata aggiornata per utilizzare solo il campo sezione (ora Formazione).

4. **Ricerca Bobine Compatibili**: La ricerca delle bobine compatibili ora si basa solo sulla tipologia e sulla formazione, ignorando il numero di conduttori e lo schermato.
