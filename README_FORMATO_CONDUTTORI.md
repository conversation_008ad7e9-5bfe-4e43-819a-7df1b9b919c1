# Correzione del Formato dei Conduttori nella Ricerca Bobine Compatibili

Questo documento descrive le modifiche apportate per risolvere il problema della ricerca delle bobine compatibili quando il formato dei conduttori è "X x Y".

## Problema

La funzionalità di ricerca delle bobine compatibili non funzionava correttamente quando il campo `n_conduttori` del cavo era nel formato "X x Y" (ad esempio "0 x 2"). In questo caso:

1. Nel frontend, il campo veniva visualizzato come "0 x 2"
2. Nel database, il campo era memorizzato come "0" (solo il primo numero)
3. Durante la ricerca, il sistema non trovava corrispondenze perché i formati non corrispondevano

## Soluzione

Abbiamo apportato le seguenti modifiche:

### 1. Backend (webapp/backend/api/parco_cavi.py)

- Aggiunto un controllo per rilevare il formato "X x Y" nel campo `n_conduttori`
- Se il formato è rilevato, viene estratto solo il primo numero (X) per la ricerca
- Migliorato il logging per facilitare il debug

### 2. Frontend (webapp/frontend/src/services/parcoCaviService.js)

- Aggiunto un controllo per rilevare il formato "X x Y" nel campo `n_conduttori`
- Se il formato è rilevato, viene estratto solo il primo numero (X) prima di inviare la richiesta al backend
- Migliorato il logging per facilitare il debug

### 3. Componente React (webapp/frontend/src/components/cavi/InserisciMetriForm.js)

- Aggiunto un controllo per rilevare il formato "X x Y" nel campo `n_conduttori` sia nella chiamata API che nel filtro manuale
- Se il formato è rilevato, viene estratto solo il primo numero (X) per la ricerca
- Migliorato il logging per facilitare il debug

## Esempio

Prima della modifica:
- Cavo con n_conduttori = "0 x 2"
- Bobina con n_conduttori = "0"
- Risultato: Nessuna bobina compatibile trovata

Dopo la modifica:
- Cavo con n_conduttori = "0 x 2" -> convertito in "0" per la ricerca
- Bobina con n_conduttori = "0"
- Risultato: Bobina compatibile trovata

## Note Importanti

1. La soluzione mantiene il comportamento originale per i campi `n_conduttori` che non sono nel formato "X x Y"
2. Il sistema ora gestisce correttamente entrambi i formati
3. I log sono stati migliorati per facilitare il debug in caso di problemi futuri

## Conclusione

Queste modifiche risolvono il problema della ricerca delle bobine compatibili quando il formato dei conduttori è "X x Y", garantendo che il sistema funzioni correttamente indipendentemente dal formato utilizzato.
