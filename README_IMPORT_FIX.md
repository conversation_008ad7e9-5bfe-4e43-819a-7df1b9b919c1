# Risoluzione Errore di Importazione Excel

## Problema
Durante l'importazione di file Excel generati con `genera_file_test.py`, il sistema restituiva un "errore sconosciuto". Dopo un'analisi approfondita, sono stati identificati due problemi principali:

1. **Intestazioni non riconosciute**: Il file Excel generato contiene una riga di titolo prima delle intestazioni delle colonne, che causava problemi di riconoscimento delle colonne durante l'importazione.

2. **Colonne mancanti**: Il file Excel generato non includeva la colonna `n_conduttori`, che è richiesta dal processo di importazione.

3. **Violazione di chiave esterna**: L'importazione falliva con un errore di violazione di chiave esterna quando `id_bobina` era vuoto.

## Soluzioni Implementate

### 1. Riconoscimento delle intestazioni
Modificata la funzione `leggi_file_excel` in `modules/excel_manager.py` per rilevare automaticamente se la prima riga è un titolo e utilizzare la seconda riga come intestazioni delle colonne. La funzione ora:
- Verifica se la prima riga ha meno celle non vuote della seconda
- Controlla se la prima riga contiene parole chiave tipiche di un titolo
- Se rileva un titolo, utilizza la seconda riga come intestazioni

### 2. Derivazione di colonne mancanti
Aggiunta logica alla funzione `valida_colonne_excel` per derivare automaticamente il valore di `n_conduttori` dalla colonna `formazione` quando `n_conduttori` non è presente. La funzione ora:
- Verifica se `n_conduttori` è mancante ma `formazione` o `sezione` è presente
- Estrae il numero di conduttori dalla stringa di formazione (es. "3x2.5" → "3")
- Crea la colonna `n_conduttori` con i valori derivati

### 3. Gestione della chiave esterna
Modificato il valore predefinito per `id_bobina` da stringa vuota a "BOBINA_VUOTA" nella funzione `valida_colonne_excel`. Questo evita violazioni di chiave esterna, poiché "BOBINA_VUOTA" è un record speciale che dovrebbe sempre esistere nella tabella `parco_cavi`.

## Come Testare le Modifiche

1. **Generare un file di test**:
   ```
   python genera_file_test.py cavi 10
   ```

2. **Importare il file generato**:
   ```
   python test_import_excel.py
   ```
   
   Oppure utilizzare l'interfaccia web per importare il file.

3. **Verificare il successo dell'importazione**:
   - Controllare che non vengano visualizzati errori
   - Verificare che i cavi siano stati importati correttamente nel database
   - Controllare il report di importazione generato nella cartella `exports`

## Note Tecniche

- La funzione `extract_conductors` utilizza espressioni regolari per estrarre il numero di conduttori dalle stringhe di formazione
- Il rilevamento della riga di titolo è euristico e potrebbe richiedere aggiustamenti per file con formati molto diversi
- Il valore "BOBINA_VUOTA" è un record speciale che deve esistere nella tabella `parco_cavi` per evitare violazioni di chiave esterna

## Conclusione

Queste modifiche rendono il processo di importazione più robusto e in grado di gestire file Excel con strutture leggermente diverse, come quelli generati da `genera_file_test.py`. L'importazione ora funziona correttamente anche quando:
- Il file ha una riga di titolo prima delle intestazioni
- Mancano alcune colonne che possono essere derivate da altre
- I valori di `id_bobina` sono vuoti