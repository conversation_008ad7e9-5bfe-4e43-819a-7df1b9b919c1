# Risoluzione Errore di Importazione Parco Bobine

## Problema
Durante l'importazione di file Excel per il parco bobine, il popup mostrava un errore e non riusciva a importare nulla. Dopo un'analisi approfondita, è stato identificato che il problema era causato dalla funzione di importazione che richiedeva input interattivo dall'utente, cosa impossibile in un ambiente web.

## Causa
La funzione `importa_parco_bobine_da_excel` in `modules/excel_manager.py` contiene diversi prompt interattivi che richiedono input dall'utente:

1. Richiesta di configurazione per la numerazione delle bobine (numeri progressivi o codici personalizzati)
2. Richiesta di cosa fare quando manca un codice bobina (inserimento manuale, generazione automatica o salto della riga)

Questi prompt causavano il blocco della funzione quando veniva chiamata dall'API, poiché non c'era modo per l'utente di fornire input attraverso l'interfaccia web.

## Soluzione
È stato modificato l'endpoint API `import_parco_bobine` in `webapp/backend/api/excel.py` per passare parametri predefiniti alla funzione `importa_parco_bobine_da_excel`, consentendole di funzionare in modalità non interattiva:

```python
success, message, _ = importa_parco_bobine_da_excel(
    percorso_file=str(temp_file), 
    id_cantiere=cantiere_id,
    config_choice="1",  # Usa numerazione progressiva
    auto_choice="2"     # Genera automaticamente codici con prefisso AUTO
)
```

Con queste modifiche:
- La funzione utilizza la numerazione progressiva per le bobine (opzione 1)
- Quando manca un codice bobina, viene generato automaticamente un codice con prefisso "AUTO" (opzione 2)
- Non viene richiesto alcun input all'utente, consentendo all'importazione di completarsi senza errori

## Come Testare
Per verificare che la correzione funzioni:

1. **Generare un file di test per il parco bobine**:
   ```
   python genera_file_test.py bobine 10
   ```

2. **Importare il file generato tramite l'interfaccia web**:
   - Accedere all'applicazione web
   - Navigare alla sezione di gestione del parco bobine
   - Cliccare su "Importa da Excel"
   - Selezionare il file generato
   - Verificare che l'importazione avvenga senza errori

3. **Verificare i dati importati**:
   - Controllare che le bobine siano state importate correttamente
   - Verificare che i codici bobina siano stati generati secondo la configurazione specificata

## Note Tecniche
- La modifica è stata apportata solo all'endpoint API, senza alterare la funzione di importazione sottostante
- I valori predefiniti scelti (config_choice="1", auto_choice="2") rappresentano le opzioni più comuni e utili per la maggior parte degli utenti
- La funzione mantiene la sua capacità di funzionare in modalità interattiva quando chiamata da script a riga di comando

## Conclusione
Questa modifica risolve il problema dell'importazione del parco bobine che non funzionava nell'interfaccia web. Gli utenti possono ora importare file Excel per il parco bobine senza errori, migliorando significativamente l'usabilità del sistema.