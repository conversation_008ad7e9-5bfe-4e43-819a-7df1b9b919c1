# Risoluzione Errore "Errore Sconosciuto" nel Popup di Importazione Excel

## Problema
Durante l'importazione di file Excel per il parco bobine, il popup mostrava un generico "errore sconosciuto" invece di un messaggio di errore specifico. Questo rendeva difficile per gli utenti capire cosa fosse andato storto durante l'importazione.

## Causa
Dopo un'analisi del codice, è stato identificato che il problema era nel componente `ExcelPopup.js`. La funzione `handleImportaParcoBobine` non aveva un blocco `catch` per gestire gli errori, a differenza della funzione `handleImportaCavi` che invece gestiva correttamente gli errori.

Quando si verificava un errore durante l'importazione del parco bobine, l'errore non veniva catturato e gestito correttamente, risultando in un messaggio generico di "errore sconosciuto" o in alcuni casi nessun messaggio di errore visibile.

## Soluzione
È stato aggiunto un blocco `catch` alla funzione `handleImportaParcoBobine` in `ExcelPopup.js` per gestire correttamente gli errori:

```javascript
catch (error) {
  onError('Errore nell\'importazione del parco bobine: ' + (error.message || 'Errore sconosciuto'));
  console.error('Errore nell\'importazione del parco bobine:', error);
}
```

Questo blocco `catch` è simile a quello presente nella funzione `handleImportaCavi`, ma con un messaggio specifico per il parco bobine.

## Benefici
Con questa modifica:
1. Gli errori durante l'importazione del parco bobine vengono ora catturati e gestiti correttamente
2. Gli utenti vedono messaggi di errore specifici invece di un generico "errore sconosciuto"
3. Gli errori vengono registrati nella console per facilitare il debug

## Come Testare
Per verificare che la correzione funzioni:
1. Aprire l'applicazione e navigare alla sezione di gestione del parco bobine
2. Tentare di importare un file Excel non valido o con dati errati
3. Verificare che venga mostrato un messaggio di errore specifico invece di "errore sconosciuto"
4. Controllare la console del browser per verificare che l'errore sia stato registrato correttamente

## Note Tecniche
La modifica è stata apportata al file `webapp\frontend\src\components\cavi\ExcelPopup.js`, aggiungendo un blocco `catch` alla funzione `handleImportaParcoBobine`. Questa è una modifica minima che non dovrebbe avere effetti collaterali su altre parti dell'applicazione.