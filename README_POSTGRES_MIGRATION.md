# Migrazione a PostgreSQL

Questo documento descrive il processo di migrazione del database da SQLite a PostgreSQL per il sistema CMS.

## Prerequisiti

Prima di iniziare la migrazione, assicurati di avere:

1. PostgreSQL installato e in esecuzione
2. Un database PostgreSQL vuoto chiamato `cantieri` (o il nome che preferisci)
3. Un utente PostgreSQL con permessi di creazione tabelle e inserimento dati
4. Il modulo Python `psycopg2` installato

```bash
pip install psycopg2
```

## Configurazione

La configurazione del database PostgreSQL può essere impostata tramite variabili d'ambiente:

```bash
# Windows
set DB_HOST=localhost
set DB_PORT=5432
set DB_NAME=cantieri
set DB_USER=postgres
set DB_PASSWORD=Taranto

# Linux/macOS
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=cantieri
export DB_USER=postgres
export DB_PASSWORD=Taranto
```

In alternativa, puoi modificare i valori predefiniti nel file `modules/database_pg.py`.

## Migrazione dei Dati

Per migrare i dati da SQLite a PostgreSQL, esegui lo script `migrate_to_postgres.py`:

```bash
python migrate_to_postgres.py
```

Lo script eseguirà le seguenti operazioni:

1. Creare le tabelle necessarie nel database PostgreSQL
2. Migrare i dati da SQLite a PostgreSQL
3. Aggiornare le sequenze per gli ID autoincrement

## Utilizzo del Sistema con PostgreSQL

Dopo la migrazione, il sistema utilizzerà automaticamente PostgreSQL invece di SQLite. Non è necessario modificare il codice esistente, poiché l'interfaccia del modulo database rimane la stessa.

## Risoluzione dei Problemi

### Errore di Connessione

Se ricevi un errore di connessione, verifica che:

- PostgreSQL sia in esecuzione
- Le credenziali siano corrette
- Il database esista
- L'utente abbia i permessi necessari

### Errori di Migrazione

Se la migrazione fallisce, controlla il file `migration.log` per i dettagli dell'errore. I problemi più comuni sono:

- Vincoli di chiave esterna non rispettati
- Tipi di dati incompatibili
- Errori di sintassi SQL

## Rollback

Se è necessario tornare a SQLite, modifica il file `modules/__init__.py` per importare da `database` invece di `database_pg`:

```python
from .cavi import *
from .database import *  # Cambia da database_pg a database
from .utils import *
from .excel_manager import *
```

E aggiorna anche il file `main.py` per importare da `database` invece di `database_pg`.

## Note Importanti

- La migrazione non modifica la logica dell'applicazione, ma solo il backend del database
- Tutti i dati esistenti vengono preservati durante la migrazione
- Le prestazioni dovrebbero migliorare con PostgreSQL, specialmente con grandi volumi di dati
- PostgreSQL offre migliori funzionalità di backup e ripristino rispetto a SQLite
