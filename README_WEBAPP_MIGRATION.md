# Migrazione da CLI a WebApp

Questo documento descrive il processo di migrazione del sistema CMS da un'applicazione CLI a una WebApp moderna.

## Piano di Migrazione

La migrazione avverrà in modo graduale, modulo per modulo, mantenendo la stessa logica di business esistente. Il processo seguirà queste fasi:

### Fase 1: Setup del Progetto e Autenticazione

1. **Creazione della struttura del progetto**
   - Creazione della directory `webapp` con sottodirectory per backend e frontend
   - Setup del framework FastAPI per il backend
   - Setup del framework React per il frontend

2. **Implementazione del modulo di autenticazione**
   - API di autenticazione (login, logout)
   - Supporto per tutti i tipi di utenti (admin, utente standard, utente cantiere)
   - Gestione dei token JWT
   - Interfaccia utente per il login

### Fase 2: Gestione Cantieri

1. **API per la gestione dei cantieri**
   - Visualizzazione, creazione, modifica ed eliminazione dei cantieri
   - Selezione del cantiere attivo

2. **Interfaccia utente per la gestione dei cantieri**
   - Lista dei cantieri
   - Form per la creazione e modifica
   - Funzionalità di eliminazione

### Fase 3: Gestione Cavi

1. **API per la gestione dei cavi**
   - CRUD per i cavi
   - Funzionalità di posa e collegamento

2. **Interfaccia utente per la gestione dei cavi**
   - Lista dei cavi
   - Form per la creazione e modifica
   - Visualizzazione dello stato dei cavi

### Fase 4: Certificazione Cavi

1. **API per la certificazione dei cavi**
   - CRUD per le certificazioni
   - Generazione di PDF

2. **Interfaccia utente per la certificazione**
   - Form per la creazione delle certificazioni
   - Visualizzazione delle certificazioni esistenti
   - Download dei PDF

### Fase 5: Report

1. **API per la generazione di report**
   - Vari tipi di report
   - Export in diversi formati

2. **Interfaccia utente per i report**
   - Selezione del tipo di report
   - Visualizzazione e download dei report

### Fase 6: Import/Export Excel

1. **API per l'import/export Excel**
   - Upload e download di file Excel
   - Generazione di template

2. **Interfaccia utente per Excel**
   - Form per l'upload
   - Download di file e template

### Fase 7: Testing e Deployment

1. **Testing completo**
   - Test unitari per il backend
   - Test di integrazione per il frontend
   - Test end-to-end

2. **Deployment**
   - Configurazione di Docker
   - Setup di Nginx
   - CI/CD pipeline

## Stack Tecnologico

### Backend
- **Framework**: FastAPI
- **ORM**: SQLAlchemy
- **Database**: PostgreSQL
- **Autenticazione**: JWT

### Frontend
- **Framework**: React
- **UI Library**: Material-UI
- **State Management**: Context API / Redux
- **Routing**: React Router

## Principi Guida

1. **Mantenere la logica di business esistente**
   - Non modificare la logica esistente senza autorizzazione esplicita
   - Riutilizzare il codice esistente quando possibile

2. **Approccio modulare**
   - Migrare un modulo alla volta
   - Testare completamente ogni modulo prima di passare al successivo

3. **Compatibilità**
   - Garantire che la WebApp funzioni con gli stessi dati della CLI
   - Mantenere la compatibilità con il database esistente

4. **Usabilità**
   - Migliorare l'esperienza utente rispetto alla CLI
   - Mantenere la familiarità per gli utenti esistenti
