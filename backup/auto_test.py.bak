# -*- coding: utf-8 -*-
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from modules.database import database_connection
from modules.utils import ValidazioneCampi

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Stati possibili per i test
STATI_TEST = ["Passato", "Fallito", "N/A"]

# Questo file è stato sostituito da certificazione_cavi.py
# Mantenuto come backup per riferimento