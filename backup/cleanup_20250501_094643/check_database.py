#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per verificare lo stato del database.
"""

import sqlite3
import logging
import os

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_database(db_name='cantieri_fresh.db'):
    """
    Verifica lo stato del database.
    
    Args:
        db_name: Nome del database
    """
    try:
        # Verifica se il database esiste
        if not os.path.exists(db_name):
            logging.error(f"❌ Il database {db_name} non esiste!")
            return
        
        # Crea una connessione al database
        conn = sqlite3.connect(db_name)
        c = conn.cursor()
        
        # Ottieni l'elenco delle tabelle
        c.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = c.fetchall()
        
        if not tables:
            logging.error(f"❌ Il database {db_name} non contiene tabelle!")
            return
        
        logging.info(f"Tabelle nel database {db_name}:")
        for table in tables:
            table_name = table[0]
            logging.info(f"- {table_name}")
            
            # Ottieni la struttura della tabella
            c.execute(f"PRAGMA table_info({table_name})")
            columns = c.fetchall()
            
            logging.info(f"  Colonne nella tabella {table_name}:")
            for column in columns:
                column_name = column[1]
                column_type = column[2]
                logging.info(f"  - {column_name} ({column_type})")
            
            # Ottieni il numero di righe nella tabella
            c.execute(f"SELECT COUNT(*) FROM {table_name}")
            row_count = c.fetchone()[0]
            logging.info(f"  Numero di righe: {row_count}")
            
            # Se è la tabella Utenti, verifica se esiste l'utente admin
            if table_name == "Utenti":
                c.execute("SELECT id_utente, username, ruolo FROM Utenti WHERE username = 'admin'")
                admin = c.fetchone()
                if admin:
                    logging.info(f"  ✅ Utente admin trovato (ID: {admin[0]}, Ruolo: {admin[2]})")
                else:
                    logging.warning(f"  ⚠️ Utente admin non trovato!")
        
        conn.close()
        
    except Exception as e:
        logging.error(f"❌ Errore durante la verifica del database: {str(e)}")

if __name__ == "__main__":
    logging.info("Inizio verifica del database...")
    check_database()
    logging.info("Verifica del database completata")
