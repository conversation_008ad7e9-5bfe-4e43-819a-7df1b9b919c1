#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per verificare i percorsi del database in tutti i file.
"""

import os
import logging
import re

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_db_paths(expected_db_name='cantieri_fresh.db'):
    """
    Verifica i percorsi del database in tutti i file.
    
    Args:
        expected_db_name: Nome atteso del database
    """
    try:
        # Lista dei file da controllare
        files_to_check = []
        
        # Aggiungi tutti i file .py nella directory modules
        for file in os.listdir('modules'):
            if file.endswith('.py'):
                files_to_check.append(os.path.join('modules', file))
        
        # Aggiungi il file main.py
        if os.path.exists('main.py'):
            files_to_check.append('main.py')
        
        # Contatore dei file con percorsi diversi
        inconsistent_files = 0
        
        # <PERSON>la ogni file
        for file_path in files_to_check:
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
                
                # Cerca tutte le occorrenze di percorsi di database
                db_paths = re.findall(r"['\"]([^'\"]*\.db)['\"]", content)
                
                if db_paths:
                    for db_path in set(db_paths):  # Usa set per eliminare i duplicati
                        if db_path != expected_db_name and 'test_' not in db_path and 'backup' not in db_path:
                            logging.warning(f"⚠️ Percorso del database non corretto in {file_path}: {db_path}")
                            inconsistent_files += 1
            except Exception as e:
                logging.error(f"❌ Errore durante la verifica del file {file_path}: {str(e)}")
        
        if inconsistent_files == 0:
            logging.info(f"✅ Tutti i file utilizzano il percorso del database corretto: {expected_db_name}")
        else:
            logging.warning(f"⚠️ Trovati {inconsistent_files} file con percorsi del database non corretti")
        
    except Exception as e:
        logging.error(f"❌ Errore durante la verifica dei percorsi del database: {str(e)}")

if __name__ == "__main__":
    logging.info("Inizio verifica dei percorsi del database...")
    check_db_paths()
    logging.info("Verifica dei percorsi del database completata")
