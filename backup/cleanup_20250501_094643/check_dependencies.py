#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per verificare le dipendenze tra i file.
"""

import os
import re
import logging

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_dependencies(files_to_check):
    """
    Verifica le dipendenze tra i file.
    
    Args:
        files_to_check: Lista di file da controllare
    """
    try:
        # Ottieni tutti i file Python nel progetto
        python_files = []
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))
        
        # Per ogni file da controllare, verifica se è importato da altri file
        for file_to_check in files_to_check:
            file_name = os.path.basename(file_to_check)
            module_name = os.path.splitext(file_name)[0]
            
            dependencies = []
            
            for python_file in python_files:
                if python_file == file_to_check:
                    continue
                
                try:
                    with open(python_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                        # Cerca pattern di import
                        patterns = [
                            rf"import\s+{module_name}",
                            rf"from\s+{module_name}\s+import",
                            rf"import\s+.*\s+as\s+{module_name}"
                        ]
                        
                        for pattern in patterns:
                            if re.search(pattern, content):
                                dependencies.append(python_file)
                                break
                except Exception as e:
                    logging.warning(f"Errore durante la lettura del file {python_file}: {str(e)}")
            
            if dependencies:
                logging.warning(f"⚠️ Il file {file_to_check} è importato dai seguenti file:")
                for dependency in dependencies:
                    logging.warning(f"  - {dependency}")
            else:
                logging.info(f"✅ Il file {file_to_check} non è importato da altri file")
        
    except Exception as e:
        logging.error(f"❌ Errore durante la verifica delle dipendenze: {str(e)}")

if __name__ == "__main__":
    # Lista dei file da controllare
    files_to_check = [
        'check_database.py',
        'check_db_paths.py',
        'check_obsolete_files.py',
        'check_schema.py',
        'check_tables.py',
        'create_new_database.py',
        'create_test_cantiere.py',
        'fix_admin_password.py',
        'recreate_database.py',
        'reset_database.py',
        'test_certificazione_cavi.py',
        'test_create_certificazione.py',
        'test_login.py',
        'update_all_db_paths.py',
        'update_database_certificazioni.py',
        'update_db_path.py',
        'utenti.py'
    ]
    
    check_dependencies(files_to_check)
