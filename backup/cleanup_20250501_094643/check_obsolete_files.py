#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per verificare la presenza di file obsoleti.
"""

import os
import logging

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_obsolete_files():
    """
    Verifica la presenza di file obsoleti.
    """
    try:
        # Lista dei file obsoleti
        obsolete_files = [
            'modules/test_cavi.py',
            'modules/auto_test.py',
            'cantieri.db',
            'cantieri_new.db'
        ]
        
        # Verifica se i file obsoleti esistono
        for file_path in obsolete_files:
            if os.path.exists(file_path):
                logging.warning(f"⚠️ File obsoleto trovato: {file_path}")
            else:
                logging.info(f"✅ File obsoleto non trovato: {file_path}")
        
        # Verifica la presenza di file di backup
        backup_files = []
        
        # Cerca file di backup nella directory principale
        for file in os.listdir('.'):
            if file.endswith('.bak') or file.endswith('.old') or file.endswith('.backup'):
                backup_files.append(file)
        
        # Cerca file di backup nella directory backup
        if os.path.exists('backup'):
            for file in os.listdir('backup'):
                if file.endswith('.bak') or file.endswith('.old') or file.endswith('.backup'):
                    backup_files.append(os.path.join('backup', file))
        
        if backup_files:
            logging.info(f"File di backup trovati: {len(backup_files)}")
            for file in backup_files:
                logging.info(f"- {file}")
        else:
            logging.info("Nessun file di backup trovato")
        
    except Exception as e:
        logging.error(f"❌ Errore durante la verifica dei file obsoleti: {str(e)}")

if __name__ == "__main__":
    logging.info("Inizio verifica dei file obsoleti...")
    check_obsolete_files()
    logging.info("Verifica dei file obsoleti completata")
