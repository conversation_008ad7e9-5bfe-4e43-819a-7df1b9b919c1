#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sqlite3

def check_tables(db_name='cantieri_new.db'):
    """
    Verifica le tabelle presenti nel database.
    """
    print(f"Verifica delle tabelle nel database {db_name}...")
    print(f"Il database esiste: {os.path.exists(db_name)}")
    
    try:
        conn = sqlite3.connect(db_name)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"Tabelle nel database: {[table[0] for table in tables]}")
        
        conn.close()
    except Exception as e:
        print(f"Errore durante la verifica delle tabelle: {str(e)}")

if __name__ == "__main__":
    check_tables()
