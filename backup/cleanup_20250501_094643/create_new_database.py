#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per creare un nuovo database con la struttura corretta.
"""

import os
import logging
import sqlite3
from datetime import datetime

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_new_database(db_name='cantieri_new.db'):
    """
    Crea un nuovo database con la struttura corretta.
    
    Args:
        db_name: Nome del nuovo database
    
    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        # Crea una connessione al nuovo database
        conn = sqlite3.connect(db_name)
        c = conn.cursor()
        
        # Abilita il supporto per le chiavi esterne
        c.execute("PRAGMA foreign_keys = ON")
        
        # Creazione tabella Utenti
        c.execute('''CREATE TABLE IF NOT EXISTS Utenti (
            id_utente INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            ruolo TEXT NOT NULL CHECK (ruolo IN ('owner', 'user', 'cantieri_user')),
            data_scadenza DATE,
            abilitato BOOLEAN DEFAULT 1,
            created_by INTEGER,
            FOREIGN KEY (created_by) REFERENCES Utenti(id_utente)
        )''')
        
        # Creazione tabella Cantieri
        c.execute('''CREATE TABLE IF NOT EXISTS Cantieri (
            id_cantiere INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            descrizione TEXT,
            data_creazione TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            password_cantiere TEXT NOT NULL,
            id_utente INTEGER NOT NULL,
            codice_univoco TEXT UNIQUE NOT NULL,
            FOREIGN KEY (id_utente) REFERENCES Utenti(id_utente)
        )''')
        
        # Creazione tabella Cavi
        c.execute('''CREATE TABLE IF NOT EXISTS Cavi (
            id_cavo TEXT NOT NULL,
            id_cantiere INTEGER NOT NULL,
            revisione_ufficiale TEXT NOT NULL DEFAULT '00',
            sistema TEXT,
            utility TEXT NOT NULL,
            colore_cavo TEXT,
            tipologia TEXT NOT NULL,
            n_conduttori INTEGER NOT NULL,
            sezione TEXT NOT NULL,
            SH TEXT,
            ubicazione_partenza TEXT NOT NULL,
            utenza_partenza TEXT,
            descrizione_utenza_partenza TEXT,
            ubicazione_arrivo TEXT NOT NULL,
            utenza_arrivo TEXT,
            descrizione_utenza_arrivo TEXT,
            metri_teorici REAL NOT NULL,
            metratura_reale REAL DEFAULT 0,
            responsabile_posa TEXT,
            id_bobina TEXT,
            stato_installazione TEXT NOT NULL,
            modificato_manualmente INTEGER DEFAULT 0,
            data_posa DATE,
            collegamenti INTEGER DEFAULT 0,  -- Flag per i collegamenti: 0=nessuno, 1=partenza, 2=arrivo, 3=entrambi
            responsabile_partenza TEXT,      -- Responsabile del collegamento lato partenza
            responsabile_arrivo TEXT,        -- Responsabile del collegamento lato arrivo
            comanda_posa TEXT,               -- Riferimento alla comanda di posa
            comanda_partenza TEXT,           -- Riferimento alla comanda di collegamento partenza
            comanda_arrivo TEXT,             -- Riferimento alla comanda di collegamento arrivo
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id_cavo, id_cantiere),  -- Chiave primaria composta
            FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE,
            FOREIGN KEY (id_bobina) REFERENCES parco_cavi(ID_BOBINA)
        )''')
        
        # Creazione tabella parco_cavi
        c.execute('''CREATE TABLE IF NOT EXISTS parco_cavi (
            ID_BOBINA TEXT PRIMARY KEY,
            numero_bobina TEXT NOT NULL,
            utility TEXT NOT NULL,
            tipologia TEXT NOT NULL,
            n_conduttori INTEGER NOT NULL,
            sezione REAL NOT NULL,
            metri_totali REAL NOT NULL,
            metri_residui REAL NOT NULL,
            stato_bobina TEXT NOT NULL,
            ubicazione_bobina TEXT,
            fornitore TEXT,
            n_DDT TEXT,
            data_DDT DATE,
            configurazione TEXT,
            id_cantiere INTEGER,  -- Nuova colonna per collegare la bobina a un cantiere
            FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE SET NULL  -- Vincolo di chiave esterna
        )''')
        
        # Tabella CertificazioniCavi per gestire le certificazioni dei cavi
        c.execute('''
            CREATE TABLE IF NOT EXISTS CertificazioniCavi (
                id_certificazione INTEGER PRIMARY KEY AUTOINCREMENT,
                id_cantiere INTEGER NOT NULL,
                id_cavo TEXT NOT NULL,
                numero_certificato TEXT NOT NULL,  -- Numero progressivo univoco del certificato
                data_certificazione DATE NOT NULL,
                id_operatore TEXT,  -- Operatore che ha eseguito la certificazione
                strumento_utilizzato TEXT,  -- modello/seriale dello strumento di misura

                -- Valori di metratura (presi direttamente dal cavo)
                lunghezza_misurata REAL,  -- in metri

                -- Risultati dei test principali (solo il valore misurato, il risultato è sempre "Passato")
                valore_continuita TEXT,
                valore_isolamento TEXT,
                valore_resistenza TEXT,

                -- Percorsi file
                percorso_certificato TEXT,  -- percorso file PDF del certificato
                percorso_foto TEXT,  -- percorso file della foto

                -- Metadati
                note TEXT,
                timestamp_creazione TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                timestamp_modifica TIMESTAMP,

                FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE,
                FOREIGN KEY (id_cavo, id_cantiere) REFERENCES Cavi(id_cavo, id_cantiere) ON DELETE CASCADE
            )
        ''')
        
        # Tabella Comande per gestire le comande di lavoro
        c.execute('''
            CREATE TABLE IF NOT EXISTS Comande (
                codice_comanda VARCHAR(50) PRIMARY KEY,
                tipo_comanda VARCHAR(20) NOT NULL,  -- 'POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'
                descrizione TEXT,
                data_creazione DATE NOT NULL,
                data_scadenza DATE,
                responsabile VARCHAR(100),
                stato VARCHAR(20) NOT NULL,  -- 'CREATA', 'ASSEGNATA', 'IN_CORSO', 'COMPLETATA'
                id_cantiere INTEGER NOT NULL,
                FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE
            )
        ''')
        
        # Creazione indici ottimizzati
        c.execute('''CREATE INDEX IF NOT EXISTS idx_cantieri_id_utente ON Cantieri(id_utente)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_cavi_id_cantiere ON Cavi(id_cantiere)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_cavi_id_bobina ON Cavi(id_bobina)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_parco_cavi_numero_bobina ON parco_cavi(numero_bobina)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_parco_cavi_id_cantiere ON parco_cavi(id_cantiere)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_cavi_data_posa ON Cavi(data_posa)''')
        
        # Indici per CertificazioniCavi
        c.execute('''CREATE INDEX IF NOT EXISTS idx_certificazioni_id_cantiere ON CertificazioniCavi(id_cantiere)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_certificazioni_id_cavo ON CertificazioniCavi(id_cavo)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_certificazioni_numero ON CertificazioniCavi(numero_certificato)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_certificazioni_data ON CertificazioniCavi(data_certificazione)''')
        
        # Indice per il campo collegamenti nella tabella Cavi
        c.execute('''CREATE INDEX IF NOT EXISTS idx_cavi_collegamenti ON Cavi(collegamenti)''')
        
        # Crea un utente admin predefinito
        c.execute('''
            INSERT OR IGNORE INTO Utenti (username, password, ruolo, abilitato)
            VALUES (?, ?, ?, ?)
        ''', ('admin', 'admin', 'owner', 1))
        
        conn.commit()
        conn.close()
        
        logging.info(f"✅ Nuovo database creato con successo: {db_name}")
        return True
        
    except Exception as e:
        logging.error(f"❌ Errore durante la creazione del nuovo database: {str(e)}")
        return False

if __name__ == "__main__":
    logging.info("Inizio creazione del nuovo database...")
    if create_new_database():
        logging.info("✅ Creazione del nuovo database completata con successo")
    else:
        logging.error("❌ Creazione del nuovo database fallita")
