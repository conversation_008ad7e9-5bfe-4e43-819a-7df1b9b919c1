#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per creare un cantiere di test nel database.
"""

import sqlite3
import logging
import uuid
import bcrypt
from datetime import datetime

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_test_cantiere(db_name='cantieri_fresh.db'):
    """
    Crea un cantiere di test nel database.
    
    Args:
        db_name: Nome del database
    
    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        # Crea una connessione al database
        conn = sqlite3.connect(db_name)
        c = conn.cursor()
        
        # Verifica se l'utente admin esiste
        c.execute("SELECT id_utente FROM Utenti WHERE username = 'admin'")
        admin = c.fetchone()
        
        if not admin:
            logging.error("❌ Utente admin non trovato!")
            return False
        
        admin_id = admin[0]
        
        # Genera un codice univoco per il cantiere
        codice_univoco = str(uuid.uuid4())[:8].upper()
        
        # Crea la password del cantiere (hash di "password")
        password_cantiere = bcrypt.hashpw('password'.encode('utf-8'), bcrypt.gensalt())
        
        # Crea il cantiere di test
        c.execute("""
            INSERT INTO Cantieri (nome, descrizione, data_creazione, password_cantiere, id_utente, codice_univoco)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            'Cantiere Test',
            'Cantiere di test creato automaticamente',
            datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            password_cantiere,
            admin_id,
            codice_univoco
        ))
        
        conn.commit()
        
        # Verifica che il cantiere sia stato creato
        c.execute("SELECT id_cantiere, nome, codice_univoco FROM Cantieri WHERE nome = 'Cantiere Test'")
        cantiere = c.fetchone()
        
        if cantiere:
            logging.info(f"✅ Cantiere di test creato con successo (ID: {cantiere[0]}, Codice: {cantiere[2]})")
            conn.close()
            return True
        else:
            logging.error("❌ Errore nella creazione del cantiere di test!")
            conn.close()
            return False
        
    except Exception as e:
        logging.error(f"❌ Errore durante la creazione del cantiere di test: {str(e)}")
        return False

if __name__ == "__main__":
    logging.info("Inizio creazione del cantiere di test...")
    if create_test_cantiere():
        logging.info("✅ Creazione del cantiere di test completata con successo")
    else:
        logging.error("❌ Creazione del cantiere di test fallita")
