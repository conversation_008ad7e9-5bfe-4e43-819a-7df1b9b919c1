#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per correggere la password dell'admin nel database.
"""

import sqlite3
import logging
import bcrypt

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def fix_admin_password(db_name='cantieri_fresh.db'):
    """
    Corregge la password dell'admin nel database.
    
    Args:
        db_name: Nome del database
    
    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        # Crea una connessione al database
        conn = sqlite3.connect(db_name)
        c = conn.cursor()
        
        # Verifica se l'utente admin esiste
        c.execute("SELECT id_utente, password FROM Utenti WHERE username = 'admin'")
        admin = c.fetchone()
        
        if admin:
            # Genera l'hash della password 'admin'
            hashed_password = bcrypt.hashpw('admin'.encode('utf-8'), bcrypt.gensalt())
            
            # Aggiorna la password dell'admin
            c.execute("UPDATE Utenti SET password = ? WHERE username = 'admin'", (hashed_password,))
            conn.commit()
            
            logging.info("✅ Password dell'admin corretta con successo")
        else:
            # Crea l'utente admin se non esiste
            hashed_password = bcrypt.hashpw('admin'.encode('utf-8'), bcrypt.gensalt())
            c.execute("""
                INSERT INTO Utenti (username, password, ruolo, abilitato)
                VALUES (?, ?, ?, ?)
            """, ('admin', hashed_password, 'owner', 1))
            conn.commit()
            
            logging.info("✅ Utente admin creato con successo")
        
        conn.close()
        return True
        
    except Exception as e:
        logging.error(f"❌ Errore durante la correzione della password dell'admin: {str(e)}")
        return False

if __name__ == "__main__":
    logging.info("Inizio correzione della password dell'admin...")
    if fix_admin_password():
        logging.info("✅ Correzione della password dell'admin completata con successo")
    else:
        logging.error("❌ Correzione della password dell'admin fallita")
