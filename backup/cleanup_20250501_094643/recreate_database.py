#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per ricreare il database con la struttura corretta.
"""

import os
import logging
import sqlite3
from datetime import datetime

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def recreate_database(db_name='cantieri_fresh.db'):
    """
    Ricrea il database con la struttura corretta.

    Args:
        db_name: Nome del database

    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        # Elimina il database se esiste
        if os.path.exists(db_name):
            os.remove(db_name)
            logging.info(f"✅ Database eliminato: {db_name}")

        # Crea una connessione al nuovo database
        conn = sqlite3.connect(db_name)
        c = conn.cursor()

        # Abilita il supporto per le chiavi esterne
        c.execute("PRAGMA foreign_keys = ON")

        # Creazione tabella Utenti
        c.execute('''CREATE TABLE IF NOT EXISTS Utenti (
            id_utente INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            ruolo TEXT NOT NULL CHECK (ruolo IN ('owner', 'user', 'cantieri_user')),
            data_scadenza DATE,
            abilitato BOOLEAN DEFAULT 1,
            created_by INTEGER,
            FOREIGN KEY (created_by) REFERENCES Utenti(id_utente)
        )''')
        logging.info("✅ Tabella Utenti creata")

        # Creazione tabella Cantieri
        c.execute('''CREATE TABLE IF NOT EXISTS Cantieri (
            id_cantiere INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            descrizione TEXT,
            data_creazione TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            password_cantiere TEXT NOT NULL,
            id_utente INTEGER NOT NULL,
            codice_univoco TEXT UNIQUE NOT NULL,
            FOREIGN KEY (id_utente) REFERENCES Utenti(id_utente)
        )''')
        logging.info("✅ Tabella Cantieri creata")

        # Creazione tabella parco_cavi
        c.execute('''CREATE TABLE IF NOT EXISTS parco_cavi (
            ID_BOBINA TEXT PRIMARY KEY,
            numero_bobina TEXT NOT NULL,
            utility TEXT NOT NULL,
            tipologia TEXT NOT NULL,
            n_conduttori INTEGER NOT NULL,
            sezione REAL NOT NULL,
            metri_totali REAL NOT NULL,
            metri_residui REAL NOT NULL,
            stato_bobina TEXT NOT NULL,
            ubicazione_bobina TEXT,
            fornitore TEXT,
            n_DDT TEXT,
            data_DDT DATE,
            configurazione TEXT,
            id_cantiere INTEGER,
            FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE SET NULL
        )''')
        logging.info("✅ Tabella parco_cavi creata")

        # Creazione tabella Cavi
        c.execute('''CREATE TABLE IF NOT EXISTS Cavi (
            id_cavo TEXT NOT NULL,
            id_cantiere INTEGER NOT NULL,
            revisione_ufficiale TEXT NOT NULL DEFAULT '00',
            sistema TEXT,
            utility TEXT NOT NULL,
            colore_cavo TEXT,
            tipologia TEXT NOT NULL,
            n_conduttori INTEGER NOT NULL,
            sezione TEXT NOT NULL,
            SH TEXT,
            ubicazione_partenza TEXT NOT NULL,
            utenza_partenza TEXT,
            descrizione_utenza_partenza TEXT,
            ubicazione_arrivo TEXT NOT NULL,
            utenza_arrivo TEXT,
            descrizione_utenza_arrivo TEXT,
            metri_teorici REAL NOT NULL,
            metratura_reale REAL DEFAULT 0,
            responsabile_posa TEXT,
            id_bobina TEXT,
            stato_installazione TEXT NOT NULL,
            modificato_manualmente INTEGER DEFAULT 0,
            data_posa DATE,
            collegamenti INTEGER DEFAULT 0,
            responsabile_partenza TEXT,
            responsabile_arrivo TEXT,
            comanda_posa TEXT,
            comanda_partenza TEXT,
            comanda_arrivo TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id_cavo, id_cantiere),
            FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE,
            FOREIGN KEY (id_bobina) REFERENCES parco_cavi(ID_BOBINA)
        )''')
        logging.info("✅ Tabella Cavi creata")

        # Tabella CertificatiTest (per retrocompatibilità)
        c.execute('''CREATE TABLE IF NOT EXISTS CertificatiTest (
            id_certificato INTEGER PRIMARY KEY AUTOINCREMENT,
            id_cantiere INTEGER NOT NULL,
            id_cavo TEXT NOT NULL,
            numero_certificato TEXT NOT NULL,
            data_certificato DATE NOT NULL,
            id_operatore TEXT,
            risultato_complessivo TEXT,
            lunghezza_misurata REAL,
            differenza_lunghezza REAL,
            strumento_utilizzato TEXT,
            percorso_certificato TEXT,
            percorso_foto TEXT,
            note TEXT,
            timestamp_creazione TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            timestamp_modifica TIMESTAMP,
            FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE,
            FOREIGN KEY (id_cavo, id_cantiere) REFERENCES Cavi(id_cavo, id_cantiere) ON DELETE CASCADE
        )''')
        logging.info("✅ Tabella CertificatiTest creata")

        # Tabella TestCavi (per retrocompatibilità)
        c.execute('''CREATE TABLE IF NOT EXISTS TestCavi (
            id_test INTEGER PRIMARY KEY AUTOINCREMENT,
            id_certificato INTEGER NOT NULL,
            id_cantiere INTEGER NOT NULL,
            id_cavo TEXT NOT NULL,
            data_test DATE NOT NULL,
            tipo_test TEXT NOT NULL,
            risultato TEXT,
            valore_misurato TEXT,
            note_test TEXT,
            FOREIGN KEY (id_certificato) REFERENCES CertificatiTest(id_certificato) ON DELETE CASCADE,
            FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE,
            FOREIGN KEY (id_cavo, id_cantiere) REFERENCES Cavi(id_cavo, id_cantiere) ON DELETE CASCADE
        )''')
        logging.info("✅ Tabella TestCavi creata")

        # Tabella CertificazioniCavi (nuova tabella)
        c.execute('''CREATE TABLE IF NOT EXISTS CertificazioniCavi (
            id_certificazione INTEGER PRIMARY KEY AUTOINCREMENT,
            id_cantiere INTEGER NOT NULL,
            id_cavo TEXT NOT NULL,
            numero_certificato TEXT NOT NULL,
            data_certificazione DATE NOT NULL,
            id_operatore TEXT,
            strumento_utilizzato TEXT,
            lunghezza_misurata REAL,
            valore_continuita TEXT,
            valore_isolamento TEXT,
            valore_resistenza TEXT,
            percorso_certificato TEXT,
            percorso_foto TEXT,
            note TEXT,
            timestamp_creazione TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            timestamp_modifica TIMESTAMP,
            FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE,
            FOREIGN KEY (id_cavo, id_cantiere) REFERENCES Cavi(id_cavo, id_cantiere) ON DELETE CASCADE
        )''')
        logging.info("✅ Tabella CertificazioniCavi creata")

        # Tabella Comande
        c.execute('''CREATE TABLE IF NOT EXISTS Comande (
            codice_comanda VARCHAR(50) PRIMARY KEY,
            tipo_comanda VARCHAR(20) NOT NULL,
            descrizione TEXT,
            data_creazione DATE NOT NULL,
            data_scadenza DATE,
            responsabile VARCHAR(100),
            stato VARCHAR(20) NOT NULL,
            id_cantiere INTEGER NOT NULL,
            FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE
        )''')
        logging.info("✅ Tabella Comande creata")

        # Crea un utente admin predefinito
        c.execute('''
            INSERT INTO Utenti (username, password, ruolo, abilitato)
            VALUES (?, ?, ?, ?)
        ''', ('admin', 'admin', 'owner', 1))
        logging.info("✅ Utente admin creato")

        conn.commit()
        conn.close()

        logging.info(f"✅ Database ricreato con successo: {db_name}")
        return True

    except Exception as e:
        logging.error(f"❌ Errore durante la ricreazione del database: {str(e)}")
        return False

if __name__ == "__main__":
    logging.info("Inizio ricreazione del database...")
    if recreate_database():
        logging.info("✅ Ricreazione del database completata con successo")
    else:
        logging.error("❌ Ricreazione del database fallita")
