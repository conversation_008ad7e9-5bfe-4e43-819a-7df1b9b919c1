#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per eliminare il database esistente e crearne uno nuovo.
"""

import os
import time
import logging
import sqlite3
import shutil
from datetime import datetime

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def reset_database():
    """
    Elimina il database esistente e ne crea uno nuovo.
    """
    db_path = 'cantieri.db'
    backup_dir = 'backup'
    
    # Crea la directory di backup se non esiste
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    # Crea un backup del database esistente
    if os.path.exists(db_path):
        try:
            backup_path = os.path.join(backup_dir, f'cantieri_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db')
            shutil.copy2(db_path, backup_path)
            logging.info(f"✅ Backup del database creato: {backup_path}")
        except Exception as e:
            logging.error(f"❌ Errore durante la creazione del backup: {str(e)}")
            return False
    
    # Elimina il database esistente
    try:
        if os.path.exists(db_path):
            # Prova a eliminare il file
            try:
                os.remove(db_path)
                logging.info(f"✅ Database eliminato: {db_path}")
            except PermissionError:
                logging.warning("⚠️ Il database è in uso. Tentativo di chiusura forzata...")
                # Attendi un po' e riprova
                time.sleep(2)
                try:
                    os.remove(db_path)
                    logging.info(f"✅ Database eliminato al secondo tentativo: {db_path}")
                except Exception as e:
                    logging.error(f"❌ Impossibile eliminare il database: {str(e)}")
                    return False
    except Exception as e:
        logging.error(f"❌ Errore durante l'eliminazione del database: {str(e)}")
        return False
    
    # Crea un nuovo database vuoto
    try:
        # Crea una connessione al nuovo database (lo crea se non esiste)
        conn = sqlite3.connect(db_path)
        conn.close()
        logging.info(f"✅ Nuovo database creato: {db_path}")
        return True
    except Exception as e:
        logging.error(f"❌ Errore durante la creazione del nuovo database: {str(e)}")
        return False

if __name__ == "__main__":
    logging.info("Inizio reset del database...")
    if reset_database():
        logging.info("✅ Reset del database completato con successo")
    else:
        logging.error("❌ Reset del database fallito")
