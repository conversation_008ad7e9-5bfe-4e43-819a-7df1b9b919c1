#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per testare il modulo di certificazione cavi.
"""

import logging
import sqlite3
from modules.certificazione_cavi import visualizza_certificazioni, menu_gestione_certificazioni

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_certificazione_cavi():
    """
    Testa il modulo di certificazione cavi.
    """
    try:
        logging.info("Test del modulo di certificazione cavi...")
        
        # Verifica se ci sono cantieri nel database
        conn = sqlite3.connect('cantieri_fresh.db')
        c = conn.cursor()
        
        c.execute("SELECT id_cantiere, nome FROM Cantieri")
        cantieri = c.fetchall()
        
        if not cantieri:
            logging.warning("⚠️ Nessun cantiere trovato nel database")
            conn.close()
            return
        
        logging.info(f"Cantieri trovati: {len(cantieri)}")
        for cantiere in cantieri:
            id_cantiere, nome_cantiere = cantiere
            logging.info(f"- Cantiere {id_cantiere}: {nome_cantiere}")
            
            # Verifica se ci sono cavi nel cantiere
            c.execute("SELECT COUNT(*) FROM Cavi WHERE id_cantiere = ?", (id_cantiere,))
            num_cavi = c.fetchone()[0]
            logging.info(f"  Numero di cavi: {num_cavi}")
            
            # Verifica se ci sono certificazioni nel cantiere
            c.execute("SELECT COUNT(*) FROM CertificazioniCavi WHERE id_cantiere = ?", (id_cantiere,))
            num_certificazioni = c.fetchone()[0]
            logging.info(f"  Numero di certificazioni: {num_certificazioni}")
        
        conn.close()
        
        # Test della funzione visualizza_certificazioni
        logging.info("Test della funzione visualizza_certificazioni...")
        for cantiere in cantieri:
            id_cantiere = cantiere[0]
            result = visualizza_certificazioni(id_cantiere)
            logging.info(f"Risultato visualizza_certificazioni per cantiere {id_cantiere}: {result}")
        
        logging.info("✅ Test del modulo di certificazione cavi completato con successo")
        
    except Exception as e:
        logging.error(f"❌ Errore durante il test del modulo di certificazione cavi: {str(e)}")

if __name__ == "__main__":
    logging.info("Inizio test del modulo di certificazione cavi...")
    test_certificazione_cavi()
    logging.info("Test del modulo di certificazione cavi completato")
