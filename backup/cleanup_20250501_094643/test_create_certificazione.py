#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per testare la creazione di una certificazione cavi.
"""

import logging
import sqlite3
import os
from datetime import datetime

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_create_certificazione():
    """
    Testa la creazione di una certificazione cavi.
    """
    try:
        logging.info("Test della creazione di una certificazione cavi...")
        
        # Connessione al database
        conn = sqlite3.connect('cantieri_fresh.db')
        c = conn.cursor()
        
        # Verifica se ci sono cantieri nel database
        c.execute("SELECT id_cantiere, nome FROM Cantieri")
        cantieri = c.fetchall()
        
        if not cantieri:
            logging.warning("⚠️ Nessun cantiere trovato nel database")
            conn.close()
            return
        
        # Seleziona il cantiere con più cavi
        cantiere_con_piu_cavi = None
        max_cavi = 0
        
        for cantiere in cantieri:
            id_cantiere, nome_cantiere = cantiere
            
            # Conta i cavi nel cantiere
            c.execute("SELECT COUNT(*) FROM Cavi WHERE id_cantiere = ?", (id_cantiere,))
            num_cavi = c.fetchone()[0]
            
            if num_cavi > max_cavi:
                max_cavi = num_cavi
                cantiere_con_piu_cavi = cantiere
        
        if not cantiere_con_piu_cavi or max_cavi == 0:
            logging.warning("⚠️ Nessun cantiere con cavi trovato nel database")
            conn.close()
            return
        
        id_cantiere, nome_cantiere = cantiere_con_piu_cavi
        logging.info(f"Cantiere selezionato: {nome_cantiere} (ID: {id_cantiere}) con {max_cavi} cavi")
        
        # Seleziona un cavo installato dal cantiere
        c.execute("""
            SELECT id_cavo, metratura_reale, stato_installazione
            FROM Cavi
            WHERE id_cantiere = ? AND stato_installazione = 'Installato'
            LIMIT 1
        """, (id_cantiere,))
        
        cavo = c.fetchone()
        
        if not cavo:
            logging.warning(f"⚠️ Nessun cavo installato trovato nel cantiere {nome_cantiere}")
            
            # Seleziona un cavo qualsiasi
            c.execute("""
                SELECT id_cavo, metratura_reale, stato_installazione
                FROM Cavi
                WHERE id_cantiere = ?
                LIMIT 1
            """, (id_cantiere,))
            
            cavo = c.fetchone()
            
            if not cavo:
                logging.warning(f"⚠️ Nessun cavo trovato nel cantiere {nome_cantiere}")
                conn.close()
                return
        
        id_cavo, metratura_reale, stato_installazione = cavo
        logging.info(f"Cavo selezionato: {id_cavo} (Metratura: {metratura_reale}, Stato: {stato_installazione})")
        
        # Ottieni il prossimo numero di certificato
        c.execute("""
            SELECT MAX(CAST(SUBSTR(numero_certificato, 5) AS INTEGER)) as ultimo_numero
            FROM CertificazioniCavi
            WHERE id_cantiere = ? AND numero_certificato LIKE 'CERT%'
        """, (id_cantiere,))
        
        result = c.fetchone()
        ultimo_numero = result[0] if result and result[0] else 0
        numero_certificato = f"CERT{ultimo_numero + 1:04d}"
        
        # Crea una nuova certificazione
        data_certificazione = datetime.now().date().isoformat()
        id_operatore = "Test Operatore"
        strumento_utilizzato = "Test Strumento"
        lunghezza_misurata = metratura_reale or 100.0
        valore_continuita = "OK"
        valore_isolamento = "500"
        valore_resistenza = "OK"
        note = "Certificazione creata dal test automatico"
        
        c.execute("""
            INSERT INTO CertificazioniCavi (
                id_cantiere, id_cavo, numero_certificato, data_certificazione, id_operatore,
                strumento_utilizzato, lunghezza_misurata, valore_continuita, valore_isolamento,
                valore_resistenza, note, timestamp_modifica
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            id_cantiere, id_cavo, numero_certificato, data_certificazione, id_operatore,
            strumento_utilizzato, lunghezza_misurata, valore_continuita, valore_isolamento,
            valore_resistenza, note, datetime.now().isoformat()
        ))
        
        conn.commit()
        
        # Verifica che la certificazione sia stata creata
        c.execute("""
            SELECT id_certificazione
            FROM CertificazioniCavi
            WHERE id_cantiere = ? AND id_cavo = ? AND numero_certificato = ?
        """, (id_cantiere, id_cavo, numero_certificato))
        
        certificazione = c.fetchone()
        
        if certificazione:
            logging.info(f"✅ Certificazione creata con successo (ID: {certificazione[0]})")
        else:
            logging.error("❌ Errore nella creazione della certificazione")
        
        conn.close()
        
    except Exception as e:
        logging.error(f"❌ Errore durante il test della creazione di una certificazione: {str(e)}")

if __name__ == "__main__":
    logging.info("Inizio test della creazione di una certificazione cavi...")
    test_create_certificazione()
    logging.info("Test della creazione di una certificazione cavi completato")
