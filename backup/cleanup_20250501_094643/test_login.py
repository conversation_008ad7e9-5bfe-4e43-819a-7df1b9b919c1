#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per testare il login e le funzionalità principali.
"""

import logging
import sqlite3
from modules.database import Database
from modules.utenti import GestoreUtenti

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_login():
    """
    Testa il login e le funzionalità principali.
    """
    try:
        logging.info("Test del login...")
        
        # Inizializza il database e il gestore utenti
        db = Database()
        gestore_utenti = GestoreUtenti(db)
        
        # Test del login con l'utente admin
        logging.info("Test del login con l'utente admin...")
        utente = gestore_utenti.verifica_credenziali('admin', 'admin')
        
        if utente:
            logging.info(f"✅ Login riuscito con l'utente admin (ID: {utente['id_utente']}, <PERSON><PERSON><PERSON>: {utente['ruolo']})")
        else:
            logging.error("❌ Login fallito con l'utente admin")
            return
        
        # Verifica se ci sono cantieri nel database
        conn = sqlite3.connect('cantieri_fresh.db')
        c = conn.cursor()
        
        c.execute("SELECT id_cantiere, nome, codice_univoco FROM Cantieri")
        cantieri = c.fetchall()
        
        if not cantieri:
            logging.warning("⚠️ Nessun cantiere trovato nel database")
            conn.close()
            return
        
        logging.info(f"Cantieri trovati: {len(cantieri)}")
        for cantiere in cantieri:
            id_cantiere, nome_cantiere, codice_univoco = cantiere
            logging.info(f"- Cantiere {id_cantiere}: {nome_cantiere} (Codice: {codice_univoco})")
            
            # Test del login con il cantiere
            logging.info(f"Test del login con il cantiere {nome_cantiere}...")
            cantiere_login = gestore_utenti.verifica_credenziali_cantiere(codice_univoco, 'password')
            
            if cantiere_login:
                if len(cantiere_login) == 2:
                    id_cantiere_login, nome_cantiere_login = cantiere_login
                    logging.info(f"✅ Login riuscito con il cantiere {nome_cantiere_login} (ID: {id_cantiere_login})")
                elif len(cantiere_login) == 3:
                    id_cantiere_login, nome_cantiere_login, password_corretta = cantiere_login
                    if password_corretta:
                        logging.info(f"✅ Login riuscito con il cantiere {nome_cantiere_login} (ID: {id_cantiere_login})")
                    else:
                        logging.warning(f"⚠️ Login riuscito con il cantiere {nome_cantiere_login} ma con password non corretta")
            else:
                logging.warning(f"⚠️ Login fallito con il cantiere {nome_cantiere}")
        
        conn.close()
        
        logging.info("✅ Test del login completato con successo")
        
    except Exception as e:
        logging.error(f"❌ Errore durante il test del login: {str(e)}")

if __name__ == "__main__":
    logging.info("Inizio test del login...")
    test_login()
    logging.info("Test del login completato")
