#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per aggiornare tutti i percorsi del database nel codice.
"""

import os
import logging
import re

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def update_all_db_paths(old_db_name='cantieri.db', new_db_name='cantieri_fresh.db'):
    """
    Aggiorna tutti i percorsi del database nel codice.
    
    Args:
        old_db_name: Nome del vecchio database
        new_db_name: Nome del nuovo database
    
    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        # Lista dei file da controllare
        files_to_check = []
        
        # Aggiungi tutti i file .py nella directory modules
        for file in os.listdir('modules'):
            if file.endswith('.py'):
                files_to_check.append(os.path.join('modules', file))
        
        # Aggiungi il file main.py
        if os.path.exists('main.py'):
            files_to_check.append('main.py')
        
        # Contatore dei file modificati
        modified_files = 0
        
        # Controlla ogni file
        for file_path in files_to_check:
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
                
                # Cerca tutte le occorrenze del vecchio nome del database
                # Utilizziamo una regex per trovare solo le stringhe che rappresentano il percorso del database
                pattern = r"['\"]" + re.escape(old_db_name) + r"['\"]"
                if re.search(pattern, content):
                    # Sostituisci il vecchio nome con il nuovo
                    new_content = re.sub(pattern, f"'{new_db_name}'", content)
                    
                    # Scrivi il nuovo contenuto nel file
                    with open(file_path, 'w', encoding='utf-8') as file:
                        file.write(new_content)
                    
                    logging.info(f"✅ Percorso del database aggiornato in {file_path}")
                    modified_files += 1
            except Exception as e:
                logging.error(f"❌ Errore durante l'aggiornamento del file {file_path}: {str(e)}")
        
        logging.info(f"✅ Aggiornati {modified_files} file con il nuovo percorso del database")
        return True
        
    except Exception as e:
        logging.error(f"❌ Errore durante l'aggiornamento dei percorsi del database: {str(e)}")
        return False

if __name__ == "__main__":
    logging.info("Inizio aggiornamento di tutti i percorsi del database...")
    if update_all_db_paths():
        logging.info("✅ Aggiornamento di tutti i percorsi del database completato con successo")
    else:
        logging.error("❌ Aggiornamento di tutti i percorsi del database fallito")
