#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per aggiornare il database e aggiungere la tabella CertificazioniCavi.
"""

import sqlite3
import logging
import os
from datetime import datetime

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def database_connection(db_path='cantieri.db'):
    """
    Crea una connessione al database.

    Args:
        db_path: Percorso del file del database

    Returns:
        sqlite3.Connection: Connessione al database
    """
    # Ottieni il percorso del database dall'ambiente o usa il valore predefinito
    db_path = os.environ.get('DB_PATH', db_path)

    # Crea la connessione
    conn = sqlite3.connect(db_path, detect_types=sqlite3.PARSE_DECLTYPES)
    conn.row_factory = sqlite3.Row

    return conn

def aggiorna_database_per_certificazioni():
    """
    Aggiorna il database per supportare le certificazioni dei cavi.
    """
    conn = None
    try:
        logging.info("Aggiornamento del database per supportare le certificazioni dei cavi...")
        conn = database_connection()
        c = conn.cursor()

        # Verifica se la tabella CertificazioniCavi esiste già
        c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='CertificazioniCavi'")
        if c.fetchone() is None:
            # Crea la tabella CertificazioniCavi
            c.execute('''
                CREATE TABLE IF NOT EXISTS CertificazioniCavi (
                    id_certificazione INTEGER PRIMARY KEY AUTOINCREMENT,
                    id_cantiere INTEGER NOT NULL,
                    id_cavo TEXT NOT NULL,
                    numero_certificato TEXT NOT NULL,  -- Numero progressivo univoco del certificato
                    data_certificazione DATE NOT NULL,
                    id_operatore TEXT,  -- Operatore che ha eseguito la certificazione
                    strumento_utilizzato TEXT,  -- modello/seriale dello strumento di misura

                    -- Valori di metratura (presi direttamente dal cavo)
                    lunghezza_misurata REAL,  -- in metri

                    -- Risultati dei test principali (solo il valore misurato, il risultato è sempre "Passato")
                    valore_continuita TEXT,
                    valore_isolamento TEXT,
                    valore_resistenza TEXT,

                    -- Percorsi file
                    percorso_certificato TEXT,  -- percorso file PDF del certificato
                    percorso_foto TEXT,  -- percorso file della foto

                    -- Metadati
                    note TEXT,
                    timestamp_creazione TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    timestamp_modifica TIMESTAMP,

                    FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE,
                    FOREIGN KEY (id_cavo, id_cantiere) REFERENCES Cavi(id_cavo, id_cantiere) ON DELETE CASCADE
                )
            ''')
            logging.info("✅ Tabella CertificazioniCavi creata con successo")

            # Crea indici per migliorare le prestazioni
            c.execute("CREATE INDEX IF NOT EXISTS idx_certificazioni_id_cantiere ON CertificazioniCavi(id_cantiere)")
            c.execute("CREATE INDEX IF NOT EXISTS idx_certificazioni_id_cavo ON CertificazioniCavi(id_cavo)")
            c.execute("CREATE INDEX IF NOT EXISTS idx_certificazioni_numero ON CertificazioniCavi(numero_certificato)")
            c.execute("CREATE INDEX IF NOT EXISTS idx_certificazioni_data ON CertificazioniCavi(data_certificazione)")
            logging.info("✅ Indici creati per la tabella CertificazioniCavi")

            # Nota: La migrazione dei dati dalle vecchie tabelle è stata disabilitata per evitare problemi
            # Utilizzare la funzione migra_certificati_vecchi() dal modulo certificazione_cavi.py per migrare i dati
            logging.info("ℹ️ Per migrare i dati dalle vecchie tabelle, utilizzare la funzione migra_certificati_vecchi() dal modulo certificazione_cavi.py")

        else:
            logging.info("ℹ️ La tabella CertificazioniCavi esiste già")

        conn.commit()
        logging.info("✅ Database aggiornato con successo per supportare le certificazioni dei cavi")

    except sqlite3.Error as e:
        logging.error(f"❌ Errore nell'aggiornamento del database: {str(e)}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    aggiorna_database_per_certificazioni()
