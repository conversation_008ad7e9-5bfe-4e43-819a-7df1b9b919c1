#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per aggiornare il percorso del database nel codice.
"""

import os
import logging

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def update_db_path():
    """
    Aggiorna il percorso del database nel file modules/database.py.
    """
    try:
        # Percorso del file database.py
        file_path = 'modules/database.py'
        
        # Leggi il contenuto del file
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # Sostituisci il nome del database
        new_content = content.replace("DB_NAME = 'cantieri.db'", "DB_NAME = 'cantieri_new.db'")
        
        # Scrivi il nuovo contenuto nel file
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(new_content)
        
        logging.info(f"✅ Percorso del database aggiornato in {file_path}")
        return True
        
    except Exception as e:
        logging.error(f"❌ Errore durante l'aggiornamento del percorso del database: {str(e)}")
        return False

if __name__ == "__main__":
    logging.info("Inizio aggiornamento del percorso del database...")
    if update_db_path():
        logging.info("✅ Aggiornamento del percorso del database completato con successo")
    else:
        logging.error("❌ Aggiornamento del percorso del database fallito")
