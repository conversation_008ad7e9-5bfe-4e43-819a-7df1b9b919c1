import psycopg2
from typing import Optional, Dict, Any
import bcrypt
import logging
from .database import Database
from datetime import datetime
from typing import Tuple

class GestoreUtenti:
    def __init__(self, db: Database):
        self.db = db

    def verifica_credenziali_cantiere(self, codice_univoco: str, password: str) -> Optional[Tuple[int, str]]:
        """Verifica le credenziali del cantiere con fallback multipli."""
        try:
            with self.db.get_connection() as conn:
                c = conn.cursor()
                c.execute('''
                          SELECT c.id_cantiere, c.nome, c.password_cantiere
                          FROM Cantieri c
                          WHERE c.codice_univoco = %s
                          ''', (codice_univoco,))

                result = c.fetchone()
                if not result:
                    logging.warning(f"❌ Cantiere con codice {codice_univoco} non trovato")
                    return None

                # Accesso ai campi in stile dizionario (PostgreSQL)
                id_cantiere = result['id_cantiere']
                nome_cantiere = result['nome']
                hashed_password = result['password_cantiere']

                # Converti la password in bytes se è una stringa
                if isinstance(hashed_password, str):
                    hashed_password = hashed_password.encode('utf-8')

                try:
                    # 1. Tentativo principale con bcrypt
                    if bcrypt.checkpw(password.encode('utf-8'), hashed_password):
                        return id_cantiere, nome_cantiere

                    logging.warning(f"❌ Password errata per il cantiere {codice_univoco}")
                except ValueError as e:
                    logging.warning(
                        f"⚠️ Formato password non valido per il cantiere {codice_univoco}. Tentativo di verifica alternativa...")

                    # 2. Fallback: verifica con password utente proprietario
                    try:
                        c.execute("""
                                  SELECT u.password
                                  FROM Utenti u
                                           JOIN Cantieri c ON u.id_utente = c.id_utente
                                  WHERE c.codice_univoco = %s
                                  """, (codice_univoco,))

                        user_result = c.fetchone()
                        if user_result:
                            user_password = user_result['password']
                            if isinstance(user_password, str):
                                user_password = user_password.encode('utf-8')

                            if bcrypt.checkpw(password.encode('utf-8'), user_password):
                                logging.info(
                                    f"✅ Accesso consentito per il cantiere {codice_univoco} con la password dell'utente")

                                # Aggiorna la password del cantiere
                                try:
                                    c.execute("""
                                              UPDATE Cantieri
                                              SET password_cantiere = %s
                                              WHERE codice_univoco = %s
                                              """, (user_password, codice_univoco))
                                    conn.commit()
                                    logging.info(f"✅ Password aggiornata per il cantiere {codice_univoco}")
                                except Exception as update_error:
                                    logging.error(
                                        f"❌ Errore durante l'aggiornamento della password: {str(update_error)}")

                                return id_cantiere, nome_cantiere
                    except Exception as e:
                        logging.error(f"❌ Errore durante il recupero della password utente: {str(e)}")

                    # Nessun meccanismo di recupero - richiede la password corretta

                    logging.warning(f"❌ Formato password non valido per il cantiere {codice_univoco}. Accesso negato.")
                    return None

        except psycopg2.Error as e:
            logging.error(f"❌ Errore nel database: {e}")
            return None

    def aggiungi_utente(self, username: str, password: str, ruolo: str, data_scadenza: Optional[str] = None,
                        created_by: Optional[int] = None,
                        allow_owner: bool = False) -> bool:
        """
        Aggiunge un nuovo utente al database.
        """
        try:
            # Validazione input
            if not username or not password:
                logging.error("❌ Username e password non possono essere vuoti")
                return False

            # Verifica che il ruolo sia valido
            if ruolo not in ('owner', 'user', 'cantieri_user'):
                logging.error(f"❌ Ruolo non valido: {ruolo}")
                return False

            # Impedisce la creazione di nuovi utenti con ruolo 'owner' a meno che allow_owner sia True
            if ruolo == 'owner' and not allow_owner:
                logging.error("❌ Non è possibile creare un utente con ruolo 'owner'")
                return False

            with self.db.get_connection() as conn:
                c = conn.cursor()

                # Verifica se l'username esiste già
                c.execute("SELECT username FROM Utenti WHERE username = %s", (username,))
                if c.fetchone():
                    logging.error("❌ Username già esistente")
                    return False

                # Hash della password
                hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

                # Se data_scadenza è una stringa vuota, impostala come NULL
                if data_scadenza == "":
                    data_scadenza = None

                # Inserimento nuovo utente
                c.execute("""
                    INSERT INTO Utenti (username, password, ruolo, data_scadenza, created_by)
                    VALUES (%s, %s, %s, %s, %s)
                """, (username, hashed_password, ruolo, data_scadenza, created_by))

                conn.commit()
                logging.info(f"✅ Utente {username} aggiunto con successo con ruolo {ruolo}")
                return True

        except psycopg2.IntegrityError as e:
            if "unique" in str(e).lower():
                logging.error("❌ Username già esistente")
            else:
                logging.error(f"❌ Errore di integrità del database: {str(e)}")
            return False
        except Exception as e:
            logging.error(f"❌ Errore durante l'aggiunta dell'utente: {str(e)}")
            return False

    def verifica_credenziali(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        try:
            with self.db.get_connection() as conn:
                c = conn.cursor()
                c.execute("""
                    SELECT id_utente, password, ruolo, created_by, abilitato
                    FROM Utenti
                    WHERE username = %s AND username != ''
                """, (username,))

                result = c.fetchone()

                if not result:
                    logging.warning(f"❌ Tentativo di accesso fallito: username {username} non trovato")
                    return None

                stored_password = result['password']
                abilitato = result['abilitato']  # Ottieni lo stato di abilitazione

                # Verifica se l'utente è disabilitato
                if not abilitato:
                    logging.warning(f"❌ Tentativo di accesso fallito: utente {username} disabilitato")
                    return None

                # Ensure stored_password is in bytes format
                if isinstance(stored_password, str):
                    stored_password = stored_password.encode('utf-8')

                try:
                    # Verify the password
                    if not bcrypt.checkpw(password.encode('utf-8'), stored_password):
                        logging.warning(f"❌ Tentativo di accesso fallito: password errata per {username}")
                        return None
                except ValueError as e:
                    # Handle "Invalid salt" or other bcrypt errors
                    logging.warning(f"⚠️ Formato password non valido per l'utente {username}. Tentativo di verifica alternativa...")

                    # Caso speciale per l'admin con password predefinita
                    if username == "admin" and password == "admin" and result['ruolo'] == "owner":
                        logging.info(f"✅ Accesso consentito per l'amministratore con password predefinita")

                        # Aggiorna la password con un hash bcrypt valido
                        try:
                            new_hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
                            c.execute("UPDATE Utenti SET password = %s WHERE username = %s",
                                     (new_hashed_password, username))
                            conn.commit()
                            logging.info(f"✅ Password aggiornata per l'utente {username}")
                        except Exception as update_error:
                            logging.error(f"❌ Errore durante l'aggiornamento della password: {str(update_error)}")
                            # Continuiamo comunque con il login anche se l'aggiornamento fallisce

                        return {
                            "id_utente": result['id_utente'],
                            "ruolo": result['ruolo'],
                            "created_by": result['created_by']
                        }
                    # Per gli utenti standard, reimpostiamo la password all'username e permettiamo l'accesso
                    # Questo è necessario per gestire il caso in cui l'hash della password non è valido
                    else:
                        # Reimpostiamo la password all'username
                        try:
                            new_password = username  # Usa l'username come nuova password
                            new_hashed_password = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt())
                            c.execute("UPDATE Utenti SET password = %s WHERE username = %s",
                                     (new_hashed_password, username))
                            conn.commit()
                            logging.info(f"✅ Password reimpostata per l'utente {username} al suo username")

                            # Verifica se la password inserita è uguale alla nuova password (username)
                            if password == new_password:
                                logging.info(f"✅ Accesso consentito per l'utente {username} con password uguale all'username")
                                return {
                                    "id_utente": result['id_utente'],
                                    "ruolo": result['ruolo'],
                                    "created_by": result['created_by']
                                }
                            else:
                                logging.warning(f"❌ Password errata per l'utente {username}. Accesso negato.")
                                return None
                        except Exception as update_error:
                            logging.error(f"❌ Errore durante la reimpostazione della password: {str(update_error)}")
                            return None


                return {
                    "id_utente": result['id_utente'],
                    "ruolo": result['ruolo'],
                    "created_by": result['created_by']
                }

        except Exception as e:
            logging.error(f"❌ Errore durante la verifica delle credenziali: {str(e)}")
            return None

    def recupera_password(self, username: str, nuova_password: str, conferma_password: str) -> tuple[bool, str]:
        """
        Recupera la password dell'amministratore.

        Args:
            username: Nome utente dell'amministratore
            nuova_password: Nuova password da impostare
            conferma_password: Conferma della nuova password

        Returns:
            tuple[bool, str]: (successo, messaggio)
        """
        if nuova_password != conferma_password:
            return False, "Le password non coincidono"

        with self.db.get_connection() as conn:
            c = conn.cursor()
            c.execute("SELECT id_utente FROM Utenti WHERE username = %s AND ruolo = 'owner'", (username,))
            if not c.fetchone():
                return False, "Nome utente non valido o non sei un amministratore"

            hashed_password = bcrypt.hashpw(nuova_password.encode('utf-8'), bcrypt.gensalt())
            c.execute("UPDATE Utenti SET password = %s WHERE username = %s",
                      (hashed_password, username))
            conn.commit()
            return True, "Password aggiornata con successo"

    def esiste_owner(self) -> bool:
        """Verifica se esiste già un utente owner."""
        with self.db.get_connection() as conn:
            c = conn.cursor()
            c.execute("SELECT id_utente FROM Utenti WHERE ruolo = 'owner'")
            return c.fetchone() is not None

    def crea_utente(self, username: str, password: str, ruolo: str, creato_da: Optional[int] = None) -> Tuple[bool, str]:
        """Crea un nuovo utente con il ruolo specificato."""
        try:
            # Validazione input
            if not username or not password or not ruolo:
                return False, "Tutti i campi sono obbligatori"
            # Impedisce la creazione di nuovi utenti con ruolo 'owner'
            if ruolo == 'owner':
                return False, "Non è possibile creare un utente con ruolo 'owner'"
            # Verifica che il ruolo sia valido
            if ruolo not in ('user', 'cantieri_user'):
                return False, "Ruolo non valido"
            with self.db.get_connection() as conn:
                c = conn.cursor()
                # Verifica se l'username esiste già
                c.execute("SELECT id_utente FROM Utenti WHERE username = %s", (username,))
                if c.fetchone():
                    return False, "Username già in uso"
                # Hash della password
                hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
                # Inserisce il nuovo utente
                c.execute("""
                    INSERT INTO Utenti (username, password, ruolo, created_by)
                    VALUES (%s, %s, %s, %s)
                """, (username, hashed_password, ruolo, creato_da))
                conn.commit()
                return True, "Utente creato con successo"
        except psycopg2.IntegrityError as e:
            if "unique" in str(e).lower():
                return False, "Username già in uso"
            else:
                logging.error(f"Errore di integrità del database: {str(e)}")
                return False, "Errore di integrità del database"
        except Exception as e:
            logging.error(f"Errore durante la creazione utente: {str(e)}")
            return False, "Errore durante la creazione dell'utente"

    def visualizza_utenti(self) -> list:
        """
        Elenca tutti gli utenti e controlla le date di scadenza.
        Se la data è scaduta, l'utente viene automaticamente disabilitato.
        """
        try:
            with self.db.get_connection() as conn:
                c = conn.cursor()
                c.execute("""
                    SELECT id_utente, username, password, ruolo, data_scadenza, abilitato, created_by
                    FROM Utenti
                    ORDER BY username
                """)
                utenti = c.fetchall()

                # Aggiorna stato in base alla data scadenza
                for utente in utenti:
                    if utente['data_scadenza']:  # se c'è una data di scadenza
                        if utente['data_scadenza'] < datetime.now().date() and utente['abilitato']:  # se data scaduta e utente ancora abilitato
                            c.execute("""
                                UPDATE Utenti
                                SET abilitato = 0
                                WHERE id_utente = %s
                            """, (utente['id_utente'],))
                            conn.commit()

                return utenti

        except Exception as e:
            logging.error(f"❌ Errore durante la visualizzazione utenti: {str(e)}")
            return []

    def disabilita_abilita_utente(self, id_utente: int, abilita: bool) -> bool:
        """
        Disabilita o abilita un utente.
        Args:
            id_utente (int): ID dell'utente
            abilita (bool): True per abilitare, False per disabilitare
        """
        try:
            with self.db.get_connection() as conn:
                c = conn.cursor()
                c.execute("""
                    UPDATE Utenti
                    SET abilitato = %s
                    WHERE id_utente = %s
                """, (1 if abilita else 0, id_utente))
                conn.commit()
                return True

        except Exception as e:
            logging.error(f"❌ Errore durante modifica stato utente: {str(e)}")
            return False

    def elimina_utente(self, id_utente: int) -> bool:
        """
        Elimina definitivamente un utente e TUTTI i dati correlati dal database.
        ATTENZIONE: Questa è un'operazione irreversibile che:
        - Elimina tutte le bobine (parco_cavi) dei cantieri dell'utente
        - Elimina tutti i cavi dei cantieri dell'utente
        - Elimina tutti i cantieri dell'utente
        - Elimina l'utente stesso

        Questa operazione dovrebbe essere eseguita solo da un amministratore
        e solo quando si è assolutamente certi di voler rimuovere tutti i dati.

        Args:
            id_utente (int): ID dell'utente da eliminare
        """
        try:
            with self.db.get_connection() as conn:
                c = conn.cursor()

                # 0. Ottieni prima l'elenco dei cantieri dell'utente per il logging
                c.execute("SELECT id_cantiere FROM Cantieri WHERE id_utente = %s", (id_utente,))
                cantieri = [row['id_cantiere'] for row in c.fetchall()]

                # 1. Elimina tutte le bobine (parco_cavi) dei cantieri dell'utente
                c.execute("""
                    DELETE FROM parco_cavi
                    WHERE id_cantiere IN (
                        SELECT id_cantiere
                        FROM Cantieri
                        WHERE id_utente = %s
                    )
                """, (id_utente,))

                # 2. Elimina tutti i cavi dei cantieri dell'utente
                c.execute("""
                    DELETE FROM Cavi
                    WHERE id_cantiere IN (
                        SELECT id_cantiere
                        FROM Cantieri
                        WHERE id_utente = %s
                    )
                """, (id_utente,))

                # 3. Elimina tutti i cantieri dell'utente
                c.execute("DELETE FROM Cantieri WHERE id_utente = %s", (id_utente,))

                # 4. Elimina l'utente
                c.execute("DELETE FROM Utenti WHERE id_utente = %s", (id_utente,))

                conn.commit()
                logging.warning(f"🗑️ Utente ID {id_utente} e tutti i suoi dati sono stati eliminati definitivamente")
                logging.warning(f"Cantieri eliminati: {cantieri}")
                return True

        except Exception as e:
            logging.error(f"❌ Errore durante eliminazione utente: {str(e)}")
            return False

    def login_cantiere(self, id_cantiere: str, password_cantiere: str) -> bool:
        """Login per utenti cantiere_user con hashing della password"""
        try:
            with self.db.get_connection() as conn:
                c = conn.cursor()
                c.execute('''
                    SELECT id_cantiere, password_cantiere
                    FROM Cantieri
                    WHERE id_cantiere = %s
                ''', (id_cantiere,))

                result = c.fetchone()
                if result:
                    stored_password = result['password_cantiere']

                    # Ensure stored_password is in bytes format
                    if isinstance(stored_password, str):
                        stored_password = stored_password.encode('utf-8')

                    try:
                        if bcrypt.checkpw(password_cantiere.encode('utf-8'), stored_password):
                            return True
                    except ValueError as e:
                        # Handle "Invalid salt" or other bcrypt errors
                        logging.warning(f"⚠️ Formato password non valido per il cantiere {id_cantiere}. Tentativo di verifica alternativa...")

                        # Recupera la password dell'utente proprietario del cantiere
                        try:
                            c.execute("""
                                SELECT u.password
                                FROM Utenti u
                                JOIN Cantieri c ON u.id_utente = c.id_utente
                                WHERE c.id_cantiere = %s
                            """, (id_cantiere,))

                            user_result = c.fetchone()
                            if not user_result:
                                logging.error(f"❌ Impossibile trovare l'utente proprietario del cantiere {id_cantiere}")
                                return False

                            user_password = user_result['password']

                            # Verifica se la password inserita corrisponde alla password dell'utente
                            try:
                                if isinstance(user_password, str):
                                    user_password = user_password.encode('utf-8')

                                try:
                                    if bcrypt.checkpw(password_cantiere.encode('utf-8'), user_password):
                                        logging.info(f"✅ Accesso consentito per il cantiere {id_cantiere} con la password dell'utente")

                                        # Aggiorna la password del cantiere con la password dell'utente
                                        try:
                                            c.execute("UPDATE Cantieri SET password_cantiere = %s WHERE id_cantiere = %s",
                                                     (user_password, id_cantiere))
                                            conn.commit()
                                            logging.info(f"✅ Password aggiornata per il cantiere {id_cantiere}")
                                        except Exception as update_error:
                                            logging.error(f"❌ Errore durante l'aggiornamento della password: {str(update_error)}")
                                            # Continuiamo comunque con il login anche se l'aggiornamento fallisce

                                        return True
                                except ValueError as salt_error:
                                    logging.error(f"❌ Errore durante la verifica della password dell'utente: {str(salt_error)}")
                                    # Fallback: verifica se la password corrisponde all'ID del cantiere
                                    if password_cantiere == id_cantiere:
                                        logging.info(f"✅ Accesso consentito per il cantiere {id_cantiere} con password uguale all'ID")

                                        # Aggiorna la password del cantiere con un hash bcrypt valido
                                        try:
                                            new_hashed_password = bcrypt.hashpw(password_cantiere.encode('utf-8'), bcrypt.gensalt())
                                            c.execute("UPDATE Cantieri SET password_cantiere = %s WHERE id_cantiere = %s",
                                                     (new_hashed_password, id_cantiere))
                                            conn.commit()
                                            logging.info(f"✅ Password aggiornata per il cantiere {id_cantiere}")
                                        except Exception as update_error:
                                            logging.error(f"❌ Errore durante l'aggiornamento della password: {str(update_error)}")
                                            # Continuiamo comunque con il login anche se l'aggiornamento fallisce

                                        return True
                            except Exception as e:
                                logging.error(f"❌ Errore durante la verifica della password dell'utente: {str(e)}")
                        except Exception as e:
                            logging.error(f"❌ Errore durante il recupero del codice univoco: {str(e)}")

                        logging.warning(f"❌ Formato password non valido per il cantiere {id_cantiere}. Accesso negato.")
                        return False
                return False

        except Exception as e:
            logging.error(f"Errore durante il login cantiere: {str(e)}")
            return False
