# -*- coding: utf-8 -*-
import sqlite3
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from modules.database import database_connection
from modules.utils import ValidazioneCampi
from modules.auto_test import esegui_test_automatico, aggiorna_certificato_automatico, genera_pdf_certificato_automatico

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Definizione dei tipi di test disponibili
TIPI_TEST = [
    "Battitura",
    "Misurazione",
    "Isolamento",
    "Completo",
    "Altro"
]

# Definizione degli stati del test
STATI_TEST = [
    "Passato",
    "Fallito",
    "N/A"
]

# Le tabelle per i test dei cavi sono ora definite nella classe Database in modules/database.py

# Questo file è stato sostituito da certificazione_cavi.py
# Mantenuto come backup per riferimento