from sqlalchemy import create_engine, text
import os

# Configurazione del database
DB_HOST = "localhost"
DB_PORT = "5432"
DB_NAME = "cantieri"
DB_USER = "postgres"
DB_PASSWORD = "Taranto"

# Stringa di connessione per SQLAlchemy
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# Crea l'engine SQLAlchemy
engine = create_engine(DATABASE_URL)

# Esegui una query per ottenere tutti i cantieri
with engine.connect() as connection:
    result = connection.execute(text("SELECT * FROM cantieri"))
    cantieri = result.fetchall()
    
    print("Cantieri nel database:")
    for cantiere in cantieri:
        print(f"ID: {cantiere.id_cantiere}, Nome: {cantiere.nome}, Codice univoco: {cantiere.codice_univoco}")
        
        # O<PERSON><PERSON> le bobine per questo cantiere
        bobine_result = connection.execute(text(f"SELECT * FROM parco_cavi WHERE id_cantiere = {cantiere.id_cantiere}"))
        bobine = bobine_result.fetchall()
        
        if bobine:
            print(f"  Bobine per il cantiere {cantiere.id_cantiere}:")
            for bobina in bobine:
                print(f"    ID: {bobina.id_bobina}, Numero: {bobina.numero_bobina}, Utility: {bobina.utility}, Tipologia: {bobina.tipologia}")
        else:
            print(f"  Nessuna bobina trovata per il cantiere {cantiere.id_cantiere}")
        
        print("")
