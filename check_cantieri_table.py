import psycopg2

try:
    # Connessione al database
    conn = psycopg2.connect(
        host='localhost',
        port='5432',
        dbname='cantieri',
        user='postgres',
        password='Taranto'
    )
    
    # Creazione di un cursore
    cursor = conn.cursor()
    
    # Verifica della struttura della tabella cantieri
    cursor.execute("""
        SELECT column_name, data_type, character_maximum_length
        FROM information_schema.columns
        WHERE table_name = 'cantieri'
        ORDER BY ordinal_position
    """)
    columns = cursor.fetchall()
    
    print("Struttura della tabella cantieri:")
    for column in columns:
        print(f"- {column[0]}: {column[1]}" + (f" (max length: {column[2]})" if column[2] else ""))
    
    # Verifica dei dati nella tabella cantieri
    cursor.execute("SELECT * FROM cantieri")
    cantieri = cursor.fetchall()
    
    print("\nDati nella tabella cantieri:")
    for cantiere in cantieri:
        print(f"Cantiere: {cantiere}")
    
    # Chiusura della connessione
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f"Errore durante la connessione al database: {e}")
