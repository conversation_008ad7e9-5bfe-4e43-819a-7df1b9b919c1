import sys
import os
from pathlib import Path

try:
    # Importa le configurazioni del database dalla CLI
    from modules.database_pg import Config
    
    print(f"Configurazione database CLI:")
    print(f"Host: {Config.DB_HOST}")
    print(f"Port: {Config.DB_PORT}")
    print(f"Database: {Config.DB_NAME}")
    print(f"User: {Config.DB_USER}")
    print(f"Password: {Config.DB_PASSWORD}")
    
    # Prova a connettersi al database
    import psycopg2
    
    conn = psycopg2.connect(
        host=Config.DB_HOST,
        port=Config.DB_PORT,
        dbname=Config.DB_NAME,
        user=Config.DB_USER,
        password=Config.DB_PASSWORD
    )
    
    print("\nConnessione al database riuscita!")
    
    # Verifica delle tabelle
    cursor = conn.cursor()
    cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'")
    tables = cursor.fetchall()
    
    print("\nTabelle nel database:")
    for table in tables:
        print(f"- {table[0]}")
    
    # Chiusura della connessione
    cursor.close()
    conn.close()
    
except ImportError as e:
    print(f"Errore di importazione: {e}")
except Exception as e:
    print(f"Errore: {e}")
