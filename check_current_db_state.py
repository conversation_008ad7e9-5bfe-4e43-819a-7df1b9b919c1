#!/usr/bin/env python3
"""
Script per verificare lo stato attuale del database PostgreSQL e determinare
se i controlli di inizializzazione sono ancora necessari.
"""

import sys
import os
from pathlib import Path

# Aggiungi il percorso del progetto al Python path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

def check_database_state():
    """Verifica lo stato attuale del database."""
    
    try:
        from modules.database_pg import database_connection
        
        print("🔍 Verifica stato database PostgreSQL...")
        
        with database_connection(dict_cursor=True) as (conn, cursor):
            # 1. Verifica tabelle esistenti
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name
            """)
            tables = [row['table_name'] for row in cursor.fetchall()]
            
            print(f"\n📊 Tabelle nel database: {len(tables)}")
            for table in tables:
                print(f"   • {table}")
            
            # 2. Verifica BOBINA_VUOTA
            if 'parco_cavi' in tables:
                cursor.execute("SELECT COUNT(*) as count FROM parco_cavi WHERE id_bobina = 'BOBINA_VUOTA'")
                bobina_vuota_count = cursor.fetchone()['count']
                
                if bobina_vuota_count > 0:
                    print(f"\n✅ BOBINA_VUOTA esiste già nel database")
                    
                    # Mostra dettagli BOBINA_VUOTA
                    cursor.execute("SELECT * FROM parco_cavi WHERE id_bobina = 'BOBINA_VUOTA'")
                    bobina_vuota = cursor.fetchone()
                    print(f"   Dettagli: {dict(bobina_vuota)}")
                else:
                    print(f"\n❌ BOBINA_VUOTA NON esiste nel database")
            else:
                print(f"\n❌ Tabella parco_cavi non esiste")
            
            # 3. Verifica cavi con problemi di id_bobina
            if 'cavi' in tables:
                # Cavi con id_bobina = NULL e metratura_reale > 0
                cursor.execute("""
                    SELECT COUNT(*) as count 
                    FROM cavi 
                    WHERE id_bobina IS NULL AND metratura_reale > 0
                """)
                cavi_null_posati = cursor.fetchone()['count']
                
                # Cavi non posati con id_bobina = 'BOBINA_VUOTA'
                cursor.execute("""
                    SELECT COUNT(*) as count 
                    FROM cavi 
                    WHERE stato_installazione = 'Da installare' AND id_bobina = 'BOBINA_VUOTA'
                """)
                cavi_non_posati_bobina_vuota = cursor.fetchone()['count']
                
                print(f"\n📈 Stato cavi:")
                print(f"   • Cavi posati con id_bobina=NULL: {cavi_null_posati}")
                print(f"   • Cavi non posati con id_bobina='BOBINA_VUOTA': {cavi_non_posati_bobina_vuota}")
                
                if cavi_null_posati == 0 and cavi_non_posati_bobina_vuota == 0:
                    print(f"   ✅ Nessun problema di id_bobina rilevato")
                else:
                    print(f"   ⚠️ Problemi di id_bobina rilevati")
            
            # 4. Verifica utenti
            if 'utenti' in tables:
                cursor.execute("SELECT COUNT(*) as count FROM utenti")
                utenti_count = cursor.fetchone()['count']
                
                cursor.execute("SELECT COUNT(*) as count FROM utenti WHERE username = 'admin'")
                admin_count = cursor.fetchone()['count']
                
                print(f"\n👥 Utenti:")
                print(f"   • Totale utenti: {utenti_count}")
                print(f"   • Admin presente: {'✅' if admin_count > 0 else '❌'}")
            
            # 5. Verifica cantieri
            if 'cantieri' in tables:
                cursor.execute("SELECT COUNT(*) as count FROM cantieri")
                cantieri_count = cursor.fetchone()['count']
                
                print(f"\n🏗️ Cantieri:")
                print(f"   • Totale cantieri: {cantieri_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore durante la verifica del database: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_scripts_validity():
    """Verifica se gli script di inizializzazione sono ancora validi."""
    
    print(f"\n🔧 Verifica validità script di inizializzazione...")
    
    scripts_to_check = [
        'scripts/add_bobina_vuota.py',
        'scripts/fix_bobina_vuota.py',
        'scripts/migrate_bobina_vuota.py'
    ]
    
    valid_scripts = []
    invalid_scripts = []
    
    for script_path in scripts_to_check:
        if os.path.exists(script_path):
            print(f"   ✅ {script_path} esiste")
            valid_scripts.append(script_path)
        else:
            print(f"   ❌ {script_path} non esiste")
            invalid_scripts.append(script_path)
    
    return valid_scripts, invalid_scripts

def main():
    """Funzione principale."""
    
    print("🚀 VERIFICA STATO SISTEMA PER AGGIORNAMENTO run_system_simple.py")
    print("=" * 70)
    
    # 1. Verifica stato database
    db_ok = check_database_state()
    
    # 2. Verifica script
    valid_scripts, invalid_scripts = check_scripts_validity()
    
    # 3. Raccomandazioni
    print(f"\n" + "=" * 70)
    print(f"📋 RACCOMANDAZIONI PER run_system_simple.py:")
    
    if db_ok:
        print(f"✅ Database PostgreSQL funzionante")
        
        # Verifica se BOBINA_VUOTA esiste
        try:
            from modules.database_pg import database_connection
            with database_connection() as (conn, cursor):
                cursor.execute("SELECT COUNT(*) FROM parco_cavi WHERE id_bobina = 'BOBINA_VUOTA'")
                bobina_vuota_exists = cursor.fetchone()[0] > 0
                
                if bobina_vuota_exists:
                    print(f"✅ BOBINA_VUOTA già presente - rimuovere initialize_bobina_vuota()")
                else:
                    print(f"⚠️ BOBINA_VUOTA mancante - mantenere initialize_bobina_vuota()")
                
                # Verifica problemi id_bobina
                cursor.execute("""
                    SELECT COUNT(*) FROM cavi 
                    WHERE stato_installazione = 'Da installare' AND id_bobina = 'BOBINA_VUOTA'
                """)
                problemi_bobina = cursor.fetchone()[0] > 0
                
                if not problemi_bobina:
                    print(f"✅ Nessun problema id_bobina - rimuovere fix_bobina_vuota()")
                else:
                    print(f"⚠️ Problemi id_bobina rilevati - mantenere fix_bobina_vuota()")
        except:
            print(f"⚠️ Impossibile verificare BOBINA_VUOTA - mantenere controlli per sicurezza")
    else:
        print(f"❌ Problemi con il database - mantenere tutti i controlli")
    
    print(f"\n🔧 AGGIORNAMENTI NECESSARI:")
    print(f"   • Aggiornare percorso backend: webapp/backend/main.py")
    print(f"   • Verificare dipendenze e controlli di inizializzazione")
    print(f"   • Aggiungere controlli di connessione database PostgreSQL")
    print(f"   • Rimuovere controlli obsoleti se il database è già configurato")

if __name__ == "__main__":
    main()
