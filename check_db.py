import psycopg2

try:
    # Connessione al database
    conn = psycopg2.connect(
        host='localhost',
        port='5432',
        dbname='cantieri',
        user='postgres',
        password='Taranto'
    )
    
    # Creazione di un cursore
    cursor = conn.cursor()
    
    # Verifica delle tabelle esistenti
    cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'")
    tables = cursor.fetchall()
    
    print("Tabelle nel database:")
    for table in tables:
        print(f"- {table[0]}")
    
    # Verifica della tabella utenti
    if any(table[0] == 'utenti' for table in tables):
        cursor.execute("SELECT id_utente, username, ruolo, abilitato FROM utenti")
        users = cursor.fetchall()
        
        print("\nUtenti nel database:")
        for user in users:
            print(f"ID: {user[0]}, Username: {user[1]}, R<PERSON><PERSON>: {user[2]}, Abilitato: {user[3]}")
    else:
        print("\nLa tabella 'utenti' non esiste nel database.")
    
    # Chiusura della connessione
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f"Errore durante la connessione al database: {e}")
