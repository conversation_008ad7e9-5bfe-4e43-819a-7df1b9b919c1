import sys
import os
from pathlib import Path

try:
    # Importa il modulo database_pg
    from modules.database_pg import Database, database_connection
    
    print("Modulo database_pg importato con successo")
    
    # Crea un'istanza del database
    db = Database()
    print("Istanza del database creata con successo")
    
    # Verifica la connessione al database
    with database_connection(autocommit=True, operation_name="test_connection") as (conn, cursor):
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        print(f"Connessione al database riuscita: {result}")
        
        # Verifica delle tabelle
        cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'")
        tables = cursor.fetchall()
        
        print("\nTabelle nel database:")
        for table in tables:
            print(f"- {table[0]}")
    
except ImportError as e:
    print(f"Errore di importazione: {e}")
except Exception as e:
    print(f"Errore: {e}")
