#!/usr/bin/env python
# -*- coding: utf-8 -*-

import psycopg2
from psycopg2.extras import RealDictCursor

def get_table_schema(table_name):
    """Ottiene lo schema di una tabella specifica."""
    try:
        conn = psycopg2.connect(
            host='localhost',
            port='5432',
            dbname='cantieri',
            user='postgres',
            password='Taranto'
        )
        
        # Usa RealDictCursor per ottenere i risultati come dizionari
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # Query per ottenere le colonne della tabella
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_schema = 'public' AND table_name = %s
            ORDER BY ordinal_position
        """, (table_name,))
        
        columns = cursor.fetchall()
        
        # Query per ottenere le chiavi primarie
        cursor.execute("""
            SELECT c.column_name
            FROM information_schema.table_constraints tc
            JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_name)
            JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema
                AND tc.table_name = c.table_name AND ccu.column_name = c.column_name
            WHERE tc.constraint_type = 'PRIMARY KEY' AND tc.table_name = %s
        """, (table_name,))
        
        primary_keys = [row['column_name'] for row in cursor.fetchall()]
        
        # Query per ottenere le chiavi esterne
        cursor.execute("""
            SELECT
                kcu.column_name,
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name
            FROM
                information_schema.table_constraints AS tc
                JOIN information_schema.key_column_usage AS kcu
                  ON tc.constraint_name = kcu.constraint_name
                  AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage AS ccu
                  ON ccu.constraint_name = tc.constraint_name
                  AND ccu.table_schema = tc.table_schema
            WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.table_name = %s
        """, (table_name,))
        
        foreign_keys = cursor.fetchall()
        
        # Chiudi la connessione
        cursor.close()
        conn.close()
        
        return {
            'columns': columns,
            'primary_keys': primary_keys,
            'foreign_keys': foreign_keys
        }
        
    except Exception as e:
        print(f"Errore durante l'ottenimento dello schema della tabella {table_name}: {e}")
        return None

def get_all_tables():
    """Ottiene l'elenco di tutte le tabelle nel database."""
    try:
        conn = psycopg2.connect(
            host='localhost',
            port='5432',
            dbname='cantieri',
            user='postgres',
            password='Taranto'
        )
        
        cursor = conn.cursor()
        
        # Query per ottenere tutte le tabelle
        cursor.execute("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        
        tables = [row[0] for row in cursor.fetchall()]
        
        # Chiudi la connessione
        cursor.close()
        conn.close()
        
        return tables
        
    except Exception as e:
        print(f"Errore durante l'ottenimento dell'elenco delle tabelle: {e}")
        return []

def print_table_schema(table_name):
    """Stampa lo schema di una tabella in formato SQL CREATE TABLE."""
    schema = get_table_schema(table_name)
    
    if not schema:
        print(f"Impossibile ottenere lo schema per la tabella {table_name}")
        return
    
    print(f"\n--- Schema della tabella {table_name} ---")
    
    # Costruisci la dichiarazione CREATE TABLE
    create_table = f"CREATE TABLE {table_name} (\n"
    
    # Aggiungi le colonne
    for column in schema['columns']:
        nullable = "NOT NULL" if column['is_nullable'] == 'NO' else ""
        default = f"DEFAULT {column['column_default']}" if column['column_default'] else ""
        
        create_table += f"    {column['column_name']} {column['data_type']} {nullable} {default},\n"
    
    # Aggiungi la chiave primaria
    if schema['primary_keys']:
        primary_key_str = ", ".join(schema['primary_keys'])
        create_table += f"    PRIMARY KEY ({primary_key_str}),\n"
    
    # Aggiungi le chiavi esterne
    for fk in schema['foreign_keys']:
        create_table += f"    FOREIGN KEY ({fk['column_name']}) REFERENCES {fk['foreign_table_name']}({fk['foreign_column_name']}),\n"
    
    # Rimuovi l'ultima virgola e chiudi la parentesi
    create_table = create_table.rstrip(",\n") + "\n);"
    
    print(create_table)

def main():
    """Funzione principale."""
    tables = get_all_tables()
    
    print("Tabelle esistenti nel database:")
    for i, table in enumerate(tables):
        print(f"{i+1}. {table}")
    
    print("\nDettagli delle tabelle:")
    for table in tables:
        print_table_schema(table)

if __name__ == "__main__":
    main()
