from sqlalchemy import create_engine, text, inspect

# Configurazione del database
DB_HOST = "localhost"
DB_PORT = "5432"
DB_NAME = "cantieri"
DB_USER = "postgres"
DB_PASSWORD = "Taranto"

# Stringa di connessione per SQLAlchemy
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# Crea l'engine SQLAlchemy
engine = create_engine(DATABASE_URL)

# Ottieni l'inspector
inspector = inspect(engine)

# Ottieni le tabelle nel database
tables = inspector.get_table_names()
print("Tabelle nel database:")
for table in tables:
    print(f"- {table}")

# Ottieni la struttura della tabella parco_cavi
print("\nStruttura della tabella parco_cavi:")
columns = inspector.get_columns("parco_cavi")
for column in columns:
    print(f"- {column['name']}: {column['type']} (nullable: {column['nullable']})")

# Ottieni le chiavi primarie
pk = inspector.get_pk_constraint("parco_cavi")
print(f"\nChiavi primarie: {pk['constrained_columns']}")

# Ottieni le chiavi esterne
fks = inspector.get_foreign_keys("parco_cavi")
print("\nChiavi esterne:")
for fk in fks:
    print(f"- {fk['constrained_columns']} -> {fk['referred_table']}.{fk['referred_columns']}")
