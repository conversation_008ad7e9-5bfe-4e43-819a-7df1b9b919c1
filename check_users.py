from sqlalchemy import create_engine, text
import os

# Configurazione del database
DB_HOST = "localhost"
DB_PORT = "5432"
DB_NAME = "cantieri"
DB_USER = "postgres"
DB_PASSWORD = "Taranto"

# Stringa di connessione per SQLAlchemy
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# Crea l'engine SQLAlchemy
engine = create_engine(DATABASE_URL)

# Esegui una query per ottenere tutti gli utenti
with engine.connect() as connection:
    result = connection.execute(text("SELECT * FROM utenti"))
    users = result.fetchall()
    
    print("Utenti nel database:")
    for user in users:
        print(f"ID: {user.id_utente}, Username: {user.username}, Ruolo: {user.ruolo}, Password: {user.password}, Abilitato: {user.abilitato}")
