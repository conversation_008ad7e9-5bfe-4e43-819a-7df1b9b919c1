import sys
import os
from pathlib import Path

# Aggiungi il percorso della webapp al sys.path
webapp_path = Path(__file__).parent / "webapp"
sys.path.append(str(webapp_path))

try:
    # Importa il modulo database della webapp
    from webapp.backend.database import get_db, engine
    
    print("Modulo database della webapp importato con successo")
    
    # Verifica la connessione al database
    from sqlalchemy import text
    
    with engine.connect() as connection:
        result = connection.execute(text("SELECT 1"))
        print(f"Connessione al database riuscita: {result.fetchone()}")
        
        # Verifica delle tabelle
        result = connection.execute(text("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"))
        tables = result.fetchall()
        
        print("\nTabelle nel database:")
        for table in tables:
            print(f"- {table[0]}")
    
except ImportError as e:
    print(f"Errore di importazione: {e}")
except Exception as e:
    print(f"Errore: {e}")
