import sys
import os
from pathlib import Path

# Aggiungi il percorso della webapp al sys.path
webapp_path = Path(__file__).parent / "webapp"
sys.path.append(str(webapp_path))

try:
    # Importa le configurazioni del database dalla webapp
    from webapp.backend.config import settings
    
    print(f"Configurazione database webapp:")
    print(f"Host: {settings.DB_HOST}")
    print(f"Port: {settings.DB_PORT}")
    print(f"Database: {settings.DB_NAME}")
    print(f"User: {settings.DB_USER}")
    print(f"Password: {settings.DB_PASSWORD}")
    
    # Prova a connettersi al database usando psycopg2
    import psycopg2
    
    conn = psycopg2.connect(
        host=settings.DB_HOST,
        port=settings.DB_PORT,
        dbname=settings.DB_NAME,
        user=settings.DB_USER,
        password=settings.DB_PASSWORD
    )
    
    print("\nConnessione al database riuscita!")
    
    # Verifica delle tabelle
    cursor = conn.cursor()
    cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'")
    tables = cursor.fetchall()
    
    print("\nTabelle nel database:")
    for table in tables:
        print(f"- {table[0]}")
    
    # Verifica della tabella utenti
    cursor.execute("SELECT id_utente, username, ruolo, abilitato FROM utenti")
    users = cursor.fetchall()
    
    print("\nUtenti nel database:")
    for user in users:
        print(f"ID: {user[0]}, Username: {user[1]}, Ruolo: {user[2]}, Abilitato: {user[3]}")
    
    # Chiusura della connessione
    cursor.close()
    conn.close()
    
except ImportError as e:
    print(f"Errore di importazione: {e}")
except Exception as e:
    print(f"Errore: {e}")
