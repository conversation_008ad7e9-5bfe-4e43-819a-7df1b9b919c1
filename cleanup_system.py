#!/usr/bin/env python3
"""
Script di pulizia completa del sistema CMS.
Rimuove tutti i file temporanei, di test, debug e non necessari per la produzione.
"""

import os
import shutil
import logging
from pathlib import Path

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def cleanup_system():
    """Pulizia completa del sistema."""
    
    print("🧹 INIZIO PULIZIA SISTEMA CMS")
    print("=" * 60)
    
    # Lista dei file Python da rimuovere (test, debug, check, etc.)
    python_files_to_remove = [
        # File di test
        "test_api.py",
        "test_api_8003.py", 
        "test_api_cavi.py",
        "test_api_template.py",
        "test_bobine_compatibili.py",
        "test_bobine_compatibili_fix.py",
        "test_cable_deletion.py",
        "test_cavi_db.py",
        "test_collegamenti.py",
        "test_collegamenti_direct.py",
        "test_commit_behavior.py",
        "test_db_connection.py",
        "test_db_transactions.py",
        "test_error_but_success.py",
        "test_first_insertion.py",
        "test_import_excel.py",
        "test_insert_bobina.py",
        "test_metri_posati.py",
        "test_metri_posati_compatibile.py",
        "test_metri_posati_simple.py",
        "test_parco_cavi.py",
        "test_reports_api.py",
        "test_template_excel.py",
        
        # File di debug
        "debug_bobine_compatibili.py",
        "debug_bobine_test.py",
        "debug_parco_cavi.py",
        
        # File di check
        "check_auth.py",
        "check_bobine.py",
        "check_cantieri_table.py",
        "check_cli_db.py",
        "check_db.py",
        "check_db_connection.py",
        "check_db_schema.py",
        "check_db_structure.py",
        "check_table_structure.py",
        "check_users.py",
        "check_webapp_db.py",
        "check_webapp_db_connection.py",
        "check_webapp_db_direct.py",
        
        # File di creazione test
        "create_test_bobina.py",
        "genera_file_test.py",
        
        # File di fix temporanei
        "fix_db_connection.py",
        
        # File di backend test
        "webapp/backend/test_api.py",
        "webapp/backend/test_cavi.py",
        "webapp/backend/test_excel_import.py",
        "webapp/backend/check_cables.py",
        
        # File di utility temporanei
        "reset_admin_password.py",
        "start_backend.py",
        "start_backend_8003.py",
        "start_frontend.py",
        "update_cavi_schema.py",
        "update_parco_cavi_schema.py",
        "query_cavi.py"
    ]
    
    # Lista dei file Excel di test da rimuovere
    excel_files_to_remove = [
        "template_test.xlsx",
        "test_100_cavi_20250524_074739.xlsx",
        "test_100_cavi_20250524_074820.xlsx",
        "test_cavi_100_20250524_074915.xlsx",
        "test_parco_bobine_20250524_074820.xlsx",
        "test_parco_bobine_import.xlsx",
        "test_template_cavi.xlsx",
        "12.xlsx",
        "BOB2.xlsx"
    ]
    
    # Lista dei file di log da rimuovere
    log_files_to_remove = [
        "fix_db_connection.log",
        "test_api_cavi.log",
        "test_cavi_db.log",
        "test_commit_behavior.log",
        "test_db_transactions.log",
        "test_error_but_success.log",
        "test_import_excel.log",
        "webapp/backend/backend_log.txt"
    ]
    
    # Lista dei file di documentazione temporanei da rimuovere
    doc_files_to_remove = [
        "analysis_of_cavi_functions.md",
        "fix_cable_deletion_summary.md",
        "README_AGGIORNAMENTO_INSERISCI_METRI.md",
        "README_BOBINA_VUOTA_FIX.md",
        "README_BOBINE_COMPATIBILI.md",
        "README_BOBINE_INCOMPATIBILI.md",
        "README_BOBINE_INCOMPATIBILI_FIX.md",
        "README_CAVO_SPECIFICO.md",
        "README_DATABASE_IMPROVEMENTS.md",
        "README_EXCEL_FIX.md",
        "README_FIELD_CHANGES.md",
        "README_FORMATO_CONDUTTORI.md",
        "README_IMPORT_FIX.md",
        "README_IMPORT_FIXES.md",
        "README_IMPORT_FIXES_2.md",
        "README_PARCO_BOBINE_FIX.md",
        "README_POPUP_FIX.md",
        "webapp/DOCUMENTAZIONE_MODIFICHE.md",
        "webapp/REPORT_FIXES_SUMMARY.md",
        "webapp/REPORT_IMPROVEMENTS_SUMMARY.md"
    ]
    
    # Lista di altri file da rimuovere
    other_files_to_remove = [
        "query",
        "setup.py",
        "pyproject.toml",
        "update_menu.js",
        "update_parco_cavi_schema.sql",
        "webapp/check_backend.py"
    ]
    
    # Contatori
    removed_count = 0
    error_count = 0
    
    # Funzione per rimuovere file
    def remove_file(file_path):
        nonlocal removed_count, error_count
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logging.info(f"✅ Rimosso: {file_path}")
                removed_count += 1
            else:
                logging.debug(f"⚠️ File non trovato: {file_path}")
        except Exception as e:
            logging.error(f"❌ Errore rimozione {file_path}: {e}")
            error_count += 1
    
    # Rimuovi file Python
    print("\n🐍 Rimozione file Python di test/debug...")
    for file_path in python_files_to_remove:
        remove_file(file_path)
    
    # Rimuovi file Excel di test
    print("\n📊 Rimozione file Excel di test...")
    for file_path in excel_files_to_remove:
        remove_file(file_path)
    
    # Rimuovi file di log
    print("\n📝 Rimozione file di log...")
    for file_path in log_files_to_remove:
        remove_file(file_path)
    
    # Rimuovi file di documentazione temporanei
    print("\n📚 Rimozione documentazione temporanea...")
    for file_path in doc_files_to_remove:
        remove_file(file_path)
    
    # Rimuovi altri file
    print("\n🗂️ Rimozione altri file temporanei...")
    for file_path in other_files_to_remove:
        remove_file(file_path)
    
    return removed_count, error_count

def cleanup_directories():
    """Rimuove directory temporanee e di backup."""
    
    print("\n📁 Rimozione directory temporanee...")
    
    directories_to_remove = [
        ".pytest_cache",
        "__pycache__",
        "cms.egg-info",
        "backup/cleanup_20250501_094643",
        "webapp/backend/__pycache__",
        "webapp/backend/api/__pycache__",
        "webapp/backend/core/__pycache__",
        "webapp/backend/models/__pycache__",
        "webapp/backend/schemas/__pycache__"
    ]
    
    removed_dirs = 0
    error_dirs = 0
    
    for dir_path in directories_to_remove:
        try:
            if os.path.exists(dir_path):
                shutil.rmtree(dir_path)
                logging.info(f"✅ Directory rimossa: {dir_path}")
                removed_dirs += 1
            else:
                logging.debug(f"⚠️ Directory non trovata: {dir_path}")
        except Exception as e:
            logging.error(f"❌ Errore rimozione directory {dir_path}: {e}")
            error_dirs += 1
    
    return removed_dirs, error_dirs

def main():
    """Funzione principale."""
    
    # Pulizia file
    removed_files, error_files = cleanup_system()
    
    # Pulizia directory
    removed_dirs, error_dirs = cleanup_directories()
    
    # Risultato finale
    print("\n" + "=" * 60)
    print("🏁 PULIZIA COMPLETATA")
    print(f"✅ File rimossi: {removed_files}")
    print(f"✅ Directory rimosse: {removed_dirs}")
    if error_files > 0 or error_dirs > 0:
        print(f"❌ Errori: {error_files + error_dirs}")
    print("=" * 60)
    
    return (error_files + error_dirs) == 0

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ Pulizia completata con successo!")
    else:
        print("⚠️ Pulizia completata con alcuni errori.")
