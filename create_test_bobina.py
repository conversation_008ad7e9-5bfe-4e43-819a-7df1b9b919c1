import requests
import json

# URL dell'API
BASE_URL = "http://localhost:8002/api"

# Credenziali di accesso
credentials = {
    "username": "admin",
    "password": "admin"
}

# Ottieni un token di accesso
def get_token():
    print("Tentativo di autenticazione con:", credentials)
    # Utilizziamo OAuth2PasswordRequestForm
    form_data = {
        "username": credentials["username"],
        "password": credentials["password"]
    }
    response = requests.post(f"{BASE_URL}/auth/login", data=form_data)
    if response.status_code == 200:
        return response.json()["access_token"]
    else:
        print(f"Errore di autenticazione: {response.status_code}")
        print(response.text)
        return None

# Test per creare una bobina
def create_test_bobina(token, cantiere_id):
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    
    # Dati della bobina di test
    bobina_data = {
        "numero_bobina": "1",
        "utility": "Test Utility",
        "tipologia": "Test Tipologia",
        "n_conduttori": "3",
        "sezione": "1.5",
        "metri_totali": 100.0,
        "ubicazione_bobina": "Magazzino",
        "fornitore": "Fornitore Test",
        "n_DDT": "12345",
        "data_DDT": "2025-05-11",
        "configurazione": "s"
    }
    
    print(f"Creazione bobina di test per il cantiere {cantiere_id}...")
    print(f"Dati bobina: {json.dumps(bobina_data, indent=2)}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/parco-cavi/{cantiere_id}", 
            headers=headers,
            json=bobina_data
        )
        
        print(f"Status code: {response.status_code}")
        
        if response.status_code == 200 or response.status_code == 201:
            bobina = response.json()
            print("Bobina creata con successo:")
            print(json.dumps(bobina, indent=2))
            return True
        else:
            print("Errore nella creazione della bobina:")
            print(response.text)
            return False
    except Exception as e:
        print(f"Eccezione durante la richiesta: {str(e)}")
        return False

# Esegui il test
if __name__ == "__main__":
    token = get_token()
    if token:
        print("Token ottenuto con successo")
        # Crea una bobina di test per il cantiere 1
        create_test_bobina(token, 1)
    else:
        print("Impossibile ottenere il token")
