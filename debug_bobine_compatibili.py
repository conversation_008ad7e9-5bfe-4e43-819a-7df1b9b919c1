#!/usr/bin/env python
# debug_bobine_compatibili.py - Script per il debug della ricerca bobine compatibili

import psycopg2
from psycopg2.extras import RealDictCursor
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import json
import sys

# Parametri di connessione al database
# Nota: questi parametri devono essere modificati in base alla configurazione locale
conn_params = {
    "dbname": "cms",
    "user": "postgres",
    "password": "postgres",  # Modifica con la password corretta
    "host": "localhost",
    "port": "5432"
}

# Verifica se è possibile connettersi al database
def test_connection():
    try:
        conn = psycopg2.connect(**conn_params)
        conn.close()
        print("Connessione al database riuscita!")
        return True
    except Exception as e:
        print(f"Errore di connessione al database: {str(e)}")
        print("\nNota: Questo script richiede una connessione al database PostgreSQL.")
        print("Modifica i parametri di connessione nel codice per adattarli alla tua configurazione.")
        return False

def check_database_schema():
    """
    Verifica lo schema del database per le tabelle parco_cavi e cavi.
    """
    try:
        conn = psycopg2.connect(**conn_params)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()

        # Verifica la struttura della tabella parco_cavi
        print("\n=== STRUTTURA TABELLA PARCO_CAVI ===")
        cursor.execute("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns
            WHERE table_name = 'parco_cavi'
            ORDER BY ordinal_position
        """)

        columns = cursor.fetchall()
        for column in columns:
            print(f"Colonna: {column[0]}, Tipo: {column[1]}, Nullable: {column[2]}")

        # Verifica la struttura della tabella cavi
        print("\n=== STRUTTURA TABELLA CAVI ===")
        cursor.execute("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns
            WHERE table_name = 'cavi'
            ORDER BY ordinal_position
        """)

        columns = cursor.fetchall()
        for column in columns:
            print(f"Colonna: {column[0]}, Tipo: {column[1]}, Nullable: {column[2]}")

        return True
    except Exception as e:
        print(f"Errore durante la verifica dello schema del database: {str(e)}")
        return False
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def check_bobine_data():
    """
    Verifica i dati presenti nella tabella parco_cavi.
    """
    try:
        conn = psycopg2.connect(**conn_params)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # Conta il numero totale di bobine
        cursor.execute("SELECT COUNT(*) as total FROM parco_cavi")
        total = cursor.fetchone()['total']
        print(f"\n=== DATI TABELLA PARCO_CAVI ({total} bobine) ===")

        # Mostra un campione di bobine
        cursor.execute("""
            SELECT id_bobina, tipologia, n_conduttori, sezione, metri_residui, stato_bobina
            FROM parco_cavi
            LIMIT 10
        """)

        bobine = cursor.fetchall()
        for bobina in bobine:
            print(f"ID: {bobina['id_bobina']}, Tipologia: {bobina['tipologia']}, " +
                  f"N_Conduttori: {bobina['n_conduttori']} (tipo: {type(bobina['n_conduttori']).__name__}), " +
                  f"Sezione: {bobina['sezione']} (tipo: {type(bobina['sezione']).__name__}), " +
                  f"Metri residui: {bobina['metri_residui']}, Stato: {bobina['stato_bobina']}")

        return True
    except Exception as e:
        print(f"Errore durante la verifica dei dati delle bobine: {str(e)}")
        return False
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def check_specific_bobine(tipologia, n_conduttori, sezione):
    """
    Verifica se esistono bobine con i parametri specificati.
    """
    try:
        conn = psycopg2.connect(**conn_params)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # Converti i parametri in stringhe
        tipologia_str = str(tipologia or '')
        n_conduttori_str = str(n_conduttori or '0')
        sezione_str = str(sezione or '0')

        print(f"\n=== RICERCA BOBINE CON PARAMETRI SPECIFICI ===")
        print(f"Tipologia: '{tipologia_str}', N_Conduttori: '{n_conduttori_str}', Sezione: '{sezione_str}'")

        # Cerca bobine con i parametri esatti
        # Nota: n_conduttori non è più utilizzato per la compatibilità
        cursor.execute("""
            SELECT id_bobina, tipologia, n_conduttori, sezione, metri_residui, stato_bobina
            FROM parco_cavi
            WHERE tipologia = %s AND sezione = %s
        """, (tipologia_str, sezione_str))

        bobine = cursor.fetchall()
        print(f"Trovate {len(bobine)} bobine con parametri esatti")

        for bobina in bobine:
            print(f"ID: {bobina['id_bobina']}, Tipologia: {bobina['tipologia']}, " +
                  f"N_Conduttori: {bobina['n_conduttori']}, Sezione: {bobina['sezione']}, " +
                  f"Metri residui: {bobina['metri_residui']}, Stato: {bobina['stato_bobina']}")

        # Se non sono state trovate bobine, cerca bobine simili
        if len(bobine) == 0:
            print("\nRicerca bobine con parametri simili...")

            # Cerca bobine con la stessa tipologia
            cursor.execute("""
                SELECT id_bobina, tipologia, n_conduttori, sezione, metri_residui, stato_bobina
                FROM parco_cavi
                WHERE tipologia = %s
                LIMIT 5
            """, (tipologia_str,))

            bobine_tipologia = cursor.fetchall()
            print(f"Trovate {len(bobine_tipologia)} bobine con tipologia '{tipologia_str}'")

            for bobina in bobine_tipologia:
                print(f"ID: {bobina['id_bobina']}, Tipologia: {bobina['tipologia']}, " +
                      f"N_Conduttori: {bobina['n_conduttori']}, Sezione: {bobina['sezione']}, " +
                      f"Metri residui: {bobina['metri_residui']}, Stato: {bobina['stato_bobina']}")

            # Nota: n_conduttori non è più utilizzato per la compatibilità

            # Cerca bobine con la stessa sezione
            cursor.execute("""
                SELECT id_bobina, tipologia, n_conduttori, sezione, metri_residui, stato_bobina
                FROM parco_cavi
                WHERE sezione = %s
                LIMIT 5
            """, (sezione_str,))

            bobine_sezione = cursor.fetchall()
            print(f"\nTrovate {len(bobine_sezione)} bobine con sezione '{sezione_str}'")

            for bobina in bobine_sezione:
                print(f"ID: {bobina['id_bobina']}, Tipologia: {bobina['tipologia']}, " +
                      f"N_Conduttori: {bobina['n_conduttori']}, Sezione: {bobina['sezione']}, " +
                      f"Metri residui: {bobina['metri_residui']}, Stato: {bobina['stato_bobina']}")

        return True
    except Exception as e:
        print(f"Errore durante la ricerca delle bobine specifiche: {str(e)}")
        return False
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def check_cavo_details(id_cavo):
    """
    Verifica i dettagli di un cavo specifico.
    """
    try:
        conn = psycopg2.connect(**conn_params)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        print(f"\n=== DETTAGLI CAVO {id_cavo} ===")

        # Cerca il cavo specifico
        cursor.execute("""
            SELECT id_cavo, id_cantiere, tipologia, n_conduttori, sezione, metri_teorici,
                   metratura_reale, stato_installazione, id_bobina
            FROM cavi
            WHERE id_cavo = %s
        """, (id_cavo,))

        cavo = cursor.fetchone()
        if cavo:
            print(f"ID: {cavo['id_cavo']}, Cantiere: {cavo['id_cantiere']}")
            print(f"Tipologia: {cavo['tipologia']} (tipo: {type(cavo['tipologia']).__name__})")
            print(f"N_Conduttori: {cavo['n_conduttori']} (tipo: {type(cavo['n_conduttori']).__name__})")
            print(f"Sezione: {cavo['sezione']} (tipo: {type(cavo['sezione']).__name__})")
            print(f"Metri teorici: {cavo['metri_teorici']}")
            print(f"Metratura reale: {cavo['metratura_reale']}")
            print(f"Stato installazione: {cavo['stato_installazione']}")
            print(f"ID Bobina: {cavo['id_bobina']}")
        else:
            print(f"Cavo con ID {id_cavo} non trovato")

        return True
    except Exception as e:
        print(f"Errore durante la verifica dei dettagli del cavo: {str(e)}")
        return False
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def main():
    """
    Funzione principale che esegue i test di debug.
    """
    # Verifica la connessione al database
    if not test_connection():
        print("Impossibile eseguire i test senza una connessione al database.")
        return

    # Verifica lo schema del database
    check_database_schema()

    # Verifica i dati delle bobine
    check_bobine_data()

    # Verifica se sono stati forniti parametri da riga di comando
    if len(sys.argv) >= 2:
        # Se è stato fornito un ID cavo, verifica i dettagli del cavo
        id_cavo = sys.argv[1]
        check_cavo_details(id_cavo)

        # Se sono stati forniti anche i parametri di ricerca, verifica le bobine compatibili
        if len(sys.argv) >= 5:
            tipologia = sys.argv[2]
            n_conduttori = sys.argv[3]
            sezione = sys.argv[4]
            check_specific_bobine(tipologia, n_conduttori, sezione)
    else:
        # Esegui un test con i parametri del caso specifico
        print("\n=== TEST CON PARAMETRI DEL CASO SPECIFICO ===")
        tipologia = "test"
        # Nota: n_conduttori non è più utilizzato per la compatibilità
        n_conduttori = "0"
        sezione = "2"
        check_specific_bobine(tipologia, n_conduttori, sezione)

        # Verifica i dettagli del cavo specifico
        id_cavo = "00ACA1021BXRA"
        check_cavo_details(id_cavo)

if __name__ == "__main__":
    main()
