#!/usr/bin/env python
# debug_bobine_test.py - Script per verificare la presenza di bobine compatibili

import psycopg2
from psycopg2.extras import RealDictCursor
import sys

# Parametri di connessione al database
conn_params = {
    "dbname": "cms",
    "user": "postgres",
    "password": "postgres",  # Modifica con la password corretta
    "host": "localhost",
    "port": "5432"
}

def check_bobine_compatibili(cantiere_id, tipologia, n_conduttori, sezione):
    """
    Verifica se ci sono bobine compatibili con i parametri specificati.
    """
    try:
        # Connessione al database
        conn = psycopg2.connect(**conn_params)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # Converti i parametri in stringhe
        tipologia_str = str(tipologia or '')
        n_conduttori_str = str(n_conduttori or '0')
        sezione_str = str(sezione or '0')
        
        print(f"Parametri di ricerca: tipologia='{tipologia_str}', n_conduttori='{n_conduttori_str}', sezione='{sezione_str}'")
        
        # Query per trovare bobine compatibili
        query = """
            SELECT id_bobina, tipologia, n_conduttori, sezione, metri_residui, stato_bobina
            FROM parco_cavi
            WHERE id_cantiere = %s
              AND tipologia = %s
              AND n_conduttori = %s
              AND sezione = %s
              AND stato_bobina NOT IN ('Over', 'Terminata')
              AND metri_residui > 0
        """
        
        # Esegui la query
        cursor.execute(query, (cantiere_id, tipologia_str, n_conduttori_str, sezione_str))
        bobine = cursor.fetchall()
        
        print(f"Trovate {len(bobine)} bobine compatibili")
        for bobina in bobine:
            print(f"ID: {bobina['id_bobina']}, Tipologia: {bobina['tipologia']}, " +
                  f"N_Conduttori: {bobina['n_conduttori']}, Sezione: {bobina['sezione']}, " +
                  f"Metri residui: {bobina['metri_residui']}, Stato: {bobina['stato_bobina']}")
        
        # Se non ci sono bobine compatibili, cerca bobine simili
        if len(bobine) == 0:
            print("\nRicerca bobine simili...")
            
            # Cerca bobine con la stessa tipologia
            cursor.execute("""
                SELECT id_bobina, tipologia, n_conduttori, sezione, metri_residui, stato_bobina
                FROM parco_cavi
                WHERE id_cantiere = %s
                  AND tipologia = %s
                  AND stato_bobina NOT IN ('Over', 'Terminata')
                  AND metri_residui > 0
                LIMIT 5
            """, (cantiere_id, tipologia_str))
            
            bobine_tipologia = cursor.fetchall()
            print(f"Trovate {len(bobine_tipologia)} bobine con tipologia '{tipologia_str}'")
            for bobina in bobine_tipologia:
                print(f"ID: {bobina['id_bobina']}, Tipologia: {bobina['tipologia']}, " +
                      f"N_Conduttori: {bobina['n_conduttori']}, Sezione: {bobina['sezione']}, " +
                      f"Metri residui: {bobina['metri_residui']}, Stato: {bobina['stato_bobina']}")
            
            # Cerca bobine con lo stesso numero di conduttori
            cursor.execute("""
                SELECT id_bobina, tipologia, n_conduttori, sezione, metri_residui, stato_bobina
                FROM parco_cavi
                WHERE id_cantiere = %s
                  AND n_conduttori = %s
                  AND stato_bobina NOT IN ('Over', 'Terminata')
                  AND metri_residui > 0
                LIMIT 5
            """, (cantiere_id, n_conduttori_str))
            
            bobine_conduttori = cursor.fetchall()
            print(f"\nTrovate {len(bobine_conduttori)} bobine con n_conduttori '{n_conduttori_str}'")
            for bobina in bobine_conduttori:
                print(f"ID: {bobina['id_bobina']}, Tipologia: {bobina['tipologia']}, " +
                      f"N_Conduttori: {bobina['n_conduttori']}, Sezione: {bobina['sezione']}, " +
                      f"Metri residui: {bobina['metri_residui']}, Stato: {bobina['stato_bobina']}")
        
        # Verifica tutte le bobine disponibili
        print("\nTutte le bobine disponibili:")
        cursor.execute("""
            SELECT id_bobina, tipologia, n_conduttori, sezione, metri_residui, stato_bobina
            FROM parco_cavi
            WHERE id_cantiere = %s
              AND stato_bobina NOT IN ('Over', 'Terminata')
              AND metri_residui > 0
            LIMIT 10
        """, (cantiere_id,))
        
        bobine_disponibili = cursor.fetchall()
        print(f"Trovate {len(bobine_disponibili)} bobine disponibili")
        for bobina in bobine_disponibili:
            print(f"ID: {bobina['id_bobina']}, Tipologia: {bobina['tipologia']}, " +
                  f"N_Conduttori: {bobina['n_conduttori']}, Sezione: {bobina['sezione']}, " +
                  f"Metri residui: {bobina['metri_residui']}, Stato: {bobina['stato_bobina']}")
        
        return bobine
    
    except Exception as e:
        print(f"Errore durante la verifica delle bobine compatibili: {str(e)}")
        return []
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def main():
    """
    Funzione principale.
    """
    # Parametri di ricerca
    cantiere_id = 1  # Modifica con l'ID del cantiere corretto
    tipologia = "test"
    n_conduttori = "0"
    sezione = "2"
    
    print(f"Verifica bobine compatibili per: tipologia='{tipologia}', n_conduttori='{n_conduttori}', sezione='{sezione}'")
    check_bobine_compatibili(cantiere_id, tipologia, n_conduttori, sezione)

if __name__ == "__main__":
    main()
