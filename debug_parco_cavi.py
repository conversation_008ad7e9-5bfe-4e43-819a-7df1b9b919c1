from sqlalchemy import create_engine, text
import logging

# Configura il logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configurazione del database
DB_HOST = "localhost"
DB_PORT = "5432"
DB_NAME = "cantieri"
DB_USER = "postgres"
DB_PASSWORD = "Taranto"

# Stringa di connessione per SQLAlchemy
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# Crea l'engine SQLAlchemy
engine = create_engine(DATABASE_URL)

# Funzione per ottenere le bobine di un cantiere
def get_bobine(cantiere_id):
    try:
        logger.info(f"Tentativo di ottenere le bobine per il cantiere {cantiere_id}")
        
        # Verifica che il cantiere esista
        with engine.connect() as connection:
            result = connection.execute(
                text("SELECT * FROM cantieri WHERE id_cantiere = :cantiere_id"),
                {"cantiere_id": cantiere_id}
            )
            cantiere = result.fetchone()
            
            if not cantiere:
                logger.error(f"Cantiere con ID {cantiere_id} non trovato")
                return {"error": f"Cantiere con ID {cantiere_id} non trovato"}
            
            logger.info(f"Cantiere trovato: {cantiere}")
            
            # Ottieni le bobine per questo cantiere
            try:
                result = connection.execute(
                    text("SELECT * FROM parco_cavi WHERE id_cantiere = :cantiere_id"),
                    {"cantiere_id": cantiere_id}
                )
                bobine = result.fetchall()
                
                logger.info(f"Numero di bobine trovate: {len(bobine)}")
                
                # Converti i risultati in dizionari
                bobine_list = []
                for bobina in bobine:
                    bobina_dict = {column: getattr(bobina, column) for column in bobina._mapping.keys()}
                    bobine_list.append(bobina_dict)
                
                return bobine_list
            except Exception as e:
                logger.error(f"Errore durante l'esecuzione della query per le bobine: {e}")
                return {"error": f"Errore durante l'esecuzione della query: {str(e)}"}
    except Exception as e:
        logger.error(f"Errore generale: {e}")
        return {"error": f"Errore generale: {str(e)}"}

# Esegui il test
if __name__ == "__main__":
    cantiere_id = 1
    result = get_bobine(cantiere_id)
    
    if isinstance(result, list):
        print(f"Bobine trovate per il cantiere {cantiere_id}: {len(result)}")
        if result:
            print("Prima bobina:")
            for key, value in result[0].items():
                print(f"  {key}: {value}")
        else:
            print("Nessuna bobina trovata per questo cantiere")
    else:
        print(f"Errore: {result.get('error', 'Errore sconosciuto')}")
