# Fix for Cable Deletion Issue

## Problem Description

The system was incorrectly deleting cables from the database when they should have been marked as SPARE. This happened because:

1. The frontend was sending a query parameter `mode` to the backend API
2. The backend API was expecting a request body parameter `options` with a `mode` property
3. Since the backend wasn't receiving the expected parameter, it was defaulting to the deletion behavior instead of marking cables as SPARE

## Solution

We modified the backend API to accept a query parameter `mode` instead of a request body parameter `options`. This ensures that when the frontend sends `mode=spare`, the backend correctly marks the cable as SPARE instead of deleting it.

### Changes Made

1. In `webapp/backend/api/cavi.py`, we modified the `delete_cavo` function to accept a query parameter:

```python
@router.delete("/{cantiere_id}/{cavo_id}", response_model=dict)
def delete_cavo(
    cantiere_id: int,
    cavo_id: str,
    mode: Optional[str] = Query(None, description="Modalità di eliminazione: 'spare' per marcare come SPARE, 'delete' per eliminare definitivamente"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
```

2. We updated the logic to check the `mode` parameter:

```python
# Se il cavo è installato e non è specificata la modalità, o se è richiesta l'eliminazione definitiva,
# impedisci l'eliminazione e suggerisci di marcarlo come SPARE
if is_installed and (not mode or mode == 'delete'):
    raise HTTPException(
        status_code=status.HTTP_400_BAD_REQUEST,
        detail="Il cavo risulta installato o parzialmente posato. Usa mode='spare' per marcarlo come SPARE invece di eliminarlo."
    )

try:
    # Se è richiesta la marcatura come SPARE o il cavo è installato
    if mode == 'spare':
        # Marca il cavo come SPARE
        cavo.stato_installazione = "SPARE"
        cavo.modificato_manualmente = 3
        cavo.timestamp = datetime.now()
        
        # ...
    else:
        # Elimina il cavo
        # ...
```

3. We added debug logging to help diagnose the issue.

## Expected Behavior

With these changes, when a user chooses to mark a cable as SPARE:

1. The frontend sends a DELETE request with `mode=spare` as a query parameter
2. The backend receives the request and checks the `mode` parameter
3. If `mode=spare`, the backend marks the cable as SPARE by setting:
   - `stato_installazione = "SPARE"`
   - `modificato_manualmente = 3`
4. The cable remains in the database but is marked as SPARE
5. SPARE cables are filtered out of the active cables list and shown only in the SPARE cables list

## Testing

To verify the fix:

1. Try to mark a cable as SPARE through the UI
2. Check the logs to ensure the backend receives the `mode=spare` parameter
3. Verify that the cable is marked as SPARE (with `modificato_manualmente = 3`) and not deleted from the database
4. Verify that the cable appears in the SPARE cables list and not in the active cables list
