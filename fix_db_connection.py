#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per migliorare la gestione delle connessioni al database nelle funzioni di aggiunta e modifica cavo.
"""

import sys
import os
import logging
from datetime import datetime
import psycopg2
from psycopg2.extras import RealDictCursor
from contextlib import contextmanager

# Configurazione del logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("fix_db_connection.log"),
        logging.StreamHandler()
    ]
)

# Configurazione del database
DB_HOST = 'localhost'
DB_PORT = '5432'
DB_NAME = 'cantieri'
DB_USER = 'postgres'
DB_PASSWORD = 'Taranto'

@contextmanager
def get_db_connection(autocommit=False, dict_cursor=False):
    """
    Context manager migliorato per la gestione delle connessioni al database.
    
    Args:
        autocommit (bool): Se True, imposta autocommit=True sulla connessione
        dict_cursor (bool): Se True, utilizza RealDictCursor
        
    Yields:
        conn: Connessione al database
        cursor: Cursore per eseguire query
    """
    conn = None
    cursor = None
    try:
        # Crea la connessione
        if dict_cursor:
            conn = psycopg2.connect(
                host=DB_HOST,
                port=DB_PORT,
                dbname=DB_NAME,
                user=DB_USER,
                password=DB_PASSWORD,
                cursor_factory=RealDictCursor
            )
        else:
            conn = psycopg2.connect(
                host=DB_HOST,
                port=DB_PORT,
                dbname=DB_NAME,
                user=DB_USER,
                password=DB_PASSWORD
            )
        
        # Imposta autocommit se richiesto
        conn.autocommit = autocommit
        
        # Crea il cursore
        cursor = conn.cursor()
        
        # Restituisci connessione e cursore
        yield conn, cursor
    except Exception as e:
        # In caso di errore, fai rollback se non in autocommit
        if conn and not autocommit:
            conn.rollback()
        logging.error(f"Errore durante la connessione al database: {e}")
        raise
    finally:
        # Chiudi il cursore e la connessione
        if cursor:
            cursor.close()
        if conn:
            # Se non in autocommit e non è stato fatto commit o rollback, fai rollback
            if not autocommit and conn.status != psycopg2.extensions.STATUS_IN_TRANSACTION:
                conn.rollback()
            conn.close()

def aggiungi_cavo_migliorato(id_cantiere, id_cavo, dati_cavo):
    """
    Versione migliorata della funzione aggiungi_cavo con gestione robusta delle transazioni.
    
    Args:
        id_cantiere (int): ID del cantiere
        id_cavo (str): ID del cavo
        dati_cavo (dict): Dati del cavo da inserire
        
    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    logging.info(f"Aggiunta cavo {id_cavo} al cantiere {id_cantiere}")
    
    try:
        # Utilizziamo il context manager migliorato
        with get_db_connection(autocommit=False) as (conn, cursor):
            # Verifica che il cavo non esista già
            cursor.execute("""
                SELECT id_cavo FROM Cavi
                WHERE id_cavo = %s AND id_cantiere = %s
            """, (id_cavo, id_cantiere))
            
            if cursor.fetchone():
                logging.warning(f"Il cavo {id_cavo} esiste già nel cantiere {id_cantiere}")
                return False
            
            # Ottieni la revisione corrente
            cursor.execute("""
                SELECT MAX(CAST(revisione_ufficiale AS INTEGER)) FROM Cavi
                WHERE id_cantiere = %s
            """, (id_cantiere,))
            
            result = cursor.fetchone()
            revisione_corrente = '00'
            if result and result[0] is not None:
                revisione_corrente = f"{int(result[0]):02d}"
            
            # Inserisci il cavo
            cursor.execute("""
                INSERT INTO Cavi (
                    id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                    tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                    descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                    metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                    stato_installazione, modificato_manualmente, timestamp
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
                )
            """, (
                id_cavo, id_cantiere, revisione_corrente, 
                dati_cavo.get('sistema', ''), 
                dati_cavo.get('utility', ''), 
                dati_cavo.get('colore_cavo', ''),
                dati_cavo.get('tipologia', ''), 
                dati_cavo.get('n_conduttori', ''), 
                dati_cavo.get('sezione', ''), 
                dati_cavo.get('sh', '-'),
                dati_cavo.get('ubicazione_partenza', ''), 
                dati_cavo.get('utenza_partenza', ''), 
                dati_cavo.get('descrizione_utenza_partenza', ''),
                dati_cavo.get('ubicazione_arrivo', ''), 
                dati_cavo.get('utenza_arrivo', ''), 
                dati_cavo.get('descrizione_utenza_arrivo', ''),
                dati_cavo.get('metri_teorici', 0), 
                dati_cavo.get('stato_installazione', 'Da installare')
            ))
            
            # Commit esplicito
            conn.commit()
            logging.info(f"Cavo {id_cavo} aggiunto con successo al cantiere {id_cantiere}")
            return True
            
    except Exception as e:
        logging.error(f"Errore durante l'aggiunta del cavo {id_cavo}: {e}")
        
        # Verifica se il cavo è stato comunque inserito (potrebbe accadere se l'errore è dopo il commit)
        try:
            with get_db_connection(autocommit=True) as (conn, cursor):
                cursor.execute("""
                    SELECT id_cavo FROM Cavi
                    WHERE id_cavo = %s AND id_cantiere = %s
                """, (id_cavo, id_cantiere))
                
                if cursor.fetchone():
                    logging.warning(f"Il cavo {id_cavo} risulta inserito nonostante l'errore!")
                    return True
        except Exception as check_e:
            logging.error(f"Errore durante la verifica dell'inserimento del cavo: {check_e}")
        
        return False

def modifica_cavo_migliorato(id_cantiere, id_cavo, dati_cavo):
    """
    Versione migliorata della funzione modifica_cavo con gestione robusta delle transazioni.
    
    Args:
        id_cantiere (int): ID del cantiere
        id_cavo (str): ID del cavo
        dati_cavo (dict): Dati del cavo da modificare
        
    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    logging.info(f"Modifica cavo {id_cavo} nel cantiere {id_cantiere}")
    
    try:
        # Utilizziamo il context manager migliorato
        with get_db_connection(autocommit=False) as (conn, cursor):
            # Verifica che il cavo esista
            cursor.execute("""
                SELECT id_cavo FROM Cavi
                WHERE id_cavo = %s AND id_cantiere = %s
            """, (id_cavo, id_cantiere))
            
            if not cursor.fetchone():
                logging.warning(f"Il cavo {id_cavo} non esiste nel cantiere {id_cantiere}")
                return False
            
            # Costruisci la query di aggiornamento
            update_fields = []
            update_values = []
            
            for field, value in dati_cavo.items():
                if field not in ['id_cavo', 'id_cantiere']:  # Ignora i campi chiave
                    update_fields.append(f"{field} = %s")
                    update_values.append(value)
            
            # Aggiungi i campi di sistema
            update_fields.append("modificato_manualmente = 1")
            update_fields.append("timestamp = CURRENT_TIMESTAMP")
            
            # Aggiungi i parametri per la clausola WHERE
            update_values.append(id_cantiere)
            update_values.append(id_cavo)
            
            # Esegui la query di aggiornamento
            query = f"""
                UPDATE Cavi
                SET {', '.join(update_fields)}
                WHERE id_cantiere = %s AND id_cavo = %s
            """
            
            cursor.execute(query, update_values)
            
            # Commit esplicito
            conn.commit()
            logging.info(f"Cavo {id_cavo} modificato con successo nel cantiere {id_cantiere}")
            return True
            
    except Exception as e:
        logging.error(f"Errore durante la modifica del cavo {id_cavo}: {e}")
        
        # Verifica se il cavo è stato comunque modificato (potrebbe accadere se l'errore è dopo il commit)
        try:
            with get_db_connection(autocommit=True, dict_cursor=True) as (conn, cursor):
                cursor.execute("""
                    SELECT * FROM Cavi
                    WHERE id_cavo = %s AND id_cantiere = %s
                """, (id_cavo, id_cantiere))
                
                cavo = cursor.fetchone()
                if cavo:
                    # Verifica se i campi sono stati aggiornati
                    modificato = False
                    for field, value in dati_cavo.items():
                        if field in cavo and cavo[field] == value:
                            modificato = True
                            break
                    
                    if modificato:
                        logging.warning(f"Il cavo {id_cavo} risulta modificato nonostante l'errore!")
                        return True
        except Exception as check_e:
            logging.error(f"Errore durante la verifica della modifica del cavo: {check_e}")
        
        return False

def test_funzioni_migliorate():
    """
    Testa le funzioni migliorate.
    """
    logging.info("=== TEST FUNZIONI MIGLIORATE ===")
    
    # Genera un ID di test
    import random
    import string
    id_cavo = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
    
    # Dati di test
    dati_cavo = {
        'sistema': 'TEST_SISTEMA',
        'utility': 'TEST_UTILITY',
        'colore_cavo': 'NERO',
        'tipologia': 'TEST_TIPO',
        'n_conduttori': '3',
        'sezione': '1.5',
        'sh': '-',
        'ubicazione_partenza': 'TEST_PARTENZA',
        'utenza_partenza': 'TEST_UTENZA_P',
        'descrizione_utenza_partenza': 'TEST_DESC_P',
        'ubicazione_arrivo': 'TEST_ARRIVO',
        'utenza_arrivo': 'TEST_UTENZA_A',
        'descrizione_utenza_arrivo': 'TEST_DESC_A',
        'metri_teorici': 100,
        'stato_installazione': 'Da installare'
    }
    
    # Test aggiunta cavo
    logging.info(f"Test aggiunta cavo {id_cavo}")
    result_add = aggiungi_cavo_migliorato(1, id_cavo, dati_cavo)
    logging.info(f"Risultato aggiunta: {result_add}")
    
    # Test modifica cavo
    if result_add:
        logging.info(f"Test modifica cavo {id_cavo}")
        dati_modifica = {
            'sistema': 'TEST_SISTEMA_MOD',
            'utility': 'TEST_UTILITY_MOD'
        }
        result_mod = modifica_cavo_migliorato(1, id_cavo, dati_modifica)
        logging.info(f"Risultato modifica: {result_mod}")
    
    logging.info("=== FINE TEST FUNZIONI MIGLIORATE ===")

if __name__ == "__main__":
    test_funzioni_migliorate()
