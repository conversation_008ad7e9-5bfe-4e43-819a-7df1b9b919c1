import logging
import random
import string
import psycopg2
import bcrypt
from datetime import datetime
from typing import Tuple, Optional, Union, Any


class CantieriManager:
    def __init__(self, db, current_user: Optional[str] = None):
        """
        Inizializza il CantieriManager.

        Args:
            db: Istanza del database
            current_user (str, optional): Username dell'utente corrente
        """
        self.db = db
        self.current_user = current_user

        if not self.current_user:
            logging.warning("CantieriManager inizializzato senza utente corrente")

    def _check_user_auth(self) -> bool:
        """Verifica se c'è un utente autenticato."""
        if not self.current_user:
            logging.error("❌ Operazione non autorizzata: nessun utente autenticato")
            return False
        return True

    def genera_codice_univoco_targa(self) -> str:
        """
        Genera un codice univoco tipo targa auto per un cantiere.

        Returns:
            str: Codice univoco nel formato C{2 lettere}{3 numeri}{2 lettere}.
        """
        while True:
            # Genera 2 lettere casuali (maiuscole)
            lettere_iniziali = ''.join(random.choices(string.ascii_uppercase, k=2))

            # Genera 3 numeri casuali
            numeri = ''.join(random.choices(string.digits, k=3))

            # Genera 2 lettere casuali (maiuscole)
            lettere_finali = ''.join(random.choices(string.ascii_uppercase, k=2))

            # Combina tutto nel formato tipo targa auto
            codice_univoco = f"C{lettere_iniziali}{numeri}{lettere_finali}"

            # Verifica se il codice esiste già nel database
            with sqlite3.connect(self.db_path) as conn:
                c = conn.cursor()
                c.execute('SELECT 1 FROM Cantieri WHERE codice_univoco = ?', (codice_univoco,))
                if not c.fetchone():
                    # Se il codice non esiste, restituiscilo
                    return codice_univoco

    def _get_cantiere_by_id(self, id_cantiere: int) -> Optional[Tuple[int, str, str]]:
        """
        Ottiene i dettagli di un cantiere dal database.

        Args:
            id_cantiere (int): ID del cantiere da cercare.

        Returns:
            Optional[Tuple[int, str, str]]: (id_cantiere, codice_univoco, nome_cantiere) se trovato, altrimenti None.
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                c = conn.cursor()
                c.execute('''
                    SELECT c.id_cantiere, c.codice_univoco, c.nome, u.username
                    FROM Cantieri c
                    JOIN Utenti u ON c.id_utente = u.id_utente
                    WHERE c.id_cantiere = ?
                ''', (id_cantiere,))
                result = c.fetchone()

                if result:
                    id_cantiere, codice_univoco, nome_cantiere, proprietario = result
                    if proprietario == self.current_user:
                        return id_cantiere, codice_univoco, nome_cantiere
                    else:
                        logging.warning(f"Utente {self.current_user} non ha accesso al cantiere {id_cantiere}")
                        return None
                else:
                    logging.warning(f"Cantiere {id_cantiere} non trovato")
                    return None
        except sqlite3.Error as e:
            logging.error(f"Errore nel database: {e}")
            return None


    def seleziona_cantiere(self) -> Tuple[Optional[int], Optional[str], Optional[str]]:
        """
        Gestisce l'interazione con l'utente per selezionare o creare un cantiere.
        """
        if not self._check_user_auth():
            return None, None, None

        print("\nI tuoi cantieri disponibili:")
        self.visualizza_cantieri()

        while True:
            scelta = input(
                "\nVuoi creare un nuovo cantiere (N), selezionarne uno esistente (E) o uscire (Q)? [n/e/q]: ").strip().lower()

            if scelta == 'q':
                print("\nOperazione annullata.")
                return None, None, None

            elif scelta == 'e':
                id_cantiere = input("\nInserisci l'ID del cantiere (o premi INVIO per annullare): ").strip()
                if not id_cantiere:
                    return None, None, None

                try:
                    id_cantiere = int(id_cantiere)
                except ValueError:
                    print("\n❌ L'ID deve essere un numero")
                    continue

                cantiere = self._get_cantiere_by_id(id_cantiere)
                if cantiere:
                    id_cantiere, codice_univoco, nome_cantiere = cantiere
                    print(f"\n✅ Selezionato cantiere: {nome_cantiere} (ID: {id_cantiere}, Codice: {codice_univoco})")
                    return id_cantiere, codice_univoco, nome_cantiere
                else:
                    print("\n❌ Cantiere non trovato o accesso negato")
                    continue

            elif scelta == 'n':
                # Creazione nuovo cantiere
                nome_cantiere = input("\nInserisci nome cantiere: ").strip()
                if not nome_cantiere:
                    print("\n❌ Il nome non può essere vuoto")
                    continue

                descrizione = input("Inserisci descrizione cantiere: ").strip()

                result = self.crea_cantiere(nome_cantiere, descrizione)
                if result:
                    id_cantiere, codice_univoco = result
                    return id_cantiere, codice_univoco, nome_cantiere
                return None, None, None

            else:
                print("\n❌ Scelta non valida. Inserisci n, e o q.")

    def visualizza_cantieri(self) -> None:
        """Visualizza tutti i cantieri dell'utente corrente."""
        if not self._check_user_auth():
            return

        try:
            with sqlite3.connect(self.db_path) as conn:
                c = conn.cursor()
                c.execute('''
                    SELECT c.id_cantiere, c.codice_univoco, c.nome, c.descrizione, c.data_creazione
                    FROM Cantieri c
                    JOIN Utenti u ON c.id_utente = u.id_utente
                    WHERE u.username = ?
                    ORDER BY c.data_creazione DESC
                ''', (self.current_user,))

                cantieri = c.fetchall()

                if cantieri:
                    print("\nI tuoi cantieri:")
                    print(f"{'ID':<6} {'Codice':<15} {'Nome':<20} {'Descrizione':<30} {'Data Creazione':<20}")
                    print("-" * 91)
                    for id_cantiere, codice_univoco, nome, descrizione, data_creazione in cantieri:
                        if isinstance(data_creazione, str):
                            data_creazione = datetime.fromisoformat(data_creazione)
                        data_str = data_creazione.strftime("%d/%m/%Y %H:%M") if data_creazione else "N/D"
                        desc_trunc = (descrizione[:27] + '...') if descrizione and len(descrizione) > 30 else (descrizione or '')
                        print(f"{id_cantiere:<6} {codice_univoco:<15} {nome:<20} {desc_trunc:<30} {data_str:<20}")
                else:
                    print("Non hai ancora creato nessun cantiere.")

        except sqlite3.Error as e:
            logging.error(f"❌ Errore nel database: {e}")

    def elimina_cantiere(self, id_cantiere: str) -> bool:
        """
        Elimina un cantiere e tutti i suoi dati correlati.

        Args:
            id_cantiere (str): ID del cantiere da eliminare

        Returns:
            bool: True se l'eliminazione ha successo, False altrimenti
        """
        if not self._check_user_auth():
            return False

        try:
            # Usa is_cantiere_owner per verificare i permessi
            if not self.is_cantiere_owner(id_cantiere, self.current_user):
                logging.error("❌ Non hai i permessi per eliminare questo cantiere")
                return False

            with sqlite3.connect(self.db_path) as conn:
                c = conn.cursor()
                # Elimina le bobine associate al cantiere
                c.execute('DELETE FROM parco_cavi WHERE id_cantiere = ?', (id_cantiere,))
                # Elimina i cavi associati
                c.execute('DELETE FROM Cavi WHERE id_cantiere = ?', (id_cantiere,))
                # Elimina il cantiere
                c.execute('DELETE FROM Cantieri WHERE id_cantiere = ?', (id_cantiere,))
                conn.commit()

                logging.info(f"✅ Cantiere {id_cantiere} eliminato con successo")
                return True

        except sqlite3.Error as e:
            logging.error(f"❌ Errore nell'eliminazione del cantiere: {e}")
            return False

    def crea_cantiere(self, nome_cantiere: str, descrizione: str) -> Optional[Tuple[int, str]]:
        """Crea un nuovo cantiere nel database con un codice univoco tipo targa auto."""
        if not self._check_user_auth():
            return None
        try:
            while True:
                password_cantiere = input("\nInserisci una password per il cantiere: ").strip()
                if not password_cantiere:
                    print("\n❌ La password non può essere vuota!")
                    continue
                conferma_password = input("Conferma la password: ").strip()
                if password_cantiere != conferma_password:
                    print("\n❌ Le password non coincidono!")
                    continue
                break

            hashed_password = bcrypt.hashpw(password_cantiere.encode('utf-8'), bcrypt.gensalt())
            with sqlite3.connect(self.db_path) as conn:
                c = conn.cursor()
                # Ottieni l'id_utente
                c.execute('SELECT id_utente FROM Utenti WHERE username = ?', (self.current_user,))
                result = c.fetchone()
                if not result:
                    logging.error("❌ Utente non trovato nel database")
                    return None
                id_utente = result[0]

                # Genera il codice univoco tipo targa auto
                codice_univoco = self.genera_codice_univoco_targa()

                # Inserisci il nuovo cantiere con il codice univoco
                c.execute('''
                    INSERT INTO Cantieri
                    (nome, descrizione, data_creazione, password_cantiere, id_utente, codice_univoco)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (nome_cantiere, descrizione, datetime.now(), hashed_password, id_utente, codice_univoco))

                id_cantiere = c.lastrowid
                conn.commit()

                print("\n✅ Cantiere creato con successo!")
                print(f"ID: {id_cantiere}")
                print(f"Codice univoco: {codice_univoco}")
                print(f"Password: {password_cantiere}")
                print("\n⚠️ Conserva queste credenziali in modo sicuro!")
                return id_cantiere, codice_univoco

        except sqlite3.Error as e:
            logging.error(f"❌ Errore nella creazione del cantiere: {str(e)}")
            return None

    def is_cantiere_owner(self, id_cantiere: int, username: str) -> bool:
        """
        Verifica se l'utente è il proprietario del cantiere.

        Args:
            id_cantiere (int): ID del cantiere
            username (str): Username da verificare

        Returns:
            bool: True se l'utente è il proprietario, False altrimenti
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                c = conn.cursor()
                c.execute('''
                    SELECT 1
                    FROM Cantieri c
                    JOIN Utenti u ON c.id_utente = u.id_utente
                    WHERE c.id_cantiere = ? AND u.username = ?
                ''', (id_cantiere, username))
                return bool(c.fetchone())
        except sqlite3.Error as e:
            logging.error(f"❌ Errore nella verifica del proprietario: {e}")
            return False
