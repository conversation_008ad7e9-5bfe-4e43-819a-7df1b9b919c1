#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Modulo per la gestione delle certificazioni dei cavi.
Fornisce funzionalità per creare, visualizzare e gestire le certificazioni dei cavi.
"""

import psycopg2
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from modules.database_pg import database_connection, Database
from modules.utils import ValidazioneCampi
from modules.strumenti_certificati import visualizza_strumenti

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Definizione dei tipi di test inclusi nella certificazione
TIPI_TEST = [
    "Continuità",
    "Isolamento",
    "Resistenza"
]

def crea_certificazione(id_cantiere: int) -> bool:
    """
    Crea una nuova certificazione per un cavo.
    I dati vengono precompilati dal cavo, l'utente deve inserire solo i valori di test.

    Args:
        id_cantiere: ID del cantiere

    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        print("\n📊 CREAZIONE CERTIFICAZIONE CAVO")

        # Richiedi l'ID del cavo
        id_cavo = input("ID cavo da certificare: ").strip().upper()
        if not id_cavo:
            print("❌ L'ID cavo è obbligatorio!")
            return False

        # Inizializza il database
        db = Database()

        with database_connection() as conn:
            c = conn.cursor()

            # Verifica che il cavo esista e sia installato
            c.execute("""
                SELECT id_cavo, stato_installazione, metratura_reale, responsabile_posa, data_posa
                FROM Cavi
                WHERE id_cavo = ? AND id_cantiere = ?
            """, (id_cavo, id_cantiere))

            cavo = c.fetchone()
            if not cavo:
                print(f"\n❌ Cavo {id_cavo} non trovato nel cantiere!")
                return False

            # Verifica che il cavo sia installato
            if cavo['stato_installazione'] != "Installato":
                print(f"\n⚠️ Attenzione: Il cavo {id_cavo} non risulta installato!")
                if not input("Continuare comunque? (s/n): ").lower().startswith('s'):
                    return False

            # Verifica se esiste già una certificazione per questo cavo
            c.execute("""
                SELECT id_certificazione FROM CertificazioniCavi
                WHERE id_cantiere = ? AND id_cavo = ?
            """, (id_cantiere, id_cavo))

            existing_cert = c.fetchone()
            if existing_cert:
                print(f"\n⚠️ Attenzione: Esiste già una certificazione per il cavo {id_cavo}!")
                scelta = input("Vuoi aggiornare la certificazione esistente? (s/n): ").lower()
                if not scelta.startswith('s'):
                    return False
                force_update = True
            else:
                force_update = False

            # Precompila i dati dal cavo
            id_operatore = input("Nome operatore: ").strip() or "Operatore"

            # Selezione dello strumento certificato
            print("\n=== SELEZIONE STRUMENTO CERTIFICATO ===")
            print("Seleziona lo strumento utilizzato per la certificazione:")
            visualizza_strumenti(id_cantiere)

            id_strumento = None
            strumento_utilizzato = None

            scelta_strumento = input("\nInserisci l'ID dello strumento utilizzato (lascia vuoto per inserire manualmente): ").strip()
            if scelta_strumento and scelta_strumento.isdigit():
                # Verifica che lo strumento esista
                with database_connection() as conn_check:
                    c_check = conn_check.cursor()
                    c_check.execute("""
                        SELECT id_strumento, nome, marca, modello, numero_serie
                        FROM StrumentiCertificati
                        WHERE id_cantiere = ? AND id_strumento = ?
                    """, (id_cantiere, int(scelta_strumento)))

                    strumento = c_check.fetchone()
                    if strumento:
                        id_strumento = strumento['id_strumento']
                        strumento_utilizzato = f"{strumento['nome']} {strumento['marca']} {strumento['modello']} (S/N: {strumento['numero_serie']})"
                        print(f"✅ Strumento selezionato: {strumento_utilizzato}")
                    else:
                        print("❌ Strumento non trovato!")

            # Se non è stato selezionato uno strumento certificato, richiedi l'inserimento manuale
            if not strumento_utilizzato:
                strumento_utilizzato = input("Strumento utilizzato (modello/seriale): ").strip() or "Megger standard"
                id_strumento = None

            # Usa la metratura reale del cavo come lunghezza misurata
            lunghezza_misurata = cavo['metratura_reale']

            # Richiedi i valori dei test (solo quelli essenziali)
            print("\n=== VALORI DEI TEST ===")
            valore_continuita = "OK"  # Valore predefinito
            valore_isolamento = input("Valore test isolamento (megger) in MΩ: ").strip() or "500"
            valore_resistenza = "OK"  # Valore predefinito

            # Note opzionali
            note = input("Note (opzionale): ").strip()

            # Crea o aggiorna la certificazione nel database
            id_certificazione = db.crea_certificazione(
                id_cantiere, id_cavo, id_operatore, strumento_utilizzato, id_strumento,
                lunghezza_misurata, valore_continuita, valore_isolamento, valore_resistenza,
                note, force_update
            )

            if id_certificazione == -1:
                print(f"\n❌ Esiste già una certificazione per il cavo {id_cavo}!")
                return False
            elif id_certificazione == 0:
                print("\n❌ Errore durante la creazione della certificazione!")
                return False

            # Ottieni il numero di certificato
            c.execute("""
                SELECT numero_certificato FROM CertificazioniCavi
                WHERE id_certificazione = ?
            """, (id_certificazione,))

            cert = c.fetchone()
            numero_certificato = cert['numero_certificato'] if cert else "N/A"

            if force_update:
                print(f"\n✅ Certificazione {numero_certificato} aggiornata con successo per il cavo {id_cavo}!")
            else:
                print(f"\n✅ Certificazione {numero_certificato} creata con successo per il cavo {id_cavo}!")

            # Chiedi se generare il PDF
            if input("\nGenerare il PDF della certificazione? (s/n): ").lower().startswith('s'):
                genera_pdf_certificazione(id_cantiere, id_certificazione)

            return True

    except Exception as e:
        logging.error(f"Errore durante la creazione della certificazione: {str(e)}")
        print(f"\n❌ Errore: {str(e)}")
        return False


def visualizza_certificazioni(id_cantiere: int, filtro_cavo: str = None) -> bool:
    """
    Visualizza le certificazioni dei cavi, con possibilità di filtrare per ID cavo.

    Args:
        id_cantiere: ID del cantiere
        filtro_cavo: Filtro opzionale per ID cavo

    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()

            # Query base
            query = """
                SELECT
                    c.id_certificazione, c.id_cavo, c.numero_certificato, c.data_certificazione,
                    c.lunghezza_misurata, c.valore_isolamento, c.id_operatore, c.strumento_utilizzato,
                    s.nome as strumento_nome, s.marca as strumento_marca, s.modello as strumento_modello
                FROM CertificazioniCavi c
                LEFT JOIN StrumentiCertificati s ON c.id_strumento = s.id_strumento AND c.id_cantiere = s.id_cantiere
                WHERE c.id_cantiere = %s
            """

            params = [id_cantiere]

            # Aggiungi filtro se specificato
            if filtro_cavo:
                query += " AND c.id_cavo LIKE %s"
                params.append(f"%{filtro_cavo}%")

            # Ordina per data e ID cavo
            query += " ORDER BY c.data_certificazione DESC, c.id_cavo"

            c.execute(query, params)
            certificazioni = c.fetchall()

            if not certificazioni:
                print("\nNessuna certificazione trovata.")
                return True

            print("\n" + "=" * 115)
            print("CERTIFICAZIONI CAVI")
            print("=" * 115)
            print("{:<5} {:<15} {:<15} {:<12} {:<10} {:<15} {:<15} {:<20}".format(
                "ID", "Cavo", "Numero", "Data", "Lunghezza", "Isolamento", "Operatore", "Strumento"
            ))
            print("-" * 115)

            for cert in certificazioni:
                data_str = cert['data_certificazione'] if cert['data_certificazione'] else "-"
                # Handle lunghezza_misurata safely - check if it's a number before formatting
                if cert['lunghezza_misurata'] is not None:
                    try:
                        # Convert to float and format with 2 decimal places
                        lunghezza = f"{float(cert['lunghezza_misurata']):.2f}"
                    except (ValueError, TypeError):
                        # If conversion fails, just use the value as is
                        lunghezza = str(cert['lunghezza_misurata'])
                else:
                    lunghezza = "-"

                # Prepara la visualizzazione dello strumento
                if cert['strumento_nome']:
                    strumento_display = f"{cert['strumento_nome']} {cert['strumento_marca']}"
                else:
                    strumento_display = cert['strumento_utilizzato'] if cert['strumento_utilizzato'] else "-"

                print("{:<5} {:<15} {:<15} {:<12} {:<10} {:<15} {:<15} {:<20}".format(
                    cert['id_certificazione'],
                    cert['id_cavo'],
                    cert['numero_certificato'],
                    data_str,
                    lunghezza,
                    cert['valore_isolamento'],
                    cert['id_operatore'],
                    strumento_display
                ))

            return True

    except Exception as e:
        logging.error(f"Errore durante la visualizzazione delle certificazioni: {str(e)}")
        print(f"\n❌ Errore: {str(e)}")
        return False


def dettagli_certificazione(id_cantiere: int, id_certificazione: int) -> bool:
    """
    Visualizza i dettagli di una certificazione specifica.

    Args:
        id_cantiere: ID del cantiere
        id_certificazione: ID della certificazione

    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()

            # Ottieni i dettagli della certificazione e del cavo
            c.execute("""
                SELECT cert.*, cavo.stato_installazione, cavo.responsabile_posa,
                       cavo.ubicazione_partenza, cavo.ubicazione_arrivo, cavo.tipologia,
                       cavo.n_conduttori, cavo.sezione,
                       s.nome as strumento_nome, s.marca as strumento_marca, s.modello as strumento_modello,
                       s.numero_serie as strumento_numero_serie
                FROM CertificazioniCavi cert
                JOIN Cavi cavo ON cert.id_cavo = cavo.id_cavo AND cert.id_cantiere = cavo.id_cantiere
                LEFT JOIN StrumentiCertificati s ON cert.id_strumento = s.id_strumento AND cert.id_cantiere = s.id_cantiere
                WHERE cert.id_certificazione = %s AND cert.id_cantiere = %s
            """, (id_certificazione, id_cantiere))

            certificazione = c.fetchone()

            if not certificazione:
                print(f"\n❌ Certificazione con ID {id_certificazione} non trovata!")
                return False

            print("\n" + "=" * 80)
            print(f"DETTAGLI CERTIFICAZIONE: {certificazione['numero_certificato']}")
            print("=" * 80)

            # Informazioni sul cavo
            print("\n[INFORMAZIONI CAVO]")
            print(f"ID Cavo: {certificazione['id_cavo']}")
            print(f"Tipologia: {certificazione['tipologia']} {certificazione['n_conduttori']}x{certificazione['sezione']}")
            print(f"Ubicazione Partenza: {certificazione['ubicazione_partenza']}")
            print(f"Ubicazione Arrivo: {certificazione['ubicazione_arrivo']}")
            print(f"Stato Installazione: {certificazione['stato_installazione']}")
            print(f"Responsabile Posa: {certificazione['responsabile_posa']}")

            # Informazioni sulla certificazione
            print("\n[INFORMAZIONI CERTIFICAZIONE]")
            print(f"Numero Certificato: {certificazione['numero_certificato']}")
            print(f"Data Certificazione: {certificazione['data_certificazione']}")
            print(f"Operatore: {certificazione['id_operatore']}")
            # Visualizza i dettagli dello strumento
            if certificazione['strumento_nome']:
                print(f"Strumento Utilizzato: {certificazione['strumento_nome']} {certificazione['strumento_marca']} {certificazione['strumento_modello']} (S/N: {certificazione['strumento_numero_serie']})")
            else:
                print(f"Strumento Utilizzato: {certificazione['strumento_utilizzato']}")
            print(f"Lunghezza Misurata: {certificazione['lunghezza_misurata']} m")

            # Risultati dei test
            print("\n[RISULTATI TEST]")
            print(f"Test Continuità: {certificazione['valore_continuita']}")
            print(f"Test Isolamento: {certificazione['valore_isolamento']} MΩ")
            print(f"Test Resistenza: {certificazione['valore_resistenza']}")

            # Note
            if certificazione['note']:
                print(f"\n[NOTE]\n{certificazione['note']}")

            # Percorsi file
            if certificazione['percorso_certificato']:
                print(f"\nPDF Certificato: {certificazione['percorso_certificato']}")
            if certificazione['percorso_foto']:
                print(f"Foto: {certificazione['percorso_foto']}")

            return True

    except Exception as e:
        logging.error(f"Errore durante la visualizzazione dei dettagli della certificazione: {str(e)}")
        print(f"\n❌ Errore: {str(e)}")
        return False


def genera_pdf_certificazione(id_cantiere: int, id_certificazione: int) -> Tuple[bool, Optional[str]]:
    """
    Genera un PDF per la certificazione.

    Args:
        id_cantiere: ID del cantiere
        id_certificazione: ID della certificazione

    Returns:
        Tuple[bool, Optional[str]]: (Successo, Percorso del file PDF)
    """
    try:
        # Verifica se ReportLab è disponibile
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.lib import colors
            from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
            from reportlab.lib.styles import getSampleStyleSheet
        except ImportError:
            logging.warning("ReportLab non disponibile. Impossibile generare PDF.")
            print("\n⚠️ ReportLab non disponibile. Impossibile generare PDF.")
            print("Installa ReportLab con: pip install reportlab")
            return False, None

        with database_connection() as conn:
            c = conn.cursor()

            # Ottieni i dettagli della certificazione
            c.execute("""
                SELECT cert.*, cavo.stato_installazione, cavo.responsabile_posa,
                       cavo.ubicazione_partenza, cavo.ubicazione_arrivo, cavo.tipologia,
                       cavo.n_conduttori, cavo.sezione, cant.nome as nome_cantiere,
                       s.nome as strumento_nome, s.marca as strumento_marca, s.modello as strumento_modello,
                       s.numero_serie as strumento_numero_serie
                FROM CertificazioniCavi cert
                JOIN Cavi cavo ON cert.id_cavo = cavo.id_cavo AND cert.id_cantiere = cavo.id_cantiere
                JOIN Cantieri cant ON cert.id_cantiere = cant.id_cantiere
                LEFT JOIN StrumentiCertificati s ON cert.id_strumento = s.id_strumento AND cert.id_cantiere = s.id_cantiere
                WHERE cert.id_certificazione = %s AND cert.id_cantiere = %s
            """, (id_certificazione, id_cantiere))

            certificazione = c.fetchone()
            if not certificazione:
                logging.error(f"Certificazione {id_certificazione} non trovata")
                print(f"\n❌ Certificazione {id_certificazione} non trovata")
                return False, None

            # Crea la directory per i PDF se non esiste
            pdf_dir = os.path.join("certificati", str(id_cantiere))
            os.makedirs(pdf_dir, exist_ok=True)

            # Percorso del file PDF
            pdf_filename = f"certificato_{certificazione['numero_certificato']}_{certificazione['id_cavo']}.pdf"
            pdf_path = os.path.join(pdf_dir, pdf_filename)

            # Crea il documento PDF
            doc = SimpleDocTemplate(pdf_path, pagesize=A4)
            styles = getSampleStyleSheet()
            elements = []

            # Titolo
            elements.append(Paragraph(f"CERTIFICAZIONE CAVO", styles['Title']))
            elements.append(Spacer(1, 12))

            # Informazioni generali
            elements.append(Paragraph(f"Cantiere: {certificazione['nome_cantiere']}", styles['Heading2']))
            elements.append(Paragraph(f"Certificato N°: {certificazione['numero_certificato']}", styles['Normal']))
            elements.append(Paragraph(f"Data: {certificazione['data_certificazione']}", styles['Normal']))
            elements.append(Paragraph(f"Operatore: {certificazione['id_operatore']}", styles['Normal']))
            elements.append(Spacer(1, 12))

            # Informazioni sul cavo
            elements.append(Paragraph("INFORMAZIONI CAVO", styles['Heading2']))
            elements.append(Spacer(1, 6))

            data = [
                ["ID Cavo", certificazione['id_cavo']],
                ["Tipologia", f"{certificazione['tipologia']} {certificazione['n_conduttori']}x{certificazione['sezione']}"],
                ["Ubicazione Partenza", certificazione['ubicazione_partenza']],
                ["Ubicazione Arrivo", certificazione['ubicazione_arrivo']],
                ["Lunghezza Misurata", str(certificazione['lunghezza_misurata'])],
                ["Responsabile Posa", certificazione['responsabile_posa']]
            ]

            t = Table(data, colWidths=[150, 300])
            t.setStyle(TableStyle([
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('PADDING', (0, 0), (-1, -1), 6)
            ]))
            elements.append(t)
            elements.append(Spacer(1, 12))

            # Risultati test
            elements.append(Paragraph("RISULTATI TEST", styles['Heading2']))
            elements.append(Spacer(1, 6))

            data = [["Tipo Test", "Valore", "Risultato"]]
            data.append(["Continuità", certificazione['valore_continuita'], "PASSATO"])
            data.append(["Isolamento", f"{certificazione['valore_isolamento']} MΩ", "PASSATO"])
            data.append(["Resistenza", certificazione['valore_resistenza'], "PASSATO"])

            t = Table(data, colWidths=[150, 150, 150])
            t.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('PADDING', (0, 0), (-1, -1), 6)
            ]))
            elements.append(t)
            elements.append(Spacer(1, 12))

            # Strumento utilizzato
            elements.append(Paragraph("STRUMENTO UTILIZZATO", styles['Heading2']))
            elements.append(Spacer(1, 6))

            # Visualizza i dettagli dello strumento
            if certificazione['strumento_nome']:
                strumento_text = f"{certificazione['strumento_nome']} {certificazione['strumento_marca']} {certificazione['strumento_modello']}"
                elements.append(Paragraph(strumento_text, styles['Normal']))
                elements.append(Paragraph(f"Numero di serie: {certificazione['strumento_numero_serie']}", styles['Normal']))
            else:
                elements.append(Paragraph(certificazione['strumento_utilizzato'], styles['Normal']))

            elements.append(Spacer(1, 12))

            # Note
            if certificazione['note']:
                elements.append(Paragraph("NOTE", styles['Heading2']))
                elements.append(Spacer(1, 6))
                elements.append(Paragraph(certificazione['note'], styles['Normal']))
                elements.append(Spacer(1, 12))

            # Firma
            elements.append(Paragraph("FIRMA OPERATORE", styles['Heading2']))
            elements.append(Spacer(1, 30))  # Spazio per la firma
            elements.append(Paragraph("_______________________________", styles['Normal']))
            elements.append(Paragraph(certificazione['id_operatore'], styles['Normal']))

            # Genera il PDF
            doc.build(elements)

            # Aggiorna il percorso del certificato nel database
            c.execute("""
                UPDATE CertificazioniCavi
                SET percorso_certificato = ?
                WHERE id_certificazione = ? AND id_cantiere = ?
            """, (pdf_path, id_certificazione, id_cantiere))

            print(f"\n✅ PDF generato con successo: {pdf_path}")
            return True, pdf_path

    except Exception as e:
        logging.error(f"Errore durante la generazione del PDF: {str(e)}")
        print(f"\n❌ Errore durante la generazione del PDF: {str(e)}")
        return False, None


def elimina_certificazione(id_cantiere: int) -> bool:
    """
    Elimina una certificazione dal database.

    Args:
        id_cantiere: ID del cantiere

    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        print("\n🚮 ELIMINAZIONE CERTIFICAZIONE CAVO")

        # Visualizza le certificazioni disponibili
        visualizza_certificazioni(id_cantiere)

        # Richiedi l'ID della certificazione da eliminare
        id_certificazione = input("\nInserisci l'ID della certificazione da eliminare: ").strip()
        if not id_certificazione.isdigit():
            print("❌ ID certificazione non valido!")
            return False

        # Chiedi conferma
        print("\n⚠️ ATTENZIONE: Questa operazione è irreversibile!")
        conferma = input("Sei sicuro di voler eliminare questa certificazione? (s/n): ").lower()
        if not conferma.startswith('s'):
            print("Operazione annullata.")
            return False

        # Elimina la certificazione direttamente usando la connessione al database
        with database_connection() as conn:
            c = conn.cursor()

            # Verifica che la certificazione esista
            c.execute("""
                SELECT id_certificazione, percorso_certificato
                FROM CertificazioniCavi
                WHERE id_certificazione = ? AND id_cantiere = ?
            """, (id_certificazione, id_cantiere))

            certificazione = c.fetchone()
            if not certificazione:
                print(f"\n❌ Certificazione con ID {id_certificazione} non trovata!")
                return False

            # Elimina la certificazione
            c.execute("""
                DELETE FROM CertificazioniCavi
                WHERE id_certificazione = ? AND id_cantiere = ?
            """, (id_certificazione, id_cantiere))

            # Se esiste un file PDF associato, elimina anche quello
            percorso_certificato = certificazione['percorso_certificato']
            if percorso_certificato and os.path.exists(percorso_certificato):
                try:
                    os.remove(percorso_certificato)
                    print(f"\n✅ File PDF eliminato: {percorso_certificato}")
                except Exception as e:
                    print(f"\n⚠️ Impossibile eliminare il file PDF: {str(e)}")

            print(f"\n✅ Certificazione eliminata con successo!")
            return True

    except Exception as e:
        logging.error(f"Errore durante l'eliminazione della certificazione: {str(e)}")
        print(f"\n❌ Errore: {str(e)}")
        return False

def menu_gestione_certificazioni(id_cantiere: int) -> None:
    """
    Menu principale per la gestione delle certificazioni dei cavi.

    Args:
        id_cantiere: ID del cantiere
    """
    while True:
        print("\n" + "=" * 60)
        print("📋 GESTIONE CERTIFICAZIONI CAVI")
        print("=" * 60)
        print("\nOpzioni disponibili:")
        print(" 1. Visualizza tutte le certificazioni")
        print(" 2. Filtra certificazioni per cavo")
        print(" 3. Crea nuova certificazione")
        print(" 4. Dettagli certificazione")
        print(" 5. Genera PDF certificazione")
        print(" 6. Elimina certificazione")
        print(" 7. Gestione strumenti certificati")
        print(" 8. Torna al menu precedente")

        scelta = input("\nSeleziona un'opzione (1-8): ").strip()

        if scelta == "1":
            visualizza_certificazioni(id_cantiere)
        elif scelta == "2":
            filtro_cavo = input("Inserisci l'ID del cavo da filtrare: ").strip().upper()
            visualizza_certificazioni(id_cantiere, filtro_cavo=filtro_cavo)
        elif scelta == "3":
            crea_certificazione(id_cantiere)
        elif scelta == "4":
            visualizza_certificazioni(id_cantiere)
            id_certificazione = input("\nInserisci l'ID della certificazione da visualizzare: ").strip()
            if id_certificazione.isdigit():
                dettagli_certificazione(id_cantiere, int(id_certificazione))
            else:
                print("❌ ID certificazione non valido!")
        elif scelta == "5":
            visualizza_certificazioni(id_cantiere)
            id_certificazione = input("\nInserisci l'ID della certificazione per cui generare il PDF: ").strip()
            if id_certificazione.isdigit():
                genera_pdf_certificazione(id_cantiere, int(id_certificazione))
            else:
                print("❌ ID certificazione non valido!")
        elif scelta == "6":
            elimina_certificazione(id_cantiere)
        elif scelta == "7":
            from modules.strumenti_certificati import menu_gestione_strumenti
            menu_gestione_strumenti(id_cantiere)
        elif scelta == "8":
            break
        else:
            print("❌ Opzione non valida!")


def migra_certificati_vecchi(id_cantiere: int) -> bool:
    """
    Migra i certificati dal vecchio sistema (CertificatiTest + TestCavi) al nuovo (CertificazioniCavi).
    Questa funzione è utile per la transizione al nuovo sistema.

    Args:
        id_cantiere: ID del cantiere

    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        print("\n🔄 MIGRAZIONE CERTIFICATI VECCHI")

        with database_connection() as conn:
            c = conn.cursor()

            # Ottieni tutti i certificati vecchi
            c.execute("""
                SELECT cert.*,
                       (SELECT valore_misurato FROM TestCavi
                        WHERE id_certificato = cert.id_certificato AND tipo_test = 'Continuità' LIMIT 1) as valore_continuita,
                       (SELECT valore_misurato FROM TestCavi
                        WHERE id_certificato = cert.id_certificato AND tipo_test = 'Isolamento' LIMIT 1) as valore_isolamento,
                       (SELECT valore_misurato FROM TestCavi
                        WHERE id_certificato = cert.id_certificato AND tipo_test = 'Resistenza' LIMIT 1) as valore_resistenza
                FROM CertificatiTest cert
                WHERE cert.id_cantiere = ?
            """, (id_cantiere,))

            certificati_vecchi = c.fetchall()

            if not certificati_vecchi:
                print("Nessun certificato vecchio trovato da migrare.")
                return True

            print(f"Trovati {len(certificati_vecchi)} certificati da migrare.")

            # Migra ogni certificato
            for cert in certificati_vecchi:
                # Verifica se esiste già una certificazione per questo cavo
                c.execute("""
                    SELECT id_certificazione FROM CertificazioniCavi
                    WHERE id_cantiere = ? AND id_cavo = ? AND numero_certificato = ?
                """, (id_cantiere, cert['id_cavo'], cert['numero_certificato']))

                if c.fetchone():
                    print(f"Certificato {cert['numero_certificato']} per cavo {cert['id_cavo']} già migrato, salto...")
                    continue

                # Inserisci il nuovo certificato
                c.execute("""
                    INSERT INTO CertificazioniCavi (
                        id_cantiere, id_cavo, numero_certificato, data_certificazione, id_operatore,
                        strumento_utilizzato, lunghezza_misurata, valore_continuita, valore_isolamento,
                        valore_resistenza, percorso_certificato, percorso_foto, note, timestamp_creazione, timestamp_modifica
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    cert['id_cantiere'],
                    cert['id_cavo'],
                    cert['numero_certificato'],
                    cert['data_certificato'],
                    cert['id_operatore'],
                    cert['strumento_utilizzato'],
                    cert['lunghezza_misurata'],
                    cert['valore_continuita'] or "OK",
                    cert['valore_isolamento'] or "500",
                    cert['valore_resistenza'] or "OK",
                    cert['percorso_certificato'],
                    cert['percorso_foto'],
                    cert['note'],
                    cert['timestamp_creazione'],
                    cert['timestamp_modifica'] or datetime.now().isoformat()
                ))

                print(f"✅ Migrato certificato {cert['numero_certificato']} per cavo {cert['id_cavo']}")

            print(f"\n✅ Migrazione completata con successo!")
            return True

    except Exception as e:
        logging.error(f"Errore durante la migrazione dei certificati: {str(e)}")
        print(f"\n❌ Errore: {str(e)}")
        return False


if __name__ == "__main__":
    # Test del modulo
    print("Modulo di gestione certificazioni cavi")
    print("Eseguire questo file direttamente per testare le funzionalità")
