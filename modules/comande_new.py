import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, date
from typing import List, Dict, Any, Optional, Tuple
from contextlib import contextmanager

# Configura il logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

# Configurazione del database
DB_HOST = 'localhost'
DB_PORT = '5432'
DB_NAME = 'cantieri'
DB_USER = 'postgres'
DB_PASSWORD = 'Taranto'

@contextmanager
def database_connection():
    """Context manager per gestire connessioni al database in modo sicuro."""
    conn = None
    try:
        # Connessione a PostgreSQL
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        # Impostazione del cursor factory per avere un comportamento simile a sqlite3.Row
        conn.cursor_factory = RealDictCursor
        yield conn
    except psycopg2.Error as e:
        logging.error(f"❌ Errore database: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()

def genera_codice_comanda(id_cantiere: int, tipo_comanda: str) -> str:
    """
    Genera un codice univoco per una comanda.
    
    Args:
        id_cantiere: ID del cantiere
        tipo_comanda: Tipo di comanda ('POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO')
        
    Returns:
        str: Codice univoco generato
    """
    # Genera un prefisso in base al tipo di comanda
    prefisso = ""
    if tipo_comanda == "POSA":
        prefisso = "POS"
    elif tipo_comanda == "COLLEGAMENTO_PARTENZA":
        prefisso = "CPT"
    elif tipo_comanda == "COLLEGAMENTO_ARRIVO":
        prefisso = "CAR"
    else:
        prefisso = "COM"
    
    # Genera un timestamp per rendere il codice univoco
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    
    # Crea il codice comanda
    codice_comanda = f"{prefisso}_{id_cantiere}_{timestamp}"
    
    return codice_comanda

def crea_comanda(id_cantiere: int, tipo_comanda: str, descrizione: str, 
                responsabile: str, data_scadenza: Optional[date] = None) -> Optional[str]:
    """
    Crea una nuova comanda e restituisce il codice univoco generato.
    
    Args:
        id_cantiere: ID del cantiere
        tipo_comanda: Tipo di comanda ('POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO')
        descrizione: Descrizione della comanda
        responsabile: Nome del responsabile
        data_scadenza: Data di scadenza (opzionale)
        
    Returns:
        Optional[str]: Il codice univoco generato o None in caso di errore
    """
    try:
        # Genera un codice univoco
        codice_comanda = genera_codice_comanda(id_cantiere, tipo_comanda)
        
        # Inserisci la comanda nel database
        with database_connection() as conn:
            c = conn.cursor()
            c.execute("""
                INSERT INTO Comande (
                    codice_comanda, tipo_comanda, descrizione, data_creazione, 
                    data_scadenza, responsabile, stato, id_cantiere
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                codice_comanda, tipo_comanda, descrizione, datetime.now().date(),
                data_scadenza, responsabile, "CREATA", id_cantiere
            ))
        
        logging.info(f"✅ Creata comanda con codice: {codice_comanda}")
        return codice_comanda
    
    except Exception as e:
        logging.error(f"❌ Errore nella creazione della comanda: {str(e)}")
        return None

def assegna_cavi_a_comanda(codice_comanda: str, lista_id_cavi: List[str]) -> bool:
    """
    Assegna una lista di cavi a una comanda.
    
    Args:
        codice_comanda: Codice univoco della comanda
        lista_id_cavi: Lista di ID dei cavi da assegnare
        
    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()
            
            # Ottieni il tipo di comanda e l'ID del cantiere
            c.execute("SELECT tipo_comanda, id_cantiere FROM Comande WHERE codice_comanda = %s", (codice_comanda,))
            result = c.fetchone()
            if not result:
                logging.error(f"❌ Comanda non trovata: {codice_comanda}")
                return False
                
            tipo_comanda = result['tipo_comanda']
            id_cantiere = result['id_cantiere']
            
            # Aggiorna i cavi in base al tipo di comanda
            cavi_assegnati = 0
            for id_cavo in lista_id_cavi:
                if tipo_comanda == "POSA":
                    # Verifica che il cavo non sia già assegnato a una comanda di posa
                    c.execute("SELECT comanda_posa FROM Cavi WHERE id_cavo = %s AND id_cantiere = %s", 
                             (id_cavo, id_cantiere))
                    result = c.fetchone()
                    if result and result['comanda_posa']:
                        logging.warning(f"⚠️ Il cavo {id_cavo} è già assegnato alla comanda di posa {result['comanda_posa']}")
                        continue
                        
                    c.execute("UPDATE Cavi SET comanda_posa = %s WHERE id_cavo = %s AND id_cantiere = %s", 
                             (codice_comanda, id_cavo, id_cantiere))
                    cavi_assegnati += 1
                
                elif tipo_comanda == "COLLEGAMENTO_PARTENZA":
                    # Verifica che il cavo non sia già assegnato a una comanda di collegamento partenza
                    c.execute("SELECT comanda_partenza FROM Cavi WHERE id_cavo = %s AND id_cantiere = %s", 
                             (id_cavo, id_cantiere))
                    result = c.fetchone()
                    if result and result['comanda_partenza']:
                        logging.warning(f"⚠️ Il cavo {id_cavo} è già assegnato alla comanda di collegamento partenza {result['comanda_partenza']}")
                        continue
                        
                    c.execute("UPDATE Cavi SET comanda_partenza = %s WHERE id_cavo = %s AND id_cantiere = %s", 
                             (codice_comanda, id_cavo, id_cantiere))
                    cavi_assegnati += 1
                
                elif tipo_comanda == "COLLEGAMENTO_ARRIVO":
                    # Verifica che il cavo non sia già assegnato a una comanda di collegamento arrivo
                    c.execute("SELECT comanda_arrivo FROM Cavi WHERE id_cavo = %s AND id_cantiere = %s", 
                             (id_cavo, id_cantiere))
                    result = c.fetchone()
                    if result and result['comanda_arrivo']:
                        logging.warning(f"⚠️ Il cavo {id_cavo} è già assegnato alla comanda di collegamento arrivo {result['comanda_arrivo']}")
                        continue
                        
                    c.execute("UPDATE Cavi SET comanda_arrivo = %s WHERE id_cavo = %s AND id_cantiere = %s", 
                             (codice_comanda, id_cavo, id_cantiere))
                    cavi_assegnati += 1
            
            # Aggiorna lo stato della comanda solo se sono stati assegnati cavi
            if cavi_assegnati > 0:
                c.execute("UPDATE Comande SET stato = 'ASSEGNATA' WHERE codice_comanda = %s", (codice_comanda,))
                logging.info(f"✅ Assegnati {cavi_assegnati} cavi alla comanda {codice_comanda}")
                return True
            else:
                logging.warning(f"⚠️ Nessun cavo assegnato alla comanda {codice_comanda}")
                return False
                
    except Exception as e:
        logging.error(f"❌ Errore nell'assegnazione dei cavi alla comanda: {str(e)}")
        return False

def ottieni_cavi_comanda(codice_comanda: str) -> List[Dict[str, Any]]:
    """
    Ottiene la lista dei cavi assegnati a una comanda.
    
    Args:
        codice_comanda: Codice univoco della comanda
        
    Returns:
        List[Dict[str, Any]]: Lista di dizionari con i dati dei cavi
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()
            
            # Ottieni il tipo di comanda e l'ID del cantiere
            c.execute("SELECT tipo_comanda, id_cantiere FROM Comande WHERE codice_comanda = %s", (codice_comanda,))
            result = c.fetchone()
            if not result:
                logging.error(f"❌ Comanda non trovata: {codice_comanda}")
                return []
                
            tipo_comanda = result['tipo_comanda']
            id_cantiere = result['id_cantiere']
            
            # Query in base al tipo di comanda
            if tipo_comanda == "POSA":
                c.execute("""
                    SELECT * FROM Cavi 
                    WHERE comanda_posa = %s AND id_cantiere = %s
                """, (codice_comanda, id_cantiere))
            elif tipo_comanda == "COLLEGAMENTO_PARTENZA":
                c.execute("""
                    SELECT * FROM Cavi 
                    WHERE comanda_partenza = %s AND id_cantiere = %s
                """, (codice_comanda, id_cantiere))
            elif tipo_comanda == "COLLEGAMENTO_ARRIVO":
                c.execute("""
                    SELECT * FROM Cavi 
                    WHERE comanda_arrivo = %s AND id_cantiere = %s
                """, (codice_comanda, id_cantiere))
            
            return [dict(row) for row in c.fetchall()]
            
    except Exception as e:
        logging.error(f"❌ Errore nel recupero dei cavi della comanda: {str(e)}")
        return []

def aggiorna_dati_posa(codice_comanda: str, dati_posa: Dict[str, Dict[str, Any]]) -> bool:
    """
    Aggiorna i dati di posa per i cavi di una comanda.
    
    Args:
        codice_comanda: Codice univoco della comanda
        dati_posa: Dizionario con id_cavo come chiave e dati di posa come valore
        
    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()
            
            # Verifica che sia una comanda di posa
            c.execute("SELECT tipo_comanda, id_cantiere FROM Comande WHERE codice_comanda = %s", (codice_comanda,))
            result = c.fetchone()
            if not result or result['tipo_comanda'] != "POSA":
                logging.error(f"❌ La comanda {codice_comanda} non è una comanda di posa")
                return False
            
            id_cantiere = result['id_cantiere']
            
            # Aggiorna i dati di posa per ogni cavo
            cavi_aggiornati = 0
            for id_cavo, dati in dati_posa.items():
                c.execute("""
                    UPDATE Cavi 
                    SET metratura_reale = %s, 
                        data_posa = %s, 
                        stato_installazione = 'Installato',
                        responsabile_posa = %s
                    WHERE id_cavo = %s AND id_cantiere = %s AND comanda_posa = %s
                """, (
                    dati.get('metratura_reale', 0), 
                    dati.get('data_posa', datetime.now().date()),
                    dati.get('responsabile_posa', ''),
                    id_cavo, id_cantiere, codice_comanda
                ))
                
                if c.rowcount > 0:
                    cavi_aggiornati += 1
            
            # Aggiorna lo stato della comanda solo se sono stati aggiornati cavi
            if cavi_aggiornati > 0:
                c.execute("UPDATE Comande SET stato = 'COMPLETATA' WHERE codice_comanda = %s", (codice_comanda,))
                logging.info(f"✅ Aggiornati dati di posa per {cavi_aggiornati} cavi della comanda {codice_comanda}")
                return True
            else:
                logging.warning(f"⚠️ Nessun cavo aggiornato per la comanda {codice_comanda}")
                return False
                
    except Exception as e:
        logging.error(f"❌ Errore nell'aggiornamento dei dati di posa: {str(e)}")
        return False

def aggiorna_dati_collegamento(codice_comanda: str, dati_collegamento: Dict[str, Dict[str, Any]]) -> bool:
    """
    Aggiorna i dati di collegamento per i cavi di una comanda.
    
    Args:
        codice_comanda: Codice univoco della comanda
        dati_collegamento: Dizionario con id_cavo come chiave e dati di collegamento come valore
        
    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()
            
            # Verifica che sia una comanda di collegamento
            c.execute("SELECT tipo_comanda, id_cantiere FROM Comande WHERE codice_comanda = %s", (codice_comanda,))
            result = c.fetchone()
            if not result:
                logging.error(f"❌ Comanda non trovata: {codice_comanda}")
                return False
            
            tipo_comanda = result['tipo_comanda']
            id_cantiere = result['id_cantiere']
            
            if not (tipo_comanda == "COLLEGAMENTO_PARTENZA" or tipo_comanda == "COLLEGAMENTO_ARRIVO"):
                logging.error(f"❌ La comanda {codice_comanda} non è una comanda di collegamento")
                return False
            
            # Aggiorna i dati di collegamento per ogni cavo
            cavi_aggiornati = 0
            for id_cavo, dati in dati_collegamento.items():
                if tipo_comanda == "COLLEGAMENTO_PARTENZA":
                    # Aggiorna il flag collegamenti per indicare che il lato partenza è collegato
                    c.execute("""
                        UPDATE Cavi 
                        SET collegamenti = CASE
                                WHEN collegamenti = 0 THEN 1  -- Nessun collegamento -> Partenza
                                WHEN collegamenti = 2 THEN 3  -- Arrivo -> Entrambi
                                ELSE collegamenti
                            END,
                            responsabile_partenza = %s
                        WHERE id_cavo = %s AND id_cantiere = %s AND comanda_partenza = %s
                    """, (
                        dati.get('responsabile', ''),
                        id_cavo, id_cantiere, codice_comanda
                    ))
                else:  # COLLEGAMENTO_ARRIVO
                    # Aggiorna il flag collegamenti per indicare che il lato arrivo è collegato
                    c.execute("""
                        UPDATE Cavi 
                        SET collegamenti = CASE
                                WHEN collegamenti = 0 THEN 2  -- Nessun collegamento -> Arrivo
                                WHEN collegamenti = 1 THEN 3  -- Partenza -> Entrambi
                                ELSE collegamenti
                            END,
                            responsabile_arrivo = %s
                        WHERE id_cavo = %s AND id_cantiere = %s AND comanda_arrivo = %s
                    """, (
                        dati.get('responsabile', ''),
                        id_cavo, id_cantiere, codice_comanda
                    ))
                
                if c.rowcount > 0:
                    cavi_aggiornati += 1
            
            # Aggiorna lo stato della comanda solo se sono stati aggiornati cavi
            if cavi_aggiornati > 0:
                c.execute("UPDATE Comande SET stato = 'COMPLETATA' WHERE codice_comanda = %s", (codice_comanda,))
                logging.info(f"✅ Aggiornati dati di collegamento per {cavi_aggiornati} cavi della comanda {codice_comanda}")
                return True
            else:
                logging.warning(f"⚠️ Nessun cavo aggiornato per la comanda {codice_comanda}")
                return False
                
    except Exception as e:
        logging.error(f"❌ Errore nell'aggiornamento dei dati di collegamento: {str(e)}")
        return False

def ottieni_comande_cantiere(id_cantiere: int, stato: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Ottiene la lista delle comande di un cantiere, opzionalmente filtrate per stato.
    
    Args:
        id_cantiere: ID del cantiere
        stato: Stato delle comande da filtrare (opzionale)
        
    Returns:
        List[Dict[str, Any]]: Lista di dizionari con i dati delle comande
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()
            
            # Query con o senza filtro per stato
            if stato:
                c.execute("""
                    SELECT * FROM Comande 
                    WHERE id_cantiere = %s AND stato = %s
                    ORDER BY data_creazione DESC
                """, (id_cantiere, stato))
            else:
                c.execute("""
                    SELECT * FROM Comande 
                    WHERE id_cantiere = %s
                    ORDER BY data_creazione DESC
                """, (id_cantiere,))
            
            return [dict(row) for row in c.fetchall()]
            
    except Exception as e:
        logging.error(f"❌ Errore nel recupero delle comande: {str(e)}")
        return []

def ottieni_dettagli_comanda(codice_comanda: str) -> Optional[Dict[str, Any]]:
    """
    Ottiene i dettagli di una comanda.
    
    Args:
        codice_comanda: Codice univoco della comanda
        
    Returns:
        Optional[Dict[str, Any]]: Dizionario con i dettagli della comanda o None se non trovata
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()
            
            c.execute("SELECT * FROM Comande WHERE codice_comanda = %s", (codice_comanda,))
            result = c.fetchone()
            
            if not result:
                return None
                
            return dict(result)
            
    except Exception as e:
        logging.error(f"❌ Errore nel recupero dei dettagli della comanda: {str(e)}")
        return None

def elimina_comanda(codice_comanda: str) -> bool:
    """
    Elimina una comanda e rimuove i riferimenti dai cavi.
    
    Args:
        codice_comanda: Codice univoco della comanda
        
    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()
            
            # Ottieni il tipo di comanda e l'ID del cantiere
            c.execute("SELECT tipo_comanda, id_cantiere FROM Comande WHERE codice_comanda = %s", (codice_comanda,))
            result = c.fetchone()
            if not result:
                logging.error(f"❌ Comanda non trovata: {codice_comanda}")
                return False
                
            tipo_comanda = result['tipo_comanda']
            id_cantiere = result['id_cantiere']
            
            # Rimuovi i riferimenti dai cavi in base al tipo di comanda
            if tipo_comanda == "POSA":
                c.execute("UPDATE Cavi SET comanda_posa = NULL WHERE comanda_posa = %s AND id_cantiere = %s", 
                         (codice_comanda, id_cantiere))
            elif tipo_comanda == "COLLEGAMENTO_PARTENZA":
                c.execute("UPDATE Cavi SET comanda_partenza = NULL WHERE comanda_partenza = %s AND id_cantiere = %s", 
                         (codice_comanda, id_cantiere))
            elif tipo_comanda == "COLLEGAMENTO_ARRIVO":
                c.execute("UPDATE Cavi SET comanda_arrivo = NULL WHERE comanda_arrivo = %s AND id_cantiere = %s", 
                         (codice_comanda, id_cantiere))
            
            # Elimina la comanda
            c.execute("DELETE FROM Comande WHERE codice_comanda = %s", (codice_comanda,))
            
            logging.info(f"✅ Eliminata comanda {codice_comanda}")
            return True
            
    except Exception as e:
        logging.error(f"❌ Errore nell'eliminazione della comanda: {str(e)}")
        return False

def cambia_stato_comanda(codice_comanda: str, nuovo_stato: str) -> bool:
    """
    Cambia lo stato di una comanda.
    
    Args:
        codice_comanda: Codice univoco della comanda
        nuovo_stato: Nuovo stato della comanda
        
    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()
            
            c.execute("UPDATE Comande SET stato = %s WHERE codice_comanda = %s", (nuovo_stato, codice_comanda))
            
            if c.rowcount > 0:
                logging.info(f"✅ Stato della comanda {codice_comanda} cambiato in {nuovo_stato}")
                return True
            else:
                logging.warning(f"⚠️ Comanda non trovata: {codice_comanda}")
                return False
                
    except Exception as e:
        logging.error(f"❌ Errore nel cambio di stato della comanda: {str(e)}")
        return False