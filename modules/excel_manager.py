# Standard library
import os
import re
import psycopg2
import logging
from datetime import datetime
from contextlib import contextmanager

# Third-party
import pandas as pd
import xlrd
import openpyxl
from openpyxl import load_workbook, Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.comments import Comment
from openpyxl.formatting.rule import CellIsRule

# Moduli interni
from modules.cavi import _marca_cavo_come_spare
from modules.utils import safe_get, StatoInstallazione, ValidazioneCampi
from .database_pg import database_connection, Database

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class Config:
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    MAX_ROWS = 10000
    BACKUP_DIR = 'backups'
    EXPORT_DIR = 'exports'
    # Configurazione del database PostgreSQL
    DB_NAME = 'cantieri'

# Il context manager database_connection è ora importato dal modulo database


# La funzione safe_get è ora importata da utils.py


def is_file_safe(filepath):
    """Verifica rapida ma efficace: esiste? È un file Excel valido? Non è corrotto?"""
    try:
        # 1. Esiste ed è un file normale (no cartelle/symlink)
        if not os.path.isfile(filepath):
            logging.error(f"❌ Percorso non valido o non è un file: {filepath}")
            return False

        # 2. Estensione consentita (.xls, .xlsx)
        if not filepath.lower().endswith(('.xls', '.xlsx')):
            logging.error(f"❌ Formato non supportato (usa .xls o .xlsx): {filepath}")
            return False

        # 3. Dimensione ragionevole
        if os.path.getsize(filepath) > Config.MAX_FILE_SIZE:
            logging.error(f"❌ File troppo grande (max {Config.MAX_FILE_SIZE//(1024*1024)}MB): {filepath}")
            return False

        # 4. Anteprima lettura (verifica che non sia corrotto)
        try:
            # Verifica più approfondita del formato del file
            with open(filepath, 'rb') as f:
                header = f.read(8)  # Legge abbastanza per entrambi i formati

                if filepath.lower().endswith('.xlsx'):
                    # Verifica signature ZIP di .xlsx
                    if header[:4] != b'PK\x03\x04':
                        raise ValueError("Non è un .xlsx valido (signature non corretta)")

                    # Verifica ulteriore per .xlsx (prova a leggere la struttura ZIP)
                    try:
                        import zipfile
                        with zipfile.ZipFile(filepath) as zf:
                            # Verifica la presenza di file essenziali in un .xlsx
                            required_files = ['[Content_Types].xml', 'xl/workbook.xml']
                            for req_file in required_files:
                                if req_file not in zf.namelist():
                                    raise ValueError(f"File .xlsx corrotto: manca {req_file}")
                    except zipfile.BadZipFile:
                        raise ValueError("File .xlsx corrotto: struttura ZIP non valida")

                elif filepath.lower().endswith('.xls'):
                    # Verifica signature di Excel vecchio formato
                    if header != b'\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1':
                        raise ValueError("Non è un .xls valido (signature non corretta)")

                    # Verifica ulteriore per .xls (prova a leggere con xlrd)
                    try:
                        import xlrd
                        # Apre solo l'intestazione per verificare la struttura
                        xlrd.open_workbook(filepath, on_demand=True).release_resources()
                    except xlrd.XLRDError as e:
                        raise ValueError(f"File .xls corrotto: {str(e)}")
        except Exception as e:
            logging.error(f"❌ File corrotto o non Excel: {str(e)}")
            return False

        return True  # Tutto ok!

    except Exception as e:
        logging.error(f"❌ Errore generico durante i controlli: {str(e)}")
        return False

def esporta_dati_excel(dati, nome_foglio, prefisso_file, id_cantiere=None, colonne_personalizzate=None, formatta_timestamp=True):
    """
    Funzione generica per esportare dati in Excel

    Args:
        dati (list): Lista di dizionari con i dati da esportare
        nome_foglio (str): Nome del foglio Excel
        prefisso_file (str): Prefisso per il nome del file
        id_cantiere (int, optional): ID del cantiere
        colonne_personalizzate (list, optional): Lista di colonne da includere
        formatta_timestamp (bool, optional): Se True, formatta la colonna timestamp

    Returns:
        str: Percorso del file esportato, False in caso di errore
    """
    try:
        # Verifica che ci siano dati da esportare
        if not dati:
            logging.warning(f"❌ Nessun dato da esportare per {prefisso_file}!")
            return False

        # Converti i dati in DataFrame
        df = pd.DataFrame(dati)

        if df.empty:
            logging.warning(f"❌ Nessun dato da esportare per {prefisso_file}!")
            return False

        # Filtra colonne se specificate
        if colonne_personalizzate:
            df = df[[col for col in colonne_personalizzate if col in df.columns]]

        # Formatta la colonna timestamp se presente e richiesto
        if formatta_timestamp and "timestamp" in df.columns:
            df["timestamp"] = pd.to_datetime(df["timestamp"])
            df["timestamp"] = df["timestamp"].dt.strftime('%Y-%m-%d %H:%M:%S')
            # Rinomina per chiarezza nel file Excel
            df.rename(columns={"timestamp": "Ultimo Aggiornamento"}, inplace=True)

        # Ottieni il nome del cantiere se necessario
        nome_cantiere = ""
        if id_cantiere:
            nome_cantiere = ottieni_nome_cantiere(id_cantiere) or f"cantiere_{id_cantiere}"

        # Genera nome file con timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        nome_file = f"{prefisso_file}_{nome_cantiere}_{timestamp}.xlsx" if nome_cantiere else f"{prefisso_file}_{timestamp}.xlsx"

        # Crea cartella 'exports' se non esiste
        os.makedirs(Config.EXPORT_DIR, exist_ok=True)
        percorso_completo = os.path.join(Config.EXPORT_DIR, nome_file)

        # Salva il file
        df.to_excel(percorso_completo, index=False, sheet_name=nome_foglio, engine='openpyxl')

        # Verifica che il file sia stato creato
        if os.path.exists(percorso_completo) and os.path.getsize(percorso_completo) > 0:
            logging.info(f"✅ File Excel esportato con successo: {percorso_completo}")
            return percorso_completo
        else:
            logging.error("❌ Errore: il file non è stato creato correttamente")
            return False

    except PermissionError:
        logging.error("❌ Errore: impossibile salvare il file. Potrebbe essere aperto in un altro programma.")
        return False
    except Exception as e:
        logging.error(f"❌ Errore durante l'esportazione: {str(e)}", exc_info=True)
        return False


def esporta_cavi_excel(id_cantiere):
    """Esporta la lista dei cavi attivi in un file Excel."""
    try:
        with database_connection() as (conn, c):
            # Query con nomi di colonne standardizzati
            c.execute('''
                SELECT
                    id_cavo,
                    sistema,
                    utility,
                    colore_cavo,
                    tipologia,
                    n_conduttori,
                    sezione as formazione,
                    sh,
                    ubicazione_partenza,
                    utenza_partenza,
                    descrizione_utenza_partenza,
                    ubicazione_arrivo,
                    utenza_arrivo,
                    descrizione_utenza_arrivo,
                    metri_teorici,
                    metratura_reale,
                    responsabile_posa,
                    stato_installazione,
                    CASE
                        WHEN modificato_manualmente = 1 THEN 'Modificato'
                        WHEN modificato_manualmente = 2 THEN 'Mantenuto da rev. precedente'
                        ELSE 'No'
                    END as modificato,
                    timestamp,
                    id_bobina
                FROM Cavi
                WHERE id_cantiere = %s
            ''', (id_cantiere,))

            cavi = [dict(row) for row in c.fetchall()]

            # Usa la funzione generica per esportare i dati
            return esporta_dati_excel(
                dati=cavi,
                nome_foglio='Cavi',
                prefisso_file='cavi',
                id_cantiere=id_cantiere
            )

    except Exception as e:
        logging.error(f"❌ Errore durante l'esportazione dei cavi: {str(e)}", exc_info=True)
        return False


def crea_backup_automatico(id_cantiere):
    """
    Crea un backup automatico dei cavi del cantiere in formato Excel

    Args:
        id_cantiere (int): ID del cantiere

    Returns:
        str: Percorso del backup creato, None se cantiere non esiste, False in caso di errore
    """
    try:
        # Verifica che il cantiere esista
        nome_cantiere = ottieni_nome_cantiere(id_cantiere)
        if not nome_cantiere:
            logging.error(f"❌ Cantiere ID {id_cantiere} non trovato")
            return None

        # Crea cartella backups se non esiste
        os.makedirs('backups', exist_ok=True)

        # Genera nome file univoco
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        nome_file = f"backup_{nome_cantiere}_{timestamp}.xlsx"
        percorso_backup = os.path.join('backups', nome_file)

        # Recupera dati e crea backup
        with database_connection(dict_cursor=True) as (conn, c):
            c.execute('''SELECT
                        id_cavo, sistema, utility, colore_cavo, tipologia, n_conduttori, sezione, sh,
                        ubicazione_partenza, utenza_partenza, descrizione_utenza_partenza,
                        ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                        metri_teorici, metratura_reale, responsabile_posa, stato_installazione,
                        modificato_manualmente, timestamp, id_bobina,
                        revisione_ufficiale
                    FROM Cavi WHERE id_cantiere = %s''', (id_cantiere,))

            cavi = [dict(row) for row in c.fetchall()]
            if not cavi:
                logging.warning(f"⚠️ Nessun dato da backuppare per cantiere {nome_cantiere}")
                return None

            # Crea DataFrame e aggiungi metadati
            df = pd.DataFrame(cavi)
            df['_backup_timestamp'] = timestamp
            df['_cantiere_id'] = id_cantiere
            df['_cantiere_nome'] = nome_cantiere

            # Salva il backup
            df.to_excel(percorso_backup, index=False, sheet_name='Backup Cavi', engine='openpyxl')

            if os.path.exists(percorso_backup):
                logging.info(f"✅ Backup creato per cantiere {nome_cantiere}: {percorso_backup}")
                return percorso_backup

            logging.error(f"❌ File di backup non creato per cantiere {nome_cantiere}")
            return False

    except psycopg2.Error as e:
        logging.error(f"❌ Errore database PostgreSQL durante il backup: {str(e)}")
        return False
    except Exception as e:
        logging.error(f"❌ Errore durante il backup: {str(e)}")
        return False


def ottieni_nome_cantiere(id_cantiere):
    """
    Ottiene il nome del cantiere dal database con gestione errori migliorata

    Args:
        id_cantiere (int): ID del cantiere

    Returns:
        str: Nome del cantiere, None se non trovato o in caso di errore
    """
    try:
        with database_connection(dict_cursor=True) as (conn, c):
            c.execute('''SELECT nome FROM Cantieri
                       WHERE id_cantiere = %s''', (id_cantiere,))
            risultato = c.fetchone()
            return risultato['nome'] if risultato else None

    except psycopg2.Error as e:
        logging.error(f"❌ Errore database durante il recupero nome cantiere: {str(e)}")
        return None
    except Exception as e:
        logging.error(f"❌ Errore durante il recupero nome cantiere: {str(e)}")
        return None



def importa_cavi_da_excel(id_cantiere, percorso_file, revisione_predefinita=None, non_interattivo=False):
    """
    Importa cavi da file Excel nel database con:
    - Backup automatico
    - Validazione semplificata
    - Gestione valori mancanti
    - Generazione report dettagliato

    Args:
        id_cantiere (int): ID del cantiere (>0)
        percorso_file (str): Percorso del file Excel (.xls o .xlsx)

    Returns:
        bool: True se l'importazione ha successo, False altrimenti
    """
    try:
        if not os.path.isfile(percorso_file):
            raise FileNotFoundError(f"File non trovato: {percorso_file}")

        # === 2. PREPARAZIONE ===
        nome_cantiere = ottieni_nome_cantiere(id_cantiere)
        if not nome_cantiere:
            # In fase di test, usa un nome predefinito invece di interrompere l'esecuzione
            nome_cantiere = f"TEST_CANTIERE_{id_cantiere}"
            logging.warning(f"⚠️ Cantiere ID {id_cantiere} non trovato nel database. Usando nome temporaneo: {nome_cantiere}")

        # === 3. BACKUP AUTOMATICO ===
        backup_path = crea_backup_automatico(id_cantiere)
        if backup_path is None:
            logging.warning("⚠️ Nessun dato da backuppare (possibile primo import)")
        elif backup_path is False:
            logging.warning("⚠️ Errore durante la creazione del backup, ma si procede comunque con l'importazione")

        # === 4. LETTURA E VALIDAZIONE FILE ===
        df_originale = leggi_file_excel(percorso_file)
        if df_originale is None:
            return False

        df_validato = valida_colonne_excel(df_originale)
        if df_validato is None:
            return False

        # === 5. RICHIESTA REVISIONE ===
        revisione = richiedi_revisione(default_revision=revisione_predefinita, non_interattivo=non_interattivo)
        if not revisione:
            logging.error("❌ Operazione annullata: codice revisione mancante")
            return False

        # === 6. ELABORAZIONE DATI ===
        cavi_non_in_revisione = trova_cavi_non_in_revisione(id_cantiere, df_validato)
        batch_dati = elabora_cavi_da_excel(id_cantiere, df_validato, revisione, cavi_non_in_revisione)

        if not batch_dati:
            logging.error("❌ Nessun dato valido da importare")
            return False

        # === 7. IMPORT NEL DATABASE ===
        if not inserisci_cavi_nel_database(id_cantiere, revisione, batch_dati):
            logging.error("❌ Errore durante il salvataggio nel database")
            return False

        # === 8. GENERAZIONE REPORT ===
        report_path = genera_report_importazione(
            df_originale=df_originale,
            df_importati=pd.DataFrame([d[:17] for d in batch_dati], columns=[
                'id_cavo', 'id_cantiere', 'revisione_ufficiale', 'utility', 'tipologia',
                'n_conduttori', 'sezione', 'ubicazione_partenza', 'utenza_partenza', 'ubicazione_arrivo',
                'utenza_arrivo', 'metri_teorici', 'metratura_reale', 'stato_installazione',
                'modificato_manualmente', 'timestamp', 'id_bobina'
            ]),
            id_cantiere=id_cantiere,
            nome_cantiere=nome_cantiere,
            revisione=revisione
        )

        logging.info(f"""
==========================================
✅ IMPORTAZIONE COMPLETATA CON SUCCESSO
• Cantiere: {nome_cantiere} (ID: {id_cantiere})
• Revisione: {revisione}
• Cavi importati: {len(batch_dati)}
• Report dettagliato: {report_path}
==========================================
""")
        return True

    except Exception as e:
        logging.error(f"""
==========================================
❌ IMPORTAZIONE FALLITA
Errore: {str(e)}
==========================================
""", exc_info=True)
        return False


def pulisci_dataframe(df):
    """Pulisce un DataFrame rimuovendo righe vuote e inutili.

    Args:
        df (DataFrame): Il DataFrame da pulire

    Returns:
        DataFrame: Il DataFrame pulito o None se il DataFrame è None o vuoto
    """
    if df is None or df.empty:
        return df

    # Rimuovi le righe completamente vuote
    df = df.dropna(how='all')

    # Rimuovi le righe dove tutte le colonne tranne id_cavo sono vuote
    if 'id_cavo' in df.columns:
        cols_to_check = [col for col in df.columns if col != 'id_cavo']
        if cols_to_check:  # Verifica che ci siano altre colonne oltre id_cavo
            # Rimuovi le righe dove tutte le colonne tranne id_cavo sono vuote
            mask = df[cols_to_check].isna().all(axis=1)
            df = df[~mask]

    # Calcola quante righe sono state rimosse
    righe_rimosse = len(df.index) - len(df)
    if righe_rimosse > 0:
        logging.info(f"Rimosse {righe_rimosse} righe vuote dal file Excel")

    return df


def leggi_file_excel(percorso_file):
    """Legge file Excel con protezioni di sicurezza unificate.
    Supporta sia il formato .xls che .xlsx con un'unica funzione.

    Args:
        percorso_file (str): Percorso del file Excel da leggere

    Returns:
        DataFrame: Il DataFrame con i dati del file Excel o None in caso di errore
    """
    try:
        # Verifica sicurezza del file (include controlli di esistenza, dimensione, formato)
        if not is_file_safe(percorso_file):
            return None

        # Selezione reader in base all'estensione
        if percorso_file.lower().endswith('.xls'):
            # Lettura con xlrd per file .xls
            with xlrd.open_workbook(percorso_file) as wb:
                sheet = wb.sheet_by_index(0)
                data = []
                for row_idx in range(min(sheet.nrows, Config.MAX_ROWS)):
                    data.append(sheet.row_values(row_idx))
        else:  # .xlsx
            # Lettura con openpyxl per file .xlsx
            wb = load_workbook(
                filename=percorso_file,
                read_only=True,
                data_only=True,
                keep_links=False,
                rich_text=False
            )
            sheet = wb.active
            data = []
            for row in sheet.iter_rows(values_only=True):
                if len(data) >= Config.MAX_ROWS:
                    logging.warning(f"Superato limite righe ({Config.MAX_ROWS}), troncamento")
                    break
                data.append(row)

        # Crea DataFrame e pulisci le righe vuote
        if not data:
            logging.error("❌ Nessun dato trovato nel file Excel")
            return None

        # Determina se la prima riga è un titolo e non le intestazioni delle colonne
        is_title_row = False
        if len(data) >= 2:
            # Verifica se la prima riga ha meno celle non vuote della seconda
            # o se contiene una stringa che sembra un titolo
            first_row_non_empty = sum(1 for cell in data[0] if cell and str(cell).strip())
            second_row_non_empty = sum(1 for cell in data[1] if cell and str(cell).strip())

            # Controlla se la prima riga contiene parole chiave tipiche di un titolo
            first_row_text = ' '.join(str(cell) for cell in data[0] if cell)
            title_keywords = ['test', 'generati', 'automaticamente', 'file di', 'template']

            if (first_row_non_empty < second_row_non_empty or
                any(keyword in first_row_text.lower() for keyword in title_keywords)):
                is_title_row = True
                logging.info(f"Rilevata riga di titolo: '{first_row_text[:50]}...'")

        # Se la prima riga è un titolo, usa la seconda come intestazioni
        if is_title_row and len(data) >= 2:
            df = pd.DataFrame(data[2:], columns=data[1])
            logging.info("Utilizzata la seconda riga come intestazioni delle colonne")
        else:
            df = pd.DataFrame(data[1:], columns=data[0])

        return pulisci_dataframe(df)

    except Exception as e:
        logging.error(f"❌ Errore durante la lettura del file Excel: {str(e)}", exc_info=True)
        return None


def valida_colonne_excel(df, auto_generate_missing_ids=True):
    """Versione avanzata con gestione valori mancanti e ID automatici"""
    if df.empty:
        logging.error("❌ Il file Excel è vuoto")
        return None

    # 1. Standardizza nomi colonne
    df.columns = df.columns.str.lower().str.replace(' ', '_')

    # 1.1 Gestisci il campo formazione (ora campo principale, sezione è legacy)
    if 'sezione' in df.columns and 'formazione' not in df.columns:
        logging.info("✅ Rilevata colonna 'sezione' (legacy), mappata a 'formazione'")
        df['formazione'] = df['sezione']
        # Mantieni anche sezione per compatibilità database
        df['sezione'] = df['formazione']
    elif 'formazione' in df.columns:
        logging.info("✅ Colonna 'formazione' presente")
        # Assicurati che sezione sia sincronizzata con formazione
        df['sezione'] = df['formazione']
    else:
        logging.info("✅ Campo 'formazione' non presente - sarà impostato a TBD")
        df['formazione'] = 'TBD'
        df['sezione'] = 'TBD'

    # 1.2 Gestisci n_conduttori e SH come campi spare (non più utilizzati)
    if 'n_conduttori' not in df.columns:
        logging.info("✅ Campo 'n_conduttori' non presente - impostato come campo spare")
        df['n_conduttori'] = '0'  # Valore di default per campo spare
    else:
        logging.info("✅ Campo 'n_conduttori' presente ma trattato come campo spare")

    if 'sh' not in df.columns:
        logging.info("✅ Campo 'SH' non presente - impostato come campo spare")
        df['sh'] = 'N'  # Valore di default per campo spare
    else:
        logging.info("✅ Campo 'SH' presente ma trattato come campo spare")

    # 2. Controllo colonne minime (n_conduttori e SH rimossi come non più obbligatori)
    colonne_obbligatorie = ['id_cavo', 'utility', 'tipologia', 'metri_teorici']
    for col in colonne_obbligatorie:
        if col not in df.columns:
            logging.error(f"❌ Colonna obbligatoria mancante: '{col}'")
            return None

    # 3. Gestione valori mancanti e controllo ID cavi
    df['id_cavo'] = df['id_cavo'].astype(str).str.strip()

    # Genera ID automatici per quelli mancanti se richiesto
    mask_id_mancante = df['id_cavo'].isna() | (df['id_cavo'] == '')
    if mask_id_mancante.any():
        if auto_generate_missing_ids:
            base_id = f"AUTO_{datetime.now().strftime('%Y%m%d')}_"
            df.loc[mask_id_mancante, 'id_cavo'] = [
                f"{base_id}{i:03d}" for i in range(1, mask_id_mancante.sum() + 1)
            ]
            logging.info(f"✅ Assegnati {mask_id_mancante.sum()} ID automatici")
        else:
            logging.error("❌ Trovati ID cavo mancanti e generazione automatica disabilitata")
            return None

    # 4. Gestione valori mancanti con valori di default
    default_values = {
        'sistema': 'TBD',           # Aggiunto sistema con valore predefinito TBD
        'utility': 'TBD',
        'colore_cavo': 'TBD',       # Aggiunto colore_cavo con valore predefinito TBD
        'tipologia': 'TBD',
        'n_conduttori': '0',         # Default a 0 per numero conduttori (ora come stringa)
        'formazione': 'TBD',        # Campo principale per formazione
        'sezione': 'TBD',           # Campo legacy sincronizzato con formazione
        'sh': 'N',                  # Valore predefinito per sh (lowercase)
        'ubicazione_partenza': 'TBD',
        'utenza_partenza': 'TBD',
        'descrizione_utenza_partenza': 'TBD',  # Aggiunto con valore predefinito TBD
        'ubicazione_arrivo': 'TBD',
        'utenza_arrivo': 'TBD',
        'descrizione_utenza_arrivo': 'TBD',    # Aggiunto con valore predefinito TBD
        'metri_teorici': 0.0,
        'stato_installazione': StatoInstallazione.DA_INSTALLARE.value,
        'id_bobina': '',            # Lascia vuoto come richiesto
        'collegamenti': 0           # Imposta collegamenti a 0 come richiesto
    }

    for col, default in default_values.items():
        if col in df.columns:
            if col == 'metri_teorici':
                # Converti esplicitamente a float
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(default).astype(float)
            else:
                # Gestisci tutti gli altri campi come stringhe
                df[col] = df[col].fillna(default).astype(str)
        else:
            df[col] = default

    # Conta solo le righe che hanno almeno un valore non nullo in una colonna obbligatoria
    # (n_conduttori rimosso dalla validazione in quanto campo spare)
    righe_con_dati = df.dropna(subset=['utility', 'tipologia', 'metri_teorici'], how='all')
    logging.info(f"✅ Dati validati: {len(righe_con_dati)} righe con dati effettivi pronte per l'elaborazione")
    return df


def richiedi_revisione(allow_empty=False, default_revision=None, max_tentativi=3, non_interattivo=False):
    """
    Richiede il codice revisione con opzioni flessibili e limite di tentativi

    Args:
        allow_empty (bool): Se True, permette revisione vuota
        default_revision (str): Valore di default se allow_empty=True e non viene inserito nulla
        max_tentativi (int): Numero massimo di tentativi prima di fallire
        non_interattivo (bool): Se True, usa direttamente default_revision senza input

    Returns:
        str: Codice revisione o None
    """
    # Modalità non interattiva
    if non_interattivo:
        if default_revision is not None:
            logging.info(f"Usando revisione di default: {default_revision}")
            return default_revision
        elif allow_empty:
            logging.info("Nessuna revisione specificata, consentito")
            return default_revision
        else:
            logging.error("Modalità non interattiva richiede default_revision valido")
            return None

    # Modalità interattiva con limite tentativi
    tentativi = 0
    while tentativi < max_tentativi:
        tentativi += 1
        try:
            revisione = input(f"Inserisci il codice identificativo della revisione: ").strip()

            if not revisione:
                if allow_empty:
                    return default_revision
                print("Errore: devi inserire un codice revisione")
                continue

            # Accetta qualsiasi formato di revisione
            return revisione

        except KeyboardInterrupt:
            logging.warning("Operazione annullata dall'utente")
            return None
        except Exception as e:
            logging.error(f"Errore durante l'inserimento: {str(e)}")
            return None

    logging.error(f"Superato limite di {max_tentativi} tentativi per inserimento revisione")
    return None

def trova_cavi_non_in_revisione(id_cantiere, df_excel):
    """Trova cavi nel database che non sono presenti nel file Excel"""
    cavi_non_in_revisione = []

    try:
        with database_connection(dict_cursor=True) as (conn, c):
            # Ottieni tutti i cavi attivi nel database
            c.execute('''
                SELECT
                    id_cavo, id_cantiere, sistema, utility, colore_cavo, tipologia, n_conduttori,
                    sezione, sh, ubicazione_partenza, utenza_partenza, descrizione_utenza_partenza,
                    ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                    metri_teorici, metratura_reale, responsabile_posa, stato_installazione, id_bobina
                FROM Cavi
                WHERE id_cantiere = %s
            ''', (id_cantiere,))

            cavi_db = [dict(row) for row in c.fetchall()]

            # Converti id_cavo in Excel in un set per ricerca veloce
            id_cavi_excel = set(df_excel['id_cavo'].astype(str))

            # Trova cavi nel DB che non sono nel file Excel
            for cavo in cavi_db:
                if str(cavo['id_cavo']) not in id_cavi_excel:
                    cavi_non_in_revisione.append(cavo)

            return cavi_non_in_revisione
    except Exception as e:
        logging.error(f"Errore nella ricerca dei cavi non in revisione: {str(e)}")
        return []


def elabora_cavi_da_excel(id_cantiere, df, revisione, cavi_non_in_revisione):
    """Elabora i cavi da un file Excel per l'importazione nel database.

    Nota: Se il file Excel contiene una colonna 'formazione' invece di 'sezione',
    la funzione valida_colonne_excel si occupa di mappare 'formazione' a 'sezione'."""
    batch_dati = []
    report = {
        'total': 0,
        'new': 0,
        'updated': 0,
        'spare': 0,
        'reintegrated': 0,  # Contatore per i cavi SPARE reintegrati
        'conflicts': [],
        'physical_conflicts': [],  # Lista per registrare conflitti di caratteristiche fisiche nei cavi installati
        'non_installed_changes': []  # Lista per registrare variazioni nei cavi non installati
    }

    try:
        with database_connection(dict_cursor=True) as (conn, c):
            # 1. Processa i cavi dal file Excel
            for _, row in df.iterrows():
                id_cavo = str(row['id_cavo']).strip()

                # Verifica se il cavo esiste già
                c.execute('''SELECT * FROM Cavi
                          WHERE id_cantiere = %s AND id_cavo = %s
                          LIMIT 1''', (id_cantiere, id_cavo))
                cavo_esistente = c.fetchone()

                # Prepara i dati - mantieni valori esistenti per campi non specificati
                if cavo_esistente:
                    # Verifica se il cavo era marcato come SPARE (modificato_manualmente = 3)
                    if cavo_esistente['modificato_manualmente'] == 3:
                        # Reintegra il cavo SPARE che riappare nella nuova revisione
                        logging.info(f"Reintegrazione cavo SPARE: {id_cavo} (stato: {cavo_esistente['stato_installazione']}, metri: {cavo_esistente['metratura_reale']})")
                        report['reintegrated'] += 1  # Incrementa il contatore dei cavi reintegrati
                        cavo_data = (
                            id_cavo,
                            id_cantiere,
                            revisione,
                            str(row.get('sistema', cavo_esistente.get('sistema', ''))),
                            str(row.get('utility', cavo_esistente['utility'])),
                            str(row.get('colore_cavo', cavo_esistente.get('colore_cavo', ''))),
                            str(row.get('tipologia', cavo_esistente['tipologia'])),
                            str(row.get('n_conduttori', cavo_esistente['n_conduttori'])),  # Tratta n_conduttori come stringa
                            str(row.get('sezione', cavo_esistente['sezione'])),
                            str(row.get('sh', cavo_esistente.get('sh', 'N'))),
                            str(row.get('ubicazione_partenza', cavo_esistente.get('ubicazione_partenza', ''))),
                            str(row.get('utenza_partenza', cavo_esistente['utenza_partenza'])),
                            str(row.get('descrizione_utenza_partenza', cavo_esistente.get('descrizione_utenza_partenza', ''))),
                            str(row.get('ubicazione_arrivo', cavo_esistente.get('ubicazione_arrivo', ''))),
                            str(row.get('utenza_arrivo', cavo_esistente['utenza_arrivo'])),
                            str(row.get('descrizione_utenza_arrivo', cavo_esistente.get('descrizione_utenza_arrivo', ''))),
                            float(row.get('metri_teorici', cavo_esistente['metri_teorici'])),
                            cavo_esistente['metratura_reale'],
                            str(row.get('responsabile_posa', cavo_esistente.get('responsabile_posa', ''))),
                            cavo_esistente.get('id_bobina', ''),  # Mantieni il riferimento alla bobina
                            cavo_esistente['stato_installazione'],  # Mantieni lo stato di installazione originale
                            0,  # Reimposta modificato_manualmente a 0 (normale)
                            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            cavo_esistente.get('collegamenti', 0)  # Mantieni il valore di collegamenti o imposta a 0 se non esiste
                        )
                    # Determina se il cavo è stato modificato manualmente in precedenza
                    elif cavo_esistente['modificato_manualmente'] == 2:
                        # Funzione di accesso sicuro per PostgreSQL dict cursor
                        def safe_get(row_obj, key, default=''):
                            try:
                                return row_obj[key] if key in row_obj.keys() else default
                            except (KeyError, TypeError, AttributeError):
                                return default

                        # Mantieni tutti i valori esistenti se era marcato come "mantenuto"
                        cavo_data = (
                            id_cavo,
                            id_cantiere,
                            revisione,
                            safe_get(cavo_esistente, 'sistema', ''),
                            cavo_esistente['utility'],
                            safe_get(cavo_esistente, 'colore_cavo', ''),
                            cavo_esistente['tipologia'],
                            str(cavo_esistente['n_conduttori']),  # Tratta n_conduttori come stringa
                            str(cavo_esistente['sezione']),       # Tratta sezione come stringa
                            safe_get(cavo_esistente, 'sh', 'N'),
                            safe_get(cavo_esistente, 'ubicazione_partenza', ''),
                            cavo_esistente['utenza_partenza'],
                            safe_get(cavo_esistente, 'descrizione_utenza_partenza', ''),
                            safe_get(cavo_esistente, 'ubicazione_arrivo', ''),
                            cavo_esistente['utenza_arrivo'],
                            safe_get(cavo_esistente, 'descrizione_utenza_arrivo', ''),
                            cavo_esistente['metri_teorici'],
                            cavo_esistente['metratura_reale'],
                            safe_get(cavo_esistente, 'responsabile_posa', ''),
                            safe_get(cavo_esistente, 'id_bobina', ''),
                            cavo_esistente['stato_installazione'],
                            2,  # Mantieni come "mantenuto da rev. precedente"
                            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            safe_get(cavo_esistente, 'collegamenti', 0)  # Mantieni il valore di collegamenti o imposta a 0 se non esiste
                        )
                    else:
                        # Verifica se il cavo è già installato
                        is_installed = cavo_esistente['stato_installazione'] == StatoInstallazione.INSTALLATO.value or cavo_esistente['stato_installazione'] == 'SPARE'

                        # Verifica se ci sono differenze nelle caratteristiche fisiche
                        caratteristiche_fisiche_modificate = False
                        conflitti_fisici = {}
                        variazioni_non_installati = {}

                        # Controlla le caratteristiche fisiche per tutti i cavi
                        for caratteristica, nome_leggibile in [
                            ('n_conduttori', 'Numero conduttori'),
                            ('sezione', 'Sezione'),
                            ('tipologia', 'Tipologia')
                        ]:
                            valore_excel = row.get(caratteristica)
                            if valore_excel is not None and str(valore_excel).strip() != '':
                                if _confronta_valori(valore_excel, cavo_esistente[caratteristica]):
                                    # Per i cavi installati, questo è un conflitto fisico
                                    if is_installed:
                                        caratteristiche_fisiche_modificate = True
                                        conflitti_fisici[nome_leggibile] = {
                                            'vecchio': cavo_esistente[caratteristica],
                                            'nuovo': valore_excel
                                        }
                                    # Per i cavi non installati, è una variazione da registrare
                                    else:
                                        variazioni_non_installati[nome_leggibile] = {
                                            'vecchio': cavo_esistente[caratteristica],
                                            'nuovo': valore_excel
                                        }

                        # Se ci sono conflitti nelle caratteristiche fisiche dei cavi installati, registrali
                        if caratteristiche_fisiche_modificate:
                            report['physical_conflicts'].append({
                                'id_cavo': id_cavo,
                                'stato': cavo_esistente['stato_installazione'],
                                'conflitti': conflitti_fisici
                            })

                        # Se ci sono variazioni nei cavi non installati, registrale
                        if variazioni_non_installati and not is_installed:
                            report['non_installed_changes'].append({
                                'id_cavo': id_cavo,
                                'stato': cavo_esistente['stato_installazione'],
                                'variazioni': variazioni_non_installati
                            })

                        # Per i cavi installati, mantieni sempre le caratteristiche fisiche originali
                        # Per i cavi non installati, accetta le modifiche dalla nuova revisione
                        # Funzione di accesso sicuro per PostgreSQL dict cursor
                        def safe_get(row_obj, key, default=''):
                            try:
                                return row_obj[key] if key in row_obj.keys() else default
                            except (KeyError, TypeError, AttributeError):
                                return default

                        cavo_data = (
                            id_cavo,
                            id_cantiere,
                            revisione,
                            str(row.get('sistema', safe_get(cavo_esistente, 'sistema', ''))),
                            str(row.get('utility', cavo_esistente['utility'])),
                            str(row.get('colore_cavo', safe_get(cavo_esistente, 'colore_cavo', ''))),
                            # Mantieni la tipologia originale se il cavo è installato
                            cavo_esistente['tipologia'] if is_installed else str(row.get('tipologia', cavo_esistente['tipologia'])),
                            # Mantieni il numero di conduttori originale se il cavo è installato
                            # Tratta n_conduttori come stringa
                            str(cavo_esistente['n_conduttori']) if is_installed else str(row.get('n_conduttori', cavo_esistente['n_conduttori'])),
                            # Mantieni la sezione originale se il cavo è installato
                            # Tratta sezione come stringa
                            str(cavo_esistente['sezione']) if is_installed else str(row.get('sezione', cavo_esistente['sezione'])),
                            str(row.get('sh', safe_get(cavo_esistente, 'sh', 'N'))),
                            str(row.get('ubicazione_partenza', safe_get(cavo_esistente, 'ubicazione_partenza', ''))),
                            str(row.get('utenza_partenza', cavo_esistente['utenza_partenza'])),
                            str(row.get('descrizione_utenza_partenza', safe_get(cavo_esistente, 'descrizione_utenza_partenza', ''))),
                            str(row.get('ubicazione_arrivo', safe_get(cavo_esistente, 'ubicazione_arrivo', ''))),
                            str(row.get('utenza_arrivo', cavo_esistente['utenza_arrivo'])),
                            str(row.get('descrizione_utenza_arrivo', safe_get(cavo_esistente, 'descrizione_utenza_arrivo', ''))),
                            float(row.get('metri_teorici', cavo_esistente['metri_teorici'])),
                            cavo_esistente['metratura_reale'],
                            str(row.get('responsabile_posa', safe_get(cavo_esistente, 'responsabile_posa', ''))),
                            # Mantieni sempre il riferimento alla bobina esistente a meno che non sia esplicitamente specificato
                            safe_get(cavo_esistente, 'id_bobina', '') if (row.get('id_bobina') is None or row.get('id_bobina') == '') else str(row.get('id_bobina')),
                            cavo_esistente['stato_installazione'],
                            # Imposta modificato_manualmente a 1 se ci sono modifiche
                            1 if any(_confronta_valori(row.get(k, ''), safe_get(cavo_esistente, k, ''))
                                   for k in ['utility', 'ubicazione_partenza', 'ubicazione_arrivo',
                                            'metri_teorici']) or caratteristiche_fisiche_modificate else 0,
                            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            safe_get(cavo_esistente, 'collegamenti', 0)  # Mantieni il valore di collegamenti o imposta a 0 se non esiste
                        )
                else:
                    # Nuovo cavo - usa valori di default coerenti
                    cavo_data = (
                        id_cavo,
                        id_cantiere,
                        revisione,
                        str(row.get('sistema', 'TBD')),                          # Modificato da '' a 'TBD'
                        str(row.get('utility', 'TBD')),
                        str(row.get('colore_cavo', 'TBD')),                      # Modificato da '' a 'TBD'
                        str(row.get('tipologia', 'TBD')),
                        str(row.get('n_conduttori', '0')),                        # Default a 0 per numero conduttori
                        str(row.get('sezione', 'TBD')),                          # Usa formazione/sezione con TBD come default
                        str(row.get('sh', 'N')),
                        str(row.get('ubicazione_partenza', 'TBD')),
                        str(row.get('utenza_partenza', 'TBD')),
                        str(row.get('descrizione_utenza_partenza', 'TBD')),      # Modificato da '' a 'TBD'
                        str(row.get('ubicazione_arrivo', 'TBD')),
                        str(row.get('utenza_arrivo', 'TBD')),
                        str(row.get('descrizione_utenza_arrivo', 'TBD')),      # Modificato da '' a 'TBD'
                        float(row.get('metri_teorici', 0)),
                        0.0,  # metratura_reale iniziale
                        str(row.get('responsabile_posa', '')),
                        # Assicurati che l'ID bobina non sia None o stringa vuota
                        str(row.get('id_bobina', '')) if row.get('id_bobina') not in [None, ''] else '',
                        StatoInstallazione.DA_INSTALLARE.value,  # stato iniziale = "Da installare" (da StatoInstallazione)
                        0,  # modificato_manualmente
                        datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        0   # collegamenti iniziale a 0
                    )

                batch_dati.append(cavo_data)
                report['total'] += 1

                # Registra se nuovo o aggiornato
                if cavo_esistente is None:
                    report['new'] += 1
                else:
                    report['updated'] += 1
                    # Verifica differenze con versione esistente
                    differenze = confronta_caratteristiche(dict(cavo_esistente), row)
                    if differenze:
                        report['conflicts'].append({
                            'id_cavo': id_cavo,
                            'differenze': differenze,
                            'old_data': dict(cavo_esistente),
                            'new_data': cavo_data
                        })

            # 2. Gestione cavi non in revisione (SPARE)
            id_cavi_importati = {c[0] for c in batch_dati}  # ID già processati
            cavi_da_marcare_spare = []

            for cavo in cavi_non_in_revisione:
                if cavo['id_cavo'] not in id_cavi_importati:
                    # Se il cavo non è già SPARE, lo aggiungiamo alla lista dei cavi da marcare come SPARE
                    # Nota: 'SPARE' è un valore speciale non presente in StatoInstallazione
                    if cavo['stato_installazione'] != 'SPARE' and cavo.get('modificato_manualmente') != 3:
                        cavi_da_marcare_spare.append(cavo)
                    else:
                        # Se è già SPARE, lo aggiungiamo direttamente al batch con i dati esistenti
                        # Funzione di accesso sicuro per PostgreSQL dict cursor
                        def safe_get(row_obj, key, default=''):
                            try:
                                return row_obj[key] if key in row_obj.keys() else default
                            except (KeyError, TypeError, AttributeError):
                                return default

                        spare_data = (
                            cavo['id_cavo'],
                            id_cantiere,
                            revisione,
                            safe_get(cavo, 'sistema', ''),
                            cavo['utility'],
                            safe_get(cavo, 'colore_cavo', ''),
                            cavo['tipologia'],
                            str(cavo['n_conduttori']),  # Tratta n_conduttori come stringa
                            str(cavo['sezione']),       # Tratta sezione come stringa
                            safe_get(cavo, 'sh', 'N'),
                            safe_get(cavo, 'ubicazione_partenza', ''),
                            cavo['utenza_partenza'],
                            safe_get(cavo, 'descrizione_utenza_partenza', ''),
                            safe_get(cavo, 'ubicazione_arrivo', ''),
                            cavo['utenza_arrivo'],
                            safe_get(cavo, 'descrizione_utenza_arrivo', ''),
                            float(cavo['metri_teorici']),
                            float(safe_get(cavo, 'metratura_reale', 0)),
                            safe_get(cavo, 'responsabile_posa', ''),
                            safe_get(cavo, 'id_bobina', ''),
                            cavo['stato_installazione'],  # Mantieni lo stato esistente
                            safe_get(cavo, 'modificato_manualmente', 3),  # Mantieni il flag esistente
                            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            safe_get(cavo, 'collegamenti', 0)  # Mantieni il valore di collegamenti o imposta a 0 se non esiste
                        )
                        batch_dati.append(spare_data)
                        report['spare'] += 1
                        report['total'] += 1

            # Ora marchiamo come SPARE i cavi che non sono presenti nella nuova revisione
            # usando la funzione specifica di cavi.py
            if cavi_da_marcare_spare:
                logging.info(f"Marcando {len(cavi_da_marcare_spare)} cavi come SPARE usando la funzione specifica")
                # Funzione di accesso sicuro per PostgreSQL dict cursor
                def safe_get(row_obj, key, default=''):
                    try:
                        return row_obj[key] if key in row_obj.keys() else default
                    except (KeyError, TypeError, AttributeError):
                        return default

                for cavo in cavi_da_marcare_spare:
                    try:
                        # Usa la funzione specifica per marcare il cavo come SPARE
                        _marca_cavo_come_spare(
                            conn,
                            cavo['id_cavo'],
                            id_cantiere,
                            cavo['stato_installazione'],
                            float(safe_get(cavo, 'metratura_reale', 0))
                        )



                        # Aggiungi il cavo al batch con lo stato SPARE e flag modificato_manualmente = 3
                        spare_data = (
                            cavo['id_cavo'],
                            id_cantiere,
                            revisione,
                            safe_get(cavo, 'sistema', ''),
                            cavo['utility'],
                            safe_get(cavo, 'colore_cavo', ''),
                            cavo['tipologia'],
                            str(cavo['n_conduttori']),  # Tratta n_conduttori come stringa
                            str(cavo['sezione']),       # Tratta sezione come stringa
                            safe_get(cavo, 'sh', 'N'),
                            safe_get(cavo, 'ubicazione_partenza', ''),
                            cavo['utenza_partenza'],
                            safe_get(cavo, 'descrizione_utenza_partenza', ''),
                            safe_get(cavo, 'ubicazione_arrivo', ''),
                            cavo['utenza_arrivo'],
                            safe_get(cavo, 'descrizione_utenza_arrivo', ''),
                            float(cavo['metri_teorici']),
                            float(safe_get(cavo, 'metratura_reale', 0)),
                            safe_get(cavo, 'responsabile_posa', ''),
                            safe_get(cavo, 'id_bobina', ''),
                            'SPARE',  # Stato SPARE - Nota: questo è un valore speciale non presente in StatoInstallazione
                            3,  # modificato_manualmente = 3 (SPARE)
                            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            safe_get(cavo, 'collegamenti', 0)  # Mantieni il valore di collegamenti o imposta a 0 se non esiste
                        )
                        batch_dati.append(spare_data)
                        report['spare'] += 1
                        report['total'] += 1
                    except Exception as e:
                        logging.error(f"Errore durante la marcatura del cavo {cavo['id_cavo']} come SPARE: {str(e)}")

            # Log di riepilogo dettagliato
            logging.info(f"""
==========================================
📊 REPORT ELABORAZIONE CAVI - REVISIONE {revisione}
• Totali processati: {report['total']}
• Nuovi cavi: {report['new']}
• Cavi aggiornati: {report['updated']}
• Cavi marcati come SPARE: {report['spare']}
• Cavi SPARE reintegrati: {report['reintegrated']}
• Conflitti rilevati: {len(report['conflicts'])}
• Conflitti fisici (cavi installati): {len(report['physical_conflicts'])}
• Variazioni in cavi non installati: {len(report['non_installed_changes'])}
==========================================
""")

            # Se ci sono conflitti fisici o variazioni nei cavi non installati, genera un report specifico
            if report['physical_conflicts'] or report['non_installed_changes']:
                # Ottieni il nome del cantiere dal database
                nome_cantiere_locale = ottieni_nome_cantiere(id_cantiere)
                # Se il nome del cantiere non è disponibile, usa un valore di default
                if nome_cantiere_locale is None:
                    nome_cantiere_locale = f"Cantiere_{id_cantiere}"
                report_path = genera_report_conflitti_fisici(
                    report['physical_conflicts'],
                    report['non_installed_changes'],
                    id_cantiere,
                    nome_cantiere_locale,
                    revisione)

                if report['physical_conflicts']:
                    logging.info(f"\n⚠️ ATTENZIONE: Rilevati {len(report['physical_conflicts'])} conflitti in caratteristiche fisiche di cavi installati.")

                if report['non_installed_changes']:
                    logging.info(f"\n📈 Rilevate {len(report['non_installed_changes'])} variazioni in cavi non installati.")

                logging.info(f"\n📝 Report dettagliato generato in: {report_path}")

            # Log dettagliato dei conflitti
            if report['conflicts']:
                logging.info("DETTAGLIO CONFLITTI:")
                for conflict in report['conflicts']:
                    logging.info(f"\n🔹 Cavo {conflict['id_cavo']}:")
                    for campo, (vecchio, nuovo) in conflict['differenze'].items():
                        logging.info(f"   - {campo}: DA '{vecchio}' A '{nuovo}'")

        return batch_dati

    except Exception as e:
        logging.error(f"Errore durante l'elaborazione dei cavi: {str(e)}", exc_info=True)
        return []


def inserisci_cavi_nel_database(id_cantiere, revisione, batch_dati):
    """Inserisce i cavi nel database con gestione transazionale migliorata."""
    try:
        with database_connection(autocommit=False) as (conn, cursor):
            for cavo_data in batch_dati:
                if len(cavo_data) != 24:  # Aggiornato per includere il campo collegamenti
                    logging.error(f"Dati cavo incompleti: {cavo_data}")
                    continue

                # Converti stringhe vuote in NULL per id_bobina (indice 19)
                cavo_data_fixed = list(cavo_data)
                if cavo_data_fixed[19] == '' or cavo_data_fixed[19] is None:
                    cavo_data_fixed[19] = None
                cavo_data_fixed = tuple(cavo_data_fixed)

                cursor.execute('''INSERT INTO Cavi (id_cavo, id_cantiere, revisione_ufficiale,
                    sistema, utility, colore_cavo, tipologia, n_conduttori, sezione, sh,
                    ubicazione_partenza, utenza_partenza, descrizione_utenza_partenza,
                    ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                    metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                    stato_installazione, modificato_manualmente, timestamp, collegamenti)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (id_cavo, id_cantiere) DO UPDATE SET
                    revisione_ufficiale = EXCLUDED.revisione_ufficiale,
                    sistema = EXCLUDED.sistema,
                    utility = EXCLUDED.utility,
                    colore_cavo = EXCLUDED.colore_cavo,
                    tipologia = EXCLUDED.tipologia,
                    n_conduttori = EXCLUDED.n_conduttori,
                    sezione = EXCLUDED.sezione,
                    sh = EXCLUDED.sh,
                    ubicazione_partenza = EXCLUDED.ubicazione_partenza,
                    utenza_partenza = EXCLUDED.utenza_partenza,
                    descrizione_utenza_partenza = EXCLUDED.descrizione_utenza_partenza,
                    ubicazione_arrivo = EXCLUDED.ubicazione_arrivo,
                    utenza_arrivo = EXCLUDED.utenza_arrivo,
                    descrizione_utenza_arrivo = EXCLUDED.descrizione_utenza_arrivo,
                    metri_teorici = EXCLUDED.metri_teorici,
                    metratura_reale = EXCLUDED.metratura_reale,
                    responsabile_posa = EXCLUDED.responsabile_posa,
                    id_bobina = EXCLUDED.id_bobina,
                    stato_installazione = EXCLUDED.stato_installazione,
                    modificato_manualmente = EXCLUDED.modificato_manualmente,
                    timestamp = EXCLUDED.timestamp,
                    collegamenti = EXCLUDED.collegamenti''', cavo_data_fixed)

            # Il commit viene gestito automaticamente dal context manager
            logging.info(f"Successo inserimento dei cavi")
            return True

    except psycopg2.Error as e:
        logging.error(f"Errore database PostgreSQL durante l'inserimento dei cavi: {str(e)}")
        return False
    except Exception as e:
        logging.error(f"Errore generale durante l'inserimento dei cavi: {str(e)}")
        return False

def confronta_caratteristiche(cavo_db, riga_excel):
    """
    Confronta i dati di un cavo esistente con quelli della nuova revisione.
    Restituisce un dict con le differenze.
    """
    # Funzione di accesso sicuro per PostgreSQL dict cursor
    def safe_get(row_obj, key, default=''):
        try:
            return row_obj[key] if key in row_obj.keys() else default
        except (KeyError, TypeError, AttributeError):
            return default

    # Mappatura campi
    campi_da_confrontare = {
        'sistema': 'Sistema',
        'utility': 'Utility',
        'colore_cavo': 'Colore cavo',
        'tipologia': 'Tipologia',
        'n_conduttori': 'Numero conduttori',  # Ora è un campo TEXT
        'sezione': 'Formazione',             # Rinominato da Sezione a Formazione
        'sh': 'SH',
        'ubicazione_partenza': 'Ubicazione partenza',
        'utenza_partenza': 'Utenza partenza',
        'descrizione_utenza_partenza': 'Descrizione utenza partenza',
        'ubicazione_arrivo': 'Ubicazione arrivo',
        'utenza_arrivo': 'Utenza arrivo',
        'descrizione_utenza_arrivo': 'Descrizione utenza arrivo',
        'metri_teorici': 'Metri teorici',
        'responsabile_posa': 'Responsabile posa',
        'id_bobina': 'Bobina'
    }

    differenze = {}

    for campo_db, nome_campo in campi_da_confrontare.items():
        # Usa l'accesso sicuro per PostgreSQL dict cursor
        val_db = safe_get(cavo_db, campo_db, '')

        # Gestisci il caso speciale per 'sezione' e 'formazione'
        if campo_db == 'sezione' and 'formazione' in riga_excel and 'sezione' not in riga_excel:
            val_excel = riga_excel.get('formazione', '')
        else:
            val_excel = riga_excel.get(campo_db, '')

        # Conversione per confronto numerico
        if campo_db in ['n_conduttori', 'sezione', 'metri_teorici']:
            try:
                val_db = float(val_db)
                val_excel = float(val_excel) if val_excel not in [None, ''] else 0.0
            except (ValueError, TypeError):
                pass

        if str(val_db).strip() != str(val_excel).strip():
            differenze[nome_campo] = (val_db, val_excel)

    return differenze


def genera_report_importazione(df_originale, df_importati, id_cantiere, nome_cantiere, revisione):
    """Crea un report dettagliato in Excel con miglior gestione degli errori."""
    try:
        # Crea struttura base del report
        report_data = []

        # Colonne comuni per l'analisi
        colonne_comuni = list(set(df_originale.columns) & set(df_importati.columns))

        # Cavi scartati (presenti in originale ma non in importati)
        df_scartati = df_originale[~df_originale['id_cavo'].isin(df_importati['id_cavo'])]

        # Dettaglio cavi importati
        for _, row in df_importati.iterrows():
            # Trova la riga corrispondente in modo più sicuro
            matching_rows = df_originale[df_originale['id_cavo'] == row['id_cavo']]
            orig_row = matching_rows.iloc[0] if not matching_rows.empty else None

            modifiche = []
            if orig_row is not None:
                for col in colonne_comuni:
                    if str(row[col]).strip() != str(orig_row[col]).strip():
                        modifiche.append(f"{col}: {orig_row[col]} → {row[col]}")

            report_data.append({
                'id_cavo': row['id_cavo'],
                'stato': 'IMPORTATO',
                'revisione': revisione,
                'modifiche': ' | '.join(modifiche) if modifiche else 'Nessuna modifica',
                'note': ''
            })

        # Dettaglio cavi scartati
        for _, row in df_scartati.iterrows():
            report_data.append({
                'id_cavo': row.get('id_cavo', 'N/D'),
                'stato': 'SCARTATO',
                'revisione': 'N/A',
                'modifiche': '',
                'note': _trova_motivazione_scarto(row)
            })

        # Crea DataFrame del report
        df_report = pd.DataFrame(report_data)

        # Crea nome file univoco
        timestamp = datetime.now().strftime('%Y%m%d_%H%M')
        report_path = os.path.join(Config.EXPORT_DIR, f"report_importazione_{id_cantiere}_{timestamp}.xlsx")
        os.makedirs(Config.EXPORT_DIR, exist_ok=True)

        # Salva il report
        with pd.ExcelWriter(report_path, engine='openpyxl') as writer:
            df_report.to_excel(writer, sheet_name='Riepilogo', index=False)
            df_importati.to_excel(writer, sheet_name='Dettaglio_Importati', index=False)
            if not df_scartati.empty:
                df_scartati[colonne_comuni].to_excel(writer, sheet_name='Cavi_Scartati', index=False)

        return report_path

    except Exception as e:
        logging.error(f"Errore durante la generazione del report: {str(e)}")
        return None

def genera_report_base(dati_principali, nome_foglio_principale, prefisso_file, id_cantiere=None, nome_cantiere=None, info_aggiuntive=None):
    """
    Funzione base per generare report Excel con più fogli

    Args:
        dati_principali (list/DataFrame): Dati da includere nel foglio principale
        nome_foglio_principale (str): Nome del foglio principale
        prefisso_file (str): Prefisso per il nome del file
        id_cantiere (int, optional): ID del cantiere
        nome_cantiere (str, optional): Nome del cantiere (se None, verrà recuperato dall'ID)
        info_aggiuntive (dict, optional): Informazioni aggiuntive con chiavi:
            - fogli_aggiuntivi: dict con nome_foglio -> dati per fogli aggiuntivi
            - revisione: str con codice revisione
            - metadati: list di dict con chiave/valore per foglio metadati

    Returns:
        str: Percorso del report generato, None in caso di errore
    """
    try:
        # Converti in DataFrame se necessario
        if not isinstance(dati_principali, pd.DataFrame):
            df_principale = pd.DataFrame(dati_principali)
        else:
            df_principale = dati_principali

        # Ottieni il nome del cantiere se necessario
        if id_cantiere and not nome_cantiere:
            nome_cantiere = ottieni_nome_cantiere(id_cantiere) or f"cantiere_{id_cantiere}"

        # Genera nome file univoco
        timestamp = datetime.now().strftime('%Y%m%d_%H%M')
        info_extra = ""
        if info_aggiuntive and 'revisione' in info_aggiuntive:
            info_extra = f"_{info_aggiuntive['revisione']}"

        nome_file = f"{prefisso_file}_{nome_cantiere}{info_extra}_{timestamp}.xlsx" if nome_cantiere else f"{prefisso_file}{info_extra}_{timestamp}.xlsx"
        report_path = os.path.join(Config.EXPORT_DIR, nome_file)
        os.makedirs(Config.EXPORT_DIR, exist_ok=True)

        # Salva il report
        with pd.ExcelWriter(report_path, engine='openpyxl') as writer:
            # Foglio principale
            df_principale.to_excel(writer, sheet_name=nome_foglio_principale, index=False)

            # Aggiungi fogli aggiuntivi se presenti
            if info_aggiuntive and 'fogli_aggiuntivi' in info_aggiuntive:
                for nome_foglio, dati in info_aggiuntive['fogli_aggiuntivi'].items():
                    if isinstance(dati, pd.DataFrame):
                        df = dati
                    else:
                        df = pd.DataFrame(dati)

                    if not df.empty:
                        df.to_excel(writer, sheet_name=nome_foglio, index=False)

            # Aggiungi foglio metadati se presenti
            if info_aggiuntive and 'metadati' in info_aggiuntive:
                pd.DataFrame(info_aggiuntive['metadati']).to_excel(writer, sheet_name='Metadati', index=False)
            elif id_cantiere or nome_cantiere:  # Crea metadati di base se non forniti
                metadati = [
                    {'Chiave': 'Cantiere', 'Valore': nome_cantiere or 'N/A'},
                    {'Chiave': 'ID Cantiere', 'Valore': id_cantiere or 'N/A'},
                    {'Chiave': 'Data Report', 'Valore': datetime.now().strftime('%d/%m/%Y %H:%M')},
                    {'Chiave': 'Generato da', 'Valore': 'Sistema CMS'}
                ]
                if info_aggiuntive and 'revisione' in info_aggiuntive:
                    metadati.append({'Chiave': 'Revisione', 'Valore': info_aggiuntive['revisione']})

                pd.DataFrame(metadati).to_excel(writer, sheet_name='Metadati', index=False)

        logging.info(f"✅ Report generato con successo: {report_path}")
        return report_path

    except Exception as e:
        logging.error(f"❌ Errore durante la generazione del report: {str(e)}", exc_info=True)
        return None


def genera_report_conflitti_fisici(conflitti_installati, variazioni_non_installati, id_cantiere, nome_cantiere, revisione):
    """
    Genera un report Excel dettagliato dei conflitti nelle caratteristiche fisiche dei cavi installati
    e delle variazioni nei cavi non installati.

    Args:
        conflitti_installati (list): Lista di dizionari con i dettagli dei conflitti nei cavi installati
        variazioni_non_installati (list): Lista di dizionari con i dettagli delle variazioni nei cavi non installati
        id_cantiere (int): ID del cantiere
        nome_cantiere (str): Nome del cantiere
        revisione (str): Codice revisione

    Returns:
        str: Percorso del file di report generato
    """
    try:
        # Prepara i dati per i conflitti nei cavi installati
        report_data_installati = []
        for conflitto in conflitti_installati:
            id_cavo = conflitto['id_cavo']
            stato = conflitto['stato']
            for nome_caratteristica, valori in conflitto['conflitti'].items():
                report_data_installati.append({
                    'ID Cavo': id_cavo,
                    'Stato': stato,
                    'Caratteristica': nome_caratteristica,
                    'Valore Attuale': valori['vecchio'],
                    'Valore Revisione': valori['nuovo'],
                    'Note': 'Mantenuto valore attuale (cavo installato)'
                })
        df_conflitti = pd.DataFrame(report_data_installati)

        # Prepara i dati per le variazioni nei cavi non installati
        report_data_non_installati = []
        for variazione in variazioni_non_installati:
            id_cavo = variazione['id_cavo']
            stato = variazione['stato']
            for nome_caratteristica, valori in variazione['variazioni'].items():
                report_data_non_installati.append({
                    'ID Cavo': id_cavo,
                    'Stato': stato,
                    'Caratteristica': nome_caratteristica,
                    'Valore Precedente': valori['vecchio'],
                    'Nuovo Valore': valori['nuovo'],
                    'Note': 'Aggiornato al nuovo valore (cavo non installato)'
                })
        df_variazioni = pd.DataFrame(report_data_non_installati)

        # Prepara i dati di istruzioni
        istruzioni = [
            {'Istruzioni': 'REPORT VARIAZIONI CARATTERISTICHE CAVI'},
            {'Istruzioni': ''},
            {'Istruzioni': '1. CONFLITTI NEI CAVI INSTALLATI:'},
            {'Istruzioni': 'Per i cavi già installati (stato: Posato, Terminato, Collegato, Installato), le caratteristiche fisiche'},
            {'Istruzioni': '(numero conduttori, sezione, tipologia) sono state mantenute invariate anche se diverse nella nuova revisione.'},
            {'Istruzioni': 'Se i nuovi valori sono corretti, è necessario aggiornare manualmente i cavi nel sistema.'},
            {'Istruzioni': ''},
            {'Istruzioni': '2. VARIAZIONI NEI CAVI NON INSTALLATI:'},
            {'Istruzioni': 'Per i cavi non ancora installati, le caratteristiche fisiche sono state aggiornate ai nuovi valori.'},
            {'Istruzioni': 'Questa scheda mostra quali caratteristiche sono cambiate rispetto alla revisione precedente.'},
            {'Istruzioni': ''},
            {'Istruzioni': f'Cantiere: {nome_cantiere} (ID: {id_cantiere})'},
            {'Istruzioni': f'Revisione: {revisione}'},
            {'Istruzioni': f'Data generazione: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'},
        ]
        df_istruzioni = pd.DataFrame(istruzioni)

        # Prepara i metadati
        metadati = [
            {'Chiave': 'Cantiere', 'Valore': nome_cantiere},
            {'Chiave': 'ID Cantiere', 'Valore': id_cantiere},
            {'Chiave': 'Revisione', 'Valore': revisione},
            {'Chiave': 'Data Report', 'Valore': datetime.now().strftime('%d/%m/%Y %H:%M')},
            {'Chiave': 'Generato da', 'Valore': 'Sistema CMS'}
        ]

        # Usa la funzione generica per generare il report
        fogli_aggiuntivi = {
            'Istruzioni': df_istruzioni
        }

        if not df_conflitti.empty:
            fogli_aggiuntivi['Conflitti Cavi Installati'] = df_conflitti
        if not df_variazioni.empty:
            fogli_aggiuntivi['Variazioni Cavi Non Installati'] = df_variazioni

        # Crea un riepilogo
        riepilogo_data = [
            {'Tipo': 'Conflitti in cavi installati', 'Conteggio': len(conflitti_installati)},
            {'Tipo': 'Variazioni in cavi non installati', 'Conteggio': len(variazioni_non_installati)},
            {'Tipo': 'Totale variazioni', 'Conteggio': len(conflitti_installati) + len(variazioni_non_installati)}
        ]
        df_riepilogo = pd.DataFrame(riepilogo_data)

        return genera_report_base(
            dati_principali=df_riepilogo,
            nome_foglio_principale='Riepilogo',
            prefisso_file='report_variazioni',
            id_cantiere=id_cantiere,
            nome_cantiere=nome_cantiere,
            info_aggiuntive={
                'fogli_aggiuntivi': fogli_aggiuntivi,
                'revisione': revisione,
                'metadati': metadati
            }
        )

    except Exception as e:
        logging.error(f"❌ Errore durante la generazione del report variazioni: {str(e)}")
        return None


def _trova_modifiche(row):
    """Identifica campi con valori sostituiti"""
    modifiche = []
    for col in row.index:
        if col == 'n_conduttori' and row[col] == 0:
            modifiche.append(f"{col}=0 (default)")
        elif isinstance(row[col], str) and row[col] == 'TBD':
            modifiche.append(f"{col}=TBD")
    return ', '.join(modifiche) if modifiche else 'Nessuna'


def _confronta_valori(val1, val2):
    """Confronta due valori tenendo conto del tipo di dato"""
    # Gestione valori None/NaN
    if val1 is None and val2 is None:
        return False  # Entrambi nulli, quindi uguali
    if val1 is None or val2 is None:
        return True  # Uno solo è nullo, quindi diversi

    # Gestione valori pandas NA/NaN
    try:
        if pd.isna(val1) and pd.isna(val2):
            return False  # Entrambi nulli, quindi uguali
        if pd.isna(val1) or pd.isna(val2):
            return True  # Uno solo è nullo, quindi diversi
    except:
        pass  # Se pd.isna fallisce, continua con i confronti successivi

    # Prova confronto numerico per valori che sembrano numerici
    if isinstance(val1, (int, float)) or isinstance(val2, (int, float)):
        try:
            # Converti entrambi in float per confronto numerico
            num1 = float(val1) if val1 != '' else 0.0
            num2 = float(val2) if val2 != '' else 0.0
            # Confronto con tolleranza per numeri float
            return abs(num1 - num2) > 0.001
        except (ValueError, TypeError):
            # Se la conversione fallisce, confronta come stringhe
            pass

    # Confronto stringhe (default)
    return str(val1).strip() != str(val2).strip()


def _trova_motivazione_scarto(row):
    """Determina perché un cavo è stato scartato"""
    if pd.isna(row.get('id_cavo')) or str(row['id_cavo']).strip() == '':
        return "Mancanza ID cavo"
    # Rimuoviamo il controllo sul numero di conduttori poiché ora è una stringa
    # e può contenere valori diversi da numeri
    elif 'metri_teorici' in row and float(row['metri_teorici']) <= 0:
        return f"Metri teorici non validi: {row['metri_teorici']}"


def esporta_parco_bobine_excel(nome_file=None):
    """
    Esporta il parco bobine in un file Excel.

    Args:
        nome_file (str, optional): Nome del file da creare. Se None, viene generato automaticamente.

    Returns:
        str: Percorso del file creato, o None in caso di errore
    """
    try:
        # Crea cartella exports se non esiste
        os.makedirs(Config.EXPORT_DIR, exist_ok=True)

        # Genera nome file univoco se non specificato
        if nome_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            nome_file = f"parco_bobine_{timestamp}.xlsx"

        # Percorso completo del file
        percorso_file = os.path.join(Config.EXPORT_DIR, nome_file)

        # Usa il context manager per la connessione al database
        with database_connection(dict_cursor=True) as (conn, c):
            # Query per ottenere tutte le bobine
            c.execute('''
                SELECT * FROM parco_cavi
                ORDER BY id_bobina
            ''')
            bobine = c.fetchall()

            # Converti i risultati in un DataFrame
            bobine_data = [dict(bobina) for bobina in bobine]
            df_bobine = pd.DataFrame(bobine_data)

            # Query per ottenere i cavi associati a ciascuna bobina
            c.execute('''
                SELECT id_bobina, COUNT(*) as num_cavi, SUM(metratura_reale) as metri_posati
                FROM cavi
                WHERE id_bobina IS NOT NULL AND id_bobina != ''
                GROUP BY id_bobina
            ''')
            cavi_per_bobina = {row['id_bobina']: {'num_cavi': row['num_cavi'], 'metri_posati': row['metri_posati']} for row in c.fetchall()}

        # Aggiungi colonne per il numero di cavi e metri posati
        df_bobine['Numero Cavi'] = df_bobine['id_bobina'].apply(lambda x: cavi_per_bobina.get(x, {}).get('num_cavi', 0))
        df_bobine['Metri Posati'] = df_bobine['id_bobina'].apply(lambda x: cavi_per_bobina.get(x, {}).get('metri_posati', 0))

        # Calcola i metri rimanenti
        df_bobine['Metri Rimanenti'] = df_bobine['metri_totali'] - df_bobine['Metri Posati']

        # Crea un writer Excel con pandas
        with pd.ExcelWriter(percorso_file, engine='openpyxl') as writer:
            # Esporta il DataFrame principale
            df_bobine.to_excel(writer, sheet_name='Parco Bobine', index=False)

            # Ottieni il foglio di lavoro
            workbook = writer.book
            worksheet = writer.sheets['Parco Bobine']

            # Formatta le intestazioni
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")

            for col_idx, column in enumerate(df_bobine.columns, 1):
                cell = worksheet.cell(row=1, column=col_idx)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment

            # Imposta la larghezza delle colonne
            for idx, column in enumerate(df_bobine.columns, 1):
                worksheet.column_dimensions[get_column_letter(idx)].width = max(len(str(column)) + 4, 15)

            # Aggiungi bordi a tutte le celle
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            for row in worksheet.iter_rows(min_row=1, max_row=len(df_bobine) + 1, min_col=1, max_col=len(df_bobine.columns)):
                for cell in row:
                    cell.border = thin_border

            # Aggiungi formattazione condizionale per i metri rimanenti
            # Evidenzia in rosso le bobine con pochi metri rimanenti
            col_letter = get_column_letter(df_bobine.columns.get_loc('Metri Rimanenti') + 1)
            worksheet.conditional_formatting.add(
                f"{col_letter}2:{col_letter}{len(df_bobine) + 1}",
                openpyxl.formatting.rule.CellIsRule(
                    operator='lessThan',
                    formula=['50'],
                    stopIfTrue=True,
                    fill=PatternFill(start_color='FFCCCC', end_color='FFCCCC', fill_type='solid')
                )
            )

            # Aggiungi un foglio con statistiche
            stats_data = {
                'Metrica': [
                    'Numero totale bobine',
                    'Metri totali disponibili',
                    'Metri totali posati',
                    'Metri totali rimanenti',
                    'Bobine completamente utilizzate',
                    'Bobine parzialmente utilizzate',
                    'Bobine non utilizzate'
                ],
                'Valore': [
                    len(df_bobine),
                    df_bobine['metri_totali'].sum(),
                    df_bobine['Metri Posati'].sum(),
                    df_bobine['Metri Rimanenti'].sum(),
                    len(df_bobine[df_bobine['Metri Rimanenti'] <= 0]),
                    len(df_bobine[(df_bobine['Metri Posati'] > 0) & (df_bobine['Metri Rimanenti'] > 0)]),
                    len(df_bobine[df_bobine['Metri Posati'] == 0])
                ]
            }

            df_stats = pd.DataFrame(stats_data)
            df_stats.to_excel(writer, sheet_name='Statistiche', index=False)

            # Formatta il foglio statistiche
            stats_sheet = writer.sheets['Statistiche']

            # Formatta le intestazioni
            for col_idx, column in enumerate(df_stats.columns, 1):
                cell = stats_sheet.cell(row=1, column=col_idx)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment

            # Imposta la larghezza delle colonne
            for idx, column in enumerate(df_stats.columns, 1):
                stats_sheet.column_dimensions[get_column_letter(idx)].width = max(len(str(column)) + 4, 20)

            # Aggiungi bordi a tutte le celle
            for row in stats_sheet.iter_rows(min_row=1, max_row=len(df_stats) + 1, min_col=1, max_col=len(df_stats.columns)):
                for cell in row:
                    cell.border = thin_border

        logging.info(f"✅ Parco bobine esportato con successo: {percorso_file}")
        return percorso_file

    except Exception as e:
        logging.error(f"❌ Errore durante l'esportazione del parco bobine: {str(e)}")
        return None


def crea_template_excel(nome_file=None):
    """
    Crea un template Excel vuoto compatibile con il sistema per l'importazione dei cavi.

    Args:
        nome_file (str, optional): Nome del file da creare. Se None, viene generato automaticamente.

    Returns:
        str: Percorso del file creato, o None in caso di errore
    """
    try:
        # Crea cartella exports se non esiste
        os.makedirs(Config.EXPORT_DIR, exist_ok=True)

        # Genera nome file univoco se non specificato
        if nome_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            nome_file = f"template_cavi_{timestamp}.xlsx"
            # Percorso completo del file nella directory di export
            percorso_file = os.path.join(Config.EXPORT_DIR, nome_file)
        else:
            # Se nome_file è specificato, usalo come percorso completo
            percorso_file = nome_file
            # Assicurati che la directory esista (solo se non è un percorso relativo)
            dir_path = os.path.dirname(percorso_file)
            if dir_path:  # Solo se c'è una directory da creare
                os.makedirs(dir_path, exist_ok=True)

        # Definisci le colonne del template
        colonne = [
            "id_cavo",
            "sistema",
            "utility",
            "colore_cavo",
            "tipologia",
            "formazione",    # Rinominato da "sezione" - questo è il campo principale
            "ubicazione_partenza",
            "utenza_partenza",
            "descrizione_utenza_partenza",
            "ubicazione_arrivo",
            "utenza_arrivo",
            "descrizione_utenza_arrivo",
            "metri_teorici"
        ]

        # Crea un workbook con openpyxl
        wb = Workbook()
        ws = wb.active
        ws.title = "Template Cavi"

        # Aggiungi intestazioni
        for col_idx, col_name in enumerate(colonne, 1):
            cell = ws.cell(row=2, column=col_idx)
            cell.value = col_name
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")

            # Imposta la larghezza della colonna
            col_letter = get_column_letter(col_idx)
            ws.column_dimensions[col_letter].width = max(len(col_name) + 4, 15)

        # Dati di esempio
        dati_esempio = [
            {
                "id_cavo": "C001",
                "sistema": "HVAC",
                "utility": "Power",
                "colore_cavo": "Nero",
                "tipologia": "FG16OR16",
                "formazione": "3x2,5+2,5YG",
                "ubicazione_partenza": "Quadro Principale",
                "utenza_partenza": "QP-01",
                "descrizione_utenza_partenza": "Quadro principale piano 1",
                "ubicazione_arrivo": "Pompa P1",
                "utenza_arrivo": "P1-01",
                "descrizione_utenza_arrivo": "Pompa circolazione 1",
                "metri_teorici": 25.5
            },
            {
                "id_cavo": "C002",
                "sistema": "BMS",
                "utility": "Control",
                "colore_cavo": "Bianco",
                "tipologia": "FROR",
                "formazione": "4x1,5+SH",
                "ubicazione_partenza": "Quadro Controllo",
                "utenza_partenza": "QC-02",
                "descrizione_utenza_partenza": "Quadro controllo piano 2",
                "ubicazione_arrivo": "Sensore Temp",
                "utenza_arrivo": "ST-01",
                "descrizione_utenza_arrivo": "Sensore temperatura zona A",
                "metri_teorici": 15.0
            }
        ]

        # Aggiungi i dati di esempio
        for row_idx, row_data in enumerate(dati_esempio, 3):
            for col_idx, col_name in enumerate(colonne, 1):
                cell = ws.cell(row=row_idx, column=col_idx)
                cell.value = row_data.get(col_name, "")

        # Aggiungi bordi a tutte le celle
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        for row in ws.iter_rows(min_row=2, max_row=len(dati_esempio) + 2, min_col=1, max_col=len(colonne)):
            for cell in row:
                cell.border = thin_border

        # Aggiungi una riga di istruzioni
        cell = ws.cell(row=1, column=1)
        cell.value = "Template per l'importazione dei cavi - Compilare tutte le colonne - Non modificare le intestazioni"
        cell.font = Font(bold=True, color="000000")
        cell.alignment = Alignment(horizontal="left", vertical="center")
        ws.merge_cells(start_row=1, start_column=1, end_row=1, end_column=len(colonne))

        # Aggiungi note per il campo formazione
        formazione_col = colonne.index("formazione") + 1
        cell = ws.cell(row=2, column=formazione_col)
        cell.comment = Comment(
            "Inserire la formazione del cavo (es: 3x2,5+2,5YG, 4x1,5+SH, 1X240MM2)", "Sistema")

        # Salva il file
        wb.save(percorso_file)

        logging.info(f"✅ Template Excel creato con successo: {percorso_file}")
        return percorso_file

    except Exception as e:
        logging.error(f"❌ Errore durante la creazione del template Excel: {str(e)}")
        return None


def crea_template_parco_bobine(nome_file=None):
    """
    Crea un template Excel semplificato per l'importazione del parco bobine.

    Args:
        nome_file (str, optional): Nome del file da creare. Se None, viene generato automaticamente.

    Returns:
        str: Percorso del file creato, o None in caso di errore
    """
    try:
        # Crea cartella exports se non esiste
        os.makedirs(Config.EXPORT_DIR, exist_ok=True)

        # Genera nome file univoco se non specificato
        if nome_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            nome_file = f"template_parco_bobine_{timestamp}.xlsx"
            # Percorso completo del file nella directory di export
            percorso_file = os.path.join(Config.EXPORT_DIR, nome_file)
        else:
            # Se nome_file è specificato, usalo come percorso completo
            percorso_file = nome_file
            # Assicurati che la directory esista (solo se non è un percorso relativo)
            dir_path = os.path.dirname(percorso_file)
            if dir_path:  # Solo se c'è una directory da creare
                os.makedirs(dir_path, exist_ok=True)

        # Definisci le colonne del template (allineate con PostgreSQL)
        colonne = [
            "id_bobina",  # Opzionale - se non specificato, verrà generato automaticamente
            "utility",
            "tipologia",
            "formazione",   # Rinominato da "sezione" - questo è il campo principale
            "metri_totali",
            "ubicazione_bobina",
            "fornitore",
            "n_ddt",        # Lowercase per PostgreSQL
            "data_ddt"      # Lowercase per PostgreSQL
        ]

        # Crea un workbook con openpyxl
        wb = Workbook()
        ws = wb.active
        ws.title = "Parco Bobine"

        # Aggiungi intestazioni
        for col_idx, col_name in enumerate(colonne, 1):
            cell = ws.cell(row=2, column=col_idx)
            cell.value = col_name
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")

            # Imposta la larghezza della colonna
            col_letter = get_column_letter(col_idx)
            ws.column_dimensions[col_letter].width = max(len(col_name) + 4, 15)

        # Aggiungi dati di esempio
        dati_esempio = [
            # Esempi per modalità numeri progressivi
            {
                "id_bobina": "1",  # Esempio con numero progressivo
                "utility": "Power",
                "tipologia": "TAAGSHBHLV01D",
                "formazione": "1X240MM2",
                "metri_totali": 500.0,
                "ubicazione_bobina": "Magazzino A",
                "fornitore": "Prysmian",
                "n_ddt": "DDT123456",
                "data_ddt": "01/01/2025"
            },
            {
                "id_bobina": "",  # Esempio senza numero (verrà generato automaticamente come 2)
                "utility": "Power",
                "tipologia": "TACHCHBHLU01I",
                "formazione": "1x185+SH",
                "metri_totali": 1000.0,
                "ubicazione_bobina": "Magazzino B",
                "fornitore": "Nexans",
                "n_ddt": "DDT654321",
                "data_ddt": "15/01/2025"
            },
            # Esempi per modalità codici personalizzati
            {
                "id_bobina": "BOBINA1",  # Esempio con codice personalizzato
                "utility": "Control",
                "tipologia": "TBAACHBHLE04I",
                "formazione": "3X2,5+YG arm",
                "metri_totali": 300.0,
                "ubicazione_bobina": "Magazzino C",
                "fornitore": "Nexans",
                "n_ddt": "DDT654321",
                "data_ddt": "15/01/2025"
            }
        ]

        # Aggiungi i dati di esempio
        for row_idx, row_data in enumerate(dati_esempio, 3):
            for col_idx, col_name in enumerate(colonne, 1):
                cell = ws.cell(row=row_idx, column=col_idx)
                cell.value = row_data.get(col_name, "")

        # Aggiungi bordi a tutte le celle
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        max_row = 6  # 2 righe di intestazione + 3 righe di esempio + 1 riga vuota
        for row in ws.iter_rows(min_row=2, max_row=max_row, min_col=1, max_col=len(colonne)):
            for cell in row:
                cell.border = thin_border

        # Aggiungi istruzioni
        cell = ws.cell(row=1, column=1)
        cell.value = "Template per l'importazione del parco bobine - Compilare i campi obbligatori"
        cell.font = Font(bold=True, color="000000")
        cell.alignment = Alignment(horizontal="left", vertical="center")
        ws.merge_cells(start_row=1, start_column=1, end_row=1, end_column=len(colonne))

        # Aggiungi note per alcuni campi
        # Nota per id_bobina
        cell = ws.cell(row=2, column=colonne.index("id_bobina") + 1)
        cell.comment = Comment("In modalità numeri progressivi: ignorato. In modalità codici personalizzati: specifica la parte Y dell'ID_BOBINA.", "Sistema")

        # Nota per formazione
        cell = ws.cell(row=2, column=colonne.index("formazione") + 1)
        cell.comment = Comment("Inserire la formazione del cavo (es. 1X240MM2, 3X2,5+YG arm, 4x1,5+SH)", "Sistema")

        # Nota per metri_totali
        cell = ws.cell(row=2, column=colonne.index("metri_totali") + 1)
        cell.comment = Comment("Obbligatorio - Numero decimale positivo", "Sistema")

        # Nota per data_ddt
        cell = ws.cell(row=2, column=colonne.index("data_ddt") + 1)
        cell.comment = Comment("Formato: gg/mm/aaaa", "Sistema")

        # Aggiungi un foglio di istruzioni
        ws_istruzioni = wb.create_sheet(title="Istruzioni")
        istruzioni = [
            ["ISTRUZIONI PER L'IMPORTAZIONE DEL PARCO BOBINE"],
            [""],
            ["1. CAMPI OBBLIGATORI"],
            ["- utility, tipologia, metri_totali"],
            [""],
            ["2. CAMPO id_bobina"],
            ["- In modalità numeri progressivi: questo campo viene ignorato, l'ID_BOBINA sarà sempre C{id_cantiere}_B{numero_progressivo}"],
            ["- In modalità codici personalizzati: questo campo viene utilizzato per specificare la parte Y dell'ID_BOBINA"],
            ["- In modalità codici personalizzati: se lasciato vuoto, il sistema chiederà cosa fare"],
            ["- Nota: il sistema determina la modalità in base alle bobine esistenti nel cantiere"],
            ["- Esempio in modalità codici personalizzati: se inserisci '123', l'ID_BOBINA sarà 'C1_B123' (per il cantiere 1)"],
            [""],
            ["3. CAMPO FORMAZIONE"],
            ["- formazione: inserire la formazione del cavo come nel file originale"],
            ["- Esempi: '1X240MM2', '3X2,5+YG arm', '1x185+SH', '4x1,5+SH'"],
            [""],
            ["4. FORMATO DEI DATI"],
            ["- metri_totali: numero decimale positivo"],
            ["- data_ddt: formato gg/mm/aaaa"],
            [""],
            ["5. NOTE"],
            ["- I campi metri_residui e stato_bobina verranno impostati automaticamente"],
            ["- Puoi mescolare bobine con numero specificato e bobine con numero automatico"],
            ["- Il campo formazione è il principale identificativo della tipologia di cavo"],
        ]

        for row_idx, row_data in enumerate(istruzioni, 1):
            cell = ws_istruzioni.cell(row=row_idx, column=1)
            cell.value = row_data[0]
            if row_idx == 1 or row_data[0].startswith("1.") or row_data[0].startswith("2.") or row_data[0].startswith("3.") or row_data[0].startswith("4.") or row_data[0].startswith("5."):
                cell.font = Font(bold=True)

        # Imposta la larghezza della colonna delle istruzioni
        ws_istruzioni.column_dimensions['A'].width = 100

        # Salva il file
        wb.save(percorso_file)

        logging.info(f"✅ Template parco bobine creato con successo: {percorso_file}")
        return percorso_file

    except Exception as e:
        logging.error(f"❌ Errore durante la creazione del template parco bobine: {str(e)}")
        return None





def importa_parco_bobine_da_excel(percorso_file, id_cantiere=None, config_choice=None, auto_choice=None):
    """
    Importa il parco bobine da un file Excel rispettando esattamente i dati presenti nel file.
    Supporta sia numeri bobina specificati che generati automaticamente.

    Args:
        percorso_file (str): Percorso del file Excel da importare
        id_cantiere (int, optional): ID del cantiere a cui associare le bobine

    Returns:
        tuple: (successo, messaggio, numero_bobine_importate)
    """
    try:
        # Verifica che il file esista e sia sicuro
        if not is_file_safe(percorso_file):
            return False, "File non valido o non sicuro", 0

        # Leggi il file Excel
        df = leggi_file_excel(percorso_file)
        if df is None or df.empty:
            return False, "File vuoto o non valido", 0

        # Verifica che le colonne necessarie siano presenti
        colonne_richieste = ["utility", "tipologia", "metri_totali"]
        for colonna in colonne_richieste:
            if colonna not in df.columns:
                return False, f"Colonna '{colonna}' mancante nel file", 0

        # Converti i tipi di dati
        try:
            # Converti solo metri_totali in numero
            if "metri_totali" in df.columns:
                df["metri_totali"] = pd.to_numeric(df["metri_totali"], errors="coerce")
            if "metri_residui" in df.columns:
                df["metri_residui"] = pd.to_numeric(df["metri_residui"], errors="coerce")
            else:
                # Se metri_residui non è specificato, usa metri_totali
                df["metri_residui"] = df["metri_totali"]

            # Assicurati che n_conduttori e sezione siano stringhe
            if "n_conduttori" in df.columns:
                df["n_conduttori"] = df["n_conduttori"].astype(str)
            if "sezione" in df.columns:
                df["sezione"] = df["sezione"].astype(str)
        except Exception as e:
            return False, f"Errore nella conversione dei dati: {str(e)}", 0

        # Verifica che i dati siano validi
        df_valido = df.dropna(subset=colonne_richieste)
        if len(df_valido) < len(df):
            righe_invalide = len(df) - len(df_valido)
            logging.warning(f"⚠️ {righe_invalide} righe con dati mancanti sono state ignorate")

        if df_valido.empty:
            return False, "Nessun dato valido trovato nel file", 0

        # Connessione al database
        with database_connection(dict_cursor=True, autocommit=False) as (conn, c):
            # Contatori
            bobine_importate = 0
            bobine_esistenti = 0
            errori = 0

            try:
                # Verifica se ci sono già bobine per questo cantiere e ottieni la configurazione
                c.execute('''
                    SELECT COUNT(*) as count
                    FROM parco_cavi
                    WHERE id_cantiere = %s
                ''', (id_cantiere,))
                result = c.fetchone()
                is_first_insert = result['count'] == 0

                # Se non è il primo inserimento, ottieni la configurazione
                config_value = "s"  # Default
                if not is_first_insert:
                    c.execute('''
                        SELECT configurazione
                        FROM parco_cavi
                        WHERE id_cantiere = %s
                        LIMIT 1
                    ''', (id_cantiere,))
                    result_config = c.fetchone()
                    if result_config and result_config['configurazione']:
                        config_value = result_config['configurazione']

                # Se è il primo inserimento, chiedi all'utente la configurazione o usa quella fornita
                if is_first_insert:
                    if config_choice is None:  # Modalità interattiva
                        print("\nQuesta è la prima importazione di bobine per questo cantiere.")
                        print("Scegli la configurazione per la numerazione delle bobine:")
                        print("1. Usa numeri progressivi (es. 1, 2, 3, ...)")
                        print("2. Usa codici personalizzati (es. BOBINA1, XYZ123, ...)")

                        while True:
                            config_choice = input("Seleziona un'opzione (1/2): ").strip()
                            if config_choice in ["1", "2"]:
                                break
                            print("\n❌ Opzione non valida! Seleziona 1 o 2.")

                    usa_numero_progressivo = (config_choice == "1")
                    config_value = "s" if usa_numero_progressivo else "n"
                    logging.info(f"Configurazione impostata: {'numeri progressivi' if usa_numero_progressivo else 'codici personalizzati'}")
                else:
                    # Usa la configurazione esistente
                    usa_numero_progressivo = (config_value == "s")

                # Ottieni l'ultimo numero bobina per la generazione automatica se necessario
                ultimo_numero = 0
                if usa_numero_progressivo:
                    try:
                        # Prima prova a trovare il massimo numero numerico
                        c.execute('''
                            SELECT MAX(numero_bobina::INTEGER) as max_num
                            FROM parco_cavi
                            WHERE id_cantiere = %s AND numero_bobina ~ '^[0-9]+$'
                        ''', (id_cantiere,))
                        result = c.fetchone()
                        ultimo_numero = result['max_num'] if result and result['max_num'] else 0
                    except Exception as e:
                        logging.warning(f"Errore nel recupero dell'ultimo numero bobina: {str(e)}")
                        # Fallback: conta semplicemente il numero di bobine e aggiungi 1
                        c.execute('SELECT COUNT(*) as count FROM parco_cavi WHERE id_cantiere = %s', (id_cantiere,))
                        result = c.fetchone()
                        ultimo_numero = result['count'] if result and result['count'] else 0

                # Importa le bobine
                for idx, row in df_valido.iterrows():
                    try:
                        # Determina il numero_bobina e ID_BOBINA in base alla configurazione
                        if usa_numero_progressivo:
                            # Modalità numeri progressivi: numero_bobina è un contatore progressivo
                            # e ID_BOBINA è sempre C{id_cantiere}_B{numero_bobina}

                            # Verifica se l'utente ha specificato un valore nel campo id_bobina
                            if "id_bobina" in row and pd.notna(row["id_bobina"]) and str(row["id_bobina"]).strip():
                                # Avvisa l'utente che il valore specificato verrà ignorato
                                logging.warning(f"\n⚠️ Riga {idx+2}: Valore '{row['id_bobina']}' nel campo id_bobina ignorato in modalità numeri progressivi")

                            # Genera un nuovo numero progressivo
                            ultimo_numero += 1
                            numero_bobina = str(ultimo_numero)  # Usa numeri semplici: 1, 2, 3, ecc.

                            # In modalità numeri progressivi, l'ID_BOBINA è sempre basato sul numero progressivo
                            # indipendentemente da cosa l'utente ha specificato nel file Excel
                            id_bobina = f"C{id_cantiere}_B{numero_bobina}"
                        else:
                            # Modalità ID personalizzati: l'utente specifica la parte Y dell'ID_BOBINA
                            # e numero_bobina è comunque un contatore progressivo

                            # Genera un nuovo numero progressivo per numero_bobina
                            ultimo_numero += 1
                            numero_bobina = str(ultimo_numero)  # Usa numeri semplici: 1, 2, 3, ecc.

                            if "id_bobina" in row and pd.notna(row["id_bobina"]) and str(row["id_bobina"]).strip():
                                # Usa il valore specificato dall'utente come parte Y dell'ID_BOBINA
                                parte_y = str(row["id_bobina"]).strip()
                                id_bobina = f"C{id_cantiere}_B{parte_y}"
                            else:
                                # In modalità manuale, se manca il numero bobina, chiedi all'utente o genera AUTO+numero
                                if auto_choice is None:  # Modalità interattiva
                                    print(f"\nRiga {idx+2}: Manca il codice della bobina")
                                    print("1. Inserisci manualmente un codice")
                                    print("2. Genera automaticamente un codice con prefisso 'AUTO'")
                                    print("3. Salta questa riga")

                                    while True:
                                        scelta = input("Seleziona un'opzione (1/2/3): ").strip()
                                        if scelta in ["1", "2", "3"]:
                                            break
                                        print("\n❌ Opzione non valida! Seleziona 1, 2 o 3.")
                                else:  # Modalità non interattiva
                                    scelta = auto_choice

                                if scelta == "1":
                                    # Inserimento manuale
                                    if auto_choice is None:  # Modalità interattiva
                                        parte_y = input("Inserisci il codice per questa bobina: ").strip()
                                        if not parte_y:
                                            print("Codice non valido, riga saltata.")
                                            errori += 1
                                            continue
                                    else:  # Modalità non interattiva, usa un valore predefinito
                                        parte_y = f"TEST{idx+1}"
                                        print(f"Generato codice di test: {parte_y}")
                                    id_bobina = f"C{id_cantiere}_B{parte_y}"
                                elif scelta == "2":
                                    # Generazione automatica con prefisso AUTO
                                    c.execute('''
                                        SELECT MAX((REPLACE(id_bobina, 'C' || %s || '_BAUTO', ''))::INTEGER) as max_auto
                                        FROM parco_cavi
                                        WHERE id_bobina LIKE 'C' || %s || '_BAUTO%%'
                                    ''', (id_cantiere, id_cantiere))
                                    result = c.fetchone()
                                    ultimo_auto = result['max_auto'] if result and result['max_auto'] else 0
                                    ultimo_auto += 1
                                    parte_y = f"AUTO{ultimo_auto}"
                                    id_bobina = f"C{id_cantiere}_B{parte_y}"
                                    print(f"Generato ID: {id_bobina}")
                                else:  # scelta == "3"
                                    print("Riga saltata.")
                                    errori += 1
                                    continue

                        # Verifica se la bobina esiste già
                        c.execute("SELECT id_bobina FROM parco_cavi WHERE id_bobina = %s", (id_bobina,))
                        if c.fetchone():
                            bobine_esistenti += 1
                            continue

                        # Prepara i dati per l'inserimento - usa esattamente i valori dal file Excel
                        utility = str(row.get("utility", "")).strip()
                        tipologia = str(row.get("tipologia", "")).strip()

                        # Usa i valori esattamente come sono nel file Excel
                        # Converti n_conduttori in stringa (ora è un campo TEXT nel database)
                        n_conduttori = str(row.get("n_conduttori", "0")).strip()
                        # Mantieni la sezione (formazione) come stringa
                        sezione = str(row.get("sezione", "")).strip()

                        # Gestisci metri_totali e metri_residui
                        try:
                            metri_totali = float(row["metri_totali"])
                            if "metri_residui" in row and pd.notna(row["metri_residui"]):
                                metri_residui = float(row["metri_residui"])
                            else:
                                metri_residui = metri_totali
                        except (ValueError, TypeError):
                            logging.error(f"Errore nella conversione dei metri per la riga {idx+2}")
                            errori += 1
                            continue

                        # Altri campi
                        stato_bobina = "Disponibile"  # Valore predefinito
                        ubicazione_bobina = str(row.get("ubicazione_bobina", "")).strip()
                        fornitore = str(row.get("fornitore", "")).strip()
                        n_ddt = str(row.get("n_ddt", "")).strip()
                        data_ddt = str(row.get("data_ddt", "")).strip()

                        # Inserisci la bobina nel database
                        c.execute("""
                            INSERT INTO parco_cavi (
                                id_bobina, numero_bobina, utility, tipologia, n_conduttori, sezione,
                                metri_totali, metri_residui, stato_bobina, ubicazione_bobina,
                                fornitore, n_ddt, data_ddt, configurazione, id_cantiere
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            id_bobina, numero_bobina, utility, tipologia, n_conduttori, sezione,
                            metri_totali, metri_residui, stato_bobina, ubicazione_bobina,
                            fornitore, n_ddt, data_ddt, config_value, id_cantiere
                        ))

                        bobine_importate += 1
                    except Exception as e:
                        logging.error(f"❌ Errore durante l'importazione della riga {idx+2}: {str(e)}")
                        errori += 1

                # Il commit viene gestito automaticamente dal context manager

                # Messaggio di successo
                messaggio = f"Importazione completata: {bobine_importate} bobine importate"
                if bobine_esistenti > 0:
                    messaggio += f", {bobine_esistenti} bobine già esistenti ignorate"
                if errori > 0:
                    messaggio += f", {errori} errori riscontrati"

                return True, messaggio, bobine_importate

            except Exception as e:
                # Il rollback viene gestito automaticamente dal context manager in caso di errore
                logging.error(f"❌ Errore durante l'importazione: {str(e)}")
                return False, f"Errore durante l'importazione: {str(e)}", 0

    except Exception as e:
        logging.error(f"❌ Errore durante l'importazione del parco bobine: {str(e)}")
        return False, f"Errore: {str(e)}", 0
