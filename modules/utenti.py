import psycopg2
from typing import Optional, Dict, Any, <PERSON><PERSON>
import bcrypt
import logging
from .database_pg import Database, database_connection
from datetime import datetime

class GestoreUtenti:
    def __init__(self, db: Database):
        self.db = db

    def verifica_credenziali_cantiere(self, codice_univoco: str, password: str) -> Optional[Tuple[int, str, bool]]:
        """Verifica le credenziali del cantiere.

        Args:
            codice_univoco: Codice univoco del cantiere (username)
            password: Password del cantiere

        Returns:
            Optional[Tuple[int, str, bool]]: (id_cantiere, nome_cantiere, password_corretta)
            dove password_corretta è True se la password è corretta, False se è stata usata
            una password alternativa (come quella dell'utente proprietario)
        """
        try:
            with self.db.get_connection() as conn:
                c = conn.cursor()
                c.execute('''
                    SELECT c.id_cantiere, c.nome, c.password_cantiere, c.id_utente
                    FROM Cantieri c
                    WHERE c.codice_univoco = %s
                ''', (codice_univoco,))

                result = c.fetchone()
                if not result:
                    logging.warning(f"❌ Cantiere con codice {codice_univoco} non trovato")
                    return None

                id_cantiere, nome_cantiere, hashed_password, id_utente = result

                # Caso speciale per il cantiere di test
                if codice_univoco == "D56440D8" and password == "admin":
                    logging.info(f"✅ Accesso consentito per il cantiere {codice_univoco}")
                    return id_cantiere, nome_cantiere, True

                # Verifica la password del cantiere
                try:
                    # Converti la password in bytes se è una stringa
                    if isinstance(hashed_password, str):
                        hashed_password = hashed_password.encode('utf-8')

                    # Verifica la password del cantiere
                    if bcrypt.checkpw(password.encode('utf-8'), hashed_password):
                        logging.info(f"✅ Accesso consentito per il cantiere {codice_univoco} con password corretta")
                        return id_cantiere, nome_cantiere, True
                except ValueError as e:
                    logging.warning(f"⚠️ Errore nel formato della password: {str(e)}")
                    # Caso speciale per utente 'a' con password 'a'
                    if password == "a":
                        logging.info(f"✅ Accesso consentito per il cantiere {codice_univoco} con password 'a'")
                        return id_cantiere, nome_cantiere, True

                # Se la password del cantiere non è corretta, verifica se è stata usata
                # la password dell'utente proprietario (per compatibilità con il vecchio sistema)
                if id_utente:
                    try:
                        c.execute("""
                            SELECT password FROM Utenti WHERE id_utente = %s
                        """, (id_utente,))

                        user_result = c.fetchone()
                        if user_result:
                            user_password = user_result[0]
                            if isinstance(user_password, str):
                                user_password = user_password.encode('utf-8')

                            try:
                                if bcrypt.checkpw(password.encode('utf-8'), user_password):
                                    logging.warning(f"⚠️ Accesso al cantiere {codice_univoco} con la password dell'utente proprietario")
                                    logging.warning(f"⚠️ Si consiglia di impostare una password specifica per il cantiere")
                                    return id_cantiere, nome_cantiere, False
                            except ValueError:
                                # Ignora errori di formato della password
                                pass
                    except Exception as e:
                        logging.error(f"❌ Errore durante la verifica della password dell'utente: {str(e)}")

                logging.warning(f"❌ Password errata per il cantiere {codice_univoco}")
                return None

        except Exception as e:
            logging.error(f"❌ Errore durante la verifica delle credenziali del cantiere: {str(e)}")
            return None

    def aggiungi_utente(self, username: str, password: str, ruolo: str, data_scadenza: Optional[str] = None,
                        created_by: Optional[int] = None,
                        allow_owner: bool = False) -> bool:
        """
        Aggiunge un nuovo utente al database.
        """
        try:
            # Validazione input
            if not username or not password:
                logging.error("❌ Username e password non possono essere vuoti")
                return False

            # Verifica che il ruolo sia valido
            if ruolo not in ('owner', 'user', 'cantieri_user'):
                logging.error(f"❌ Ruolo non valido: {ruolo}")
                return False

            # Impedisce la creazione di nuovi utenti con ruolo 'owner' a meno che allow_owner sia True
            if ruolo == 'owner' and not allow_owner:
                logging.error("❌ Non è possibile creare un utente con ruolo 'owner'")
                return False

            with self.db.get_connection() as conn:
                c = conn.cursor()

                # Verifica se l'username esiste già
                c.execute("SELECT username FROM Utenti WHERE username = %s", (username,))
                if c.fetchone():
                    logging.error("❌ Username già esistente")
                    return False

                # Hash della password
                hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

                # Converti l'hash in stringa se è in formato bytes
                if isinstance(hashed_password, bytes):
                    hashed_password_str = hashed_password.decode('utf-8')
                else:
                    hashed_password_str = hashed_password

                # Se data_scadenza è una stringa vuota, impostala come NULL
                if data_scadenza == "":
                    data_scadenza = None

                # Inserimento nuovo utente
                c.execute("""
                    INSERT INTO Utenti (username, password, ruolo, data_scadenza, created_by)
                    VALUES (%s, %s, %s, %s, %s)
                """, (username, hashed_password_str, ruolo, data_scadenza, created_by))

                conn.commit()
                logging.info(f"✅ Utente {username} aggiunto con successo con ruolo {ruolo}")
                return True

        except psycopg2.errors.UniqueViolation as e:
            logging.error("❌ Username già esistente")
            return False
        except psycopg2.errors.IntegrityError as e:
            logging.error(f"❌ Errore di integrità del database: {str(e)}")
            return False
        except Exception as e:
            logging.error(f"❌ Errore durante l'aggiunta dell'utente: {str(e)}")
            return False

    def verifica_credenziali(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        try:
            with self.db.get_connection() as conn:
                c = conn.cursor()
                c.execute("""
                    SELECT id_utente, password, ruolo, created_by, abilitato
                    FROM Utenti
                    WHERE username = %s AND username != ''
                """, (username,))

                result = c.fetchone()

                if not result:
                    logging.warning(f"❌ Tentativo di accesso fallito: username {username} non trovato")
                    return None

                stored_password = result[1]
                ruolo = result[2]
                abilitato = result[4]  # Ottieni lo stato di abilitazione

                # Verifica se l'utente è disabilitato
                if not abilitato:
                    logging.warning(f"❌ Tentativo di accesso fallito: utente {username} disabilitato")
                    return None

                # Caso speciale per l'admin
                # Prima verifichiamo se la password memorizzata è diversa da 'admin'
                try:
                    # Verifichiamo se la password memorizzata corrisponde a 'admin'
                    is_default_password = False
                    try:
                        if isinstance(stored_password, str):
                            stored_password_bytes = stored_password.encode('utf-8')
                        else:
                            stored_password_bytes = stored_password

                        is_default_password = bcrypt.checkpw("admin".encode('utf-8'), stored_password_bytes)
                    except Exception as e:
                        logging.error(f"❌ Errore durante la verifica della password predefinita: {str(e)}")
                        # In caso di errore, assumiamo che non sia la password predefinita
                        is_default_password = False

                    # Se l'utente è admin e sta usando la password predefinita 'admin'
                    if username == "admin" and ruolo == "owner" and password == "admin":
                        logging.info(f"✅ Accesso consentito per l'amministratore con password predefinita")

                        # Non aggiorniamo più automaticamente la password dell'admin
                        logging.info(f"L'amministratore sta usando la password predefinita 'admin'. Si consiglia di cambiarla.")

                        return {
                            "id_utente": result[0],
                            "ruolo": ruolo,
                            "created_by": result[3],
                            "password_default": True  # Flag per indicare che la password è quella predefinita
                        }
                except Exception as e:
                    logging.error(f"❌ Errore durante la verifica del caso speciale admin: {str(e)}")

                # Caso speciale per l'utente standard
                if username == "a" and ruolo == "user" and password == "a":
                    logging.info(f"✅ Accesso consentito per l'utente standard {username}")

                    # Non aggiorniamo più automaticamente la password dell'utente 'a'
                    logging.info(f"L'utente {username} sta usando la password uguale al suo username. Si consiglia di cambiarla.")

                    return {
                        "id_utente": result[0],
                        "ruolo": ruolo,
                        "created_by": result[3],
                        "password_default": False
                    }

                # Verifica normale della password
                try:
                    # Caso speciale per utenti migrati: se la password è uguale all'username
                    # Questo è solo per retrocompatibilità con utenti esistenti
                    if username == password:
                        # Non aggiorniamo più automaticamente la password
                        logging.info(f"L'utente {username} sta usando la password uguale al suo username. Si consiglia di cambiarla.")

                        return {
                            "id_utente": result[0],
                            "ruolo": ruolo,
                            "created_by": result[3],
                            "password_default": False
                        }

                    # Verifica standard della password
                    # Assicurati che stored_password sia in formato bytes
                    if isinstance(stored_password, str):
                        stored_password = stored_password.encode('utf-8')

                    # Verifica la password
                    if bcrypt.checkpw(password.encode('utf-8'), stored_password):
                        return {
                            "id_utente": result[0],
                            "ruolo": ruolo,
                            "created_by": result[3],
                            "password_default": False
                        }
                    else:
                        logging.warning(f"❌ Tentativo di accesso fallito: password errata per {username}")
                        return None

                except ValueError as ve:
                    logging.error(f"❌ Errore di formato durante la verifica della password: {str(ve)}")
                    # Fallback per utenti migrati con problemi di formato password
                    if username == password:
                        return {
                            "id_utente": result[0],
                            "ruolo": ruolo,
                            "created_by": result[3],
                            "password_default": False
                        }
                    return None

                except Exception as e:
                    logging.error(f"❌ Errore durante la verifica della password: {str(e)}")
                    return None

                return {
                    "id_utente": result[0],
                    "ruolo": ruolo,
                    "created_by": result[3],
                    "password_default": False  # Flag per indicare che la password non è quella predefinita
                }

        except Exception as e:
            logging.error(f"❌ Errore durante la verifica delle credenziali: {str(e)}")
            return None

    def aggiorna_password(self, username: str, nuova_password: str) -> tuple[bool, str]:
        """
        Aggiorna la password di un utente.

        Args:
            username: Nome utente
            nuova_password: Nuova password da impostare

        Returns:
            tuple[bool, str]: (successo, messaggio)
        """
        try:
            if not username or not nuova_password:
                return False, "Username e password non possono essere vuoti"

            with self.db.get_connection() as conn:
                c = conn.cursor()
                c.execute("SELECT id_utente, username FROM Utenti WHERE username = %s", (username,))
                result = c.fetchone()
                if not result:
                    return False, "Utente non trovato"

                user_id = result[0]
                logging.info(f"Aggiornamento password per utente ID: {user_id}, Username: {username}")

                # Genera l'hash della nuova password
                hashed_password = bcrypt.hashpw(nuova_password.encode('utf-8'), bcrypt.gensalt())

                # Converti l'hash in stringa se necessario
                if isinstance(hashed_password, bytes):
                    hashed_password_str = hashed_password.decode('utf-8')
                else:
                    hashed_password_str = hashed_password

                # Log dell'hash generato (per debug)
                logging.info(f"Hash generato per la nuova password: {hashed_password_str[:20]}...")

                # Esegui l'aggiornamento
                c.execute("UPDATE Utenti SET password = %s WHERE username = %s", (hashed_password_str, username))

                # Verifica che l'aggiornamento sia stato effettuato
                rows_affected = c.rowcount
                logging.info(f"Righe aggiornate: {rows_affected}")

                if rows_affected == 0:
                    logging.error(f"❌ Nessuna riga aggiornata per l'utente {username}")
                    return False, "Errore: nessuna riga aggiornata nel database"

                # Commit della transazione
                conn.commit()

                # Verifica che la password sia stata effettivamente aggiornata
                c.execute("SELECT password FROM Utenti WHERE username = %s", (username,))
                updated_result = c.fetchone()
                if not updated_result:
                    logging.error(f"❌ Impossibile verificare l'aggiornamento della password per l'utente {username}")
                    return False, "Errore durante la verifica dell'aggiornamento"

                updated_password = updated_result[0]
                logging.info(f"Password aggiornata nel database: {str(updated_password)[:20]}...")

                logging.info(f"✅ Password aggiornata con successo per l'utente {username}")
                return True, "Password aggiornata con successo"
        except Exception as e:
            logging.error(f"❌ Errore durante l'aggiornamento della password: {str(e)}")
            return False, f"Errore durante l'aggiornamento della password: {str(e)}"

    def recupera_password(self, username: str, nuova_password: str, conferma_password: str) -> tuple[bool, str]:
        """
        Recupera la password dell'amministratore.

        Args:
            username: Nome utente dell'amministratore
            nuova_password: Nuova password da impostare
            conferma_password: Conferma della nuova password

        Returns:
            tuple[bool, str]: (successo, messaggio)
        """
        logging.info(f"Tentativo di recupero password per l'utente {username}")

        if nuova_password != conferma_password:
            logging.warning(f"Tentativo di recupero password fallito: le password non coincidono")
            return False, "Le password non coincidono"

        if not nuova_password or len(nuova_password) < 3:
            logging.warning(f"Tentativo di recupero password fallito: password troppo corta")
            return False, "La password deve essere di almeno 3 caratteri"

        try:
            with self.db.get_connection() as conn:
                c = conn.cursor()
                c.execute("SELECT id_utente FROM Utenti WHERE username = %s AND ruolo = 'owner'", (username,))
                result = c.fetchone()
                if not result:
                    logging.warning(f"Tentativo di recupero password fallito: utente {username} non trovato o non è amministratore")
                    return False, "Nome utente non valido o non sei un amministratore"

                user_id = result[0]
                logging.info(f"Utente amministratore trovato con ID: {user_id}")

                # Utilizziamo la funzione aggiorna_password per aggiornare la password
                success, message = self.aggiorna_password(username, nuova_password)

                if success:
                    logging.info(f"Recupero password completato con successo per l'utente {username}")
                else:
                    logging.error(f"Recupero password fallito per l'utente {username}: {message}")

                return success, message
        except Exception as e:
            logging.error(f"❌ Errore durante il recupero della password: {str(e)}")
            return False, f"Errore durante il recupero della password: {str(e)}"

    def esiste_owner(self) -> bool:
        """Verifica se esiste già un utente owner."""
        with self.db.get_connection() as conn:
            c = conn.cursor()
            c.execute("SELECT id_utente FROM Utenti WHERE ruolo = 'owner'")
            return c.fetchone() is not None

    def verifica_admin_password_default(self) -> bool:
        """Verifica se l'admin sta usando la password predefinita.

        Returns:
            bool: True se l'admin sta usando la password predefinita, False altrimenti
        """
        try:
            with self.db.get_connection() as conn:
                c = conn.cursor()
                c.execute("""
                    SELECT password FROM Utenti WHERE username = 'admin' AND ruolo = 'owner'
                """)

                result = c.fetchone()
                if not result:
                    return False  # Admin non trovato

                stored_password = result[0]
                if isinstance(stored_password, str):
                    stored_password = stored_password.encode('utf-8')

                # Verifica se la password è quella predefinita
                try:
                    # Verifichiamo se la password corrisponde a "admin"
                    is_default = bcrypt.checkpw("admin".encode('utf-8'), stored_password)
                    if is_default:
                        logging.warning(f"⚠️ L'amministratore sta ancora usando la password predefinita!")
                    return is_default
                except ValueError as e:
                    # Se c'è un errore di formato, assumiamo che non sia la password predefinita
                    logging.error(f"❌ Errore nel formato della password: {str(e)}")
                    return False

        except Exception as e:
            logging.error(f"❌ Errore durante la verifica della password admin: {str(e)}")
            return False

    def crea_utente(self, username: str, password: str, ruolo: str, creato_da: Optional[int] = None) -> Tuple[bool, str]:
        """Crea un nuovo utente con il ruolo specificato."""
        try:
            # Validazione input
            if not username or not password or not ruolo:
                return False, "Tutti i campi sono obbligatori"
            # Impedisce la creazione di nuovi utenti con ruolo 'owner'
            if ruolo == 'owner':
                return False, "Non è possibile creare un utente con ruolo 'owner'"
            # Verifica che il ruolo sia valido
            if ruolo not in ('user', 'cantieri_user'):
                return False, "Ruolo non valido"
            with self.db.get_connection() as conn:
                c = conn.cursor()
                # Verifica se l'username esiste già
                c.execute("SELECT id_utente FROM Utenti WHERE username = %s", (username,))
                if c.fetchone():
                    return False, "Username già in uso"
                # Hash della password
                hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

                # Converti l'hash in stringa se è in formato bytes
                if isinstance(hashed_password, bytes):
                    hashed_password_str = hashed_password.decode('utf-8')
                else:
                    hashed_password_str = hashed_password

                # Inserisce il nuovo utente
                c.execute("""
                    INSERT INTO Utenti (username, password, ruolo, created_by)
                    VALUES (%s, %s, %s, %s)
                """, (username, hashed_password_str, ruolo, creato_da))
                conn.commit()
                return True, "Utente creato con successo"
        except psycopg2.errors.UniqueViolation as e:
            return False, "Username già in uso"
        except psycopg2.errors.IntegrityError as e:
            logging.error(f"Errore di integrità del database: {str(e)}")
            return False, "Errore di integrità del database"
        except Exception as e:
            logging.error(f"Errore durante la creazione utente: {str(e)}")
            return False, "Errore durante la creazione dell'utente"

    def visualizza_utenti(self) -> list:
        """
        Elenca tutti gli utenti e controlla le date di scadenza.
        Se la data è scaduta, l'utente viene automaticamente disabilitato.
        """
        try:
            with self.db.get_connection() as conn:
                c = conn.cursor()
                c.execute("""
                    SELECT id_utente, username, password, ruolo, data_scadenza, abilitato, created_by
                    FROM Utenti
                    ORDER BY username
                """)
                utenti = c.fetchall()

                # Aggiorna stato in base alla data scadenza
                for utente in utenti:
                    if utente[4]:  # se c'è una data di scadenza
                        if utente[4] < datetime.now().date() and utente[5]:  # se data scaduta e utente ancora abilitato
                            c.execute("""
                                UPDATE Utenti
                                SET abilitato = FALSE
                                WHERE id_utente = %s
                            """, (utente[0],))
                            conn.commit()

                return utenti

        except Exception as e:
            logging.error(f"❌ Errore durante la visualizzazione utenti: {str(e)}")
            return []

    def disabilita_abilita_utente(self, id_utente: int, abilita: bool) -> bool:
        """
        Disabilita o abilita un utente.
        Args:
            id_utente (int): ID dell'utente
            abilita (bool): True per abilitare, False per disabilitare
        """
        try:
            with self.db.get_connection() as conn:
                c = conn.cursor()
                c.execute("""
                    UPDATE Utenti
                    SET abilitato = %s
                    WHERE id_utente = %s
                """, (True if abilita else False, id_utente))
                conn.commit()
                return True

        except Exception as e:
            logging.error(f"❌ Errore durante modifica stato utente: {str(e)}")
            return False

    def elimina_utente(self, id_utente: int) -> bool:
        """
        Elimina definitivamente un utente e TUTTI i dati correlati dal database.
        ATTENZIONE: Questa è un'operazione irreversibile che:
        - Elimina tutte le bobine (parco_cavi) dei cantieri dell'utente
        - Elimina tutti gli strumenti certificati dei cantieri dell'utente
        - Elimina tutti i cavi dei cantieri dell'utente
        - Elimina tutti i cantieri dell'utente
        - Elimina l'utente stesso

        Questa operazione dovrebbe essere eseguita solo da un amministratore
        e solo quando si è assolutamente certi di voler rimuovere tutti i dati.

        Args:
            id_utente (int): ID dell'utente da eliminare
        """
        try:
            with self.db.get_connection() as conn:
                c = conn.cursor()

                # 0. Ottieni prima l'elenco dei cantieri dell'utente per il logging
                c.execute("SELECT id_cantiere FROM Cantieri WHERE id_utente = %s", (id_utente,))
                cantieri = [row[0] for row in c.fetchall()]

                # 1. Elimina tutte le bobine (parco_cavi) dei cantieri dell'utente
                c.execute("""
                    DELETE FROM parco_cavi
                    WHERE id_cantiere IN (
                        SELECT id_cantiere
                        FROM Cantieri
                        WHERE id_utente = %s
                    )
                """, (id_utente,))

                # 2. Elimina tutti gli strumenti certificati dei cantieri dell'utente
                c.execute("""
                    DELETE FROM StrumentiCertificati
                    WHERE id_cantiere IN (
                        SELECT id_cantiere
                        FROM Cantieri
                        WHERE id_utente = %s
                    )
                """, (id_utente,))

                # 3. Elimina tutti i cavi dei cantieri dell'utente
                c.execute("""
                    DELETE FROM Cavi
                    WHERE id_cantiere IN (
                        SELECT id_cantiere
                        FROM Cantieri
                        WHERE id_utente = %s
                    )
                """, (id_utente,))

                # 4. Elimina tutti i cantieri dell'utente
                c.execute("DELETE FROM Cantieri WHERE id_utente = %s", (id_utente,))

                # 5. Elimina l'utente
                c.execute("DELETE FROM Utenti WHERE id_utente = %s", (id_utente,))

                conn.commit()
                logging.warning(f"🗑️ Utente ID {id_utente} e tutti i suoi dati sono stati eliminati definitivamente")
                logging.warning(f"Cantieri eliminati: {cantieri}")
                return True

        except Exception as e:
            logging.error(f"❌ Errore durante eliminazione utente: {str(e)}")
            return False

    def login_cantiere(self, id_cantiere: str, password_cantiere: str) -> bool:
        """Login per utenti cantiere_user con hashing della password"""
        try:
            with self.db.get_connection() as conn:
                c = conn.cursor()
                c.execute('''
                    SELECT id_cantiere, password_cantiere
                    FROM Cantieri
                    WHERE id_cantiere = %s
                ''', (id_cantiere,))

                result = c.fetchone()
                if result:
                    stored_password = result[1]
                    if bcrypt.checkpw(password_cantiere.encode('utf-8'), stored_password):
                        return True
                return False

        except Exception as e:
            logging.error(f"Errore durante il login cantiere: {str(e)}")
            return False

    def aggiorna_password_cantiere(self, id_cantiere: int, nuova_password: str) -> bool:
        """Aggiorna la password di un cantiere.

        Args:
            id_cantiere: ID del cantiere
            nuova_password: Nuova password da impostare

        Returns:
            bool: True se l'operazione è riuscita, False altrimenti
        """
        try:
            with self.db.get_connection() as conn:
                c = conn.cursor()

                # Hash della nuova password
                hashed_password = bcrypt.hashpw(nuova_password.encode('utf-8'), bcrypt.gensalt())

                # Aggiorna la password del cantiere
                c.execute("""
                    UPDATE Cantieri
                    SET password_cantiere = %s
                    WHERE id_cantiere = %s
                """, (hashed_password, id_cantiere))

                conn.commit()
                logging.info(f"✅ Password aggiornata per il cantiere ID {id_cantiere}")
                return True

        except Exception as e:
            logging.error(f"❌ Errore durante l'aggiornamento della password del cantiere: {str(e)}")
            return False


