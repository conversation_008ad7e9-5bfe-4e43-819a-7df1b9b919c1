import logging
import sys
from enum import Enum, auto
from typing import Tuple, Union, Optional, TypeVar, Generic
from dataclasses import dataclass

# Definizione del tipo generico per RisultatoValidazione
T = TypeVar('T', int, float, str)


class StatoBobina(Enum):
    """
    Enum che rappresenta i possibili stati di una bobina.
    """
    DISPONIBILE = "Disponibile"
    IN_USO = "In uso"
    TERMINATA = "Terminata"
    DANNEGGIATA = "Danneggiata"
    OVER = "Over"  # Aggiunto stato OVER per bobine con metri residui negativi

    @classmethod
    def get_stati_validi(cls) -> list[str]:
        """
        Restituisce la lista degli stati validi in formato stringa.

        Returns:
            Lista degli stati validi.
        """
        return [stato.value for stato in cls]

    @classmethod
    def from_string(cls, value: str) -> 'StatoBobina':
        """
        Converte una stringa nello stato corrispondente.

        Args:
            value: Stringa rappresentante lo stato.

        Returns:
            StatoBobina corrispondente.

        Raises:
            ValueError: Se lo stato non è valido.
        """
        value = value.strip().lower()  # Rimuove spazi e converte in minuscolo
        for stato in cls:
            if stato.value.lower() == value:
                return stato
        raise ValueError(f"Stato della bobina non valido. Stati ammessi: {', '.join(cls.get_stati_validi())}")


class StatoInstallazione(Enum):
    """
    Enum che rappresenta i possibili stati di installazione di un cavo.
    """
    INSTALLATO = "Installato"
    DA_INSTALLARE = "Da installare"
    IN_CORSO = "In corso"

    @classmethod
    def get_stati_validi(cls) -> list[str]:
        """
        Restituisce la lista degli stati validi in formato stringa.

        Returns:
            Lista degli stati validi.
        """
        return [stato.value for stato in cls]

    @classmethod
    def from_string(cls, value: str) -> 'StatoInstallazione':
        """
        Converte una stringa nello stato corrispondente.

        Args:
            value: Stringa rappresentante lo stato.

        Returns:
            StatoInstallazione corrispondente.

        Raises:
            ValueError: Se lo stato non è valido.
        """
        value = value.strip().lower()  # Rimuove spazi e converte in minuscolo
        for stato in cls:
            if stato.value.lower() == value:
                return stato
        raise ValueError(f"Stato di installazione non valido. Stati ammessi: {', '.join(cls.get_stati_validi())}")


@dataclass
class RisultatoValidazione(Generic[T]):
    valido: bool
    messaggio: str
    valore: Optional[T]

    def __bool__(self) -> bool:
        return self.valido

    def to_tuple(self) -> Tuple[bool, str, Optional[T]]:
        """
        Converte l'oggetto RisultatoValidazione in una tupla.

        Returns:
            Una tupla (valido, messaggio, valore).
        """
        return self.valido, self.messaggio, self.valore


class ValidazioneCampi:
    """
    Classe per la validazione di campi di input.
    """
    TBD = "TBD"

    @staticmethod
    def valida_stato_bobina(valore: str) -> RisultatoValidazione[str]:
        """
        Valida lo stato della bobina. Se il valore è vuoto, imposta lo stato a DISPONIBILE.
        Supporta tutti gli stati incluso OVER per bobine con metri residui negativi.

        Args:
            valore: Valore da validare.

        Returns:
            RisultatoValidazione: Risultato della validazione.
        """
        if ValidazioneCampi._is_empty(valore):
            return RisultatoValidazione(True, "", StatoBobina.DISPONIBILE.value)

        try:
            stato = StatoBobina.from_string(valore)
            return RisultatoValidazione(True, "", stato.value)
        except ValueError as e:
            return RisultatoValidazione(False, str(e), None)

    @staticmethod
    def _is_empty(valore: Union[str, float, int, None]) -> bool:
        """
        Verifica se un valore è vuoto o None.

        Args:
            valore: Valore da verificare.

        Returns:
            True se il valore è vuoto o None, False altrimenti.
        """
        return valore is None or (isinstance(valore, str) and not valore.strip())

    @staticmethod
    def valida_numero(valore: Union[str, float, int], campo: str) -> RisultatoValidazione[float]:
        """
        Valida che il valore sia un numero con regole specifiche.

        Args:
            valore: Valore da validare
            campo: Nome del campo (per messaggi di errore)

        Returns:
            RisultatoValidazione: Risultato della validazione
        """
        try:
            # Gestione campi vuoti
            if ValidazioneCampi._is_empty(valore):
                if campo in ["Numero conduttori", "Sezione", "Metri teorici"]:
                    return RisultatoValidazione(True, "", "0")
                return RisultatoValidazione(False, f"⚠️ {campo} non può essere vuoto", None)

            # Normalizzazione input se è stringa
            if isinstance(valore, str):
                valore = valore.strip().replace(',', '.')
                if valore == '.':
                    return RisultatoValidazione(False, f"⚠️ {campo} non valido", None)

            # Conversione
            numero = float(valore)

            # Validazione numero negativo
            if numero < 0:
                return RisultatoValidazione(False, f"⚠️ {campo} non può essere negativo", None)

            # Validazione limiti specifici
            if campo == "Numero conduttori" and numero > 24:
                print(f"\n⚠️ ATTENZIONE: Il numero di conduttori ({numero}) supera il limite standard di 24")
                conferma = input("Sei sicuro di voler procedere? (s/n): ").strip().lower()
                if conferma != 's':
                    return RisultatoValidazione(False, "Inserimento annullato dall'utente", None)

            if campo == "Sezione" and numero > 1000:
                print(f"\n⚠️ ATTENZIONE: La sezione ({numero}) supera il limite standard di 1000")
                conferma = input("Sei sicuro di voler procedere? (s/n): ").strip().lower()
                if conferma != 's':
                    return RisultatoValidazione(False, "Inserimento annullato dall'utente", None)

            return RisultatoValidazione(True, "", str(numero))

        except ValueError:
            while True:
                print(f"\n⚠️ Il valore inserito per {campo} non è un numero valido")
                scelta = input("Vuoi inserire un nuovo valore? (s/n): ").strip().lower()
                if scelta == 'n':
                    return RisultatoValidazione(False, "Inserimento annullato dall'utente", None)
                elif scelta == 's':
                    nuovo_valore = input(f"Inserisci un nuovo valore per {campo}: ").strip()
                    try:
                        numero = float(nuovo_valore)
                        return RisultatoValidazione(True, "", str(numero))
                    except ValueError:
                        continue
                else:
                    print("\n⚠️ Scelta non valida. Inserisci 's' per riprovare o 'n' per annullare")

    @staticmethod
    def valida_stato_installazione(valore: str) -> RisultatoValidazione[str]:
        """
        Valida lo stato di installazione.

        Args:
            valore: Valore da validare.

        Returns:
            RisultatoValidazione: Risultato della validazione.
        """
        if ValidazioneCampi._is_empty(valore):
            return RisultatoValidazione(True, "", ValidazioneCampi.TBD)

        try:
            # Cerca l'enum corrispondente
            stato = StatoInstallazione.from_string(valore)
            return RisultatoValidazione(True, "", stato.value)
        except ValueError as e:
            return RisultatoValidazione(False, str(e), None)

    @staticmethod
    def valida_campo_testo(valore: str) -> RisultatoValidazione[str]:
        """
        Validazione per campi di testo come la sezione del cavo.
        Accetta qualsiasi testo non vuoto, senza validazione specifica.

        Args:
            valore: Valore da validare.

        Returns:
            RisultatoValidazione: Risultato della validazione.
        """
        if ValidazioneCampi._is_empty(valore):
            return RisultatoValidazione(False, "Il campo non può essere vuoto", None)

        return RisultatoValidazione(True, "Campo valido", str(valore).strip())

    @staticmethod
    def valida_campo_base(valore: str) -> RisultatoValidazione[str]:
        """
        Validazione base per campi di testo.
        Se il campo è vuoto, restituisce TBD.

        Args:
            valore: Valore da validare.

        Returns:
            RisultatoValidazione: Risultato della validazione.
        """
        if ValidazioneCampi._is_empty(valore):
            return RisultatoValidazione(True, "Campo vuoto", ValidazioneCampi.TBD)
        return RisultatoValidazione(True, "Campo valido", str(valore).strip())

    @staticmethod
    def valida_metri_teorici(valore: Union[str, float, int]) -> RisultatoValidazione[float]:
        """
        Valida i metri teorici con regole specifiche.

        Args:
            valore: Valore da validare.

        Returns:
            RisultatoValidazione: Risultato della validazione.
        """
        try:
            if ValidazioneCampi._is_empty(valore):
                return RisultatoValidazione(False, "I metri teorici sono obbligatori", None)

            val = float(str(valore).replace(',', '.'))

            if val <= 0:
                return RisultatoValidazione(False, "I metri teorici devono essere maggiori di zero", None)

            if val > 100000:  # 100km come limite ragionevole
                return RisultatoValidazione(False, "I metri teorici non possono superare 100.000", None)

            return RisultatoValidazione(True, "Valore valido", val)

        except ValueError:
            return RisultatoValidazione(False, "Il valore deve essere un numero valido", None)

    @staticmethod
    def valida_sh(valore: str) -> RisultatoValidazione[str]:
        """
        Valida il campo SH (schermato).
        Accetta 'S', 's', 'SI', 'si', 'Y', 'y', 'YES', 'yes' come valori positivi.
        Accetta 'N', 'n', 'NO', 'no' come valori negativi.
        Se il valore è vuoto, restituisce 'N' come valore di default.

        Args:
            valore: Valore da validare.

        Returns:
            RisultatoValidazione: Risultato della validazione.
        """
        if ValidazioneCampi._is_empty(valore):
            return RisultatoValidazione(True, "", "N")

        valore = valore.strip().upper()

        # Valori positivi
        if valore in ['S', 'SI', 'Y', 'YES']:
            return RisultatoValidazione(True, "", "S")

        # Valori negativi
        if valore in ['N', 'NO']:
            return RisultatoValidazione(True, "", "N")

        return RisultatoValidazione(False, "Valore non valido per SH. Inserire 'S' o 'N'.", None)

    @staticmethod
    def valida_metratura_reale(valore: Union[str, float, int], metri_teorici: float) -> RisultatoValidazione[float]:
        """
        Valida la metratura reale con regole specifiche.
        Se supera del 10% i metri teorici, chiede conferma all'utente.

        Args:
            valore: Valore da validare.
            metri_teorici: Metri teorici di riferimento.

        Returns:
            RisultatoValidazione: Risultato della validazione.
        """
        try:
            if ValidazioneCampi._is_empty(valore):
                return RisultatoValidazione(True, "", 0.0)

            val = converti_float(valore)

            if val < 0:
                return RisultatoValidazione(False, "La metratura reale non può essere negativa", None)

            if val > metri_teorici * 1.1:  # 10% di tolleranza
                print(
                    f"\n⚠️ ATTENZIONE: La metratura reale ({val}m) supera del {((val / metri_teorici) - 1) * 100:.1f}% i metri teorici ({metri_teorici}m)")
                conferma = input("Sei sicuro di voler procedere? (s/n): ").strip().lower()
                if conferma != 's':
                    return RisultatoValidazione(False, "Inserimento annullato dall'utente", None)
                # Se l'utente conferma, procediamo con il valore
                return RisultatoValidazione(True, "", val)

            return RisultatoValidazione(True, "", val)

        except ValueError:
            return RisultatoValidazione(False, "Inserire un numero valido per la metratura reale", None)




def converti_float(valore: Union[str, float, int, None]) -> float:
    """
    Converte un valore in float, gestendo vari formati e casi particolari.

    Args:
        valore: Il valore da convertire (può essere None, int, float, str).

    Returns:
        float: Il valore convertito in float. Se la conversione fallisce, restituisce 0.0.
    """
    if valore is None or (isinstance(valore, str) and not valore.strip()):
        logging.debug("Valore None o stringa vuota convertito in 0.0")
        return 0.0

    if isinstance(valore, (int, float)):
        return float(valore)

    if isinstance(valore, str):
        valore = valore.strip().replace(',', '.')
        try:
            return float(valore)
        except ValueError:
            logging.warning(f"❌ Errore conversione float: '{valore}' non è un numero valido.")
            return 0.0

    logging.warning(f"❌ Tipo di valore non supportato: {type(valore)}")
    return 0.0


def safe_get(obj, key, default='TBD'):
    """
    Funzione di accesso sicuro per dizionari, oggetti Row o altri oggetti con accesso a chiave.
    Restituisce il valore predefinito 'TBD' per campi vuoti o mancanti.

    Args:
        obj: Oggetto da cui recuperare il valore (dict, Row, ecc.)
        key: Chiave da cercare
        default: Valore predefinito (default: 'TBD')

    Returns:
        Il valore associato alla chiave o il valore predefinito
    """
    try:
        if hasattr(obj, 'keys') and callable(obj.keys):
            return obj[key] if key in obj.keys() else default
        elif hasattr(obj, '__getitem__'):
            try:
                return obj[key]
            except (KeyError, IndexError, TypeError):
                return default
        elif hasattr(obj, key):
            return getattr(obj, key)
        return default
    except (KeyError, TypeError, AttributeError, IndexError):
        return default


def validatore_campo(campo: str, valore: Union[str, float, int]) -> Tuple[bool, str, Optional[str]]:
    """
    Valida i campi in base al loro tipo e regole specifiche.
    Garantisce valori predefiniti coerenti per tutti i campi.
    """
    # Definizione dei campi che richiedono validazione speciale
    validazioni_speciali = {
        'metri_teorici': lambda v: ValidazioneCampi.valida_numero(v, "Metri teorici"),
        'metratura_reale': lambda v: ValidazioneCampi.valida_numero(v, "Metratura reale"),
        'stato_installazione': ValidazioneCampi.valida_stato_installazione,
        'numero_conduttori': ValidazioneCampi.valida_campo_testo,  # Accetta qualsiasi testo per il numero di conduttori
        'sezione': ValidazioneCampi.valida_campo_testo,  # Accetta qualsiasi testo per la sezione
        'sh': lambda v: ValidazioneCampi.valida_sh(v),  # Validazione specifica per SH
    }

    # Campi che devono avere "TBD" come valore predefinito quando vuoti
    campi_con_tbd = [
        'sistema', 'utility', 'colore_cavo', 'tipologia',
        'partenza', 'utenza_partenza', 'arrivo', 'utenza_arrivo',
        'descrizione_utenza_partenza', 'descrizione_utenza_arrivo'
    ]

    # Se il campo richiede validazione speciale, usala
    if campo in validazioni_speciali:
        risultato = validazioni_speciali[campo](valore)
        return risultato.to_tuple()

    # Se il campo deve avere TBD come valore predefinito
    if campo in campi_con_tbd:
        if ValidazioneCampi._is_empty(valore):
            return True, "", ValidazioneCampi.TBD
        return True, "", str(valore).strip()

    # Per tutti gli altri campi, usa la validazione base
    risultato = ValidazioneCampi.valida_campo_base(valore)
    return risultato.to_tuple()
