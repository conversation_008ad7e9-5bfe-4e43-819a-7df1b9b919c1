#!/usr/bin/env python
# -*- coding: utf-8 -*-

import psycopg2

# Parametri di connessione
conn_params = {
    'host': 'localhost',
    'port': '5432',
    'dbname': 'cantieri',
    'user': 'postgres',
    'password': 'Taranto'
}

try:
    # Connessione al database
    print("Connessione al database...")
    conn = psycopg2.connect(**conn_params)
    cursor = conn.cursor()
    
    # Esegui la query per ottenere i cavi
    cursor.execute("""
        SELECT id_cavo, id_cantiere, stato_installazione, id_bobina 
        FROM cavi 
        ORDER BY id_cantiere, id_cavo
        LIMIT 50
    """)
    
    rows = cursor.fetchall()
    
    # Stampa i risultati
    print("\nRisultati della query:")
    print("ID_CAVO | ID_CANTIERE | STATO_INSTALLAZIONE | ID_BOBINA")
    print("-" * 80)
    
    for row in rows:
        id_cavo = row[0]
        id_cantiere = row[1]
        stato_installazione = row[2]
        id_bobina = row[3] if row[3] is not None else "NULL"
        
        print(f"{id_cavo} | {id_cantiere} | {stato_installazione} | {id_bobina}")
    
    # Esegui una query per contare i cavi per stato e id_bobina
    cursor.execute("""
        SELECT 
            stato_installazione, 
            CASE 
                WHEN id_bobina IS NULL THEN 'NULL'
                WHEN id_bobina = 'BOBINA_VUOTA' THEN 'BOBINA_VUOTA'
                ELSE 'ALTRA_BOBINA'
            END as tipo_bobina,
            COUNT(*) as conteggio
        FROM cavi
        GROUP BY stato_installazione, tipo_bobina
        ORDER BY stato_installazione, tipo_bobina
    """)
    
    stats = cursor.fetchall()
    
    # Stampa le statistiche
    print("\nStatistiche per stato e tipo di bobina:")
    print("STATO_INSTALLAZIONE | TIPO_BOBINA | CONTEGGIO")
    print("-" * 60)
    
    for stat in stats:
        stato = stat[0]
        tipo_bobina = stat[1]
        conteggio = stat[2]
        
        print(f"{stato} | {tipo_bobina} | {conteggio}")
    
except Exception as e:
    print(f"Errore durante l'interrogazione del database: {e}")
finally:
    # Chiudi la connessione
    if 'conn' in locals() and conn:
        cursor.close()
        conn.close()
        print("\nConnessione al database chiusa.")
