from sqlalchemy import create_engine, text
import bcrypt

# Configurazione del database
DB_HOST = "localhost"
DB_PORT = "5432"
DB_NAME = "cantieri"
DB_USER = "postgres"
DB_PASSWORD = "Taranto"

# Stringa di connessione per SQLAlchemy
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# Crea l'engine SQLAlchemy
engine = create_engine(DATABASE_URL)

# Funzione per generare l'hash della password
def get_password_hash(password: str) -> str:
    salt = bcrypt.gensalt(rounds=12)
    hashed_password = bcrypt.hashpw(password.encode(), salt)
    return hashed_password.decode()

# Nuova password per l'admin
new_password = "admin"
hashed_password = get_password_hash(new_password)

print(f"Nuova password hash: {hashed_password}")

# Aggiorna la password dell'admin
with engine.connect() as connection:
    # Verifica se l'utente admin esiste
    result = connection.execute(text("SELECT * FROM utenti WHERE username = 'admin'"))
    admin = result.fetchone()
    
    if admin:
        print(f"Utente admin trovato con ID: {admin.id_utente}")
        # Aggiorna la password
        connection.execute(
            text("UPDATE utenti SET password = :password WHERE username = 'admin'"),
            {"password": hashed_password}
        )
        connection.commit()
        print("Password dell'admin aggiornata con successo")
    else:
        print("Utente admin non trovato")
        # Crea l'utente admin se non esiste
        connection.execute(
            text("""
                INSERT INTO utenti (username, password, ruolo, abilitato, nome, cognome, email)
                VALUES ('admin', :password, 'owner', TRUE, 'Admin', 'User', '<EMAIL>')
            """),
            {"password": hashed_password}
        )
        connection.commit()
        print("Utente admin creato con successo")
