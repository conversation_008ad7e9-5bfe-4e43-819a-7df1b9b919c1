#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script di migrazione per aggiungere il record BOBINA_VUOTA alla tabella parco_cavi
e aggiornare i cavi che hanno id_bobina=NULL a id_bobina='BOBINA_VUOTA'.
"""

import sys
import os
import logging
from datetime import datetime

# Aggiungi la directory principale al path per importare i moduli
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Importa il modulo database_pg
from modules.database_pg import DatabaseManager

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def migrate_bobina_vuota():
    """
    1. Aggiunge il record BOBINA_VUOTA alla tabella parco_cavi se non esiste già
    2. Aggiorna tutti i cavi con id_bobina=NULL a id_bobina='BOBINA_VUOTA' se hanno metratura_reale > 0
    """
    db = DatabaseManager()
    
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # 1. Verifica se il record BOBINA_VUOTA esiste già
            cursor.execute("SELECT id_bobina FROM parco_cavi WHERE id_bobina = 'BOBINA_VUOTA'")
            if cursor.fetchone():
                logging.info("✅ Il record BOBINA_VUOTA esiste già nella tabella parco_cavi")
            else:
                # Inserisci il record BOBINA_VUOTA
                cursor.execute("""
                    INSERT INTO parco_cavi (
                        id_bobina, numero_bobina, utility, tipologia, 
                        n_conduttori, sezione, metri_totali, metri_residui, 
                        stato_bobina, ubicazione_bobina, fornitore, n_ddt, data_ddt, configurazione, id_cantiere
                    ) VALUES (
                        'BOBINA_VUOTA', 'VUOTA', 'N/A', 'N/A', 
                        '0', '0', 0, 0, 
                        'Terminata', 'N/A', 'N/A', 'N/A', NULL, 'n', NULL
                    )
                """)
                logging.info("✅ Record BOBINA_VUOTA aggiunto con successo alla tabella parco_cavi")
            
            # 2. Aggiorna i cavi con id_bobina=NULL a id_bobina='BOBINA_VUOTA' se hanno metratura_reale > 0
            cursor.execute("""
                UPDATE cavi
                SET id_bobina = 'BOBINA_VUOTA'
                WHERE id_bobina IS NULL AND metratura_reale > 0
            """)
            
            rows_updated = cursor.rowcount
            logging.info(f"✅ {rows_updated} cavi aggiornati da id_bobina=NULL a id_bobina='BOBINA_VUOTA'")
            
            conn.commit()
            logging.info("✅ Migrazione completata con successo")
            return True
            
    except Exception as e:
        logging.error(f"❌ Errore durante la migrazione: {str(e)}")
        return False

if __name__ == "__main__":
    migrate_bobina_vuota()
