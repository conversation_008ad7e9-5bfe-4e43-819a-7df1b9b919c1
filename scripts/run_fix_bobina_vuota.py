#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per eseguire la correzione dei cavi non posati con id_bobina errato.
"""

import sys
import os
import logging

# Aggiungi la directory principale al path per importare i moduli
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Importa lo script di correzione
from scripts.fix_bobina_vuota import fix_bobina_vuota

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

if __name__ == "__main__":
    print("Avvio della correzione dei cavi non posati con id_bobina errato...")
    result = fix_bobina_vuota()
    
    if result:
        print("✅ Correzione completata con successo!")
    else:
        print("❌ Si è verificato un errore durante la correzione.")
