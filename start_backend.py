import os
import sys
import subprocess
import time

# Imposta la directory corrente alla directory del backend
os.chdir(os.path.join(os.getcwd(), "webapp", "backend"))

# Avvia il server FastAPI sulla porta 8003
print("Avvio del server FastAPI sulla porta 8003...")
process = subprocess.Popen(
    ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8003"],
    stdout=subprocess.PIPE,
    stderr=subprocess.PIPE,
    text=True
)

# Attendi un po' per permettere al server di avviarsi
time.sleep(5)

# Verifica se il processo è ancora in esecuzione
if process.poll() is None:
    print("Server avviato con successo sulla porta 8003")
    print(f"PID: {process.pid}")
    
    # Mantieni lo script in esecuzione
    try:
        while True:
            line = process.stdout.readline()
            if line:
                print(line.strip())
            if process.poll() is not None:
                break
            time.sleep(0.1)
    except KeyboardInterrupt:
        print("Interruzione richiesta dall'utente")
        process.terminate()
        process.wait()
        print("Server terminato")
else:
    print("Errore durante l'avvio del server")
    stdout, stderr = process.communicate()
    print("STDOUT:", stdout)
    print("STDERR:", stderr)
