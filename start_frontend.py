import os
import sys
import subprocess
import time

# Imposta la directory corrente alla directory del frontend
os.chdir(os.path.join(os.getcwd(), "webapp", "frontend"))

# Avvia il server React
print("Avvio del server React...")
process = subprocess.Popen(
    ["npm", "start"],
    stdout=subprocess.PIPE,
    stderr=subprocess.PIPE,
    text=True
)

# Attendi un po' per permettere al server di avviarsi
time.sleep(5)

# Verifica se il processo è ancora in esecuzione
if process.poll() is None:
    print("Server React avviato con successo")
    print(f"PID: {process.pid}")
    
    # <PERSON><PERSON><PERSON> lo script in esecuzione
    try:
        while True:
            line = process.stdout.readline()
            if line:
                print(line.strip())
            if process.poll() is not None:
                break
            time.sleep(0.1)
    except KeyboardInterrupt:
        print("Interruzione richiesta dall'utente")
        process.terminate()
        process.wait()
        print("Server terminato")
else:
    print("Errore durante l'avvio del server React")
    stdout, stderr = process.communicate()
    print("STDOUT:", stdout)
    print("STDERR:", stderr)
