import requests
import json

# URL dell'API
BASE_URL = "http://localhost:8003/api"

# Credenziali di accesso
credentials = {
    "username": "admin",
    "password": "admin"
}

# Ottieni un token di accesso
def get_token():
    print("Tentativo di autenticazione con:", credentials)
    # Utilizziamo OAuth2PasswordRequestForm
    form_data = {
        "username": credentials["username"],
        "password": credentials["password"]
    }
    response = requests.post(f"{BASE_URL}/auth/login", data=form_data)
    print("Headers della risposta:", response.headers)
    if response.status_code == 200:
        return response.json()["access_token"]
    else:
        print(f"Errore di autenticazione: {response.status_code}")
        print(response.text)
        return None

# Test per ottenere le bobine di un cantiere
def test_get_bobine(token, cantiere_id):
    headers = {"Authorization": f"Bearer {token}"}
    print(f"Richiesta GET a {BASE_URL}/parco-cavi/{cantiere_id} con token: {token[:10]}...")
    
    try:
        response = requests.get(f"{BASE_URL}/parco-cavi/{cantiere_id}", headers=headers)
        
        print(f"Status code: {response.status_code}")
        print(f"Headers risposta: {response.headers}")
        
        if response.status_code == 200:
            bobine = response.json()
            print(f"Numero di bobine trovate: {len(bobine)}")
            if len(bobine) > 0:
                print("Prima bobina:")
                print(json.dumps(bobine[0], indent=2))
            else:
                print("Nessuna bobina trovata per questo cantiere")
        else:
            print("Errore nella richiesta:")
            print(response.text)
    except Exception as e:
        print(f"Eccezione durante la richiesta: {str(e)}")

# Esegui il test
if __name__ == "__main__":
    token = get_token()
    if token:
        print("Token ottenuto con successo")
        # Prova con l'ID del cantiere 1 (modifica se necessario)
        test_get_bobine(token, 1)
    else:
        print("Impossibile ottenere il token")
