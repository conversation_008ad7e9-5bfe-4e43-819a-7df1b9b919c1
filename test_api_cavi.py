#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script di test per verificare le API di aggiunta e modifica cavo.
Questo script simula le richieste HTTP che il frontend invia al backend.
"""

import sys
import os
import logging
import time
import random
import string
import json
import requests
from datetime import datetime

# Configurazione del logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("test_api_cavi.log"),
        logging.StreamHandler()
    ]
)

# Configurazione dell'API
API_URL = 'http://localhost:8001/api'
CANTIERE_ID = 1  # Modificare con un ID cantiere esistente

# Token di autenticazione (sostituire con un token valido)
# Per ottenere un token, effettuare il login tramite l'API /api/auth/login
TOKEN = None  # Verrà impostato durante l'autenticazione

def login():
    """Effettua il login e ottiene un token di autenticazione."""
    try:
        # Usa form data invece di JSON per il login
        response = requests.post(
            f"{API_URL}/auth/login",
            data={"username": "admin", "password": "admin"},
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )

        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            if token:
                logging.info("Login effettuato con successo")
                return token
            else:
                logging.error("Token non trovato nella risposta")
                return None
        else:
            logging.error(f"Errore durante il login: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        logging.error(f"Errore durante il login: {e}")
        return None

def generate_test_id():
    """Genera un ID casuale per il cavo di test."""
    random_suffix = ''.join(random.choices(string.ascii_uppercase + string.digits, k=5))
    return f"TEST_API_{random_suffix}"

def check_cavo_exists(id_cavo, cantiere_id, token):
    """Verifica se un cavo esiste tramite API."""
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(
            f"{API_URL}/cavi/{cantiere_id}/check/{id_cavo}",
            headers=headers
        )

        if response.status_code == 200:
            data = response.json()
            return data.get('exists', False)
        else:
            logging.error(f"Errore durante la verifica del cavo: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        logging.error(f"Errore durante la verifica del cavo: {e}")
        return False

def test_create_cavo(cantiere_id, token):
    """Testa la creazione di un cavo tramite API."""
    id_cavo = generate_test_id()
    logging.info(f"Test creazione cavo con ID: {id_cavo}")

    # Verifica che il cavo non esista già
    if check_cavo_exists(id_cavo, cantiere_id, token):
        logging.warning(f"Il cavo {id_cavo} esiste già. Generazione nuovo ID.")
        id_cavo = generate_test_id()

    # Dati del cavo da creare
    cavo_data = {
        "id_cavo": id_cavo,
        "revisione_ufficiale": "00",
        "sistema": "TEST_API",
        "utility": "TEST",
        "colore_cavo": "NERO",
        "tipologia": "TEST",
        "n_conduttori": "3",
        "sezione": "1.5",
        "sh": "-",
        "ubicazione_partenza": "TEST",
        "utenza_partenza": "TEST",
        "descrizione_utenza_partenza": "TEST",
        "ubicazione_arrivo": "TEST",
        "utenza_arrivo": "TEST",
        "descrizione_utenza_arrivo": "TEST",
        "metri_teorici": 100,
        "metratura_reale": 0,
        "stato_installazione": "Da installare"
    }

    try:
        # Prima richiesta - dovrebbe funzionare
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        logging.info(f"Invio richiesta POST a {API_URL}/cavi/{cantiere_id}")
        logging.info(f"Dati: {json.dumps(cavo_data, indent=2)}")

        response = requests.post(
            f"{API_URL}/cavi/{cantiere_id}",
            headers=headers,
            json=cavo_data,
            timeout=30  # 30 secondi di timeout
        )

        if response.status_code == 200 or response.status_code == 201:
            logging.info(f"Cavo creato con successo: {response.status_code}")
            logging.info(f"Risposta: {json.dumps(response.json(), indent=2)}")
            return id_cavo
        else:
            logging.error(f"Errore durante la creazione del cavo: {response.status_code}")
            logging.error(f"Risposta: {response.text}")

            # Verifica se il cavo è stato comunque creato
            time.sleep(1)  # Attendi un secondo
            if check_cavo_exists(id_cavo, cantiere_id, token):
                logging.warning(f"Il cavo {id_cavo} risulta creato nonostante l'errore!")
                return id_cavo

            return None
    except requests.exceptions.Timeout:
        logging.error("Timeout durante la richiesta di creazione cavo")

        # Verifica se il cavo è stato comunque creato
        time.sleep(1)  # Attendi un secondo
        if check_cavo_exists(id_cavo, cantiere_id, token):
            logging.warning(f"Il cavo {id_cavo} risulta creato nonostante il timeout!")
            return id_cavo

        return None
    except Exception as e:
        logging.error(f"Errore durante la creazione del cavo: {e}")
        return None

def test_update_cavo(cantiere_id, id_cavo, token):
    """Testa l'aggiornamento di un cavo tramite API."""
    if not id_cavo:
        logging.error("Impossibile testare l'aggiornamento: ID cavo non valido")
        return False

    logging.info(f"Test aggiornamento cavo con ID: {id_cavo}")

    # Dati da aggiornare
    update_data = {
        "sistema": f"SISTEMA_API_MODIFICATO_{random.randint(1, 1000)}"
    }

    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        logging.info(f"Invio richiesta PUT a {API_URL}/cavi/{cantiere_id}/{id_cavo}")
        logging.info(f"Dati: {json.dumps(update_data, indent=2)}")

        response = requests.put(
            f"{API_URL}/cavi/{cantiere_id}/{id_cavo}",
            headers=headers,
            json=update_data,
            timeout=30  # 30 secondi di timeout
        )

        if response.status_code == 200:
            logging.info(f"Cavo aggiornato con successo: {response.status_code}")
            logging.info(f"Risposta: {json.dumps(response.json(), indent=2)}")
            return True
        else:
            logging.error(f"Errore durante l'aggiornamento del cavo: {response.status_code}")
            logging.error(f"Risposta: {response.text}")
            return False
    except requests.exceptions.Timeout:
        logging.error("Timeout durante la richiesta di aggiornamento cavo")
        return False
    except Exception as e:
        logging.error(f"Errore durante l'aggiornamento del cavo: {e}")
        return False

def test_concurrent_requests(cantiere_id, token):
    """Testa richieste concorrenti per verificare problemi di race condition."""
    import threading

    logging.info("Test di richieste concorrenti")

    # Genera un ID base per i cavi
    base_id = f"TEST_CONC_{random.randint(1000, 9999)}"

    # Funzione per thread che crea un cavo
    def create_cavo_thread(thread_id):
        id_cavo = f"{base_id}_{thread_id}"

        # Dati del cavo da creare
        cavo_data = {
            "id_cavo": id_cavo,
            "revisione_ufficiale": "00",
            "sistema": f"TEST_THREAD_{thread_id}",
            "utility": "TEST",
            "colore_cavo": "NERO",
            "tipologia": "TEST",
            "n_conduttori": "3",
            "sezione": "1.5",
            "sh": "-",
            "ubicazione_partenza": "TEST",
            "utenza_partenza": "TEST",
            "descrizione_utenza_partenza": "TEST",
            "ubicazione_arrivo": "TEST",
            "utenza_arrivo": "TEST",
            "descrizione_utenza_arrivo": "TEST",
            "metri_teorici": 100,
            "metratura_reale": 0,
            "stato_installazione": "Da installare"
        }

        try:
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }

            logging.info(f"Thread {thread_id}: Invio richiesta POST per cavo {id_cavo}")

            response = requests.post(
                f"{API_URL}/cavi/{cantiere_id}",
                headers=headers,
                json=cavo_data,
                timeout=30
            )

            if response.status_code == 200 or response.status_code == 201:
                logging.info(f"Thread {thread_id}: Cavo {id_cavo} creato con successo")
            else:
                logging.error(f"Thread {thread_id}: Errore {response.status_code} - {response.text}")

                # Verifica se il cavo è stato comunque creato
                time.sleep(1)
                if check_cavo_exists(id_cavo, cantiere_id, token):
                    logging.warning(f"Thread {thread_id}: Cavo {id_cavo} risulta creato nonostante l'errore!")
        except Exception as e:
            logging.error(f"Thread {thread_id}: Errore - {e}")

    # Crea e avvia i thread
    threads = []
    for i in range(3):  # Crea 3 thread
        t = threading.Thread(target=create_cavo_thread, args=(i,))
        threads.append(t)
        t.start()

    # Attendi che tutti i thread terminino
    for t in threads:
        t.join()

    logging.info("Test di richieste concorrenti completato")

    # Verifica quali cavi sono stati creati
    for i in range(3):
        id_cavo = f"{base_id}_{i}"
        exists = check_cavo_exists(id_cavo, cantiere_id, token)
        logging.info(f"Cavo {id_cavo}: {'Esiste' if exists else 'Non esiste'}")

def main():
    """Funzione principale per eseguire i test."""
    logging.info("=== INIZIO TEST API CAVI ===")

    # Effettua il login
    token = login()
    if not token:
        logging.error("Impossibile procedere senza token di autenticazione")
        return

    # Test creazione cavo
    id_cavo = test_create_cavo(CANTIERE_ID, token)

    # Test aggiornamento cavo
    if id_cavo:
        test_update_cavo(CANTIERE_ID, id_cavo, token)

    # Test richieste concorrenti
    test_concurrent_requests(CANTIERE_ID, token)

    logging.info("=== FINE TEST API CAVI ===")

if __name__ == "__main__":
    main()
