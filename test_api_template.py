#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per testare gli API endpoint per la generazione e l'esportazione di file Excel.
"""

import os
import sys
import requests
import logging
from urllib.parse import urljoin

# Configura il logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Configurazione
API_BASE_URL = "http://localhost:8000"  # Modifica se necessario
ENDPOINTS = {
    "template_cavi": "/api/excel/template-cavi",
    "template_bobine": "/api/excel/template-parco-bobine",
    "export_cavi": "/api/excel/1/export-cavi",  # Sostituisci 1 con un ID cantiere valido
    "export_bobine": "/api/excel/1/export-parco-bobine"  # Sostituisci 1 con un ID cantiere valido
}

def test_api_endpoint(endpoint_name, endpoint_url):
    """
    Testa un API endpoint che restituisce un file Excel.

    Args:
        endpoint_name (str): Nome dell'endpoint per il logging
        endpoint_url (str): URL dell'endpoint da testare

    Returns:
        bool: True se il test è riuscito, False altrimenti
    """
    print(f"🔍 Test dell'API endpoint {endpoint_name}...")

    # Costruisci l'URL completo
    url = urljoin(API_BASE_URL, endpoint_url)

    try:
        # Effettua la richiesta GET all'endpoint
        print(f"📡 Chiamata API a: {url}")
        response = requests.get(url)

        # Verifica che la risposta sia valida
        if response.status_code == 200:
            # Verifica che il Content-Type sia corretto per un file Excel
            content_type = response.headers.get('Content-Type', '')
            if 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' in content_type:
                print(f"✅ Content-Type corretto: {content_type}")

                # Verifica che il Content-Disposition sia impostato per il download
                content_disp = response.headers.get('Content-Disposition', '')
                if 'attachment' in content_disp:
                    print(f"✅ Content-Disposition corretto: {content_disp}")
                else:
                    print(f"⚠️ Content-Disposition non impostato per il download: {content_disp}")

                # Salva il file localmente
                local_file = f"test_{endpoint_name}.xlsx"
                with open(local_file, "wb") as f:
                    f.write(response.content)

                print(f"✅ File scaricato con successo: {local_file}")
                print(f"📊 Verifica manualmente che il file si apra correttamente in Excel.")
                return True
            else:
                print(f"❌ Content-Type non valido per Excel: {content_type}")
        else:
            print(f"❌ Errore nella chiamata API: {response.status_code}")
            print(response.text)

    except Exception as e:
        print(f"❌ Eccezione durante il test: {str(e)}")

    return False

def run_all_tests():
    """
    Esegue i test per tutti gli endpoint configurati.

    Returns:
        bool: True se tutti i test sono riusciti, False altrimenti
    """
    results = {}

    for name, url in ENDPOINTS.items():
        print("\n" + "="*50)
        results[name] = test_api_endpoint(name, url)
        print("="*50)

    # Stampa il riepilogo
    print("\n📋 RIEPILOGO DEI TEST:")
    all_success = True
    for name, success in results.items():
        status = "✅ SUCCESSO" if success else "❌ FALLITO"
        print(f"{name}: {status}")
        all_success = all_success and success

    return all_success

if __name__ == "__main__":
    print("🧪 TEST DEGLI API ENDPOINT PER I FILE EXCEL")
    print("="*50)

    # Se viene specificato un endpoint specifico, testa solo quello
    if len(sys.argv) > 1 and sys.argv[1] in ENDPOINTS:
        endpoint_name = sys.argv[1]
        success = test_api_endpoint(endpoint_name, ENDPOINTS[endpoint_name])
    else:
        # Altrimenti esegui tutti i test
        success = run_all_tests()

    # Esci con codice appropriato
    sys.exit(0 if success else 1)
