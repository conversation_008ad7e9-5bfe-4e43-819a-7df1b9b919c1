#!/usr/bin/env python
# -*- coding: utf-8 -*-

import psycopg2
from psycopg2.extras import RealDictCursor
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import random
import string

# Parametri di connessione
conn_params = {
    'host': 'localhost',
    'port': '5432',
    'dbname': 'cantieri',
    'user': 'postgres',
    'password': 'Taranto'
}

def get_bobine_compatibili(id_cantiere, tipologia, n_conduttori, sezione, visualizza=True):
    """
    Trova bobine compatibili con un cavo specifico.
    Simula la funzione get_bobine_disponibili del modulo cavi.py.
    """
    try:
        # Connessione al database
        conn = psycopg2.connect(**conn_params)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # Query per trovare bobine compatibili
        # Nota: n_conduttori non è più utilizzato per la compatibilità
        query = """
            SELECT id_bobina, numero_bobina, tipologia, n_conduttori, sezione, metri_residui, stato_bobina
            FROM parco_cavi
            WHERE id_cantiere = %s
            AND tipologia = %s
            AND sezione = %s
            AND stato_bobina != 'Terminata'
            AND stato_bobina != 'Over'
            AND metri_residui > 0
            ORDER BY metri_residui DESC
        """

        cursor.execute(query, (id_cantiere, tipologia, sezione))
        bobine = cursor.fetchall()

        if visualizza:
            print(f"\nBobine compatibili per: Tipologia={tipologia}, N_Conduttori={n_conduttori}, Sezione={sezione}")
            print("-" * 80)
            if bobine:
                print(f"{'ID_BOBINA':<20} {'NUMERO':<10} {'TIPOLOGIA':<15} {'N_CONDUTTORI':<15} {'SEZIONE':<15} {'METRI_RESIDUI':<15} {'STATO':<15}")
                print("-" * 80)
                for bobina in bobine:
                    print(f"{bobina['id_bobina']:<20} {bobina['numero_bobina']:<10} {bobina['tipologia']:<15} {bobina['n_conduttori']:<15} {bobina['sezione']:<15} {bobina['metri_residui']:<15} {bobina['stato_bobina']:<15}")
            else:
                print("Nessuna bobina compatibile trovata.")

        cursor.close()
        conn.close()
        return bobine
    except Exception as e:
        print(f"Errore durante la ricerca di bobine compatibili: {e}")
        return []

def test_compatibilita():
    """Testa la compatibilità tra cavi e bobine con diversi tipi di dati."""
    # Test 1: Compatibilità con tipologia e sezione
    print("\nTEST 1: Compatibilità con tipologia e sezione")
    # Nota: n_conduttori non è più utilizzato per la compatibilità
    bobine = get_bobine_compatibili(1, 'TEST', '0', '2x1.5')

    # Test 2: Compatibilità con altra sezione
    print("\nTEST 2: Compatibilità con altra sezione")
    # Nota: n_conduttori non è più utilizzato per la compatibilità
    bobine = get_bobine_compatibili(1, 'TEST', '', '3x2.5+1x1.5')

    # Test 3: Compatibilità con un cavo esistente
    try:
        # Connessione al database
        conn = psycopg2.connect(**conn_params)
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # Trova un cavo esistente
        cursor.execute("SELECT id_cavo, id_cantiere, tipologia, n_conduttori, sezione FROM cavi LIMIT 1")
        cavo = cursor.fetchone()

        if cavo:
            print(f"\nTEST 3: Compatibilità con un cavo esistente: {cavo['id_cavo']}")
            print(f"Dati cavo: Tipologia={cavo['tipologia']}, Sezione={cavo['sezione']}")
            # Nota: n_conduttori non è più utilizzato per la compatibilità

            bobine = get_bobine_compatibili(
                cavo['id_cantiere'],
                cavo['tipologia'],
                cavo['n_conduttori'],  # Questo parametro viene ignorato
                cavo['sezione']
            )
        else:
            print("\nTEST 3: Nessun cavo trovato nel database per il test.")

        cursor.close()
        conn.close()
    except Exception as e:
        print(f"Errore durante il test 3: {e}")

if __name__ == "__main__":
    print("Test di compatibilità tra cavi e bobine...")
    test_compatibilita()
    print("\nTest completati.")
