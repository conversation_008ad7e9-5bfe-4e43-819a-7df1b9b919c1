#!/usr/bin/env python
# test_bobine_compatibili_fix.py - Script per testare la funzionalità di ricerca bobine compatibili

import psycopg2
from psycopg2.extras import RealDictCursor
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import json
import sys

# Parametri di connessione al database
# Nota: questi parametri devono essere modificati in base alla configurazione locale
conn_params = {
    "dbname": "cms",
    "user": "postgres",
    "password": "postgres",  # Modifica con la password corretta
    "host": "localhost",
    "port": "5432"
}

# Verifica se è possibile connettersi al database
def test_connection():
    try:
        conn = psycopg2.connect(**conn_params)
        conn.close()
        print("Connessione al database riuscita!")
        return True
    except Exception as e:
        print(f"Errore di connessione al database: {str(e)}")
        print("\nNota: Questo script richiede una connessione al database PostgreSQL.")
        print("Modifica i parametri di connessione nel codice per adattarli alla tua configurazione.")
        return False

def get_bobine_compatibili(id_cantiere, tipologia, n_conduttori, sezione):
    """
    Trova bobine compatibili con un cavo specifico.
    Simula la funzione get_bobine_disponibili del modulo cavi.py.
    """
    try:
        # Connessione al database
        conn = psycopg2.connect(**conn_params)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # Converti i parametri in stringhe
        tipologia_str = str(tipologia or '')
        n_conduttori_str = str(n_conduttori or '0')
        sezione_str = str(sezione or '0')

        print(f"Parametri dopo conversione: tipologia='{tipologia_str}', n_conduttori='{n_conduttori_str}', sezione='{sezione_str}'")

        # Query per trovare bobine compatibili
        query = """
            SELECT id_bobina, numero_bobina, tipologia, n_conduttori, sezione, metri_residui, stato_bobina
            FROM parco_cavi
            WHERE id_cantiere = %s
              AND tipologia = %s
              AND n_conduttori = %s
              AND sezione = %s
              AND stato_bobina NOT IN ('Over', 'Terminata')
              AND metri_residui > 0
            ORDER BY
              CASE WHEN stato_bobina = 'Disponibile' THEN 0 ELSE 1 END,
              metri_residui DESC,
              id_bobina
        """

        # Esegui la query
        cursor.execute(query, (id_cantiere, tipologia_str, n_conduttori_str, sezione_str))
        bobine = cursor.fetchall()

        # Converti i risultati in una lista di dizionari
        bobine_list = [dict(bobina) for bobina in bobine]

        # Visualizza i risultati
        print(f"\nBobine compatibili trovate: {len(bobine_list)}")
        if bobine_list:
            print("\nDettagli delle bobine compatibili:")
            for bobina in bobine_list:
                print(f"ID: {bobina['id_bobina']}, Tipologia: {bobina['tipologia']}, N_Conduttori: {bobina['n_conduttori']}, Sezione: {bobina['sezione']}, Metri residui: {bobina['metri_residui']}, Stato: {bobina['stato_bobina']}")

        return bobine_list

    except Exception as e:
        print(f"Errore durante la ricerca delle bobine compatibili: {str(e)}")
        return []
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def test_with_params(id_cantiere, tipologia, n_conduttori, sezione):
    """
    Esegue un test con i parametri specificati.
    """
    print("\n" + "=" * 80)
    print(f"TEST CON PARAMETRI: cantiere={id_cantiere}, tipologia='{tipologia}', n_conduttori='{n_conduttori}', sezione='{sezione}'")
    print("=" * 80)

    # Mostra i tipi dei parametri
    print(f"Tipi dei parametri: tipologia={type(tipologia)}, n_conduttori={type(n_conduttori)}, sezione={type(sezione)}")

    # Esegui la ricerca
    bobine = get_bobine_compatibili(id_cantiere, tipologia, n_conduttori, sezione)

    # Mostra il risultato
    print(f"\nRisultato: {len(bobine)} bobine trovate")

    return bobine

def main():
    """
    Funzione principale che esegue i test.
    """
    # Verifica la connessione al database
    if not test_connection():
        print("Impossibile eseguire i test senza una connessione al database.")
        return

    # Verifica se sono stati forniti parametri da riga di comando
    if len(sys.argv) >= 5:
        id_cantiere = int(sys.argv[1])
        tipologia = sys.argv[2]
        n_conduttori = sys.argv[3]
        sezione = sys.argv[4]

        # Esegui il test con i parametri forniti
        test_with_params(id_cantiere, tipologia, n_conduttori, sezione)
    else:
        # Esegui una serie di test predefiniti
        print("Esecuzione di test predefiniti...")

        # Test 1: Parametri come stringhe
        test_with_params(1, "FG16OR16", "3", "2.5")

        # Test 2: Parametri come numeri
        test_with_params(1, "FG16OR16", 3, 2.5)

        # Test 3: Parametri misti
        test_with_params(1, "FG16OR16", "3", 2.5)

        # Test 4: Parametri con valori null
        test_with_params(1, "FG16OR16", None, "2.5")

        # Test 5: Tutti i parametri null
        test_with_params(1, None, None, None)

        print("\nNota: Se non vedi risultati, potrebbe essere necessario inserire dati di test nel database.")
        print("Assicurati di avere bobine nel database con i parametri specificati nei test.")

if __name__ == "__main__":
    main()
