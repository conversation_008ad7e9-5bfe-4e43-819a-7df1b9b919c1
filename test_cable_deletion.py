#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per verificare il comportamento della cancellazione dei cavi e della marcatura come SPARE.
"""

import os
import sys
import argparse
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from datetime import datetime

# Configurazione del database
DATABASE_URL = "postgresql://postgres:postgres@localhost:5432/cms"

def main():
    # Crea la connessione al database
    print(f"Connessione al database: {DATABASE_URL}")
    engine = create_engine(DATABASE_URL)
    Session = sessionmaker(bind=engine)
    session = Session()

    try:
        # Verifica se ci sono cavi con modificato_manualmente = 3
        query1 = """
        SELECT id_cavo, stato_installazione, modificato_manualmente, metratura_reale
        FROM cavi
        WHERE modificato_manualmente = 3
        ORDER BY id_cavo
        LIMIT 10
        """
        result1 = session.execute(text(query1))
        rows1 = result1.fetchall()
        
        print(f"Cavi con modificato_manualmente = 3 (SPARE): {len(rows1)}")
        for row in rows1:
            print(f"  - ID: {row.id_cavo}, Stato: {row.stato_installazione}, Modificato: {row.modificato_manualmente}, Metri: {row.metratura_reale}")
        
        print("\n" + "=" * 80 + "\n")

        # Verifica se ci sono cavi con stato_installazione = 'SPARE'
        query2 = """
        SELECT id_cavo, stato_installazione, modificato_manualmente, metratura_reale
        FROM cavi
        WHERE stato_installazione = 'SPARE'
        ORDER BY id_cavo
        LIMIT 10
        """
        result2 = session.execute(text(query2))
        rows2 = result2.fetchall()
        
        print(f"Cavi con stato_installazione = 'SPARE': {len(rows2)}")
        for row in rows2:
            print(f"  - ID: {row.id_cavo}, Stato: {row.stato_installazione}, Modificato: {row.modificato_manualmente}, Metri: {row.metratura_reale}")
        
        print("\n" + "=" * 80 + "\n")

        # Crea un cavo di test
        test_cavo_id = f"TEST_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        print(f"Creazione cavo di test con ID: {test_cavo_id}")
        
        # Inserisci il cavo di test
        insert_query = """
        INSERT INTO cavi (id_cavo, id_cantiere, revisione_ufficiale, utility, tipologia, n_conduttori, sezione, 
                         ubicazione_partenza, ubicazione_arrivo, metri_teorici, metratura_reale, 
                         stato_installazione, modificato_manualmente, timestamp)
        VALUES (:id_cavo, 1, '00', 'TEST', 'TEST', '1', '1', 'TEST', 'TEST', 10, 0, 'Da installare', 0, :timestamp)
        """
        session.execute(text(insert_query), {"id_cavo": test_cavo_id, "timestamp": datetime.now()})
        session.commit()
        print("Cavo di test creato con successo")
        
        # Verifica che il cavo sia stato creato
        check_query = """
        SELECT id_cavo, stato_installazione, modificato_manualmente
        FROM cavi
        WHERE id_cavo = :id_cavo
        """
        check_result = session.execute(text(check_query), {"id_cavo": test_cavo_id})
        check_row = check_result.fetchone()
        
        if check_row:
            print(f"Cavo di test trovato: ID={check_row.id_cavo}, Stato={check_row.stato_installazione}, Modificato={check_row.modificato_manualmente}")
        else:
            print("Errore: Cavo di test non trovato dopo la creazione")
            return
        
        # Marca il cavo come SPARE
        print("\nMarcatura del cavo come SPARE")
        update_query = """
        UPDATE cavi
        SET stato_installazione = 'SPARE', modificato_manualmente = 3, timestamp = :timestamp
        WHERE id_cavo = :id_cavo
        """
        session.execute(text(update_query), {"id_cavo": test_cavo_id, "timestamp": datetime.now()})
        session.commit()
        
        # Verifica che il cavo sia stato marcato come SPARE
        check_result = session.execute(text(check_query), {"id_cavo": test_cavo_id})
        check_row = check_result.fetchone()
        
        if check_row:
            print(f"Dopo marcatura SPARE: ID={check_row.id_cavo}, Stato={check_row.stato_installazione}, Modificato={check_row.modificato_manualmente}")
        else:
            print("Errore: Cavo di test non trovato dopo la marcatura come SPARE")
            return
        
        # Crea un altro cavo di test per l'eliminazione
        test_cavo_id2 = f"TEST_DEL_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        print(f"\nCreazione secondo cavo di test con ID: {test_cavo_id2}")
        
        session.execute(text(insert_query), {"id_cavo": test_cavo_id2, "timestamp": datetime.now()})
        session.commit()
        
        # Verifica che il secondo cavo sia stato creato
        check_result = session.execute(text(check_query), {"id_cavo": test_cavo_id2})
        check_row = check_result.fetchone()
        
        if check_row:
            print(f"Secondo cavo di test trovato: ID={check_row.id_cavo}, Stato={check_row.stato_installazione}, Modificato={check_row.modificato_manualmente}")
        else:
            print("Errore: Secondo cavo di test non trovato dopo la creazione")
            return
        
        # Elimina il secondo cavo
        print("\nEliminazione del secondo cavo di test")
        delete_query = """
        DELETE FROM cavi
        WHERE id_cavo = :id_cavo
        """
        session.execute(text(delete_query), {"id_cavo": test_cavo_id2})
        session.commit()
        
        # Verifica che il secondo cavo sia stato eliminato
        check_result = session.execute(text(check_query), {"id_cavo": test_cavo_id2})
        check_row = check_result.fetchone()
        
        if check_row:
            print(f"ERRORE: Il secondo cavo di test è ancora presente nel database dopo l'eliminazione")
        else:
            print(f"Secondo cavo di test eliminato con successo")
        
        print("\nTest completati")

    except Exception as e:
        print(f"Errore durante l'esecuzione: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        session.close()

if __name__ == "__main__":
    main()
