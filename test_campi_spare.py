#!/usr/bin/env python3
"""
Test per verificare che i campi n_conduttori e SH siano trattati come spare.
"""

import sys
import os
import tempfile
import pandas as pd
import logging
from pathlib import Path

# Aggiungi il percorso del progetto al Python path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

# Configura il logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_minimal_excel():
    """Crea un file Excel con solo i campi obbligatori (senza n_conduttori e SH)."""
    
    # Dati di test con SOLO i campi obbligatori
    test_data = {
        'id_cavo': ['SPARE_001', 'SPARE_002'],
        'utility': ['Energia', 'Telecom'],
        'tipologia': ['MT', 'BT'],
        'metri_teorici': [100, 150]
        # Nota: NO n_conduttori, NO SH, NO formazione
    }
    
    df = pd.DataFrame(test_data)
    
    # Crea file temporaneo
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
    df.to_excel(temp_file.name, index=False)
    temp_file.close()
    
    print(f"📊 File Excel minimo creato: {temp_file.name}")
    print(f"📋 Colonne: {list(df.columns)}")
    print(f"📈 Righe di dati: {len(df)}")
    
    return temp_file.name

def test_spare_fields():
    """Testa che i campi spare vengano gestiti correttamente."""
    
    try:
        from webapp.backend.api.excel import importa_cavi_da_excel_webapp
        
        # Crea file di test con solo campi obbligatori
        test_file = create_minimal_excel()
        
        # Test importazione
        print("\n🔄 Test importazione con campi spare...")
        result = importa_cavi_da_excel_webapp(
            id_cantiere=1,
            percorso_file=test_file,
            revisione_predefinita="SPARE_TEST"
        )
        
        print(f"\n📊 Risultato:")
        print(f"   Success: {result.get('success', 'N/A')}")
        print(f"   Message: {result.get('message', 'N/A')}")
        
        if result.get('details'):
            details = result['details']
            print(f"   Details:")
            for key, value in details.items():
                print(f"     {key}: {value}")
        
        # Cleanup
        try:
            os.unlink(test_file)
        except:
            pass
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ Errore durante il test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_validation():
    """Testa la validazione con i nuovi campi obbligatori."""
    
    try:
        from modules.excel_manager import valida_colonne_excel
        
        # Test 1: File con solo campi obbligatori
        print("\n🔍 Test validazione campi obbligatori...")
        df_minimal = pd.DataFrame({
            'id_cavo': ['TEST_001'],
            'utility': ['Energia'],
            'tipologia': ['MT'],
            'metri_teorici': [100]
        })
        
        result = valida_colonne_excel(df_minimal)
        if result is not None:
            print("✅ Validazione con campi minimi: PASSATA")
            print(f"   Colonne risultanti: {list(result.columns)}")
            
            # Verifica che n_conduttori e SH siano stati aggiunti come spare
            if 'n_conduttori' in result.columns:
                print(f"   n_conduttori (spare): {result['n_conduttori'].iloc[0]}")
            if 'sh' in result.columns:
                print(f"   sh (spare): {result['sh'].iloc[0]}")
        else:
            print("❌ Validazione con campi minimi: FALLITA")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Errore durante test validazione: {e}")
        return False

def main():
    """Funzione principale."""
    
    print("🚀 TEST CAMPI SPARE (n_conduttori e SH)")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: Validazione
    if test_validation():
        tests_passed += 1
        print("✅ Test validazione: PASSATO")
    else:
        print("❌ Test validazione: FALLITO")
    
    # Test 2: Importazione
    if test_spare_fields():
        tests_passed += 1
        print("✅ Test importazione: PASSATO")
    else:
        print("❌ Test importazione: FALLITO")
    
    # Risultato finale
    print("\n" + "=" * 50)
    print(f"🏁 RISULTATO: {tests_passed}/{total_tests} test passati")
    
    if tests_passed == total_tests:
        print("✅ TUTTI I TEST PASSATI!")
        print("🎉 I campi n_conduttori e SH sono ora correttamente trattati come spare!")
        print("\n📋 CAMPI OBBLIGATORI AGGIORNATI:")
        print("   • id_cavo")
        print("   • utility") 
        print("   • tipologia")
        print("   • metri_teorici")
        print("\n⚠️ CAMPI SPARE (non più obbligatori):")
        print("   • n_conduttori (impostato automaticamente a '0')")
        print("   • sh (impostato automaticamente a 'N')")
        print("   • formazione (ora opzionale)")
        return True
    else:
        print("❌ ALCUNI TEST FALLITI!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
