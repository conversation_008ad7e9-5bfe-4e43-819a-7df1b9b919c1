#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script di test per verificare le funzioni di aggiunta e modifica cavo nel database.
Questo script testa direttamente le operazioni sul database per identificare
eventuali problemi di connessione o transazione.
"""

import sys
import os
import logging
import time
import random
import string
from datetime import datetime
import psycopg2
from psycopg2.extras import RealDictCursor

# Configurazione del logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("test_cavi_db.log"),
        logging.StreamHandler()
    ]
)

# Configurazione del database
DB_HOST = 'localhost'
DB_PORT = '5432'
DB_NAME = 'cantieri'
DB_USER = 'postgres'
DB_PASSWORD = 'Taranto'

# ID del cantiere di test (modificare con un ID esistente)
ID_CANTIERE_TEST = 1

def get_connection(with_dict_cursor=False):
    """Crea e restituisce una connessione al database."""
    try:
        if with_dict_cursor:
            conn = psycopg2.connect(
                host=DB_HOST,
                port=DB_PORT,
                dbname=DB_NAME,
                user=DB_USER,
                password=DB_PASSWORD,
                cursor_factory=RealDictCursor
            )
        else:
            conn = psycopg2.connect(
                host=DB_HOST,
                port=DB_PORT,
                dbname=DB_NAME,
                user=DB_USER,
                password=DB_PASSWORD
            )
        
        # Importante: non impostiamo autocommit=True per testare la gestione delle transazioni
        return conn
    except Exception as e:
        logging.error(f"Errore durante la connessione al database: {e}")
        raise

def generate_test_id():
    """Genera un ID casuale per il cavo di test."""
    random_suffix = ''.join(random.choices(string.ascii_uppercase + string.digits, k=5))
    return f"TEST_{random_suffix}"

def check_cavo_exists(id_cavo, id_cantiere):
    """Verifica se un cavo esiste nel database."""
    try:
        with get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT id_cavo FROM Cavi
                    WHERE id_cavo = %s AND id_cantiere = %s
                """, (id_cavo, id_cantiere))
                return cursor.fetchone() is not None
    except Exception as e:
        logging.error(f"Errore durante la verifica dell'esistenza del cavo: {e}")
        return False

def get_cavo_details(id_cavo, id_cantiere):
    """Ottiene i dettagli di un cavo dal database."""
    try:
        with get_connection(with_dict_cursor=True) as conn:
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM Cavi
                    WHERE id_cavo = %s AND id_cantiere = %s
                """, (id_cavo, id_cantiere))
                return cursor.fetchone()
    except Exception as e:
        logging.error(f"Errore durante il recupero dei dettagli del cavo: {e}")
        return None

def test_aggiungi_cavo():
    """Testa la funzione di aggiunta cavo."""
    id_cavo = generate_test_id()
    logging.info(f"Test aggiunta cavo con ID: {id_cavo}")
    
    # Verifica che il cavo non esista già
    if check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
        logging.warning(f"Il cavo {id_cavo} esiste già nel database. Generazione nuovo ID.")
        id_cavo = generate_test_id()
    
    try:
        with get_connection() as conn:
            with conn.cursor() as cursor:
                # Ottieni la revisione corrente
                cursor.execute("""
                    SELECT MAX(CAST(revisione_ufficiale AS INTEGER)) FROM Cavi
                    WHERE id_cantiere = %s
                """, (ID_CANTIERE_TEST,))
                result = cursor.fetchone()
                revisione_corrente = '00'
                if result and result[0] is not None:
                    revisione_corrente = f"{int(result[0]):02d}"
                
                logging.info(f"Revisione corrente: {revisione_corrente}")
                
                # Prepara i dati del cavo
                try:
                    # Prima prova: inserimento con commit esplicito
                    logging.info("PROVA 1: Inserimento con commit esplicito")
                    cursor.execute("""
                        INSERT INTO Cavi (
                            id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                            tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                            descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                            metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                            stato_installazione, modificato_manualmente, timestamp
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
                        )
                    """, (
                        id_cavo, ID_CANTIERE_TEST, revisione_corrente, 'TEST', 'TEST', 'NERO',
                        'TEST', '3', '1.5', '-', 'TEST', 'TEST', 'TEST',
                        'TEST', 'TEST', 'TEST', 100, 'Da installare'
                    ))
                    conn.commit()
                    logging.info("✅ Cavo aggiunto con successo (Prova 1)")
                except Exception as e:
                    conn.rollback()
                    logging.error(f"❌ Errore durante l'inserimento del cavo (Prova 1): {e}")
                    
                    # Verifica se il cavo è stato comunque inserito
                    if check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
                        logging.warning("⚠️ Il cavo risulta inserito nonostante l'errore!")
                    else:
                        logging.info("Il cavo non è stato inserito, come previsto dopo un errore.")
                    
                    # Seconda prova: inserimento con un nuovo ID
                    id_cavo = generate_test_id()
                    logging.info(f"PROVA 2: Nuovo tentativo con ID: {id_cavo}")
                    try:
                        cursor.execute("""
                            INSERT INTO Cavi (
                                id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                                tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                                descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                                metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                                stato_installazione, modificato_manualmente, timestamp
                            ) VALUES (
                                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
                            )
                        """, (
                            id_cavo, ID_CANTIERE_TEST, revisione_corrente, 'TEST', 'TEST', 'NERO',
                            'TEST', '3', '1.5', '-', 'TEST', 'TEST', 'TEST',
                            'TEST', 'TEST', 'TEST', 100, 'Da installare'
                        ))
                        conn.commit()
                        logging.info("✅ Cavo aggiunto con successo (Prova 2)")
                    except Exception as e:
                        conn.rollback()
                        logging.error(f"❌ Errore durante l'inserimento del cavo (Prova 2): {e}")
                        return None
    except Exception as e:
        logging.error(f"❌ Errore generale durante il test di aggiunta cavo: {e}")
        return None
    
    # Verifica finale
    if check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
        logging.info(f"✅ Verifica finale: il cavo {id_cavo} è stato inserito correttamente")
        return id_cavo
    else:
        logging.error(f"❌ Verifica finale: il cavo {id_cavo} NON è stato inserito")
        return None

def test_modifica_cavo(id_cavo):
    """Testa la funzione di modifica cavo."""
    if not id_cavo:
        logging.error("❌ Impossibile testare la modifica: ID cavo non valido")
        return False
    
    logging.info(f"Test modifica cavo con ID: {id_cavo}")
    
    # Ottieni i dettagli attuali del cavo
    cavo_prima = get_cavo_details(id_cavo, ID_CANTIERE_TEST)
    if not cavo_prima:
        logging.error(f"❌ Impossibile trovare il cavo {id_cavo} per la modifica")
        return False
    
    logging.info(f"Dettagli cavo prima della modifica: {cavo_prima}")
    
    # Modifica un campo del cavo
    nuovo_sistema = f"SISTEMA_MODIFICATO_{random.randint(1, 1000)}"
    
    try:
        with get_connection() as conn:
            with conn.cursor() as cursor:
                try:
                    # Utilizziamo psycopg2.sql per costruire la query in modo sicuro
                    cursor.execute("""
                        UPDATE Cavi
                        SET sistema = %s,
                            modificato_manualmente = 1,
                            timestamp = CURRENT_TIMESTAMP
                        WHERE id_cantiere = %s AND id_cavo = %s
                    """, (nuovo_sistema, ID_CANTIERE_TEST, id_cavo))
                    
                    conn.commit()
                    logging.info(f"✅ Campo 'sistema' modificato a '{nuovo_sistema}'")
                except Exception as e:
                    conn.rollback()
                    logging.error(f"❌ Errore durante la modifica del cavo: {e}")
                    return False
    except Exception as e:
        logging.error(f"❌ Errore generale durante il test di modifica cavo: {e}")
        return False
    
    # Verifica che la modifica sia stata applicata
    time.sleep(1)  # Piccola pausa per assicurarsi che la modifica sia stata salvata
    cavo_dopo = get_cavo_details(id_cavo, ID_CANTIERE_TEST)
    
    if not cavo_dopo:
        logging.error(f"❌ Impossibile trovare il cavo {id_cavo} dopo la modifica")
        return False
    
    logging.info(f"Dettagli cavo dopo la modifica: {cavo_dopo}")
    
    if cavo_dopo['sistema'] == nuovo_sistema:
        logging.info(f"✅ Verifica finale: il campo 'sistema' è stato modificato correttamente a '{nuovo_sistema}'")
        return True
    else:
        logging.error(f"❌ Verifica finale: il campo 'sistema' NON è stato modificato correttamente. Valore attuale: '{cavo_dopo['sistema']}'")
        return False

def test_transazioni_multiple():
    """Testa il comportamento con transazioni multiple."""
    id_cavo = generate_test_id()
    logging.info(f"Test transazioni multiple con ID: {id_cavo}")
    
    # Test 1: Connessione con autocommit=True
    try:
        conn1 = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        conn1.autocommit = True
        
        with conn1.cursor() as cursor:
            cursor.execute("""
                INSERT INTO Cavi (
                    id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                    tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                    descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                    metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                    stato_installazione, modificato_manualmente, timestamp
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
                )
            """, (
                id_cavo, ID_CANTIERE_TEST, '00', 'TEST_AUTO', 'TEST', 'NERO',
                'TEST', '3', '1.5', '-', 'TEST', 'TEST', 'TEST',
                'TEST', 'TEST', 'TEST', 100, 'Da installare'
            ))
            
        logging.info("✅ Inserimento con autocommit=True completato")
    except Exception as e:
        logging.error(f"❌ Errore durante l'inserimento con autocommit=True: {e}")
    finally:
        conn1.close()
    
    # Verifica se il cavo è stato inserito
    if check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
        logging.info(f"✅ Il cavo {id_cavo} è stato inserito con autocommit=True")
    else:
        logging.error(f"❌ Il cavo {id_cavo} NON è stato inserito con autocommit=True")
    
    # Test 2: Connessione con gestione manuale delle transazioni
    id_cavo = generate_test_id()
    try:
        conn2 = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        # Non impostiamo autocommit=True
        
        with conn2.cursor() as cursor:
            cursor.execute("""
                INSERT INTO Cavi (
                    id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                    tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                    descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                    metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                    stato_installazione, modificato_manualmente, timestamp
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
                )
            """, (
                id_cavo, ID_CANTIERE_TEST, '00', 'TEST_MANUAL', 'TEST', 'NERO',
                'TEST', '3', '1.5', '-', 'TEST', 'TEST', 'TEST',
                'TEST', 'TEST', 'TEST', 100, 'Da installare'
            ))
            
            # Commit esplicito
            conn2.commit()
            
        logging.info("✅ Inserimento con commit manuale completato")
    except Exception as e:
        if 'conn2' in locals():
            conn2.rollback()
        logging.error(f"❌ Errore durante l'inserimento con commit manuale: {e}")
    finally:
        if 'conn2' in locals():
            conn2.close()
    
    # Verifica se il cavo è stato inserito
    if check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
        logging.info(f"✅ Il cavo {id_cavo} è stato inserito con commit manuale")
    else:
        logging.error(f"❌ Il cavo {id_cavo} NON è stato inserito con commit manuale")
    
    return True

def main():
    """Funzione principale per eseguire i test."""
    logging.info("=== INIZIO TEST FUNZIONI CAVI ===")
    
    # Test connessione al database
    try:
        with get_connection() as conn:
            logging.info("✅ Connessione al database stabilita con successo")
    except Exception as e:
        logging.error(f"❌ Impossibile connettersi al database: {e}")
        return
    
    # Test transazioni multiple
    test_transazioni_multiple()
    
    # Test aggiunta cavo
    id_cavo = test_aggiungi_cavo()
    
    # Test modifica cavo
    if id_cavo:
        test_modifica_cavo(id_cavo)
    
    logging.info("=== FINE TEST FUNZIONI CAVI ===")

if __name__ == "__main__":
    main()
