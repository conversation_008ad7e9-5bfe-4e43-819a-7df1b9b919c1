#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script di test per la funzione di collegamento cavi.
"""

import psycopg2
import requests
import json
from typing import Dict, Any

# Configurazione database
DB_CONFIG = {
    'host': 'localhost',
    'database': 'cantieri',
    'user': 'postgres',
    'password': 'Taranto',
    'port': 5432
}

# Configurazione API
API_URL = "http://localhost:8001/api"

def get_db_connection():
    """Crea una connessione al database."""
    return psycopg2.connect(**DB_CONFIG)

def check_cavi_installati():
    """Verifica se ci sono cavi installati nel database."""
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT id_cantiere, id_cavo, stato_installazione, collegamenti, 
                           responsabile_partenza, responsabile_arrivo
                    FROM cavi 
                    WHERE stato_installazione = 'Installato'
                    ORDER BY id_cantiere, id_cavo
                    LIMIT 10
                """)
                
                cavi = cursor.fetchall()
                
                print(f"=== CAVI INSTALLATI NEL DATABASE ===")
                print(f"Trovati {len(cavi)} cavi installati")
                
                if cavi:
                    print("\nPrimi 10 cavi installati:")
                    print("CANTIERE | ID_CAVO | STATO | COLLEGAMENTI | RESP_PART | RESP_ARR")
                    print("-" * 80)
                    
                    for cavo in cavi:
                        cantiere_id, id_cavo, stato, collegamenti, resp_part, resp_arr = cavo
                        collegamenti = collegamenti or 0
                        resp_part = resp_part or "-"
                        resp_arr = resp_arr or "-"
                        print(f"{cantiere_id:8} | {id_cavo:15} | {stato:10} | {collegamenti:11} | {resp_part:9} | {resp_arr}")
                
                return cavi
                
    except Exception as e:
        print(f"❌ Errore durante la verifica dei cavi: {e}")
        return []

def create_test_cavo_if_needed():
    """Crea un cavo di test se non ce ne sono di installati."""
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                # Verifica se esiste almeno un cantiere
                cursor.execute("SELECT id_cantiere FROM cantieri LIMIT 1")
                cantiere = cursor.fetchone()
                
                if not cantiere:
                    print("❌ Nessun cantiere trovato nel database")
                    return None
                
                cantiere_id = cantiere[0]
                
                # Crea un cavo di test
                test_cavo_id = "TEST_COLLEGAMENTO_001"
                
                # Verifica se il cavo esiste già
                cursor.execute("""
                    SELECT id_cavo FROM cavi 
                    WHERE id_cantiere = %s AND id_cavo = %s
                """, (cantiere_id, test_cavo_id))
                
                if cursor.fetchone():
                    print(f"✅ Cavo di test {test_cavo_id} già esistente")
                    
                    # Aggiorna lo stato a Installato
                    cursor.execute("""
                        UPDATE cavi 
                        SET stato_installazione = 'Installato', 
                            metratura_reale = 50.0,
                            collegamenti = 0,
                            responsabile_partenza = NULL,
                            responsabile_arrivo = NULL
                        WHERE id_cantiere = %s AND id_cavo = %s
                    """, (cantiere_id, test_cavo_id))
                    conn.commit()
                    
                else:
                    # Inserisci il cavo di test
                    cursor.execute("""
                        INSERT INTO cavi (
                            id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                            tipologia, n_conduttori, sezione, sh, ubicazione_partenza, utenza_partenza,
                            descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                            metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                            stato_installazione, modificato_manualmente, timestamp, collegamenti,
                            responsabile_partenza, responsabile_arrivo
                        ) VALUES (
                            %s, %s, '00', 'TEST', 'TEST', 'NERO',
                            'TEST', '4', '1.5', '-', 'QUADRO_A', 'QA',
                            'Quadro principale A', 'QUADRO_B', 'QB', 'Quadro secondario B',
                            50.0, 50.0, 'test_user', NULL,
                            'Installato', 0, CURRENT_TIMESTAMP, 0,
                            NULL, NULL
                        )
                    """, (test_cavo_id, cantiere_id))
                    conn.commit()
                    print(f"✅ Cavo di test {test_cavo_id} creato nel cantiere {cantiere_id}")
                
                return cantiere_id, test_cavo_id
                
    except Exception as e:
        print(f"❌ Errore durante la creazione del cavo di test: {e}")
        return None

def test_api_get_cavi_installati(cantiere_id: int):
    """Testa l'endpoint per ottenere i cavi installati."""
    try:
        print(f"\n=== TEST API GET CAVI INSTALLATI ===")
        print(f"Testando endpoint: GET /cavi/{cantiere_id}/installati")
        
        # Nota: per ora testiamo senza autenticazione
        response = requests.get(f"{API_URL}/cavi/{cantiere_id}/installati")
        
        print(f"Status code: {response.status_code}")
        
        if response.status_code == 200:
            cavi = response.json()
            print(f"✅ Ricevuti {len(cavi)} cavi installati")
            
            if cavi:
                print("\nPrimi 3 cavi:")
                for i, cavo in enumerate(cavi[:3]):
                    print(f"  {i+1}. ID: {cavo['id_cavo']}, Collegamenti: {cavo.get('collegamenti', 0)}")
            
            return cavi
        else:
            print(f"❌ Errore API: {response.status_code}")
            print(f"Risposta: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Errore durante il test API: {e}")
        return None

def test_api_collegamento(cantiere_id: int, cavo_id: str):
    """Testa l'endpoint per collegare un cavo."""
    try:
        print(f"\n=== TEST API COLLEGAMENTO ===")
        print(f"Testando collegamento cavo {cavo_id} nel cantiere {cantiere_id}")
        
        # Test collegamento lato partenza
        data = {
            "lato": "partenza",
            "responsabile": "test_user"
        }
        
        print(f"Inviando richiesta POST: {data}")
        response = requests.post(
            f"{API_URL}/cavi/{cantiere_id}/{cavo_id}/collegamento",
            json=data
        )
        
        print(f"Status code: {response.status_code}")
        
        if response.status_code == 200:
            cavo_aggiornato = response.json()
            print(f"✅ Collegamento riuscito!")
            print(f"Collegamenti: {cavo_aggiornato.get('collegamenti', 0)}")
            print(f"Responsabile partenza: {cavo_aggiornato.get('responsabile_partenza', 'N/A')}")
            return True
        else:
            print(f"❌ Errore API: {response.status_code}")
            print(f"Risposta: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Errore durante il test collegamento: {e}")
        return False

def main():
    """Funzione principale di test."""
    print("=== TEST SISTEMA COLLEGAMENTI CAVI ===\n")
    
    # 1. Verifica cavi installati nel database
    cavi_installati = check_cavi_installati()
    
    # 2. Se non ci sono cavi installati, crea un cavo di test
    if not cavi_installati:
        print("\n❌ Nessun cavo installato trovato. Creazione cavo di test...")
        result = create_test_cavo_if_needed()
        if not result:
            print("❌ Impossibile creare cavo di test")
            return
        cantiere_id, cavo_id = result
    else:
        # Usa il primo cavo installato trovato
        cantiere_id, cavo_id = cavi_installati[0][0], cavi_installati[0][1]
    
    print(f"\n🎯 Usando cantiere {cantiere_id}, cavo {cavo_id}")
    
    # 3. Testa l'API per ottenere i cavi installati
    cavi_api = test_api_get_cavi_installati(cantiere_id)
    
    # 4. Testa l'API per collegare un cavo
    if cavi_api:
        test_api_collegamento(cantiere_id, cavo_id)
    
    print("\n=== FINE TEST ===")

if __name__ == "__main__":
    main()
