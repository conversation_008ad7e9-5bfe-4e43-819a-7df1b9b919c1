#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test diretto delle funzioni di collegamento cavi senza passare per l'API.
"""

import sys
import os

# Aggiungi il percorso del backend al PYTHONPATH
sys.path.append(os.path.join(os.path.dirname(__file__), 'webapp', 'backend'))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from webapp.backend.config import settings
from webapp.backend.modules.collegamenti_webapp import (
    get_cavi_installati, 
    collega_cavo_webapp, 
    scollega_cavo_webapp
)

def test_collegamenti_direct():
    """Test diretto delle funzioni di collegamento."""
    print("=== TEST DIRETTO FUNZIONI COLLEGAMENTO ===\n")
    
    # Crea la connessione al database
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 1. Test get_cavi_installati
        print("1. Test get_cavi_installati...")
        cantiere_id = 1  # Usa il cantiere 1 che sappiamo avere cavi
        
        cavi = get_cavi_installati(db, cantiere_id)
        print(f"✅ Trovati {len(cavi)} cavi installati nel cantiere {cantiere_id}")
        
        if not cavi:
            print("❌ Nessun cavo installato trovato. Test interrotto.")
            return
        
        # Usa il primo cavo per i test
        cavo_test = cavi[0]
        print(f"🎯 Usando cavo: {cavo_test.id_cavo}")
        print(f"   Stato collegamenti iniziale: {cavo_test.collegamenti}")
        print(f"   Responsabile partenza: {cavo_test.responsabile_partenza}")
        print(f"   Responsabile arrivo: {cavo_test.responsabile_arrivo}")
        
        # 2. Test collegamento lato partenza
        print(f"\n2. Test collegamento lato partenza...")
        try:
            cavo_aggiornato = collega_cavo_webapp(
                db, cantiere_id, cavo_test.id_cavo, "partenza", "test_user"
            )
            print(f"✅ Collegamento partenza riuscito!")
            print(f"   Nuovi collegamenti: {cavo_aggiornato.collegamenti}")
            print(f"   Responsabile partenza: {cavo_aggiornato.responsabile_partenza}")
            
            # Verifica che il bit 1 sia impostato
            if cavo_aggiornato.collegamenti & 1:
                print("✅ Bit partenza (1) correttamente impostato")
            else:
                print("❌ Bit partenza (1) NON impostato")
                
        except Exception as e:
            print(f"❌ Errore collegamento partenza: {e}")
        
        # 3. Test collegamento lato arrivo
        print(f"\n3. Test collegamento lato arrivo...")
        try:
            cavo_aggiornato = collega_cavo_webapp(
                db, cantiere_id, cavo_test.id_cavo, "arrivo", "test_user_2"
            )
            print(f"✅ Collegamento arrivo riuscito!")
            print(f"   Nuovi collegamenti: {cavo_aggiornato.collegamenti}")
            print(f"   Responsabile arrivo: {cavo_aggiornato.responsabile_arrivo}")
            
            # Verifica che il bit 2 sia impostato
            if cavo_aggiornato.collegamenti & 2:
                print("✅ Bit arrivo (2) correttamente impostato")
            else:
                print("❌ Bit arrivo (2) NON impostato")
                
            # Verifica che entrambi i bit siano impostati (valore 3)
            if cavo_aggiornato.collegamenti == 3:
                print("✅ Entrambi i lati collegati (valore 3)")
            else:
                print(f"⚠️ Valore collegamenti: {cavo_aggiornato.collegamenti} (atteso: 3)")
                
        except Exception as e:
            print(f"❌ Errore collegamento arrivo: {e}")
        
        # 4. Test tentativo di ricollegamento (dovrebbe fallire)
        print(f"\n4. Test tentativo di ricollegamento partenza (dovrebbe fallire)...")
        try:
            collega_cavo_webapp(
                db, cantiere_id, cavo_test.id_cavo, "partenza", "test_user_3"
            )
            print("❌ Il ricollegamento NON dovrebbe essere riuscito!")
        except ValueError as e:
            print(f"✅ Ricollegamento correttamente rifiutato: {e}")
        except Exception as e:
            print(f"❌ Errore inaspettato: {e}")
        
        # 5. Test scollegamento lato partenza
        print(f"\n5. Test scollegamento lato partenza...")
        try:
            cavo_aggiornato = scollega_cavo_webapp(
                db, cantiere_id, cavo_test.id_cavo, "partenza"
            )
            print(f"✅ Scollegamento partenza riuscito!")
            print(f"   Nuovi collegamenti: {cavo_aggiornato.collegamenti}")
            print(f"   Responsabile partenza: {cavo_aggiornato.responsabile_partenza}")
            
            # Verifica che solo il bit arrivo sia impostato (valore 2)
            if cavo_aggiornato.collegamenti == 2:
                print("✅ Solo lato arrivo collegato (valore 2)")
            else:
                print(f"⚠️ Valore collegamenti: {cavo_aggiornato.collegamenti} (atteso: 2)")
                
        except Exception as e:
            print(f"❌ Errore scollegamento partenza: {e}")
        
        # 6. Test scollegamento lato arrivo
        print(f"\n6. Test scollegamento lato arrivo...")
        try:
            cavo_aggiornato = scollega_cavo_webapp(
                db, cantiere_id, cavo_test.id_cavo, "arrivo"
            )
            print(f"✅ Scollegamento arrivo riuscito!")
            print(f"   Nuovi collegamenti: {cavo_aggiornato.collegamenti}")
            print(f"   Responsabile arrivo: {cavo_aggiornato.responsabile_arrivo}")
            
            # Verifica che nessun lato sia collegato (valore 0)
            if cavo_aggiornato.collegamenti == 0:
                print("✅ Nessun lato collegato (valore 0)")
            else:
                print(f"⚠️ Valore collegamenti: {cavo_aggiornato.collegamenti} (atteso: 0)")
                
        except Exception as e:
            print(f"❌ Errore scollegamento arrivo: {e}")
        
        print(f"\n=== TEST COMPLETATO ===")
        
    except Exception as e:
        print(f"❌ Errore generale durante il test: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_collegamenti_direct()
