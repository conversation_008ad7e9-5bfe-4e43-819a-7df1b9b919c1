2025-05-12 06:07:23,746 - INFO - === INIZIO TEST COMPORTAMENTO COMMIT ===
2025-05-12 06:07:23,899 - INFO - Connessione al database stabilita con successo
2025-05-12 06:07:23,899 - INFO - Test comportamento commit, ID: TEST_CB_Q6W05
2025-05-12 06:07:24,015 - INFO - Commit eseguito
2025-05-12 06:07:24,070 - INFO - Il cavo TEST_CB_Q6W05 � stato inserito con successo
2025-05-12 06:07:24,070 - INFO - Test comportamento autocommit, ID: TEST_CB_FTM6T
2025-05-12 06:07:24,270 - INFO - Inserimento eseguito con autocommit=True
2025-05-12 06:07:24,316 - INFO - Il cavo TEST_CB_FTM6T � stato inserito con successo (autocommit=True)
2025-05-12 06:07:24,316 - INFO - Test errore dopo commit, ID: TEST_CB_LUYB6
2025-05-12 06:07:24,624 - INFO - Commit eseguito
2025-05-12 06:07:24,624 - INFO - Generazione errore intenzionale dopo commit...
2025-05-12 06:07:24,625 - ERROR - Errore dopo commit: Errore intenzionale dopo commit
2025-05-12 06:07:24,667 - INFO - Il cavo TEST_CB_LUYB6 � stato inserito nonostante l'errore dopo commit
2025-05-12 06:07:24,667 - INFO - === RIEPILOGO DEI RISULTATI ===
2025-05-12 06:07:24,667 - INFO - Test commit_behavior: SUCCESSO
2025-05-12 06:07:24,668 - INFO - Test autocommit_behavior: SUCCESSO
2025-05-12 06:07:24,668 - INFO - Test error_after_commit: SUCCESSO
2025-05-12 06:07:24,668 - INFO - === FINE TEST COMPORTAMENTO COMMIT ===
