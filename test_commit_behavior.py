#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script di test per verificare il comportamento del commit nel database.
Questo script testa diversi scenari di commit per identificare potenziali problemi.
"""

import sys
import os
import logging
import time
import random
import string
from datetime import datetime
import psycopg2
from psycopg2.extras import RealDictCursor

# Configurazione del logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("test_commit_behavior.log"),
        logging.StreamHandler()
    ]
)

# Configurazione del database
DB_HOST = 'localhost'
DB_PORT = '5432'
DB_NAME = 'cantieri'
DB_USER = 'postgres'
DB_PASSWORD = 'Taranto'

# ID del cantiere di test (modificare con un ID esistente)
ID_CANTIERE_TEST = 1

def get_connection(with_dict_cursor=False, autocommit=False):
    """Crea e restituisce una connessione al database."""
    try:
        if with_dict_cursor:
            conn = psycopg2.connect(
                host=DB_HOST,
                port=DB_PORT,
                dbname=DB_NAME,
                user=DB_USER,
                password=DB_PASSWORD,
                cursor_factory=RealDictCursor
            )
        else:
            conn = psycopg2.connect(
                host=DB_HOST,
                port=DB_PORT,
                dbname=DB_NAME,
                user=DB_USER,
                password=DB_PASSWORD
            )
        
        conn.autocommit = autocommit
        return conn
    except Exception as e:
        logging.error(f"Errore durante la connessione al database: {e}")
        raise

def generate_test_id():
    """Genera un ID casuale per il cavo di test."""
    random_suffix = ''.join(random.choices(string.ascii_uppercase + string.digits, k=5))
    return f"TEST_CB_{random_suffix}"

def check_cavo_exists(id_cavo, id_cantiere):
    """Verifica se un cavo esiste nel database."""
    try:
        with get_connection(autocommit=True) as conn:
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT id_cavo FROM Cavi
                    WHERE id_cavo = %s AND id_cantiere = %s
                """, (id_cavo, id_cantiere))
                return cursor.fetchone() is not None
    except Exception as e:
        logging.error(f"Errore durante la verifica dell'esistenza del cavo: {e}")
        return False

def test_commit_behavior():
    """Testa il comportamento del commit."""
    id_cavo = generate_test_id()
    logging.info(f"Test comportamento commit, ID: {id_cavo}")
    
    # Verifica che il cavo non esista già
    if check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
        logging.warning(f"Il cavo {id_cavo} esiste già nel database. Generazione nuovo ID.")
        id_cavo = generate_test_id()
    
    # Fase 1: Inserimento del cavo con commit esplicito
    try:
        conn = get_connection(autocommit=False)
        cursor = conn.cursor()
        
        # Inserimento del cavo
        cursor.execute("""
            INSERT INTO Cavi (
                id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                stato_installazione, modificato_manualmente, timestamp
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
            )
        """, (
            id_cavo, ID_CANTIERE_TEST, '00', 'TEST_COMMIT', 'TEST', 'NERO',
            'TEST', '3', '1.5', '-', 'TEST', 'TEST', 'TEST',
            'TEST', 'TEST', 'TEST', 100, 'Da installare'
        ))
        
        # Commit esplicito
        conn.commit()
        logging.info("Commit eseguito")
        
        # Chiudi la connessione
        cursor.close()
        conn.close()
        
        # Verifica se il cavo è stato inserito
        if check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
            logging.info(f"Il cavo {id_cavo} è stato inserito con successo")
            return True
        else:
            logging.error(f"Il cavo {id_cavo} NON è stato inserito")
            return False
    except Exception as e:
        logging.error(f"Errore durante il test di commit: {e}")
        return False

def test_autocommit_behavior():
    """Testa il comportamento con autocommit=True."""
    id_cavo = generate_test_id()
    logging.info(f"Test comportamento autocommit, ID: {id_cavo}")
    
    # Verifica che il cavo non esista già
    if check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
        logging.warning(f"Il cavo {id_cavo} esiste già nel database. Generazione nuovo ID.")
        id_cavo = generate_test_id()
    
    # Fase 1: Inserimento del cavo con autocommit=True
    try:
        conn = get_connection(autocommit=True)
        cursor = conn.cursor()
        
        # Inserimento del cavo
        cursor.execute("""
            INSERT INTO Cavi (
                id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                stato_installazione, modificato_manualmente, timestamp
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
            )
        """, (
            id_cavo, ID_CANTIERE_TEST, '00', 'TEST_AUTOCOMMIT', 'TEST', 'NERO',
            'TEST', '3', '1.5', '-', 'TEST', 'TEST', 'TEST',
            'TEST', 'TEST', 'TEST', 100, 'Da installare'
        ))
        
        # Non è necessario chiamare commit() con autocommit=True
        logging.info("Inserimento eseguito con autocommit=True")
        
        # Chiudi la connessione
        cursor.close()
        conn.close()
        
        # Verifica se il cavo è stato inserito
        if check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
            logging.info(f"Il cavo {id_cavo} è stato inserito con successo (autocommit=True)")
            return True
        else:
            logging.error(f"Il cavo {id_cavo} NON è stato inserito (autocommit=True)")
            return False
    except Exception as e:
        logging.error(f"Errore durante il test di autocommit: {e}")
        return False

def test_error_after_commit():
    """Testa il comportamento quando si verifica un errore dopo il commit."""
    id_cavo = generate_test_id()
    logging.info(f"Test errore dopo commit, ID: {id_cavo}")
    
    # Verifica che il cavo non esista già
    if check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
        logging.warning(f"Il cavo {id_cavo} esiste già nel database. Generazione nuovo ID.")
        id_cavo = generate_test_id()
    
    # Fase 1: Inserimento del cavo con commit esplicito e poi errore
    try:
        conn = get_connection(autocommit=False)
        cursor = conn.cursor()
        
        # Inserimento del cavo
        cursor.execute("""
            INSERT INTO Cavi (
                id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                stato_installazione, modificato_manualmente, timestamp
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
            )
        """, (
            id_cavo, ID_CANTIERE_TEST, '00', 'TEST_ERROR_AFTER', 'TEST', 'NERO',
            'TEST', '3', '1.5', '-', 'TEST', 'TEST', 'TEST',
            'TEST', 'TEST', 'TEST', 100, 'Da installare'
        ))
        
        # Commit esplicito
        conn.commit()
        logging.info("Commit eseguito")
        
        # Genera un errore intenzionale
        logging.info("Generazione errore intenzionale dopo commit...")
        raise Exception("Errore intenzionale dopo commit")
        
    except Exception as e:
        logging.error(f"Errore dopo commit: {e}")
        
        # Verifica se il cavo è stato inserito nonostante l'errore
        if check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
            logging.info(f"Il cavo {id_cavo} è stato inserito nonostante l'errore dopo commit")
            return True
        else:
            logging.error(f"Il cavo {id_cavo} NON è stato inserito")
            return False
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def main():
    """Funzione principale per eseguire i test."""
    logging.info("=== INIZIO TEST COMPORTAMENTO COMMIT ===")
    
    # Test connessione al database
    try:
        with get_connection() as conn:
            logging.info("Connessione al database stabilita con successo")
    except Exception as e:
        logging.error(f"Impossibile connettersi al database: {e}")
        return
    
    # Esegui i test
    results = {
        "commit_behavior": test_commit_behavior(),
        "autocommit_behavior": test_autocommit_behavior(),
        "error_after_commit": test_error_after_commit()
    }
    
    # Riepilogo dei risultati
    logging.info("=== RIEPILOGO DEI RISULTATI ===")
    for test_name, result in results.items():
        logging.info(f"Test {test_name}: {'SUCCESSO' if result else 'FALLIMENTO'}")
    
    logging.info("=== FINE TEST COMPORTAMENTO COMMIT ===")

if __name__ == "__main__":
    main()
