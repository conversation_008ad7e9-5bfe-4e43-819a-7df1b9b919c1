#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script di test per verificare le migliorie alla gestione delle connessioni al database.
"""

import logging
import time
import random
import string
from modules.database_pg import Database, database_connection

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def generate_random_id(length=8):
    """Genera un ID casuale di lunghezza specificata."""
    return ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))

def test_insert_cavo(id_cantiere=1):
    """Test di inserimento di un cavo con gestione migliorata degli errori."""
    db = Database()
    id_cavo = f"TEST_{generate_random_id()}"

    print(f"\n=== TEST INSERIMENTO CAVO {id_cavo} ===")

    try:
        # Fase 1: Inserimento normale
        with database_connection(autocommit=False, operation_name="test_insert_cavo") as (conn, c):
            # Verifica che il cavo non esista già
            c.execute("""
                SELECT COUNT(*) FROM Cavi
                WHERE id_cavo = %s AND id_cantiere = %s
            """, (id_cavo, id_cantiere))

            if c.fetchone()[0] > 0:
                print(f"Il cavo {id_cavo} esiste già nel database")
                return

            # Inserisci il nuovo cavo
            c.execute("""
                INSERT INTO Cavi (
                    id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                    tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                    descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                    metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                    stato_installazione, modificato_manualmente, timestamp
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
                )
            """, (
                id_cavo, id_cantiere, "00", "TEST", "TEST", "NERO",
                "TEST", "4", "1.5", "-", "TEST", "TEST", "TEST",
                "TEST", "TEST", "TEST", 10.0, "Da installare"
            ))

        print(f"✅ Cavo {id_cavo} inserito con successo")

        # Fase 2: Simulazione di errore durante l'inserimento
        error_id_cavo = f"TEST_ERR_{generate_random_id()}"
        print(f"\n=== TEST ERRORE INSERIMENTO CAVO {error_id_cavo} ===")

        try:
            with database_connection(autocommit=False, operation_name="test_insert_cavo_error") as (conn, c):
                # Inserisci il nuovo cavo
                c.execute("""
                    INSERT INTO Cavi (
                        id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                        tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                        descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                        metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                        stato_installazione, modificato_manualmente, timestamp
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
                    )
                """, (
                    error_id_cavo, id_cantiere, "00", "TEST", "TEST", "NERO",
                    "TEST", "4", "1.5", "-", "TEST", "TEST", "TEST",
                    "TEST", "TEST", "TEST", 10.0, "Da installare"
                ))

                # Simula un errore dopo l'inserimento ma prima del commit
                print("Simulazione errore dopo inserimento ma prima del commit...")
                raise Exception("Errore simulato")

        except Exception as e:
            print(f"❌ Errore durante l'inserimento (come previsto): {e}")

            # Verifica se il cavo è stato comunque inserito
            risultato = db.verifica_stato_dopo_errore(
                "Cavi",
                {"id_cavo": error_id_cavo, "id_cantiere": id_cantiere},
                None,
                f"inserimento cavo {error_id_cavo} dopo errore"
            )

            if risultato['esiste']:
                print(f"⚠️ Nonostante l'errore, il cavo {error_id_cavo} risulta inserito nel database")
            else:
                print(f"✅ Come previsto, il cavo {error_id_cavo} non è stato inserito nel database")

    except Exception as e:
        print(f"❌ Errore non previsto durante il test: {e}")

def test_update_cavo(id_cantiere=1):
    """Test di aggiornamento di un cavo con gestione migliorata degli errori."""
    db = Database()

    # Prima creiamo un cavo di test
    id_cavo = f"TEST_UPD_{generate_random_id()}"

    print(f"\n=== TEST AGGIORNAMENTO CAVO {id_cavo} ===")

    try:
        # Fase 1: Inserimento del cavo di test
        with database_connection(autocommit=False, operation_name="test_update_cavo_insert") as (conn, c):
            # Inserisci il nuovo cavo
            c.execute("""
                INSERT INTO Cavi (
                    id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                    tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                    descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                    metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                    stato_installazione, modificato_manualmente, timestamp
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
                )
            """, (
                id_cavo, id_cantiere, "00", "TEST", "TEST", "NERO",
                "TEST", "4", "1.5", "-", "TEST", "TEST", "TEST",
                "TEST", "TEST", "TEST", 10.0, "Da installare"
            ))

        print(f"✅ Cavo {id_cavo} inserito con successo per il test di aggiornamento")

        # Fase 2: Aggiornamento normale
        with database_connection(autocommit=False, operation_name="test_update_cavo") as (conn, c):
            # Aggiorna il cavo
            c.execute("""
                UPDATE Cavi
                SET sistema = %s,
                    modificato_manualmente = 1,
                    timestamp = CURRENT_TIMESTAMP
                WHERE id_cantiere = %s AND id_cavo = %s
            """, ("TEST_UPDATED", id_cantiere, id_cavo))

        print(f"✅ Cavo {id_cavo} aggiornato con successo")

        # Fase 3: Simulazione di errore durante l'aggiornamento
        print(f"\n=== TEST ERRORE AGGIORNAMENTO CAVO {id_cavo} ===")

        try:
            with database_connection(autocommit=False, operation_name="test_update_cavo_error") as (conn, c):
                # Aggiorna il cavo
                c.execute("""
                    UPDATE Cavi
                    SET utility = %s,
                        modificato_manualmente = 1,
                        timestamp = CURRENT_TIMESTAMP
                    WHERE id_cantiere = %s AND id_cavo = %s
                """, ("TEST_ERROR", id_cantiere, id_cavo))

                # Simula un errore dopo l'aggiornamento ma prima del commit
                print("Simulazione errore dopo aggiornamento ma prima del commit...")
                raise Exception("Errore simulato")

        except Exception as e:
            print(f"❌ Errore durante l'aggiornamento (come previsto): {e}")

            # Verifica se il cavo è stato comunque aggiornato
            risultato = db.verifica_stato_dopo_errore(
                "Cavi",
                {"id_cavo": id_cavo, "id_cantiere": id_cantiere},
                {"utility": "TEST_ERROR"},
                f"aggiornamento cavo {id_cavo} dopo errore"
            )

            if risultato['esiste'] and risultato['valori_corretti']:
                print(f"⚠️ Nonostante l'errore, il cavo {id_cavo} risulta aggiornato nel database")
            else:
                print(f"✅ Come previsto, il cavo {id_cavo} non è stato aggiornato nel database")

    except Exception as e:
        print(f"❌ Errore non previsto durante il test: {e}")

def main():
    """Funzione principale per eseguire i test."""
    print("=== INIZIO TEST GESTIONE CONNESSIONI DATABASE ===")

    # Test di inserimento
    test_insert_cavo()

    # Test di aggiornamento
    test_update_cavo()

    print("\n=== FINE TEST GESTIONE CONNESSIONI DATABASE ===")

if __name__ == "__main__":
    main()
