2025-05-12 06:04:03,766 - INFO - === INIZIO TEST TRANSAZIONI DATABASE ===
2025-05-12 06:04:03,823 - INFO - Test inserimento con autocommit=True, ID: TEST_TX_5LQP5
2025-05-12 06:04:03,910 - INFO - Test inserimento con commit manuale, ID: TEST_TX_JW1VQ
2025-05-12 06:04:03,989 - INFO - Test rollback, ID: TEST_TX_NJYNN
2025-05-12 06:04:04,301 - INFO - Test gestione errori, ID: TEST_TX_JT6J5
2025-05-12 06:04:04,346 - INFO - Errore generato intenzionalmente: ERRORE:  la relazione "tabella_inesistente" non esiste
LINE 1: SELECT * FROM tabella_inesistente
                      ^

2025-05-12 06:04:04,391 - INFO - Test transazioni concorrenti
2025-05-12 06:04:04,392 - INFO - Thread 0: Inserimento cavo TEST_CONC_1738_0
2025-05-12 06:04:04,392 - INFO - Thread 1: Inserimento cavo TEST_CONC_1738_1
2025-05-12 06:04:04,392 - INFO - Thread 2: Inserimento cavo TEST_CONC_1738_2
2025-05-12 06:04:04,834 - INFO - Thread 0: Commit completato
2025-05-12 06:04:04,850 - INFO - Thread 1: Commit completato
2025-05-12 06:04:04,894 - INFO - Thread 2: Commit completato
2025-05-12 06:04:04,895 - INFO - Test di transazioni concorrenti completato
2025-05-12 06:04:04,938 - INFO - Cavo TEST_CONC_1738_0: Esiste
2025-05-12 06:04:04,982 - INFO - Cavo TEST_CONC_1738_1: Esiste
2025-05-12 06:04:05,026 - INFO - Cavo TEST_CONC_1738_2: Esiste
2025-05-12 06:04:05,026 - INFO - Test chiusura connessione senza commit, ID: TEST_TX_QFV8I
2025-05-12 06:04:05,232 - INFO - === RIEPILOGO DEI RISULTATI ===
2025-05-12 06:04:05,239 - INFO - === FINE TEST TRANSAZIONI DATABASE ===
