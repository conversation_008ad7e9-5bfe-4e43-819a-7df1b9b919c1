#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script di test per verificare il comportamento delle transazioni nel database.
Questo script testa diversi scenari di transazioni per identificare potenziali problemi.
"""

import sys
import os
import logging
import time
import random
import string
from datetime import datetime
import psycopg2
from psycopg2.extras import RealDictCursor
import threading

# Configurazione del logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("test_db_transactions.log"),
        logging.StreamHandler()
    ]
)

# Configurazione del database
DB_HOST = 'localhost'
DB_PORT = '5432'
DB_NAME = 'cantieri'
DB_USER = 'postgres'
DB_PASSWORD = 'Taranto'

# ID del cantiere di test (modificare con un ID esistente)
ID_CANTIERE_TEST = 1

def get_connection(with_dict_cursor=False, autocommit=False):
    """Crea e restituisce una connessione al database."""
    try:
        if with_dict_cursor:
            conn = psycopg2.connect(
                host=DB_HOST,
                port=DB_PORT,
                dbname=DB_NAME,
                user=DB_USER,
                password=DB_PASSWORD,
                cursor_factory=RealDictCursor
            )
        else:
            conn = psycopg2.connect(
                host=DB_HOST,
                port=DB_PORT,
                dbname=DB_NAME,
                user=DB_USER,
                password=DB_PASSWORD
            )
        
        conn.autocommit = autocommit
        return conn
    except Exception as e:
        logging.error(f"Errore durante la connessione al database: {e}")
        raise

def generate_test_id():
    """Genera un ID casuale per il cavo di test."""
    random_suffix = ''.join(random.choices(string.ascii_uppercase + string.digits, k=5))
    return f"TEST_TX_{random_suffix}"

def check_cavo_exists(id_cavo, id_cantiere):
    """Verifica se un cavo esiste nel database."""
    try:
        with get_connection(autocommit=True) as conn:
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT id_cavo FROM Cavi
                    WHERE id_cavo = %s AND id_cantiere = %s
                """, (id_cavo, id_cantiere))
                return cursor.fetchone() is not None
    except Exception as e:
        logging.error(f"Errore durante la verifica dell'esistenza del cavo: {e}")
        return False

def test_transaction_autocommit():
    """Testa l'inserimento con autocommit=True."""
    id_cavo = generate_test_id()
    logging.info(f"Test inserimento con autocommit=True, ID: {id_cavo}")
    
    try:
        # Connessione con autocommit=True
        conn = get_connection(autocommit=True)
        cursor = conn.cursor()
        
        # Inserimento del cavo
        cursor.execute("""
            INSERT INTO Cavi (
                id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                stato_installazione, modificato_manualmente, timestamp
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
            )
        """, (
            id_cavo, ID_CANTIERE_TEST, '00', 'TEST_AUTOCOMMIT', 'TEST', 'NERO',
            'TEST', '3', '1.5', '-', 'TEST', 'TEST', 'TEST',
            'TEST', 'TEST', 'TEST', 100, 'Da installare'
        ))
        
        # Non è necessario chiamare commit() con autocommit=True
        cursor.close()
        conn.close()
        
        # Verifica se il cavo è stato inserito
        if check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
            logging.info(f"✅ Cavo {id_cavo} inserito con successo (autocommit=True)")
            return True
        else:
            logging.error(f"❌ Cavo {id_cavo} NON inserito (autocommit=True)")
            return False
    except Exception as e:
        logging.error(f"❌ Errore durante il test con autocommit=True: {e}")
        return False

def test_transaction_manual_commit():
    """Testa l'inserimento con commit manuale."""
    id_cavo = generate_test_id()
    logging.info(f"Test inserimento con commit manuale, ID: {id_cavo}")
    
    try:
        # Connessione con autocommit=False (default)
        conn = get_connection(autocommit=False)
        cursor = conn.cursor()
        
        # Inserimento del cavo
        cursor.execute("""
            INSERT INTO Cavi (
                id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                stato_installazione, modificato_manualmente, timestamp
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
            )
        """, (
            id_cavo, ID_CANTIERE_TEST, '00', 'TEST_MANUAL', 'TEST', 'NERO',
            'TEST', '3', '1.5', '-', 'TEST', 'TEST', 'TEST',
            'TEST', 'TEST', 'TEST', 100, 'Da installare'
        ))
        
        # Commit esplicito
        conn.commit()
        
        cursor.close()
        conn.close()
        
        # Verifica se il cavo è stato inserito
        if check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
            logging.info(f"✅ Cavo {id_cavo} inserito con successo (commit manuale)")
            return True
        else:
            logging.error(f"❌ Cavo {id_cavo} NON inserito (commit manuale)")
            return False
    except Exception as e:
        logging.error(f"❌ Errore durante il test con commit manuale: {e}")
        return False

def test_transaction_rollback():
    """Testa il rollback di una transazione."""
    id_cavo = generate_test_id()
    logging.info(f"Test rollback, ID: {id_cavo}")
    
    try:
        # Connessione con autocommit=False (default)
        conn = get_connection(autocommit=False)
        cursor = conn.cursor()
        
        # Inserimento del cavo
        cursor.execute("""
            INSERT INTO Cavi (
                id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                stato_installazione, modificato_manualmente, timestamp
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
            )
        """, (
            id_cavo, ID_CANTIERE_TEST, '00', 'TEST_ROLLBACK', 'TEST', 'NERO',
            'TEST', '3', '1.5', '-', 'TEST', 'TEST', 'TEST',
            'TEST', 'TEST', 'TEST', 100, 'Da installare'
        ))
        
        # Rollback esplicito
        conn.rollback()
        
        cursor.close()
        conn.close()
        
        # Verifica se il cavo è stato inserito (non dovrebbe esserlo)
        if not check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
            logging.info(f"✅ Cavo {id_cavo} NON inserito dopo rollback (comportamento corretto)")
            return True
        else:
            logging.error(f"❌ Cavo {id_cavo} inserito nonostante il rollback (comportamento errato)")
            return False
    except Exception as e:
        logging.error(f"❌ Errore durante il test di rollback: {e}")
        return False

def test_transaction_error_handling():
    """Testa la gestione degli errori nelle transazioni."""
    id_cavo = generate_test_id()
    logging.info(f"Test gestione errori, ID: {id_cavo}")
    
    try:
        # Connessione con autocommit=False (default)
        conn = get_connection(autocommit=False)
        cursor = conn.cursor()
        
        try:
            # Inserimento del cavo
            cursor.execute("""
                INSERT INTO Cavi (
                    id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                    tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                    descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                    metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                    stato_installazione, modificato_manualmente, timestamp
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
                )
            """, (
                id_cavo, ID_CANTIERE_TEST, '00', 'TEST_ERROR', 'TEST', 'NERO',
                'TEST', '3', '1.5', '-', 'TEST', 'TEST', 'TEST',
                'TEST', 'TEST', 'TEST', 100, 'Da installare'
            ))
            
            # Genera un errore intenzionale
            cursor.execute("SELECT * FROM tabella_inesistente")
            
            # Questo commit non dovrebbe essere eseguito
            conn.commit()
        except Exception as e:
            logging.info(f"Errore generato intenzionalmente: {e}")
            conn.rollback()
        
        cursor.close()
        conn.close()
        
        # Verifica se il cavo è stato inserito (non dovrebbe esserlo)
        if not check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
            logging.info(f"✅ Cavo {id_cavo} NON inserito dopo errore (comportamento corretto)")
            return True
        else:
            logging.error(f"❌ Cavo {id_cavo} inserito nonostante l'errore (comportamento errato)")
            return False
    except Exception as e:
        logging.error(f"❌ Errore durante il test di gestione errori: {e}")
        return False

def test_concurrent_transactions():
    """Testa transazioni concorrenti."""
    logging.info("Test transazioni concorrenti")
    
    # Genera un ID base per i cavi
    base_id = f"TEST_CONC_{random.randint(1000, 9999)}"
    results = [False, False, False]  # Risultati per ogni thread
    
    # Funzione per thread che inserisce un cavo
    def insert_cavo_thread(thread_id):
        id_cavo = f"{base_id}_{thread_id}"
        logging.info(f"Thread {thread_id}: Inserimento cavo {id_cavo}")
        
        try:
            # Connessione con autocommit=False
            conn = get_connection(autocommit=False)
            cursor = conn.cursor()
            
            try:
                # Inserimento del cavo
                cursor.execute("""
                    INSERT INTO Cavi (
                        id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                        tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                        descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                        metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                        stato_installazione, modificato_manualmente, timestamp
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
                    )
                """, (
                    id_cavo, ID_CANTIERE_TEST, '00', f'TEST_THREAD_{thread_id}', 'TEST', 'NERO',
                    'TEST', '3', '1.5', '-', 'TEST', 'TEST', 'TEST',
                    'TEST', 'TEST', 'TEST', 100, 'Da installare'
                ))
                
                # Simula un'operazione che richiede tempo
                time.sleep(random.uniform(0.1, 0.5))
                
                # Commit
                conn.commit()
                logging.info(f"Thread {thread_id}: Commit completato")
                results[thread_id] = True
            except Exception as e:
                conn.rollback()
                logging.error(f"Thread {thread_id}: Errore - {e}")
            finally:
                cursor.close()
                conn.close()
        except Exception as e:
            logging.error(f"Thread {thread_id}: Errore di connessione - {e}")
    
    # Crea e avvia i thread
    threads = []
    for i in range(3):  # Crea 3 thread
        t = threading.Thread(target=insert_cavo_thread, args=(i,))
        threads.append(t)
        t.start()
    
    # Attendi che tutti i thread terminino
    for t in threads:
        t.join()
    
    logging.info("Test di transazioni concorrenti completato")
    
    # Verifica quali cavi sono stati creati
    success_count = 0
    for i in range(3):
        id_cavo = f"{base_id}_{i}"
        exists = check_cavo_exists(id_cavo, ID_CANTIERE_TEST)
        logging.info(f"Cavo {id_cavo}: {'Esiste' if exists else 'Non esiste'}")
        if exists:
            success_count += 1
    
    return success_count == 3  # Tutti e tre i cavi dovrebbero essere stati creati

def test_connection_close_behavior():
    """Testa il comportamento quando la connessione viene chiusa senza commit."""
    id_cavo = generate_test_id()
    logging.info(f"Test chiusura connessione senza commit, ID: {id_cavo}")
    
    try:
        # Connessione con autocommit=False (default)
        conn = get_connection(autocommit=False)
        cursor = conn.cursor()
        
        # Inserimento del cavo
        cursor.execute("""
            INSERT INTO Cavi (
                id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                stato_installazione, modificato_manualmente, timestamp
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
            )
        """, (
            id_cavo, ID_CANTIERE_TEST, '00', 'TEST_CLOSE', 'TEST', 'NERO',
            'TEST', '3', '1.5', '-', 'TEST', 'TEST', 'TEST',
            'TEST', 'TEST', 'TEST', 100, 'Da installare'
        ))
        
        # Chiudi la connessione senza commit
        cursor.close()
        conn.close()
        
        # Verifica se il cavo è stato inserito (non dovrebbe esserlo)
        if not check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
            logging.info(f"✅ Cavo {id_cavo} NON inserito dopo chiusura senza commit (comportamento corretto)")
            return True
        else:
            logging.error(f"❌ Cavo {id_cavo} inserito nonostante la chiusura senza commit (comportamento errato)")
            return False
    except Exception as e:
        logging.error(f"❌ Errore durante il test di chiusura connessione: {e}")
        return False

def main():
    """Funzione principale per eseguire i test."""
    logging.info("=== INIZIO TEST TRANSAZIONI DATABASE ===")
    
    # Test connessione al database
    try:
        with get_connection() as conn:
            logging.info("✅ Connessione al database stabilita con successo")
    except Exception as e:
        logging.error(f"❌ Impossibile connettersi al database: {e}")
        return
    
    # Esegui i test
    results = {
        "autocommit": test_transaction_autocommit(),
        "manual_commit": test_transaction_manual_commit(),
        "rollback": test_transaction_rollback(),
        "error_handling": test_transaction_error_handling(),
        "concurrent": test_concurrent_transactions(),
        "connection_close": test_connection_close_behavior()
    }
    
    # Riepilogo dei risultati
    logging.info("=== RIEPILOGO DEI RISULTATI ===")
    for test_name, result in results.items():
        logging.info(f"Test {test_name}: {'✅ SUCCESSO' if result else '❌ FALLIMENTO'}")
    
    logging.info("=== FINE TEST TRANSAZIONI DATABASE ===")

if __name__ == "__main__":
    main()
