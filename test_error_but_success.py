#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script di test per verificare il problema specifico: errore durante l'inserimento
ma poi l'operazione viene completata correttamente.
"""

import sys
import os
import logging
import time
import random
import string
from datetime import datetime
import psycopg2
from psycopg2.extras import RealDictCursor
import threading

# Configurazione del logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("test_error_but_success.log"),
        logging.StreamHandler()
    ]
)

# Configurazione del database
DB_HOST = 'localhost'
DB_PORT = '5432'
DB_NAME = 'cantieri'
DB_USER = 'postgres'
DB_PASSWORD = 'Taranto'

# ID del cantiere di test (modificare con un ID esistente)
ID_CANTIERE_TEST = 1

def get_connection(with_dict_cursor=False, autocommit=False):
    """Crea e restituisce una connessione al database."""
    try:
        if with_dict_cursor:
            conn = psycopg2.connect(
                host=DB_HOST,
                port=DB_PORT,
                dbname=DB_NAME,
                user=DB_USER,
                password=DB_PASSWORD,
                cursor_factory=RealDictCursor
            )
        else:
            conn = psycopg2.connect(
                host=DB_HOST,
                port=DB_PORT,
                dbname=DB_NAME,
                user=DB_USER,
                password=DB_PASSWORD
            )
        
        conn.autocommit = autocommit
        return conn
    except Exception as e:
        logging.error(f"Errore durante la connessione al database: {e}")
        raise

def generate_test_id():
    """Genera un ID casuale per il cavo di test."""
    random_suffix = ''.join(random.choices(string.ascii_uppercase + string.digits, k=5))
    return f"TEST_ERR_{random_suffix}"

def check_cavo_exists(id_cavo, id_cantiere):
    """Verifica se un cavo esiste nel database."""
    try:
        with get_connection(autocommit=True) as conn:
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT id_cavo FROM Cavi
                    WHERE id_cavo = %s AND id_cantiere = %s
                """, (id_cavo, id_cantiere))
                return cursor.fetchone() is not None
    except Exception as e:
        logging.error(f"Errore durante la verifica dell'esistenza del cavo: {e}")
        return False

def test_insert_with_error_simulation():
    """
    Simula il problema: errore durante l'inserimento ma poi l'operazione viene completata.
    Questo test simula un errore di rete o timeout durante l'inserimento.
    """
    id_cavo = generate_test_id()
    logging.info(f"Test inserimento con simulazione errore, ID: {id_cavo}")
    
    # Verifica che il cavo non esista già
    if check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
        logging.warning(f"Il cavo {id_cavo} esiste già nel database. Generazione nuovo ID.")
        id_cavo = generate_test_id()
    
    # Fase 1: Inserimento del cavo
    try:
        conn = get_connection(autocommit=False)
        cursor = conn.cursor()
        
        # Inserimento del cavo
        cursor.execute("""
            INSERT INTO Cavi (
                id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                stato_installazione, modificato_manualmente, timestamp
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
            )
        """, (
            id_cavo, ID_CANTIERE_TEST, '00', 'TEST_ERROR_SIM', 'TEST', 'NERO',
            'TEST', '3', '1.5', '-', 'TEST', 'TEST', 'TEST',
            'TEST', 'TEST', 'TEST', 100, 'Da installare'
        ))
        
        # Simula un errore prima del commit
        logging.info("Simulazione errore prima del commit...")
        
        # Opzione 1: Commit e poi simula un errore di connessione
        conn.commit()
        logging.info("Commit eseguito, ma ora simuliamo un errore di connessione...")
        
        # Simula un errore di connessione
        raise Exception("Errore di connessione simulato")
        
    except Exception as e:
        logging.error(f"Errore durante l'inserimento: {e}")
        
        # Verifica se il cavo è stato comunque inserito
        if check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
            logging.warning(f"Il cavo {id_cavo} risulta inserito nonostante l'errore!")
            return True
        else:
            logging.info(f"Il cavo {id_cavo} NON è stato inserito dopo l'errore.")
            return False
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def test_update_with_error_simulation():
    """
    Simula il problema: errore durante l'aggiornamento ma poi l'operazione viene completata.
    """
    # Prima inserisci un cavo di test
    id_cavo = generate_test_id()
    logging.info(f"Test aggiornamento con simulazione errore, ID: {id_cavo}")
    
    # Inserisci il cavo
    try:
        with get_connection(autocommit=True) as conn:
            with conn.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO Cavi (
                        id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                        tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                        descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                        metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                        stato_installazione, modificato_manualmente, timestamp
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
                    )
                """, (
                    id_cavo, ID_CANTIERE_TEST, '00', 'TEST_UPDATE_SIM', 'TEST', 'NERO',
                    'TEST', '3', '1.5', '-', 'TEST', 'TEST', 'TEST',
                    'TEST', 'TEST', 'TEST', 100, 'Da installare'
                ))
    except Exception as e:
        logging.error(f"Errore durante l'inserimento del cavo per il test di aggiornamento: {e}")
        return False
    
    # Verifica che il cavo sia stato inserito
    if not check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
        logging.error(f"Il cavo {id_cavo} non è stato inserito. Impossibile procedere con il test di aggiornamento.")
        return False
    
    # Fase 2: Aggiornamento del cavo con simulazione errore
    try:
        conn = get_connection(autocommit=False)
        cursor = conn.cursor()
        
        # Aggiornamento del cavo
        nuovo_sistema = f"SISTEMA_MODIFICATO_{random.randint(1, 1000)}"
        cursor.execute("""
            UPDATE Cavi
            SET sistema = %s,
                modificato_manualmente = 1,
                timestamp = CURRENT_TIMESTAMP
            WHERE id_cantiere = %s AND id_cavo = %s
        """, (nuovo_sistema, ID_CANTIERE_TEST, id_cavo))
        
        # Simula un errore prima del commit
        logging.info("Simulazione errore prima del commit dell'aggiornamento...")
        
        # Opzione 1: Commit e poi simula un errore di connessione
        conn.commit()
        logging.info("Commit dell'aggiornamento eseguito, ma ora simuliamo un errore di connessione...")
        
        # Simula un errore di connessione
        raise Exception("Errore di connessione simulato durante l'aggiornamento")
        
    except Exception as e:
        logging.error(f"Errore durante l'aggiornamento: {e}")
        
        # Verifica se il cavo è stato comunque aggiornato
        try:
            with get_connection(with_dict_cursor=True, autocommit=True) as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT sistema FROM Cavi
                        WHERE id_cavo = %s AND id_cantiere = %s
                    """, (id_cavo, ID_CANTIERE_TEST))
                    result = cursor.fetchone()
                    if result and result['sistema'] == nuovo_sistema:
                        logging.warning(f"Il cavo {id_cavo} risulta aggiornato nonostante l'errore! Sistema = {result['sistema']}")
                        return True
                    else:
                        logging.info(f"Il cavo {id_cavo} NON è stato aggiornato dopo l'errore. Sistema = {result['sistema'] if result else 'N/A'}")
                        return False
        except Exception as check_e:
            logging.error(f"Errore durante la verifica dell'aggiornamento: {check_e}")
            return False
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def test_multiple_connections():
    """
    Testa il comportamento con connessioni multiple.
    Questo test simula il caso in cui una connessione viene chiusa prematuramente
    ma un'altra connessione completa l'operazione.
    """
    id_cavo = generate_test_id()
    logging.info(f"Test connessioni multiple, ID: {id_cavo}")
    
    # Connessione 1: Inizia la transazione ma non la completa
    conn1 = None
    try:
        conn1 = get_connection(autocommit=False)
        cursor1 = conn1.cursor()
        
        # Inserimento del cavo
        cursor1.execute("""
            INSERT INTO Cavi (
                id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                stato_installazione, modificato_manualmente, timestamp
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
            )
        """, (
            id_cavo, ID_CANTIERE_TEST, '00', 'TEST_MULTI_CONN', 'TEST', 'NERO',
            'TEST', '3', '1.5', '-', 'TEST', 'TEST', 'TEST',
            'TEST', 'TEST', 'TEST', 100, 'Da installare'
        ))
        
        # Non facciamo commit
        logging.info("Connessione 1: Inserimento eseguito ma senza commit")
        
        # Verifica che il cavo non sia visibile ad altre connessioni
        if check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
            logging.error("Il cavo è visibile ad altre connessioni senza commit!")
        else:
            logging.info("Il cavo non è visibile ad altre connessioni (comportamento corretto)")
        
        # Connessione 2: Tenta di inserire lo stesso cavo
        try:
            conn2 = get_connection(autocommit=True)
            cursor2 = conn2.cursor()
            
            # Inserimento dello stesso cavo
            cursor2.execute("""
                INSERT INTO Cavi (
                    id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                    tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                    descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                    metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                    stato_installazione, modificato_manualmente, timestamp
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
                )
            """, (
                id_cavo, ID_CANTIERE_TEST, '00', 'TEST_MULTI_CONN_2', 'TEST', 'NERO',
                'TEST', '3', '1.5', '-', 'TEST', 'TEST', 'TEST',
                'TEST', 'TEST', 'TEST', 100, 'Da installare'
            ))
            
            logging.info("Connessione 2: Inserimento eseguito con autocommit=True")
            conn2.close()
        except Exception as e:
            logging.error(f"Connessione 2: Errore durante l'inserimento: {e}")
        
        # Ora facciamo rollback sulla prima connessione
        conn1.rollback()
        logging.info("Connessione 1: Rollback eseguito")
        
        # Verifica finale
        if check_cavo_exists(id_cavo, ID_CANTIERE_TEST):
            logging.info(f"Il cavo {id_cavo} esiste nel database dopo il test")
            return True
        else:
            logging.info(f"Il cavo {id_cavo} NON esiste nel database dopo il test")
            return False
        
    except Exception as e:
        logging.error(f"Errore durante il test di connessioni multiple: {e}")
        return False
    finally:
        if conn1:
            conn1.close()

def main():
    """Funzione principale per eseguire i test."""
    logging.info("=== INIZIO TEST ERRORE MA SUCCESSO ===")
    
    # Test connessione al database
    try:
        with get_connection() as conn:
            logging.info("Connessione al database stabilita con successo")
    except Exception as e:
        logging.error(f"Impossibile connettersi al database: {e}")
        return
    
    # Esegui i test
    results = {
        "insert_with_error": test_insert_with_error_simulation(),
        "update_with_error": test_update_with_error_simulation(),
        "multiple_connections": test_multiple_connections()
    }
    
    # Riepilogo dei risultati
    logging.info("=== RIEPILOGO DEI RISULTATI ===")
    for test_name, result in results.items():
        logging.info(f"Test {test_name}: {'SUCCESSO' if result else 'FALLIMENTO'}")
    
    logging.info("=== FINE TEST ERRORE MA SUCCESSO ===")

if __name__ == "__main__":
    main()
