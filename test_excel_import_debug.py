#!/usr/bin/env python3
"""
Script di debug per testare l'importazione Excel e identificare errori specifici.
"""

import sys
import os
import tempfile
import pandas as pd
import logging
from pathlib import Path

# Aggiungi il percorso del progetto al Python path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

# Configura il logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_test_excel():
    """Crea un file Excel di test con i campi corretti."""
    
    # Dati di test con tutti i campi obbligatori
    test_data = {
        'id_cavo': ['C001', 'C002', 'C003'],
        'utility': ['Energia', 'Telecom', 'Energia'],
        'tipologia': ['MT', 'BT', 'MT'],
        'formazione': ['3x2.5', '4x1.5', '2x4'],
        'metri_teorici': [100, 150, 200],
        'sistema': ['SIS1', 'SIS2', 'SIS1'],
        'ubicazione_partenza': ['Cabina A', 'Palo 1', 'Cabina C'],
        'ubicazione_arrivo': ['Cabina B', 'Palo 2', 'Cabina D']
    }
    
    df = pd.DataFrame(test_data)
    
    # Crea file temporaneo
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
    df.to_excel(temp_file.name, index=False)
    temp_file.close()
    
    print(f"📊 File Excel di test creato: {temp_file.name}")
    print(f"📋 Colonne: {list(df.columns)}")
    print(f"📈 Righe di dati: {len(df)}")
    
    return temp_file.name

def test_excel_validation():
    """Testa la validazione del file Excel."""
    
    print("🔍 Test validazione file Excel...")
    
    try:
        from modules.excel_manager import (
            is_file_safe,
            leggi_file_excel,
            valida_colonne_excel
        )
        
        # Crea file di test
        test_file = create_test_excel()
        
        # Test 1: Sicurezza file
        print("\n1️⃣ Test sicurezza file...")
        if is_file_safe(test_file):
            print("✅ File sicuro")
        else:
            print("❌ File non sicuro")
            return False
        
        # Test 2: Lettura file
        print("\n2️⃣ Test lettura file...")
        df_originale = leggi_file_excel(test_file)
        if df_originale is not None:
            print(f"✅ File letto correttamente: {len(df_originale)} righe")
            print(f"📋 Colonne originali: {list(df_originale.columns)}")
        else:
            print("❌ Errore nella lettura del file")
            return False
        
        # Test 3: Validazione colonne
        print("\n3️⃣ Test validazione colonne...")
        df_validato = valida_colonne_excel(df_originale)
        if df_validato is not None:
            print(f"✅ Validazione riuscita: {len(df_validato)} righe")
            print(f"📋 Colonne validate: {list(df_validato.columns)}")
            
            # Verifica campi obbligatori
            campi_obbligatori = ['id_cavo', 'utility', 'tipologia', 'n_conduttori', 'metri_teorici']
            for campo in campi_obbligatori:
                if campo in df_validato.columns:
                    print(f"✅ Campo obbligatorio presente: {campo}")
                else:
                    print(f"❌ Campo obbligatorio mancante: {campo}")
                    
        else:
            print("❌ Errore nella validazione delle colonne")
            return False
        
        # Cleanup
        os.unlink(test_file)
        
        return True
        
    except Exception as e:
        print(f"❌ Errore durante il test: {e}")
        return False

def test_webapp_import():
    """Testa l'importazione tramite la funzione webapp."""
    
    print("\n🌐 Test importazione webapp...")
    
    try:
        from webapp.backend.api.excel import importa_cavi_da_excel_webapp
        
        # Crea file di test
        test_file = create_test_excel()
        
        # Test importazione
        print("\n4️⃣ Test importazione webapp...")
        result = importa_cavi_da_excel_webapp(
            id_cantiere=1,  # Usa cantiere ID 1 per test
            percorso_file=test_file,
            revisione_predefinita="TEST_REV"
        )
        
        print(f"📊 Risultato importazione:")
        print(f"   Success: {result.get('success', 'N/A')}")
        print(f"   Message: {result.get('message', 'N/A')}")
        
        if result.get('details'):
            details = result['details']
            print(f"   Details:")
            for key, value in details.items():
                print(f"     {key}: {value}")
        
        # Cleanup
        os.unlink(test_file)
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ Errore durante il test webapp: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cli_import():
    """Testa l'importazione tramite la funzione CLI originale."""
    
    print("\n💻 Test importazione CLI...")
    
    try:
        from modules.excel_manager import importa_cavi_da_excel
        
        # Crea file di test
        test_file = create_test_excel()
        
        # Test importazione CLI
        print("\n5️⃣ Test importazione CLI...")
        result = importa_cavi_da_excel(
            id_cantiere=1,
            percorso_file=test_file,
            revisione_predefinita="TEST_REV_CLI",
            non_interattivo=True
        )
        
        print(f"📊 Risultato importazione CLI: {result}")
        
        # Cleanup
        os.unlink(test_file)
        
        return result
        
    except Exception as e:
        print(f"❌ Errore durante il test CLI: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Funzione principale per eseguire tutti i test."""
    
    print("🚀 INIZIO DEBUG IMPORTAZIONE EXCEL")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 4
    
    # Test 1: Validazione Excel
    if test_excel_validation():
        tests_passed += 1
        print("✅ Test validazione Excel: PASSATO")
    else:
        print("❌ Test validazione Excel: FALLITO")
    
    # Test 2: Importazione Webapp
    if test_webapp_import():
        tests_passed += 1
        print("✅ Test importazione webapp: PASSATO")
    else:
        print("❌ Test importazione webapp: FALLITO")
    
    # Test 3: Importazione CLI
    if test_cli_import():
        tests_passed += 1
        print("✅ Test importazione CLI: PASSATO")
    else:
        print("❌ Test importazione CLI: FALLITO")
    
    # Risultato finale
    print("\n" + "=" * 60)
    print(f"🏁 RISULTATO DEBUG: {tests_passed}/{total_tests} test passati")
    
    if tests_passed == total_tests:
        print("✅ Tutti i test sono passati! L'importazione dovrebbe funzionare.")
    else:
        print("❌ Alcuni test sono falliti. Controllare i dettagli sopra.")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
