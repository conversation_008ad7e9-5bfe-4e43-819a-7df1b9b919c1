#!/usr/bin/env python3
"""
Test finale per verificare che l'importazione Excel funzioni completamente.
"""

import sys
import os
import tempfile
import pandas as pd
import logging
from pathlib import Path

# Aggiungi il percorso del progetto al Python path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

# Configura il logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_test_excel():
    """Crea un file Excel di test con i campi corretti."""
    
    # Dati di test con tutti i campi obbligatori
    test_data = {
        'id_cavo': ['FINAL_001', 'FINAL_002'],
        'utility': ['Energia', 'Telecom'],
        'tipologia': ['MT', 'BT'],
        'formazione': ['3x2.5', '4x1.5'],
        'metri_teorici': [100, 150]
    }
    
    df = pd.DataFrame(test_data)
    
    # Crea file temporaneo
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
    df.to_excel(temp_file.name, index=False)
    temp_file.close()
    
    print(f"📊 File Excel di test creato: {temp_file.name}")
    print(f"📋 Colonne: {list(df.columns)}")
    print(f"📈 Righe di dati: {len(df)}")
    
    return temp_file.name

def test_import():
    """Testa l'importazione."""
    
    try:
        from webapp.backend.api.excel import importa_cavi_da_excel_webapp
        
        # Crea file di test
        test_file = create_test_excel()
        
        # Test importazione
        print("\n🔄 Test importazione finale...")
        result = importa_cavi_da_excel_webapp(
            id_cantiere=1,
            percorso_file=test_file,
            revisione_predefinita="FINAL_TEST"
        )
        
        print(f"\n📊 Risultato:")
        print(f"   Success: {result.get('success', 'N/A')}")
        print(f"   Message: {result.get('message', 'N/A')}")
        
        if result.get('details'):
            details = result['details']
            print(f"   Details:")
            for key, value in details.items():
                print(f"     {key}: {value}")
        
        # Cleanup - prova più volte se necessario
        for i in range(3):
            try:
                os.unlink(test_file)
                break
            except PermissionError:
                import time
                time.sleep(1)
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ Errore durante il test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Funzione principale."""
    
    print("🚀 TEST FINALE IMPORTAZIONE EXCEL")
    print("=" * 40)
    
    if test_import():
        print("\n✅ TEST FINALE PASSATO!")
        print("🎉 L'importazione Excel funziona correttamente!")
        print("\n📋 CAMPI RICHIESTI PER L'IMPORTAZIONE:")
        print("   • id_cavo (obbligatorio)")
        print("   • utility (obbligatorio)")
        print("   • tipologia (obbligatorio)")
        print("   • formazione (obbligatorio)")
        print("   • metri_teorici (obbligatorio)")
        print("\n💡 NOTA: Il campo 'n_conduttori' viene derivato automaticamente da 'formazione'")
        return True
    else:
        print("\n❌ TEST FINALE FALLITO!")
        print("Controllare i log per i dettagli.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
