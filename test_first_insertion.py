import requests
import json

# Ottieni il token di accesso
login_url = "http://localhost:8001/api/auth/login"
login_data = {"username": "admin", "password": "admin"}
headers = {"Content-Type": "application/json"}
response = requests.post(login_url, data=json.dumps(login_data), headers=headers)
token = response.json().get("access_token")

if not token:
    print("Errore durante il login:", response.text)
    exit(1)

print("Token ottenuto con successo")

# Testa l'endpoint is-first-insertion per il cantiere con ID 2
cantiere_id = 2
url = f"http://localhost:8001/api/parco-cavi/{cantiere_id}/is-first-insertion"
headers = {"Authorization": f"Bearer {token}"}

try:
    response = requests.get(url, headers=headers)
    print(f"Status code: {response.status_code}")
    print(f"Risposta: {response.text}")

    if response.status_code == 200:
        data = response.json()
        print(f"\nÈ il primo inserimento per il cantiere {cantiere_id}? {data.get('is_first_insertion')}")
    else:
        print(f"Errore nella richiesta: {response.text}")
except Exception as e:
    print(f"Errore durante la richiesta: {str(e)}")

# Verifica anche se ci sono bobine nel cantiere
try:
    bobine_url = f"http://localhost:8001/api/parco-cavi/{cantiere_id}"
    response = requests.get(bobine_url, headers=headers)
    print(f"\nStatus code (lista bobine): {response.status_code}")

    if response.status_code == 200:
        bobine = response.json()
        print(f"Numero di bobine nel cantiere {cantiere_id}: {len(bobine)}")
        if bobine:
            print("Prime bobine:")
            for i, bobina in enumerate(bobine[:3]):
                print(f"  {i+1}. ID: {bobina.get('id_bobina')}, Numero: {bobina.get('numero_bobina')}, Configurazione: {bobina.get('configurazione')}")
    else:
        print(f"Errore nella richiesta delle bobine: {response.text}")
except Exception as e:
    print(f"Errore durante la richiesta delle bobine: {str(e)}")
