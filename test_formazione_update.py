#!/usr/bin/env python3
"""
Test per verificare che il campo formazione sia ora il campo principale e che 
i valori vuoti vengano impostati a "TBD".
"""

import sys
import os
import tempfile
import pandas as pd
import logging
from pathlib import Path

# Aggiungi il percorso del progetto al Python path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

# Configura il logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_formazione_field():
    """Testa la gestione del campo formazione."""
    
    try:
        from modules.excel_manager import valida_colonne_excel
        
        print("🔍 Test gestione campo formazione...")
        
        # Test 1: File con campo 'formazione'
        print("\n1️⃣ Test con campo 'formazione'...")
        df_formazione = pd.DataFrame({
            'id_cavo': ['FORM_001'],
            'utility': ['Energia'],
            'tipologia': ['MT'],
            'metri_teorici': [100],
            'formazione': ['3x2.5']
        })
        
        result = valida_colonne_excel(df_formazione)
        if result is not None:
            print("✅ Validazione con 'formazione': PASSATA")
            print(f"   formazione: {result['formazione'].iloc[0]}")
            print(f"   sezione: {result['sezione'].iloc[0]}")
        else:
            print("❌ Validazione con 'formazione': FALLITA")
            return False
        
        # Test 2: File con campo 'sezione' (legacy)
        print("\n2️⃣ Test con campo 'sezione' (legacy)...")
        df_sezione = pd.DataFrame({
            'id_cavo': ['SEZI_001'],
            'utility': ['Energia'],
            'tipologia': ['MT'],
            'metri_teorici': [100],
            'sezione': ['4x1.5']
        })
        
        result = valida_colonne_excel(df_sezione)
        if result is not None:
            print("✅ Validazione con 'sezione': PASSATA")
            print(f"   formazione: {result['formazione'].iloc[0]}")
            print(f"   sezione: {result['sezione'].iloc[0]}")
        else:
            print("❌ Validazione con 'sezione': FALLITA")
            return False
        
        # Test 3: File senza campo formazione/sezione (deve essere TBD)
        print("\n3️⃣ Test senza campo formazione/sezione...")
        df_vuoto = pd.DataFrame({
            'id_cavo': ['VUOTO_001'],
            'utility': ['Energia'],
            'tipologia': ['MT'],
            'metri_teorici': [100]
        })
        
        result = valida_colonne_excel(df_vuoto)
        if result is not None:
            print("✅ Validazione senza formazione: PASSATA")
            print(f"   formazione: {result['formazione'].iloc[0]}")
            print(f"   sezione: {result['sezione'].iloc[0]}")
            
            # Verifica che sia TBD
            if result['formazione'].iloc[0] == 'TBD' and result['sezione'].iloc[0] == 'TBD':
                print("✅ Valori TBD impostati correttamente")
            else:
                print("❌ Valori TBD non impostati correttamente")
                return False
        else:
            print("❌ Validazione senza formazione: FALLITA")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Errore durante test formazione: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_import_with_formazione():
    """Testa l'importazione con il campo formazione."""
    
    try:
        from webapp.backend.api.excel import importa_cavi_da_excel_webapp
        
        print("\n🔄 Test importazione con formazione...")
        
        # Crea file Excel con formazione
        test_data = {
            'id_cavo': ['FORM_IMP_001', 'FORM_IMP_002'],
            'utility': ['Energia', 'Telecom'],
            'tipologia': ['MT', 'BT'],
            'metri_teorici': [100, 150],
            'formazione': ['3x2.5', '4x1.5']
        }
        
        df = pd.DataFrame(test_data)
        
        # Crea file temporaneo
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
        df.to_excel(temp_file.name, index=False)
        temp_file.close()
        
        print(f"📊 File Excel creato con formazione: {temp_file.name}")
        print(f"📋 Colonne: {list(df.columns)}")
        
        # Test importazione
        result = importa_cavi_da_excel_webapp(
            id_cantiere=1,
            percorso_file=temp_file.name,
            revisione_predefinita="FORM_TEST"
        )
        
        print(f"\n📊 Risultato importazione:")
        print(f"   Success: {result.get('success', 'N/A')}")
        print(f"   Message: {result.get('message', 'N/A')}")
        
        # Cleanup
        try:
            os.unlink(temp_file.name)
        except:
            pass
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ Errore durante test importazione: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Funzione principale."""
    
    print("🚀 TEST AGGIORNAMENTO CAMPO FORMAZIONE")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: Gestione campo formazione
    if test_formazione_field():
        tests_passed += 1
        print("✅ Test gestione formazione: PASSATO")
    else:
        print("❌ Test gestione formazione: FALLITO")
    
    # Test 2: Importazione con formazione
    if test_import_with_formazione():
        tests_passed += 1
        print("✅ Test importazione formazione: PASSATO")
    else:
        print("❌ Test importazione formazione: FALLITO")
    
    # Risultato finale
    print("\n" + "=" * 50)
    print(f"🏁 RISULTATO: {tests_passed}/{total_tests} test passati")
    
    if tests_passed == total_tests:
        print("✅ TUTTI I TEST PASSATI!")
        print("🎉 Il campo formazione è ora correttamente configurato!")
        print("\n📋 AGGIORNAMENTI COMPLETATI:")
        print("   • 'formazione' è ora il campo principale")
        print("   • 'sezione' è campo legacy (compatibilità)")
        print("   • Valori vuoti impostati a 'TBD'")
        print("   • Sincronizzazione automatica formazione ↔ sezione")
        return True
    else:
        print("❌ ALCUNI TEST FALLITI!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
