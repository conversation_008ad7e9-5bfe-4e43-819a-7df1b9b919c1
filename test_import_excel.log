2025-05-26 19:42:29,150 - INFO - === INIZIO TEST IMPORTAZIONE EXCEL ===
2025-05-26 19:42:29,150 - INFO - Generazione file Excel di test...
2025-05-26 19:42:29,192 - INFO - File generato: exports\test_cavi_10_20250526_194229.xlsx con 10 cavi
2025-05-26 19:42:29,193 - INFO - Importazione file Excel: exports\test_cavi_10_20250526_194229.xlsx
2025-05-26 19:42:29,722 - INFO - === FINE TEST IMPORTAZIONE EXCEL ===
2025-05-26 19:43:18,231 - INFO - === INIZIO TEST IMPORTAZIONE EXCEL ===
2025-05-26 19:43:18,231 - INFO - Generazione file Excel di test...
2025-05-26 19:43:18,253 - INFO - File generato: exports\test_cavi_10_20250526_194318.xlsx con 10 cavi
2025-05-26 19:43:18,254 - INFO - Importazione file Excel: exports\test_cavi_10_20250526_194318.xlsx
2025-05-26 19:43:18,254 - INFO - 🔄 Inizio operazione database [inizializzazione_database]
2025-05-26 19:43:18,300 - INFO - ✅ Inizializzazione database completata
2025-05-26 19:43:18,301 - INFO - ✅ Operazione database completata con successo [inizializzazione_database]
2025-05-26 19:43:18,301 - INFO - 🔄 Inizio operazione database
2025-05-26 19:43:18,339 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:43:18,339 - INFO - 🔄 Inizio operazione database
2025-05-26 19:43:18,390 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:43:18,391 - INFO - 🔄 Inizio operazione database
2025-05-26 19:43:18,763 - INFO - ✅ Backup creato per cantiere Cantiere Test: backups\backup_Cantiere Test_20250526_194318.xlsx
2025-05-26 19:43:18,813 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:43:18,844 - ERROR - ❌ Colonna obbligatoria mancante: 'id_cavo'
2025-05-26 19:43:18,845 - ERROR - [ERRORE] Importazione fallita senza eccezioni specifiche
2025-05-26 19:43:18,845 - INFO - === FINE TEST IMPORTAZIONE EXCEL ===
2025-05-26 19:43:44,295 - INFO - === INIZIO TEST IMPORTAZIONE EXCEL ===
2025-05-26 19:43:44,295 - INFO - Generazione file Excel di test...
2025-05-26 19:43:44,321 - INFO - File generato: exports\test_cavi_10_20250526_194344.xlsx con 10 cavi
2025-05-26 19:43:44,331 - INFO - Colonne nel file Excel: ['File di test con 10 cavi generati automaticamente', 'Unnamed: 1', 'Unnamed: 2', 'Unnamed: 3', 'Unnamed: 4', 'Unnamed: 5', 'Unnamed: 6', 'Unnamed: 7', 'Unnamed: 8', 'Unnamed: 9', 'Unnamed: 10', 'Unnamed: 11', 'Unnamed: 12']
2025-05-26 19:43:44,331 - ERROR - La colonna 'id_cavo' non è presente nel file Excel. Colonne disponibili: ['File di test con 10 cavi generati automaticamente', 'Unnamed: 1', 'Unnamed: 2', 'Unnamed: 3', 'Unnamed: 4', 'Unnamed: 5', 'Unnamed: 6', 'Unnamed: 7', 'Unnamed: 8', 'Unnamed: 9', 'Unnamed: 10', 'Unnamed: 11', 'Unnamed: 12']
2025-05-26 19:43:44,335 - INFO - Prime righe del file Excel:
  File di test con 10 cavi generati automaticamente   Unnamed: 1 Unnamed: 2   Unnamed: 3     Unnamed: 4   Unnamed: 5           Unnamed: 6       Unnamed: 7                   Unnamed: 8         Unnamed: 9    Unnamed: 10                Unnamed: 11    Unnamed: 12
0                                           id_cavo      sistema    utility  colore_cavo      tipologia   formazione  ubicazione_partenza  utenza_partenza  descrizione_utenza_partenza  ubicazione_arrivo  utenza_arrivo  descrizione_utenza_arrivo  metri_teorici
1                                              C001        Power     Signal       Giallo          LIYCY     4x1.5+SH    Quadro Secondario            QP-64               Quadro piano 1     Ventilatore T1          UT-82             Utenza piano 3           25.8
2                                              C002         HVAC  Emergency       Bianco       FG16OR16  3x2.5+2.5YG    Quadro Secondario            QP-52               Quadro piano 1         Sensore V1          UT-14             Utenza piano 1           74.1
3                                              C003     Lighting      Power        Rosso       FG16OR16        1x2.5    Quadro Principale            QP-08               Quadro piano 1     Ventilatore P1          UT-96             Utenza piano 1           99.5
4                                              C004  Fire Safety  Emergency          Blu  TAAGSHBHLV01D     1X240MM2    Quadro Secondario            QP-48               Quadro piano 3     Ventilatore P1          UT-38             Utenza piano 1           61.4
2025-05-26 19:43:44,335 - INFO - Importazione file Excel: exports\test_cavi_10_20250526_194344.xlsx
2025-05-26 19:43:44,335 - INFO - 🔄 Inizio operazione database [inizializzazione_database]
2025-05-26 19:43:44,381 - INFO - ✅ Inizializzazione database completata
2025-05-26 19:43:44,382 - INFO - ✅ Operazione database completata con successo [inizializzazione_database]
2025-05-26 19:43:44,382 - INFO - 🔄 Inizio operazione database
2025-05-26 19:43:44,419 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:43:44,419 - INFO - 🔄 Inizio operazione database
2025-05-26 19:43:44,457 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:43:44,457 - INFO - 🔄 Inizio operazione database
2025-05-26 19:43:44,525 - INFO - ✅ Backup creato per cantiere Cantiere Test: backups\backup_Cantiere Test_20250526_194344.xlsx
2025-05-26 19:43:44,526 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:43:44,532 - ERROR - ❌ Colonna obbligatoria mancante: 'id_cavo'
2025-05-26 19:43:44,532 - ERROR - [ERRORE] Importazione fallita senza eccezioni specifiche
2025-05-26 19:43:44,532 - INFO - === FINE TEST IMPORTAZIONE EXCEL ===
2025-05-26 19:44:37,918 - INFO - === INIZIO TEST IMPORTAZIONE EXCEL ===
2025-05-26 19:44:37,918 - INFO - Generazione file Excel di test...
2025-05-26 19:44:37,943 - INFO - File generato: exports\test_cavi_10_20250526_194437.xlsx con 10 cavi
2025-05-26 19:44:37,952 - INFO - Colonne nel file Excel: ['File di test con 10 cavi generati automaticamente', 'Unnamed: 1', 'Unnamed: 2', 'Unnamed: 3', 'Unnamed: 4', 'Unnamed: 5', 'Unnamed: 6', 'Unnamed: 7', 'Unnamed: 8', 'Unnamed: 9', 'Unnamed: 10', 'Unnamed: 11', 'Unnamed: 12']
2025-05-26 19:44:37,952 - ERROR - La colonna 'id_cavo' non è presente nel file Excel. Colonne disponibili: ['File di test con 10 cavi generati automaticamente', 'Unnamed: 1', 'Unnamed: 2', 'Unnamed: 3', 'Unnamed: 4', 'Unnamed: 5', 'Unnamed: 6', 'Unnamed: 7', 'Unnamed: 8', 'Unnamed: 9', 'Unnamed: 10', 'Unnamed: 11', 'Unnamed: 12']
2025-05-26 19:44:37,955 - INFO - Prime righe del file Excel:
  File di test con 10 cavi generati automaticamente Unnamed: 1 Unnamed: 2   Unnamed: 3     Unnamed: 4   Unnamed: 5           Unnamed: 6       Unnamed: 7                   Unnamed: 8         Unnamed: 9    Unnamed: 10                Unnamed: 11    Unnamed: 12
0                                           id_cavo    sistema    utility  colore_cavo      tipologia   formazione  ubicazione_partenza  utenza_partenza  descrizione_utenza_partenza  ubicazione_arrivo  utenza_arrivo  descrizione_utenza_arrivo  metri_teorici
1                                              C001   Lighting  Emergency         Nero          LIYCY     4x1.5+SH     Quadro Controllo            QP-35               Quadro piano 3     Ventilatore V1          UT-77             Utenza piano 1           92.4
2                                              C002   Security      Power       Bianco       FG16OR16        4x1.5    Quadro Principale            QP-87               Quadro piano 2     Ventilatore P1          UT-29             Utenza piano 2           89.7
3                                              C003        BMS   Lighting        Rosso  TAAGSHBHLV01D        1x2.5    Quadro Principale            QP-33               Quadro piano 1           Pompa T1          UT-61             Utenza piano 3           73.2
4                                              C004   Security       Data       Bianco  TAAGSHBHLV01D  3x2.5+2.5YG    Quadro Secondario            QP-28               Quadro piano 3         Sensore P1          UT-46             Utenza piano 1           76.2
2025-05-26 19:44:37,956 - INFO - Importazione file Excel: exports\test_cavi_10_20250526_194437.xlsx
2025-05-26 19:44:37,956 - INFO - 🔄 Inizio operazione database [inizializzazione_database]
2025-05-26 19:44:38,011 - INFO - ✅ Inizializzazione database completata
2025-05-26 19:44:38,012 - INFO - ✅ Operazione database completata con successo [inizializzazione_database]
2025-05-26 19:44:38,012 - INFO - 🔄 Inizio operazione database
2025-05-26 19:44:38,154 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:44:38,155 - INFO - 🔄 Inizio operazione database
2025-05-26 19:44:38,195 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:44:38,195 - INFO - 🔄 Inizio operazione database
2025-05-26 19:44:38,308 - INFO - ✅ Backup creato per cantiere Cantiere Test: backups\backup_Cantiere Test_20250526_194438.xlsx
2025-05-26 19:44:38,309 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:44:38,318 - INFO - Rilevata riga di titolo: 'File di test con 10 cavi generati automaticamente...'
2025-05-26 19:44:38,318 - INFO - Utilizzata la seconda riga come intestazioni delle colonne
2025-05-26 19:44:38,321 - INFO - ✅ Rilevata colonna 'formazione', mappata a 'sezione'
2025-05-26 19:44:38,321 - ERROR - ❌ Colonna obbligatoria mancante: 'n_conduttori'
2025-05-26 19:44:38,321 - ERROR - [ERRORE] Importazione fallita senza eccezioni specifiche
2025-05-26 19:44:38,322 - INFO - === FINE TEST IMPORTAZIONE EXCEL ===
2025-05-26 19:45:14,259 - INFO - === INIZIO TEST IMPORTAZIONE EXCEL ===
2025-05-26 19:45:14,259 - INFO - Generazione file Excel di test...
2025-05-26 19:45:14,283 - INFO - File generato: exports\test_cavi_10_20250526_194514.xlsx con 10 cavi
2025-05-26 19:45:14,291 - INFO - Colonne nel file Excel (pandas): ['File di test con 10 cavi generati automaticamente', 'Unnamed: 1', 'Unnamed: 2', 'Unnamed: 3', 'Unnamed: 4', 'Unnamed: 5', 'Unnamed: 6', 'Unnamed: 7', 'Unnamed: 8', 'Unnamed: 9', 'Unnamed: 10', 'Unnamed: 11', 'Unnamed: 12']
2025-05-26 19:45:14,291 - ERROR - La colonna 'id_cavo' non è presente nel file Excel. Colonne disponibili: ['File di test con 10 cavi generati automaticamente', 'Unnamed: 1', 'Unnamed: 2', 'Unnamed: 3', 'Unnamed: 4', 'Unnamed: 5', 'Unnamed: 6', 'Unnamed: 7', 'Unnamed: 8', 'Unnamed: 9', 'Unnamed: 10', 'Unnamed: 11', 'Unnamed: 12']
2025-05-26 19:45:14,294 - INFO - Prime righe del file Excel:
  File di test con 10 cavi generati automaticamente   Unnamed: 1 Unnamed: 2   Unnamed: 3 Unnamed: 4   Unnamed: 5           Unnamed: 6       Unnamed: 7                   Unnamed: 8         Unnamed: 9    Unnamed: 10                Unnamed: 11    Unnamed: 12
0                                           id_cavo      sistema    utility  colore_cavo  tipologia   formazione  ubicazione_partenza  utenza_partenza  descrizione_utenza_partenza  ubicazione_arrivo  utenza_arrivo  descrizione_utenza_arrivo  metri_teorici
1                                              C001         HVAC  Emergency        Rosso      LIYCY  3x2.5+2.5YG    Quadro Secondario            QP-72               Quadro piano 2           Pompa V1          UT-97             Utenza piano 3           63.1
2                                              C002  Fire Safety  Emergency       Giallo      LIYCY        1x2.5     Quadro Controllo            QP-71               Quadro piano 2         Sensore P1          UT-10             Utenza piano 3           27.3
3                                              C003     Security    Control         Nero      LIYCY        4x1.5     Quadro Controllo            QP-24               Quadro piano 3           Pompa V1          UT-32             Utenza piano 3           52.6
4                                              C004        Power    Control        Verde       FROR        4x1.5     Quadro Controllo            QP-33               Quadro piano 1         Sensore T1          UT-68             Utenza piano 3           91.2
2025-05-26 19:45:14,298 - INFO - Intestazioni dalla seconda riga (openpyxl): ['id_cavo', 'sistema', 'utility', 'colore_cavo', 'tipologia', 'formazione', 'ubicazione_partenza', 'utenza_partenza', 'descrizione_utenza_partenza', 'ubicazione_arrivo', 'utenza_arrivo', 'descrizione_utenza_arrivo', 'metri_teorici']
2025-05-26 19:45:14,298 - INFO - Intestazioni standardizzate: ['id_cavo', 'sistema', 'utility', 'colore_cavo', 'tipologia', 'formazione', 'ubicazione_partenza', 'utenza_partenza', 'descrizione_utenza_partenza', 'ubicazione_arrivo', 'utenza_arrivo', 'descrizione_utenza_arrivo', 'metri_teorici']
2025-05-26 19:45:14,298 - INFO - Colonna obbligatoria 'id_cavo' trovata nelle intestazioni standardizzate
2025-05-26 19:45:14,298 - INFO - Colonna obbligatoria 'utility' trovata nelle intestazioni standardizzate
2025-05-26 19:45:14,298 - INFO - Colonna obbligatoria 'tipologia' trovata nelle intestazioni standardizzate
2025-05-26 19:45:14,298 - ERROR - Colonna obbligatoria 'n_conduttori' non trovata nelle intestazioni standardizzate
2025-05-26 19:45:14,298 - INFO - Colonna obbligatoria 'metri_teorici' trovata nelle intestazioni standardizzate
2025-05-26 19:45:14,299 - INFO - Importazione file Excel: exports\test_cavi_10_20250526_194514.xlsx
2025-05-26 19:45:14,299 - INFO - 🔄 Inizio operazione database [inizializzazione_database]
2025-05-26 19:45:14,346 - INFO - ✅ Inizializzazione database completata
2025-05-26 19:45:14,346 - INFO - ✅ Operazione database completata con successo [inizializzazione_database]
2025-05-26 19:45:14,346 - INFO - 🔄 Inizio operazione database
2025-05-26 19:45:14,384 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:45:14,384 - INFO - 🔄 Inizio operazione database
2025-05-26 19:45:14,424 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:45:14,425 - INFO - 🔄 Inizio operazione database
2025-05-26 19:45:14,598 - INFO - ✅ Backup creato per cantiere Cantiere Test: backups\backup_Cantiere Test_20250526_194514.xlsx
2025-05-26 19:45:14,599 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:45:14,603 - INFO - Rilevata riga di titolo: 'File di test con 10 cavi generati automaticamente...'
2025-05-26 19:45:14,604 - INFO - Utilizzata la seconda riga come intestazioni delle colonne
2025-05-26 19:45:14,606 - INFO - ✅ Rilevata colonna 'formazione', mappata a 'sezione'
2025-05-26 19:45:14,606 - ERROR - ❌ Colonna obbligatoria mancante: 'n_conduttori'
2025-05-26 19:45:14,606 - ERROR - [ERRORE] Importazione fallita senza eccezioni specifiche
2025-05-26 19:45:14,606 - INFO - === FINE TEST IMPORTAZIONE EXCEL ===
2025-05-26 19:46:03,335 - INFO - === INIZIO TEST IMPORTAZIONE EXCEL ===
2025-05-26 19:46:03,335 - INFO - Generazione file Excel di test...
2025-05-26 19:46:03,361 - INFO - File generato: exports\test_cavi_10_20250526_194603.xlsx con 10 cavi
2025-05-26 19:46:03,369 - INFO - Colonne nel file Excel (pandas): ['File di test con 10 cavi generati automaticamente', 'Unnamed: 1', 'Unnamed: 2', 'Unnamed: 3', 'Unnamed: 4', 'Unnamed: 5', 'Unnamed: 6', 'Unnamed: 7', 'Unnamed: 8', 'Unnamed: 9', 'Unnamed: 10', 'Unnamed: 11', 'Unnamed: 12']
2025-05-26 19:46:03,369 - ERROR - La colonna 'id_cavo' non è presente nel file Excel. Colonne disponibili: ['File di test con 10 cavi generati automaticamente', 'Unnamed: 1', 'Unnamed: 2', 'Unnamed: 3', 'Unnamed: 4', 'Unnamed: 5', 'Unnamed: 6', 'Unnamed: 7', 'Unnamed: 8', 'Unnamed: 9', 'Unnamed: 10', 'Unnamed: 11', 'Unnamed: 12']
2025-05-26 19:46:03,371 - INFO - Prime righe del file Excel:
  File di test con 10 cavi generati automaticamente   Unnamed: 1 Unnamed: 2   Unnamed: 3 Unnamed: 4  Unnamed: 5           Unnamed: 6       Unnamed: 7                   Unnamed: 8         Unnamed: 9    Unnamed: 10                Unnamed: 11    Unnamed: 12
0                                           id_cavo      sistema    utility  colore_cavo  tipologia  formazione  ubicazione_partenza  utenza_partenza  descrizione_utenza_partenza  ubicazione_arrivo  utenza_arrivo  descrizione_utenza_arrivo  metri_teorici
1                                              C001  Fire Safety   Lighting       Bianco     N07V-K       3x2.5    Quadro Secondario            QP-50               Quadro piano 2           Pompa V1          UT-97             Utenza piano 1           33.2
2                                              C002          BMS    Control        Rosso   FG16OR16    1X240MM2    Quadro Principale            QP-55               Quadro piano 1         Sensore P1          UT-54             Utenza piano 3           29.2
3                                              C003  Fire Safety   Lighting       Grigio      LIYCY       4x1.5     Quadro Controllo            QP-66               Quadro piano 1           Pompa P1          UT-57             Utenza piano 2           47.9
4                                              C004        Power   Lighting        Rosso   FG16OR16       3x2.5     Quadro Controllo            QP-47               Quadro piano 2           Pompa V1          UT-24             Utenza piano 3           39.8
2025-05-26 19:46:03,375 - INFO - Intestazioni dalla seconda riga (openpyxl): ['id_cavo', 'sistema', 'utility', 'colore_cavo', 'tipologia', 'formazione', 'ubicazione_partenza', 'utenza_partenza', 'descrizione_utenza_partenza', 'ubicazione_arrivo', 'utenza_arrivo', 'descrizione_utenza_arrivo', 'metri_teorici']
2025-05-26 19:46:03,375 - INFO - Intestazioni standardizzate: ['id_cavo', 'sistema', 'utility', 'colore_cavo', 'tipologia', 'formazione', 'ubicazione_partenza', 'utenza_partenza', 'descrizione_utenza_partenza', 'ubicazione_arrivo', 'utenza_arrivo', 'descrizione_utenza_arrivo', 'metri_teorici']
2025-05-26 19:46:03,375 - INFO - Colonna obbligatoria 'id_cavo' trovata nelle intestazioni standardizzate
2025-05-26 19:46:03,376 - INFO - Colonna obbligatoria 'utility' trovata nelle intestazioni standardizzate
2025-05-26 19:46:03,376 - INFO - Colonna obbligatoria 'tipologia' trovata nelle intestazioni standardizzate
2025-05-26 19:46:03,376 - ERROR - Colonna obbligatoria 'n_conduttori' non trovata nelle intestazioni standardizzate
2025-05-26 19:46:03,376 - INFO - Colonna obbligatoria 'metri_teorici' trovata nelle intestazioni standardizzate
2025-05-26 19:46:03,376 - INFO - Importazione file Excel: exports\test_cavi_10_20250526_194603.xlsx
2025-05-26 19:46:03,376 - INFO - 🔄 Inizio operazione database [inizializzazione_database]
2025-05-26 19:46:03,425 - INFO - ✅ Inizializzazione database completata
2025-05-26 19:46:03,425 - INFO - ✅ Operazione database completata con successo [inizializzazione_database]
2025-05-26 19:46:03,425 - INFO - 🔄 Inizio operazione database
2025-05-26 19:46:03,464 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:46:03,464 - INFO - 🔄 Inizio operazione database
2025-05-26 19:46:03,502 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:46:03,502 - INFO - 🔄 Inizio operazione database
2025-05-26 19:46:03,572 - INFO - ✅ Backup creato per cantiere Cantiere Test: backups\backup_Cantiere Test_20250526_194603.xlsx
2025-05-26 19:46:03,572 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:46:03,577 - INFO - Rilevata riga di titolo: 'File di test con 10 cavi generati automaticamente...'
2025-05-26 19:46:03,577 - INFO - Utilizzata la seconda riga come intestazioni delle colonne
2025-05-26 19:46:03,579 - INFO - ✅ Rilevata colonna 'formazione', mappata a 'sezione'
2025-05-26 19:46:03,579 - INFO - ✅ Colonna 'n_conduttori' mancante, tentativo di derivazione da formazione/sezione
2025-05-26 19:46:03,580 - INFO - ✅ Colonna 'n_conduttori' derivata da 'formazione'
2025-05-26 19:46:03,585 - INFO - ✅ Dati validati: 10 righe con dati effettivi pronte per l'elaborazione
2025-05-26 19:46:03,585 - INFO - Usando revisione di default: TEST_20250526_194603
2025-05-26 19:46:03,585 - INFO - 🔄 Inizio operazione database
2025-05-26 19:46:03,627 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:46:03,627 - INFO - 🔄 Inizio operazione database
2025-05-26 19:46:03,668 - INFO - Marcando 33 cavi come SPARE usando la funzione specifica
2025-05-26 19:46:03,674 - ERROR - Errore durante la marcatura del cavo TEST20250501103050 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,675 - ERROR - Errore durante la marcatura del cavo TEST20250501103438 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,675 - ERROR - Errore durante la marcatura del cavo TEST20250501110511 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,676 - ERROR - Errore durante la marcatura del cavo TEST_CBGV7 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,676 - ERROR - Errore durante la marcatura del cavo TEST_G4T5W come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,676 - ERROR - Errore durante la marcatura del cavo TEST_EZJZN come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,677 - ERROR - Errore durante la marcatura del cavo TEST_TX_5LQP5 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,677 - ERROR - Errore durante la marcatura del cavo TEST_TX_JW1VQ come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,677 - ERROR - Errore durante la marcatura del cavo TEST_CONC_1738_0 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,678 - ERROR - Errore durante la marcatura del cavo TEST_CONC_1738_2 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,678 - ERROR - Errore durante la marcatura del cavo TEST_CONC_1738_1 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,678 - ERROR - Errore durante la marcatura del cavo TEST_ERR_USSLY come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,679 - ERROR - Errore durante la marcatura del cavo TEST_ERR_PVKW4 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,679 - ERROR - Errore durante la marcatura del cavo TEST_ERR_DL1WI come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,679 - ERROR - Errore durante la marcatura del cavo TEST_CB_Q6W05 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,680 - ERROR - Errore durante la marcatura del cavo TEST_CB_FTM6T come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,680 - ERROR - Errore durante la marcatura del cavo TEST_CB_LUYB6 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,680 - ERROR - Errore durante la marcatura del cavo YRXRISF7 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,681 - ERROR - Errore durante la marcatura del cavo TEST_UPD_Z97KOTBW come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,681 - ERROR - Errore durante la marcatura del cavo TEST_3N5NPKQV come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,682 - ERROR - Errore durante la marcatura del cavo TEST_SUUWFFKO come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,682 - ERROR - Errore durante la marcatura del cavo TEST_UPD_ZGUN682P come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,682 - ERROR - Errore durante la marcatura del cavo TEST_2UG2TZ1K come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,683 - ERROR - Errore durante la marcatura del cavo TEST20250501100824 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,683 - ERROR - Errore durante la marcatura del cavo TEST_MXI2FWYJ come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,683 - ERROR - Errore durante la marcatura del cavo TEST_UPD_Z15AYJ36 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,683 - ERROR - Errore durante la marcatura del cavo TEST_UPD_RIEMQ7JI come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,684 - ERROR - Errore durante la marcatura del cavo TEST_JPYUHP0G come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,684 - ERROR - Errore durante la marcatura del cavo TEST_UPD_DFVUJIKG come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,684 - ERROR - Errore durante la marcatura del cavo TEST_H6RVRWKB come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,685 - ERROR - Errore durante la marcatura del cavo TEST_UPD_3NLTK9F8 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,685 - ERROR - Errore durante la marcatura del cavo TEST_CB_JX1SJ come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,685 - ERROR - Errore durante la marcatura del cavo TEST_CB_8C4QH come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:03,686 - INFO - 
==========================================
📊 REPORT ELABORAZIONE CAVI - REVISIONE TEST_20250526_194603
• Totali processati: 10
• Nuovi cavi: 10
• Cavi aggiornati: 0
• Cavi marcati come SPARE: 0
• Cavi SPARE reintegrati: 0
• Conflitti rilevati: 0
• Conflitti fisici (cavi installati): 0
• Variazioni in cavi non installati: 0
==========================================

2025-05-26 19:46:03,686 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:46:03,686 - INFO - 🔄 Inizio operazione database
2025-05-26 19:46:03,738 - INFO - Rollback eseguito dopo errore: ERRORE:  la INSERT o l'UPDATE sulla tabella "cavi" viola il vincolo di chiave esterna "cavi_id_bobina_fkey"
DETAIL:  La chiave (id_bobina)=() non è presente nella tabella "parco_cavi".

2025-05-26 19:46:03,739 - ERROR - ❌ Violazione di chiave esterna durante operazione: ERRORE:  la INSERT o l'UPDATE sulla tabella "cavi" viola il vincolo di chiave esterna "cavi_id_bobina_fkey"
DETAIL:  La chiave (id_bobina)=() non è presente nella tabella "parco_cavi".

2025-05-26 19:46:03,739 - WARNING - ⚠️ Operazione database completata con errori
2025-05-26 19:46:03,739 - ERROR - Errore database PostgreSQL durante l'inserimento dei cavi: ERRORE:  la INSERT o l'UPDATE sulla tabella "cavi" viola il vincolo di chiave esterna "cavi_id_bobina_fkey"
DETAIL:  La chiave (id_bobina)=() non è presente nella tabella "parco_cavi".

2025-05-26 19:46:03,739 - ERROR - ❌ Errore durante il salvataggio nel database
2025-05-26 19:46:03,739 - ERROR - [ERRORE] Importazione fallita senza eccezioni specifiche
2025-05-26 19:46:03,739 - INFO - === FINE TEST IMPORTAZIONE EXCEL ===
2025-05-26 19:46:39,973 - INFO - === INIZIO TEST IMPORTAZIONE EXCEL ===
2025-05-26 19:46:39,973 - INFO - Generazione file Excel di test...
2025-05-26 19:46:39,999 - INFO - File generato: exports\test_cavi_10_20250526_194639.xlsx con 10 cavi
2025-05-26 19:46:40,008 - INFO - Colonne nel file Excel (pandas): ['File di test con 10 cavi generati automaticamente', 'Unnamed: 1', 'Unnamed: 2', 'Unnamed: 3', 'Unnamed: 4', 'Unnamed: 5', 'Unnamed: 6', 'Unnamed: 7', 'Unnamed: 8', 'Unnamed: 9', 'Unnamed: 10', 'Unnamed: 11', 'Unnamed: 12']
2025-05-26 19:46:40,008 - ERROR - La colonna 'id_cavo' non è presente nel file Excel. Colonne disponibili: ['File di test con 10 cavi generati automaticamente', 'Unnamed: 1', 'Unnamed: 2', 'Unnamed: 3', 'Unnamed: 4', 'Unnamed: 5', 'Unnamed: 6', 'Unnamed: 7', 'Unnamed: 8', 'Unnamed: 9', 'Unnamed: 10', 'Unnamed: 11', 'Unnamed: 12']
2025-05-26 19:46:40,011 - INFO - Prime righe del file Excel:
  File di test con 10 cavi generati automaticamente   Unnamed: 1 Unnamed: 2   Unnamed: 3 Unnamed: 4   Unnamed: 5           Unnamed: 6       Unnamed: 7                   Unnamed: 8         Unnamed: 9    Unnamed: 10                Unnamed: 11    Unnamed: 12
0                                           id_cavo      sistema    utility  colore_cavo  tipologia   formazione  ubicazione_partenza  utenza_partenza  descrizione_utenza_partenza  ubicazione_arrivo  utenza_arrivo  descrizione_utenza_arrivo  metri_teorici
1                                              C001          BMS      Power          Blu       FROR        1x2.5    Quadro Secondario            QP-79               Quadro piano 1         Sensore P1          UT-07             Utenza piano 2           84.4
2                                              C002  Fire Safety       Data       Giallo       FROR     4x1.5+SH    Quadro Principale            QP-29               Quadro piano 2     Ventilatore P1          UT-27             Utenza piano 3           32.3
3                                              C003        Power    Control       Giallo       FROR        3x2.5     Quadro Controllo            QP-76               Quadro piano 2     Ventilatore T1          UT-35             Utenza piano 1           40.7
4                                              C004        Power    Control        Verde   FG16OR16  3x2.5+2.5YG     Quadro Controllo            QP-71               Quadro piano 1     Ventilatore T1          UT-84             Utenza piano 2           69.2
2025-05-26 19:46:40,016 - INFO - Intestazioni dalla seconda riga (openpyxl): ['id_cavo', 'sistema', 'utility', 'colore_cavo', 'tipologia', 'formazione', 'ubicazione_partenza', 'utenza_partenza', 'descrizione_utenza_partenza', 'ubicazione_arrivo', 'utenza_arrivo', 'descrizione_utenza_arrivo', 'metri_teorici']
2025-05-26 19:46:40,016 - INFO - Intestazioni standardizzate: ['id_cavo', 'sistema', 'utility', 'colore_cavo', 'tipologia', 'formazione', 'ubicazione_partenza', 'utenza_partenza', 'descrizione_utenza_partenza', 'ubicazione_arrivo', 'utenza_arrivo', 'descrizione_utenza_arrivo', 'metri_teorici']
2025-05-26 19:46:40,016 - INFO - Colonna obbligatoria 'id_cavo' trovata nelle intestazioni standardizzate
2025-05-26 19:46:40,016 - INFO - Colonna obbligatoria 'utility' trovata nelle intestazioni standardizzate
2025-05-26 19:46:40,016 - INFO - Colonna obbligatoria 'tipologia' trovata nelle intestazioni standardizzate
2025-05-26 19:46:40,016 - ERROR - Colonna obbligatoria 'n_conduttori' non trovata nelle intestazioni standardizzate
2025-05-26 19:46:40,016 - INFO - Colonna obbligatoria 'metri_teorici' trovata nelle intestazioni standardizzate
2025-05-26 19:46:40,016 - INFO - Importazione file Excel: exports\test_cavi_10_20250526_194639.xlsx
2025-05-26 19:46:40,017 - INFO - 🔄 Inizio operazione database [inizializzazione_database]
2025-05-26 19:46:40,072 - INFO - ✅ Inizializzazione database completata
2025-05-26 19:46:40,072 - INFO - ✅ Operazione database completata con successo [inizializzazione_database]
2025-05-26 19:46:40,072 - INFO - 🔄 Inizio operazione database
2025-05-26 19:46:40,116 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:46:40,116 - INFO - 🔄 Inizio operazione database
2025-05-26 19:46:40,157 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:46:40,157 - INFO - 🔄 Inizio operazione database
2025-05-26 19:46:40,228 - INFO - ✅ Backup creato per cantiere Cantiere Test: backups\backup_Cantiere Test_20250526_194640.xlsx
2025-05-26 19:46:40,229 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:46:40,233 - INFO - Rilevata riga di titolo: 'File di test con 10 cavi generati automaticamente...'
2025-05-26 19:46:40,234 - INFO - Utilizzata la seconda riga come intestazioni delle colonne
2025-05-26 19:46:40,236 - INFO - ✅ Rilevata colonna 'formazione', mappata a 'sezione'
2025-05-26 19:46:40,236 - INFO - ✅ Colonna 'n_conduttori' mancante, tentativo di derivazione da formazione/sezione
2025-05-26 19:46:40,237 - INFO - ✅ Colonna 'n_conduttori' derivata da 'formazione'
2025-05-26 19:46:40,240 - INFO - ✅ Dati validati: 10 righe con dati effettivi pronte per l'elaborazione
2025-05-26 19:46:40,241 - INFO - Usando revisione di default: TEST_20250526_194640
2025-05-26 19:46:40,241 - INFO - 🔄 Inizio operazione database
2025-05-26 19:46:40,282 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:46:40,283 - INFO - 🔄 Inizio operazione database
2025-05-26 19:46:40,323 - INFO - Marcando 33 cavi come SPARE usando la funzione specifica
2025-05-26 19:46:40,325 - ERROR - Errore durante la marcatura del cavo TEST20250501103050 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,326 - ERROR - Errore durante la marcatura del cavo TEST20250501103438 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,327 - ERROR - Errore durante la marcatura del cavo TEST20250501110511 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,327 - ERROR - Errore durante la marcatura del cavo TEST_CBGV7 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,328 - ERROR - Errore durante la marcatura del cavo TEST_G4T5W come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,328 - ERROR - Errore durante la marcatura del cavo TEST_EZJZN come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,328 - ERROR - Errore durante la marcatura del cavo TEST_TX_5LQP5 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,329 - ERROR - Errore durante la marcatura del cavo TEST_TX_JW1VQ come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,329 - ERROR - Errore durante la marcatura del cavo TEST_CONC_1738_0 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,329 - ERROR - Errore durante la marcatura del cavo TEST_CONC_1738_2 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,330 - ERROR - Errore durante la marcatura del cavo TEST_CONC_1738_1 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,330 - ERROR - Errore durante la marcatura del cavo TEST_ERR_USSLY come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,330 - ERROR - Errore durante la marcatura del cavo TEST_ERR_PVKW4 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,331 - ERROR - Errore durante la marcatura del cavo TEST_ERR_DL1WI come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,331 - ERROR - Errore durante la marcatura del cavo TEST_CB_Q6W05 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,331 - ERROR - Errore durante la marcatura del cavo TEST_CB_FTM6T come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,332 - ERROR - Errore durante la marcatura del cavo TEST_CB_LUYB6 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,332 - ERROR - Errore durante la marcatura del cavo YRXRISF7 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,332 - ERROR - Errore durante la marcatura del cavo TEST_UPD_Z97KOTBW come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,333 - ERROR - Errore durante la marcatura del cavo TEST_3N5NPKQV come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,333 - ERROR - Errore durante la marcatura del cavo TEST_SUUWFFKO come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,333 - ERROR - Errore durante la marcatura del cavo TEST_UPD_ZGUN682P come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,334 - ERROR - Errore durante la marcatura del cavo TEST_2UG2TZ1K come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,334 - ERROR - Errore durante la marcatura del cavo TEST20250501100824 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,334 - ERROR - Errore durante la marcatura del cavo TEST_MXI2FWYJ come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,335 - ERROR - Errore durante la marcatura del cavo TEST_UPD_Z15AYJ36 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,335 - ERROR - Errore durante la marcatura del cavo TEST_UPD_RIEMQ7JI come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,335 - ERROR - Errore durante la marcatura del cavo TEST_JPYUHP0G come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,336 - ERROR - Errore durante la marcatura del cavo TEST_UPD_DFVUJIKG come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,336 - ERROR - Errore durante la marcatura del cavo TEST_H6RVRWKB come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,336 - ERROR - Errore durante la marcatura del cavo TEST_UPD_3NLTK9F8 come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,337 - ERROR - Errore durante la marcatura del cavo TEST_CB_JX1SJ come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,337 - ERROR - Errore durante la marcatura del cavo TEST_CB_8C4QH come SPARE: ERRORE:  errore di sintassi a o presso ","
LINE 3:             SET stato_installazione = ?,
                                               ^

2025-05-26 19:46:40,337 - INFO - 
==========================================
📊 REPORT ELABORAZIONE CAVI - REVISIONE TEST_20250526_194640
• Totali processati: 10
• Nuovi cavi: 10
• Cavi aggiornati: 0
• Cavi marcati come SPARE: 0
• Cavi SPARE reintegrati: 0
• Conflitti rilevati: 0
• Conflitti fisici (cavi installati): 0
• Variazioni in cavi non installati: 0
==========================================

2025-05-26 19:46:40,337 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:46:40,337 - INFO - 🔄 Inizio operazione database
2025-05-26 19:46:40,384 - INFO - Successo inserimento dei cavi
2025-05-26 19:46:40,385 - INFO - ✅ Commit eseguito con successo
2025-05-26 19:46:40,386 - INFO - ✅ Operazione database completata con successo
2025-05-26 19:46:40,427 - INFO - 
==========================================
✅ IMPORTAZIONE COMPLETATA CON SUCCESSO
• Cantiere: Cantiere Test (ID: 1)
• Revisione: TEST_20250526_194640
• Cavi importati: 10
• Report dettagliato: exports\report_importazione_1_20250526_1946.xlsx
==========================================

2025-05-26 19:46:40,428 - INFO - [SUCCESSO] Importazione completata con successo
2025-05-26 19:46:40,428 - INFO - === FINE TEST IMPORTAZIONE EXCEL ===
