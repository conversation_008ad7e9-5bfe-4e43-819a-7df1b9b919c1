#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per testare l'importazione di file Excel con cavi.
Questo script genera un file di test e poi tenta di importarlo,
catturando e analizzando eventuali errori.
"""

import os
import sys
import logging
import traceback
from datetime import datetime

# Configura il logging
logging.basicConfig(
    level=logging.DEBUG,  # Usa DEBUG per ottenere più informazioni
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("test_import_excel.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# Aggiungi il percorso del progetto al PYTHONPATH
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Importa le funzioni necessarie
from genera_file_test import genera_cavi_test
from modules.excel_manager import importa_cavi_da_excel

def test_import_excel():
    """
    Testa l'importazione di un file Excel con cavi.
    Genera un file di test e poi tenta di importarlo.
    """
    logging.info("=== INIZIO TEST IMPORTAZIONE EXCEL ===")

    try:
        # 1. Genera un file Excel di test
        logging.info("Generazione file Excel di test...")
        file_path, num_cavi = genera_cavi_test(10)  # Genera 10 cavi per il test
        logging.info(f"File generato: {file_path} con {num_cavi} cavi")

        # 2. Verifica che il file esista
        if not os.path.exists(file_path):
            logging.error(f"File non trovato: {file_path}")
            return False

        # 2.1 Verifica le colonne del file Excel
        try:
            import pandas as pd
            df = pd.read_excel(file_path)
            logging.info(f"Colonne nel file Excel (pandas): {list(df.columns)}")

            # Verifica se la colonna id_cavo è presente
            if 'id_cavo' not in df.columns:
                logging.error(f"La colonna 'id_cavo' non è presente nel file Excel. Colonne disponibili: {list(df.columns)}")

                # Verifica se esiste una colonna simile (case-insensitive)
                for col in df.columns:
                    if col.lower() == 'id_cavo':
                        logging.info(f"Trovata colonna simile: '{col}' invece di 'id_cavo'")

                # Mostra le prime righe del file per debug
                logging.info(f"Prime righe del file Excel:\n{df.head().to_string()}")
            else:
                logging.info(f"Colonna 'id_cavo' trovata nel file Excel")

            # Verifica manuale delle colonne usando openpyxl
            from openpyxl import load_workbook
            wb = load_workbook(file_path, read_only=True, data_only=True)
            sheet = wb.active

            # Ottieni le intestazioni dalla seconda riga (indice 1)
            headers = [cell.value for cell in sheet[2]]
            logging.info(f"Intestazioni dalla seconda riga (openpyxl): {headers}")

            # Standardizza i nomi delle colonne come fa valida_colonne_excel
            standardized_headers = [str(h).lower().replace(' ', '_') if h else '' for h in headers]
            logging.info(f"Intestazioni standardizzate: {standardized_headers}")

            # Verifica le colonne obbligatorie
            colonne_obbligatorie = ['id_cavo', 'utility', 'tipologia', 'n_conduttori', 'metri_teorici']
            for col in colonne_obbligatorie:
                if col not in standardized_headers:
                    logging.error(f"Colonna obbligatoria '{col}' non trovata nelle intestazioni standardizzate")

                    # Cerca colonne simili
                    for h in standardized_headers:
                        if h and (col in h or h in col):
                            logging.info(f"Possibile corrispondenza per '{col}': '{h}'")
                else:
                    logging.info(f"Colonna obbligatoria '{col}' trovata nelle intestazioni standardizzate")

        except Exception as e:
            logging.error(f"Errore durante la verifica delle colonne: {str(e)}")
            logging.error(traceback.format_exc())

        # 3. Importa il file Excel
        logging.info(f"Importazione file Excel: {file_path}")
        id_cantiere = 1  # Usa l'ID del cantiere 1 per il test
        revisione = f"TEST_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Attiva la modalità debug per catturare più informazioni
        logging.getLogger().setLevel(logging.DEBUG)

        # Tenta l'importazione con gestione dettagliata degli errori
        try:
            result = importa_cavi_da_excel(
                id_cantiere=id_cantiere,
                percorso_file=file_path,
                revisione_predefinita=revisione,
                non_interattivo=True
            )

            # 4. Verifica il risultato
            if result:
                logging.info("[SUCCESSO] Importazione completata con successo")
                return True
            else:
                logging.error("[ERRORE] Importazione fallita senza eccezioni specifiche")
                return False

        except Exception as import_error:
            error_traceback = traceback.format_exc()
            logging.error(f"[ERRORE DETTAGLIATO] Durante l'importazione: {str(import_error)}")
            logging.error(f"Traceback completo:\n{error_traceback}")

            # Analizza l'errore per fornire informazioni più specifiche
            if "KeyError" in error_traceback:
                logging.error("Possibile problema di chiave mancante nel dizionario")
            elif "TypeError" in error_traceback:
                logging.error("Possibile problema di tipo di dati incompatibile")
            elif "ValueError" in error_traceback:
                logging.error("Possibile problema di valore non valido")
            elif "IndexError" in error_traceback:
                logging.error("Possibile problema di indice fuori intervallo")

            return False

    except Exception as e:
        error_traceback = traceback.format_exc()
        logging.error(f"[ERRORE GENERALE] Durante il test: {str(e)}")
        logging.error(f"Traceback completo:\n{error_traceback}")
        return False
    finally:
        logging.info("=== FINE TEST IMPORTAZIONE EXCEL ===")

if __name__ == "__main__":
    success = test_import_excel()
    sys.exit(0 if success else 1)
