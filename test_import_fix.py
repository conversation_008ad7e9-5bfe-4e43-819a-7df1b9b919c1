#!/usr/bin/env python3
"""
Test rapido per verificare che l'importazione Excel funzioni dopo il fix.
"""

import sys
import os
import tempfile
import pandas as pd
import logging
from pathlib import Path

# Aggiungi il percorso del progetto al Python path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

# Configura il logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_test_excel():
    """Crea un file Excel di test con i campi corretti."""
    
    # Dati di test con tutti i campi obbligatori
    test_data = {
        'id_cavo': ['TEST_FIX_001', 'TEST_FIX_002'],
        'utility': ['Energia', 'Telecom'],
        'tipologia': ['MT', 'BT'],
        'formazione': ['3x2.5', '4x1.5'],
        'metri_teorici': [100, 150]
    }
    
    df = pd.DataFrame(test_data)
    
    # Crea file temporaneo
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
    df.to_excel(temp_file.name, index=False)
    temp_file.close()
    
    print(f"📊 File Excel di test creato: {temp_file.name}")
    print(f"📋 Colonne: {list(df.columns)}")
    print(f"📈 Righe di dati: {len(df)}")
    
    return temp_file.name

def test_import():
    """Testa l'importazione."""
    
    try:
        from webapp.backend.api.excel import importa_cavi_da_excel_webapp
        
        # Crea file di test
        test_file = create_test_excel()
        
        # Test importazione
        print("\n🔄 Test importazione dopo fix...")
        result = importa_cavi_da_excel_webapp(
            id_cantiere=1,
            percorso_file=test_file,
            revisione_predefinita="TEST_FIX"
        )
        
        print(f"\n📊 Risultato:")
        print(f"   Success: {result.get('success', 'N/A')}")
        print(f"   Message: {result.get('message', 'N/A')}")
        
        if result.get('details'):
            details = result['details']
            print(f"   Details:")
            for key, value in details.items():
                print(f"     {key}: {value}")
        
        # Cleanup
        os.unlink(test_file)
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ Errore durante il test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Funzione principale."""
    
    print("🚀 TEST RAPIDO IMPORTAZIONE EXCEL DOPO FIX")
    print("=" * 50)
    
    if test_import():
        print("\n✅ Test PASSATO! L'importazione funziona correttamente.")
        return True
    else:
        print("\n❌ Test FALLITO! Controllare i log per i dettagli.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
