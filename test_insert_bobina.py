#!/usr/bin/env python
# -*- coding: utf-8 -*-

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import random
import string

# Parametri di connessione
conn_params = {
    'host': 'localhost',
    'port': '5432',
    'dbname': 'cantieri',
    'user': 'postgres',
    'password': 'Taranto'
}

# Genera un ID casuale per il test
def random_id():
    return ''.join(random.choices(string.ascii_uppercase + string.digits, k=5))

try:
    # Connessione al database
    print("Connessione al database...")
    conn = psycopg2.connect(**conn_params)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    
    # Genera un ID univoco per la bobina di test
    test_id = random_id()
    id_bobina = f"C1_BTEST_{test_id}"
    numero_bobina = f"TEST_{test_id}"
    
    # Test 1: Inserimento con n_conduttori = 0
    print(f"\nTEST 1: Inserimento con n_conduttori = '0'")
    cursor.execute("""
        INSERT INTO parco_cavi (
            id_bobina, numero_bobina, utility, tipologia, 
            n_conduttori, sezione, metri_totali, metri_residui, 
            stato_bobina, id_cantiere
        ) VALUES (
            %s, %s, 'TEST', 'TEST', 
            '0', '2x1.5', 100, 100, 
            'Disponibile', 1
        ) RETURNING id_bobina
    """, (id_bobina, numero_bobina))
    
    result = cursor.fetchone()
    print(f"Inserimento completato. ID bobina: {result[0]}")
    
    # Verifica il record inserito
    cursor.execute("SELECT id_bobina, numero_bobina, n_conduttori, sezione FROM parco_cavi WHERE id_bobina = %s", (id_bobina,))
    row = cursor.fetchone()
    print(f"Record inserito: ID={row[0]}, Numero={row[1]}, N_Conduttori={row[2]}, Sezione={row[3]}")
    
    # Test 2: Inserimento con n_conduttori vuoto (dovrebbe diventare '0' o '')
    test_id = random_id()
    id_bobina2 = f"C1_BTEST_{test_id}"
    numero_bobina2 = f"TEST_{test_id}"
    
    print(f"\nTEST 2: Inserimento con n_conduttori vuoto")
    cursor.execute("""
        INSERT INTO parco_cavi (
            id_bobina, numero_bobina, utility, tipologia, 
            n_conduttori, sezione, metri_totali, metri_residui, 
            stato_bobina, id_cantiere
        ) VALUES (
            %s, %s, 'TEST', 'TEST', 
            '', '3x2.5+1x1.5', 100, 100, 
            'Disponibile', 1
        ) RETURNING id_bobina
    """, (id_bobina2, numero_bobina2))
    
    result = cursor.fetchone()
    print(f"Inserimento completato. ID bobina: {result[0]}")
    
    # Verifica il record inserito
    cursor.execute("SELECT id_bobina, numero_bobina, n_conduttori, sezione FROM parco_cavi WHERE id_bobina = %s", (id_bobina2,))
    row = cursor.fetchone()
    print(f"Record inserito: ID={row[0]}, Numero={row[1]}, N_Conduttori={row[2]}, Sezione={row[3]}")
    
    print("\nTest completati con successo!")
    
except Exception as e:
    print(f"Errore durante il test: {e}")
finally:
    # Chiudi la connessione
    if 'conn' in locals() and conn:
        cursor.close()
        conn.close()
        print("Connessione al database chiusa.")
