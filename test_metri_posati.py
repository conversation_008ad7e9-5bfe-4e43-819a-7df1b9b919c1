#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script di test per verificare la funzionalità di inserimento metri posati.
Questo script testa direttamente la funzionalità di aggiornamento dei metri posati
per un cavo specifico.
"""

import sys
import os
import json
import requests
from datetime import datetime

# URL base dell'API
API_URL = "http://localhost:8001/api"

# Token di autenticazione (sostituire con un token valido)
TOKEN = None  # Verrà ottenuto durante l'autenticazione

def login():
    """Effettua il login e ottiene un token di autenticazione."""
    global TOKEN

    print("Effettuo login...")

    # Utilizziamo il formato richiesto da OAuth2PasswordRequestForm
    login_data = {
        "username": "admin",
        "password": "admin"
    }

    try:
        # Usa form data invece di JSON per OAuth2
        response = requests.post(
            f"{API_URL}/auth/login", 
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        response.raise_for_status()

        data = response.json()
        TOKEN = data.get("access_token")

        if TOKEN:
            print("✅ Login effettuato con successo")
            return True
        else:
            print("❌ Token non trovato nella risposta")
            return False
    except Exception as e:
        print(f"❌ Errore durante il login: {str(e)}")
        if hasattr(e, 'response'):
            print(f"Dettaglio risposta: {e.response.text}")
        return False

def get_cantieri():
    """Ottiene la lista dei cantieri disponibili."""
    if not TOKEN:
        print("❌ Token non disponibile. Effettuare prima il login.")
        return None

    print("Ottengo la lista dei cantieri...")

    headers = {
        "Authorization": f"Bearer {TOKEN}"
    }

    try:
        response = requests.get(f"{API_URL}/cantieri/", headers=headers)
        response.raise_for_status()

        cantieri = response.json()
        print(f"✅ Trovati {len(cantieri)} cantieri")

        # Stampa i primi 5 cantieri
        for i, cantiere in enumerate(cantieri[:5]):
            print(f"  {i+1}. ID: {cantiere['id_cantiere']}, Nome: {cantiere['nome']}")

        return cantieri
    except Exception as e:
        print(f"❌ Errore durante il recupero dei cantieri: {str(e)}")
        return None

def get_cavi(cantiere_id):
    """Ottiene la lista dei cavi di un cantiere."""
    if not TOKEN:
        print("❌ Token non disponibile. Effettuare prima il login.")
        return None

    print(f"Ottengo la lista dei cavi per il cantiere {cantiere_id}...")

    headers = {
        "Authorization": f"Bearer {TOKEN}"
    }

    try:
        response = requests.get(f"{API_URL}/cavi/{cantiere_id}?tipo_cavo=0", headers=headers)
        response.raise_for_status()

        cavi = response.json()
        print(f"✅ Trovati {len(cavi)} cavi")

        # Filtra i cavi non ancora posati
        cavi_da_posare = [cavo for cavo in cavi if cavo['stato_installazione'] == 'Da installare']
        print(f"✅ Di cui {len(cavi_da_posare)} cavi da posare")

        # Stampa i primi 5 cavi da posare
        for i, cavo in enumerate(cavi_da_posare[:5]):
            print(f"  {i+1}. ID: {cavo['id_cavo']}, Tipologia: {cavo['tipologia']}, Metri teorici: {cavo['metri_teorici']}")

        return cavi_da_posare
    except Exception as e:
        print(f"❌ Errore durante il recupero dei cavi: {str(e)}")
        return None

def get_bobine(cantiere_id):
    """Ottiene la lista delle bobine di un cantiere."""
    if not TOKEN:
        print("❌ Token non disponibile. Effettuare prima il login.")
        return None

    print(f"Ottengo la lista delle bobine per il cantiere {cantiere_id}...")

    headers = {
        "Authorization": f"Bearer {TOKEN}"
    }

    try:
        response = requests.get(f"{API_URL}/parco-cavi/{cantiere_id}", headers=headers)
        response.raise_for_status()

        bobine = response.json()
        print(f"✅ Trovate {len(bobine)} bobine")

        # Filtra le bobine disponibili
        bobine_disponibili = [bobina for bobina in bobine if bobina['stato_bobina'] == 'Disponibile']
        print(f"✅ Di cui {len(bobine_disponibili)} bobine disponibili")

        # Stampa le prime 5 bobine disponibili
        for i, bobina in enumerate(bobine_disponibili[:5]):
            print(f"  {i+1}. ID: {bobina['id_bobina']}, Tipologia: {bobina['tipologia']}, Metri residui: {bobina['metri_residui']}")

        return bobine_disponibili
    except Exception as e:
        print(f"❌ Errore durante il recupero delle bobine: {str(e)}")
        return None

def update_metri_posati(cantiere_id, cavo_id, metri_posati, id_bobina=None, force_over=False):
    """Aggiorna i metri posati di un cavo."""
    if not TOKEN:
        print("❌ Token non disponibile. Effettuare prima il login.")
        return None

    print(f"Aggiorno i metri posati per il cavo {cavo_id} nel cantiere {cantiere_id}...")

    headers = {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }

    # Prepara i dati da inviare
    data = {
        "metri_posati": metri_posati,
        "data_posa": datetime.now().isoformat()
    }

    # Aggiungi id_bobina solo se specificato
    if id_bobina:
        data["id_bobina"] = id_bobina

    # Aggiungi force_over se specificato
    if force_over:
        data["force_over"] = True

    try:
        response = requests.post(
            f"{API_URL}/cavi/{cantiere_id}/{cavo_id}/metri-posati",
            headers=headers,
            json=data
        )
        response.raise_for_status()

        cavo_aggiornato = response.json()
        print(f"✅ Metri posati aggiornati con successo")
        print(f"  - Cavo: {cavo_aggiornato['id_cavo']}")
        print(f"  - Metri posati: {cavo_aggiornato['metratura_reale']}")
        print(f"  - Stato: {cavo_aggiornato['stato_installazione']}")
        print(f"  - Bobina: {cavo_aggiornato['id_bobina'] or 'BOBINA_VUOTA'}")

        return cavo_aggiornato
    except Exception as e:
        print(f"❌ Errore durante l'aggiornamento dei metri posati: {str(e)}")
        if hasattr(e, 'response') and e.response:
            try:
                error_data = e.response.json()
                print(f"  Dettaglio errore: {error_data.get('detail', 'Nessun dettaglio disponibile')}")
            except:
                print(f"  Codice di stato: {e.response.status_code}")
        return None

def verify_cavo(cantiere_id, cavo_id):
    """Verifica lo stato di un cavo dopo l'aggiornamento."""
    if not TOKEN:
        print("❌ Token non disponibile. Effettuare prima il login.")
        return None

    print(f"Verifico lo stato del cavo {cavo_id} nel cantiere {cantiere_id}...")

    headers = {
        "Authorization": f"Bearer {TOKEN}"
    }

    try:
        response = requests.get(f"{API_URL}/cavi/{cantiere_id}/{cavo_id}", headers=headers)
        response.raise_for_status()

        cavo = response.json()
        print(f"✅ Stato del cavo verificato")
        print(f"  - Cavo: {cavo['id_cavo']}")
        print(f"  - Metri posati: {cavo['metratura_reale']}")
        print(f"  - Stato: {cavo['stato_installazione']}")
        print(f"  - Bobina: {cavo['id_bobina'] or 'BOBINA_VUOTA'}")

        return cavo
    except Exception as e:
        print(f"❌ Errore durante la verifica del cavo: {str(e)}")
        return None

def test_metri_posati_bobina_vuota(cantiere_id, cavo_id, metri_posati):
    """Testa l'aggiornamento dei metri posati con BOBINA_VUOTA."""
    print("\n=== Test aggiornamento metri posati con BOBINA_VUOTA ===")

    # Aggiorna i metri posati
    cavo_aggiornato = update_metri_posati(cantiere_id, cavo_id, metri_posati, "BOBINA_VUOTA")

    if cavo_aggiornato:
        # Verifica lo stato del cavo
        cavo_verificato = verify_cavo(cantiere_id, cavo_id)

        if cavo_verificato:
            # Verifica che i metri posati siano stati aggiornati correttamente
            if cavo_verificato['metratura_reale'] == metri_posati:
                print("✅ Test superato: i metri posati sono stati aggiornati correttamente")
            else:
                print(f"❌ Test fallito: i metri posati non corrispondono ({cavo_verificato['metratura_reale']} != {metri_posati})")

            # Verifica che lo stato di installazione sia corretto
            stato_atteso = "Installato" if metri_posati >= cavo_verificato['metri_teorici'] else "In corso"
            if cavo_verificato['stato_installazione'] == stato_atteso:
                print(f"✅ Test superato: lo stato di installazione è corretto ({stato_atteso})")
            else:
                print(f"❌ Test fallito: lo stato di installazione non è corretto ({cavo_verificato['stato_installazione']} != {stato_atteso})")

            # Verifica che la bobina sia NULL (BOBINA_VUOTA)
            if cavo_verificato['id_bobina'] is None:
                print("✅ Test superato: la bobina è NULL (BOBINA_VUOTA)")
            else:
                print(f"❌ Test fallito: la bobina non è NULL ({cavo_verificato['id_bobina']})")

        return cavo_verificato

    return None

def test_metri_posati_bobina_reale(cantiere_id, cavo_id, metri_posati, id_bobina):
    """Testa l'aggiornamento dei metri posati con una bobina reale."""
    print("\n=== Test aggiornamento metri posati con bobina reale ===")

    # Aggiorna i metri posati
    cavo_aggiornato = update_metri_posati(cantiere_id, cavo_id, metri_posati, id_bobina)

    if cavo_aggiornato:
        # Verifica lo stato del cavo
        cavo_verificato = verify_cavo(cantiere_id, cavo_id)

        if cavo_verificato:
            # Verifica che i metri posati siano stati aggiornati correttamente
            if cavo_verificato['metratura_reale'] == metri_posati:
                print("✅ Test superato: i metri posati sono stati aggiornati correttamente")
            else:
                print(f"❌ Test fallito: i metri posati non corrispondono ({cavo_verificato['metratura_reale']} != {metri_posati})")

            # Verifica che lo stato di installazione sia corretto
            stato_atteso = "Installato" if metri_posati >= cavo_verificato['metri_teorici'] else "In corso"
            if cavo_verificato['stato_installazione'] == stato_atteso:
                print(f"✅ Test superato: lo stato di installazione è corretto ({stato_atteso})")
            else:
                print(f"❌ Test fallito: lo stato di installazione non è corretto ({cavo_verificato['stato_installazione']} != {stato_atteso})")

            # Verifica che la bobina sia quella specificata
            if cavo_verificato['id_bobina'] == id_bobina:
                print(f"✅ Test superato: la bobina è corretta ({id_bobina})")
            else:
                print(f"❌ Test fallito: la bobina non è corretta ({cavo_verificato['id_bobina']} != {id_bobina})")

        return cavo_verificato

    return None

def main():
    """Funzione principale."""
    print("=== Test inserimento metri posati ===")

    # Effettua il login
    if not login():
        return

    # Ottieni la lista dei cantieri
    cantieri = get_cantieri()
    if not cantieri:
        return

    # Seleziona il primo cantiere
    cantiere_id = cantieri[0]['id_cantiere']
    print(f"\nSelezionato cantiere: {cantieri[0]['nome']} (ID: {cantiere_id})")

    # Ottieni la lista dei cavi
    cavi = get_cavi(cantiere_id)
    if not cavi:
        return

    # Seleziona il primo cavo da posare
    cavo = cavi[0]
    cavo_id = cavo['id_cavo']
    print(f"\nSelezionato cavo: {cavo_id} (Metri teorici: {cavo['metri_teorici']})")

    # Ottieni la lista delle bobine
    bobine = get_bobine(cantiere_id)
    if not bobine:
        return

    # Test con BOBINA_VUOTA
    metri_posati = float(cavo['metri_teorici']) / 2  # Metà dei metri teorici
    test_metri_posati_bobina_vuota(cantiere_id, cavo_id, metri_posati)

    # Seleziona un altro cavo da posare
    if len(cavi) > 1:
        cavo = cavi[1]
        cavo_id = cavo['id_cavo']
        print(f"\nSelezionato cavo: {cavo_id} (Metri teorici: {cavo['metri_teorici']})")

        # Seleziona la prima bobina disponibile
        if bobine:
            bobina = bobine[0]
            bobina_id = bobina['id_bobina']
            print(f"\nSelezionata bobina: {bobina_id} (Metri residui: {bobina['metri_residui']})")

            # Test con bobina reale
            metri_posati = min(float(cavo['metri_teorici']) / 2, bobina['metri_residui'] / 2)  # Metà dei metri teorici o metà dei metri residui
            test_metri_posati_bobina_reale(cantiere_id, cavo_id, metri_posati, bobina_id)

if __name__ == "__main__":
    main()
