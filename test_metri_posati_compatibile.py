#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script di test per verificare la funzionalità di inserimento metri posati con bobina compatibile.
"""

import requests
import json
from datetime import datetime
import random
import string

# URL base dell'API
API_URL = "http://localhost:8001/api"

def generate_random_id(length=8):
    """Genera un ID casuale di lunghezza specificata."""
    return ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))

def login():
    """Effettua il login e ottiene un token di autenticazione."""
    print("Effettuo login...")

    login_data = {
        "username": "admin",
        "password": "admin"
    }

    try:
        response = requests.post(
            f"{API_URL}/auth/login", 
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        response.raise_for_status()

        data = response.json()
        token = data.get("access_token")

        if token:
            print("✅ Login effettuato con successo")
            return token
        else:
            print("❌ Token non trovato nella risposta")
            return None
    except Exception as e:
        print(f"❌ Errore durante il login: {str(e)}")
        if hasattr(e, 'response'):
            print(f"Dettaglio risposta: {e.response.text}")
        return None

def get_cantieri(token):
    """Ottiene la lista dei cantieri disponibili."""
    if not token:
        print("❌ Token non disponibile. Effettuare prima il login.")
        return None

    print("Ottengo la lista dei cantieri...")

    headers = {
        "Authorization": f"Bearer {token}"
    }

    try:
        response = requests.get(f"{API_URL}/cantieri/", headers=headers)
        response.raise_for_status()

        cantieri = response.json()
        print(f"✅ Trovati {len(cantieri)} cantieri")

        # Stampa i cantieri
        for i, cantiere in enumerate(cantieri):
            print(f"  {i+1}. ID: {cantiere['id_cantiere']}, Nome: {cantiere['nome']}")

        return cantieri
    except Exception as e:
        print(f"❌ Errore durante il recupero dei cantieri: {str(e)}")
        return None

def create_bobina(token, cantiere_id, tipologia, n_conduttori, sezione, metri_totali):
    """Crea una nuova bobina."""
    if not token:
        print("❌ Token non disponibile. Effettuare prima il login.")
        return None

    print(f"Creo una nuova bobina per il cantiere {cantiere_id}...")

    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    # Genera un ID univoco per la bobina
    numero_bobina = f"TEST_{generate_random_id(5)}"

    # Prepara i dati da inviare
    data = {
        "numero_bobina": numero_bobina,
        "utility": "TEST",
        "tipologia": tipologia,
        "n_conduttori": n_conduttori,
        "sezione": sezione,
        "metri_totali": metri_totali,
        "metri_residui": metri_totali,
        "stato_bobina": "Disponibile",
        "ubicazione_bobina": "TEST",
        "fornitore": "TEST",
        "n_DDT": "TEST",
        "data_DDT": "2025-05-17",
        "configurazione": "TEST"
    }

    try:
        response = requests.post(
            f"{API_URL}/parco-cavi/{cantiere_id}",
            headers=headers,
            json=data
        )
        response.raise_for_status()

        bobina = response.json()
        print(f"✅ Bobina creata con successo")
        print(f"  - ID: {bobina['id_bobina']}")
        print(f"  - Tipologia: {bobina['tipologia']}")
        print(f"  - Conduttori: {bobina['n_conduttori']}")
        print(f"  - Sezione: {bobina['sezione']}")
        print(f"  - Metri totali: {bobina['metri_totali']}")

        return bobina
    except Exception as e:
        print(f"❌ Errore durante la creazione della bobina: {str(e)}")
        if hasattr(e, 'response'):
            print(f"Dettaglio risposta: {e.response.text}")
        return None

def create_cavo(token, cantiere_id, tipologia, n_conduttori, sezione, metri_teorici):
    """Crea un nuovo cavo."""
    if not token:
        print("❌ Token non disponibile. Effettuare prima il login.")
        return None

    print(f"Creo un nuovo cavo per il cantiere {cantiere_id}...")

    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    # Genera un ID univoco per il cavo
    id_cavo = f"TEST_CB_{generate_random_id(5)}"

    # Prepara i dati da inviare
    data = {
        "id_cavo": id_cavo,
        "revisione_ufficiale": "00",
        "sistema": "TEST",
        "utility": "TEST",
        "colore_cavo": "NERO",
        "tipologia": tipologia,
        "n_conduttori": n_conduttori,
        "sezione": sezione,
        "sh": "-",
        "ubicazione_partenza": "TEST",
        "utenza_partenza": "TEST",
        "descrizione_utenza_partenza": "TEST",
        "ubicazione_arrivo": "TEST",
        "utenza_arrivo": "TEST",
        "descrizione_utenza_arrivo": "TEST",
        "metri_teorici": metri_teorici
    }

    try:
        response = requests.post(
            f"{API_URL}/cavi/{cantiere_id}",
            headers=headers,
            json=data
        )
        response.raise_for_status()

        cavo = response.json()
        print(f"✅ Cavo creato con successo")
        print(f"  - ID: {cavo['id_cavo']}")
        print(f"  - Tipologia: {cavo['tipologia']}")
        print(f"  - Conduttori: {cavo['n_conduttori']}")
        print(f"  - Sezione: {cavo['sezione']}")
        print(f"  - Metri teorici: {cavo['metri_teorici']}")

        return cavo
    except Exception as e:
        print(f"❌ Errore durante la creazione del cavo: {str(e)}")
        if hasattr(e, 'response'):
            print(f"Dettaglio risposta: {e.response.text}")
        return None

def update_metri_posati(token, cantiere_id, cavo_id, metri_posati, id_bobina=None):
    """Aggiorna i metri posati di un cavo."""
    if not token:
        print("❌ Token non disponibile. Effettuare prima il login.")
        return None

    print(f"Aggiorno i metri posati per il cavo {cavo_id} nel cantiere {cantiere_id}...")

    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    # Prepara i dati da inviare
    data = {
        "metri_posati": metri_posati,
        "data_posa": datetime.now().isoformat()
    }

    # Aggiungi id_bobina solo se specificato
    if id_bobina:
        data["id_bobina"] = id_bobina

    try:
        response = requests.post(
            f"{API_URL}/cavi/{cantiere_id}/{cavo_id}/metri-posati",
            headers=headers,
            json=data
        )
        response.raise_for_status()

        cavo_aggiornato = response.json()
        print(f"✅ Metri posati aggiornati con successo")
        print(f"  - Cavo: {cavo_aggiornato['id_cavo']}")
        print(f"  - Metri posati: {cavo_aggiornato['metratura_reale']}")
        print(f"  - Stato: {cavo_aggiornato['stato_installazione']}")
        print(f"  - Bobina: {cavo_aggiornato['id_bobina'] or 'BOBINA_VUOTA'}")

        return cavo_aggiornato
    except Exception as e:
        print(f"❌ Errore durante l'aggiornamento dei metri posati: {str(e)}")
        if hasattr(e, 'response'):
            print(f"Dettaglio risposta: {e.response.text}")
        return None

def get_bobine(token, cantiere_id):
    """Ottiene la lista delle bobine di un cantiere."""
    if not token:
        print("❌ Token non disponibile. Effettuare prima il login.")
        return None

    print(f"Ottengo la lista delle bobine per il cantiere {cantiere_id}...")

    headers = {
        "Authorization": f"Bearer {token}"
    }

    try:
        response = requests.get(f"{API_URL}/parco-cavi/{cantiere_id}", headers=headers)
        response.raise_for_status()

        bobine = response.json()
        print(f"✅ Trovate {len(bobine)} bobine")

        # Stampa le prime 5 bobine
        for i, bobina in enumerate(bobine[:5]):
            print(f"  {i+1}. ID: {bobina['id_bobina']}, Tipologia: {bobina['tipologia']}, Stato: {bobina['stato_bobina']}")

        return bobine
    except Exception as e:
        print(f"❌ Errore durante il recupero delle bobine: {str(e)}")
        if hasattr(e, 'response'):
            print(f"Dettaglio risposta: {e.response.text}")
        return None

def get_bobina_by_id(token, cantiere_id, bobina_id):
    """Ottiene i dettagli di una bobina specifica."""
    if not token:
        print("❌ Token non disponibile. Effettuare prima il login.")
        return None

    print(f"Ottengo i dettagli della bobina {bobina_id} nel cantiere {cantiere_id}...")

    # Ottieni tutte le bobine e filtra per ID
    bobine = get_bobine(token, cantiere_id)
    if not bobine:
        return None

    # Cerca la bobina con l'ID specificato
    bobina = next((b for b in bobine if b['id_bobina'] == bobina_id), None)

    if bobina:
        print(f"✅ Dettagli bobina ottenuti con successo")
        print(f"  - ID: {bobina['id_bobina']}")
        print(f"  - Tipologia: {bobina['tipologia']}")
        print(f"  - Conduttori: {bobina['n_conduttori']}")
        print(f"  - Sezione: {bobina['sezione']}")
        print(f"  - Metri residui: {bobina['metri_residui']}")
        print(f"  - Stato: {bobina['stato_bobina']}")

        return bobina
    else:
        print(f"❌ Bobina con ID {bobina_id} non trovata")
        return None

def main():
    """Funzione principale."""
    print("=== Test inserimento metri posati con bobina compatibile ===")

    # Effettua il login
    token = login()
    if not token:
        return

    # Ottieni la lista dei cantieri
    cantieri = get_cantieri(token)
    if not cantieri:
        return

    # Seleziona il primo cantiere
    cantiere_id = cantieri[0]['id_cantiere']
    print(f"\nSelezionato cantiere: {cantieri[0]['nome']} (ID: {cantiere_id})")

    # Crea una nuova bobina
    tipologia = "TEST_COMPATIBILE"
    n_conduttori = "4"
    sezione = "1.5"
    metri_totali = 100.0

    bobina = create_bobina(token, cantiere_id, tipologia, n_conduttori, sezione, metri_totali)
    if not bobina:
        return

    # Crea un nuovo cavo compatibile con la bobina
    metri_teorici = 50.0

    cavo = create_cavo(token, cantiere_id, tipologia, n_conduttori, sezione, metri_teorici)
    if not cavo:
        return

    # Aggiorna i metri posati del cavo con la bobina compatibile
    metri_posati = 25.0  # Metà dei metri teorici

    cavo_aggiornato = update_metri_posati(token, cantiere_id, cavo['id_cavo'], metri_posati, bobina['id_bobina'])
    if not cavo_aggiornato:
        return

    # Verifica che i metri residui della bobina siano stati aggiornati correttamente
    bobina_aggiornata = get_bobina_by_id(token, cantiere_id, bobina['id_bobina'])
    if not bobina_aggiornata:
        return

    # Verifica che i metri residui siano stati aggiornati correttamente
    metri_residui_attesi = metri_totali - metri_posati
    if bobina_aggiornata['metri_residui'] == metri_residui_attesi:
        print(f"\n✅ Metri residui della bobina aggiornati correttamente: {bobina_aggiornata['metri_residui']} (attesi: {metri_residui_attesi})")
    else:
        print(f"\n❌ Metri residui della bobina non aggiornati correttamente: {bobina_aggiornata['metri_residui']} (attesi: {metri_residui_attesi})")

    # Verifica che lo stato della bobina sia stato aggiornato correttamente
    stato_atteso = "In uso"
    if bobina_aggiornata['stato_bobina'] == stato_atteso:
        print(f"✅ Stato della bobina aggiornato correttamente: {bobina_aggiornata['stato_bobina']} (atteso: {stato_atteso})")
    else:
        print(f"❌ Stato della bobina non aggiornato correttamente: {bobina_aggiornata['stato_bobina']} (atteso: {stato_atteso})")

    print("\nTest completato con successo!")

if __name__ == "__main__":
    main()
