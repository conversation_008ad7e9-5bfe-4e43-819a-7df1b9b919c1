#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script di test semplificato per verificare la funzionalità di inserimento metri posati.
"""

import requests
import json
from datetime import datetime

# URL base dell'API
API_URL = "http://localhost:8001/api"

def login():
    """Effettua il login e ottiene un token di autenticazione."""
    print("Effettuo login...")

    login_data = {
        "username": "admin",
        "password": "admin"
    }

    try:
        response = requests.post(f"{API_URL}/auth/login", data=login_data)
        response.raise_for_status()

        data = response.json()
        token = data.get("access_token")

        if token:
            print("✅ Login effettuato con successo")
            return token
        else:
            print("❌ Token non trovato nella risposta")
            return None
    except Exception as e:
        print(f"❌ Errore durante il login: {str(e)}")
        if hasattr(e, 'response'):
            print(f"Dettaglio risposta: {e.response.text}")
        return None

def get_cantieri(token):
    """Ottiene la lista dei cantieri disponibili."""
    if not token:
        print("❌ Token non disponibile. Effettuare prima il login.")
        return None

    print("Ottengo la lista dei cantieri...")

    headers = {
        "Authorization": f"Bearer {token}"
    }

    try:
        response = requests.get(f"{API_URL}/cantieri/", headers=headers)
        response.raise_for_status()

        cantieri = response.json()
        print(f"✅ Trovati {len(cantieri)} cantieri")

        # Stampa i cantieri
        for i, cantiere in enumerate(cantieri):
            print(f"  {i+1}. ID: {cantiere['id_cantiere']}, Nome: {cantiere['nome']}")

        return cantieri
    except Exception as e:
        print(f"❌ Errore durante il recupero dei cantieri: {str(e)}")
        return None

def get_cavi(token, cantiere_id):
    """Ottiene la lista dei cavi di un cantiere."""
    if not token:
        print("❌ Token non disponibile. Effettuare prima il login.")
        return None

    print(f"Ottengo la lista dei cavi per il cantiere {cantiere_id}...")

    headers = {
        "Authorization": f"Bearer {token}"
    }

    try:
        response = requests.get(f"{API_URL}/cavi/{cantiere_id}", headers=headers)
        response.raise_for_status()

        cavi = response.json()
        print(f"✅ Trovati {len(cavi)} cavi")

        # Stampa i primi 5 cavi
        for i, cavo in enumerate(cavi[:5]):
            print(f"  {i+1}. ID: {cavo['id_cavo']}, Tipologia: {cavo['tipologia']}, Stato: {cavo['stato_installazione']}")

        return cavi
    except Exception as e:
        print(f"❌ Errore durante il recupero dei cavi: {str(e)}")
        if hasattr(e, 'response'):
            print(f"Dettaglio risposta: {e.response.text}")
        return None

def get_bobine(token, cantiere_id):
    """Ottiene la lista delle bobine di un cantiere."""
    if not token:
        print("❌ Token non disponibile. Effettuare prima il login.")
        return None

    print(f"Ottengo la lista delle bobine per il cantiere {cantiere_id}...")

    headers = {
        "Authorization": f"Bearer {token}"
    }

    try:
        response = requests.get(f"{API_URL}/parco-cavi/{cantiere_id}", headers=headers)
        response.raise_for_status()

        bobine = response.json()
        print(f"✅ Trovate {len(bobine)} bobine")

        # Stampa le prime 5 bobine
        for i, bobina in enumerate(bobine[:5]):
            print(f"  {i+1}. ID: {bobina['id_bobina']}, Tipologia: {bobina['tipologia']}, Stato: {bobina['stato_bobina']}")

        return bobine
    except Exception as e:
        print(f"❌ Errore durante il recupero delle bobine: {str(e)}")
        if hasattr(e, 'response'):
            print(f"Dettaglio risposta: {e.response.text}")
        return None

def update_metri_posati(token, cantiere_id, cavo_id, metri_posati, id_bobina=None):
    """Aggiorna i metri posati di un cavo."""
    if not token:
        print("❌ Token non disponibile. Effettuare prima il login.")
        return None

    print(f"Aggiorno i metri posati per il cavo {cavo_id} nel cantiere {cantiere_id}...")

    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    # Prepara i dati da inviare
    data = {
        "metri_posati": metri_posati,
        "data_posa": datetime.now().isoformat()
    }

    # Aggiungi id_bobina solo se specificato
    if id_bobina:
        data["id_bobina"] = id_bobina

    try:
        response = requests.post(
            f"{API_URL}/cavi/{cantiere_id}/{cavo_id}/metri-posati",
            headers=headers,
            json=data
        )
        response.raise_for_status()

        cavo_aggiornato = response.json()
        print(f"✅ Metri posati aggiornati con successo")
        print(f"  - Cavo: {cavo_aggiornato['id_cavo']}")
        print(f"  - Metri posati: {cavo_aggiornato['metratura_reale']}")
        print(f"  - Stato: {cavo_aggiornato['stato_installazione']}")
        print(f"  - Bobina: {cavo_aggiornato['id_bobina'] or 'BOBINA_VUOTA'}")

        return cavo_aggiornato
    except Exception as e:
        print(f"❌ Errore durante l'aggiornamento dei metri posati: {str(e)}")
        if hasattr(e, 'response'):
            print(f"Dettaglio risposta: {e.response.text}")
        return None

def main():
    """Funzione principale."""
    print("=== Test inserimento metri posati (semplificato) ===")

    # Effettua il login
    token = login()
    if not token:
        return

    # Ottieni la lista dei cantieri
    cantieri = get_cantieri(token)
    if not cantieri:
        return

    # Seleziona il primo cantiere
    cantiere_id = cantieri[0]['id_cantiere']
    print(f"\nSelezionato cantiere: {cantieri[0]['nome']} (ID: {cantiere_id})")

    # Ottieni la lista dei cavi
    cavi = get_cavi(token, cantiere_id)
    if not cavi:
        return

    # Filtra i cavi non ancora posati
    cavi_da_posare = [cavo for cavo in cavi if cavo['stato_installazione'] == 'Da installare']
    if not cavi_da_posare:
        print("\nNon ci sono cavi da posare in questo cantiere.")
        return

    # Seleziona il primo cavo da posare
    cavo = cavi_da_posare[0]
    cavo_id = cavo['id_cavo']
    print(f"\nSelezionato cavo: {cavo_id} (Metri teorici: {cavo['metri_teorici']})")

    # Ottieni la lista delle bobine
    bobine = get_bobine(token, cantiere_id)
    if not bobine:
        return

    # Filtra le bobine disponibili
    bobine_disponibili = [bobina for bobina in bobine if bobina['stato_bobina'] == 'Disponibile']
    if not bobine_disponibili:
        print("\nNon ci sono bobine disponibili in questo cantiere.")

    # Test con BOBINA_VUOTA
    metri_posati = float(cavo['metri_teorici']) / 2  # Metà dei metri teorici
    print(f"\nTest con BOBINA_VUOTA - Metri posati: {metri_posati}")
    update_metri_posati(token, cantiere_id, cavo_id, metri_posati, "BOBINA_VUOTA")

    # Se ci sono bobine disponibili, prova anche con una bobina reale
    if bobine_disponibili:
        # Cerca una bobina compatibile
        bobina_compatibile = None
        for bobina in bobine_disponibili:
            if bobina['tipologia'] == cavo['tipologia'] and bobina['n_conduttori'] == cavo['n_conduttori']:
                bobina_compatibile = bobina
                break

        if bobina_compatibile:
            bobina_id = bobina_compatibile['id_bobina']
            print(f"\nSelezionata bobina compatibile: {bobina_id} (Tipologia: {bobina_compatibile['tipologia']}, Conduttori: {bobina_compatibile['n_conduttori']}, Metri residui: {bobina_compatibile['metri_residui']})")

            # Test con bobina reale
            metri_posati = min(float(cavo['metri_teorici']) / 2, bobina_compatibile['metri_residui'] / 2)  # Metà dei metri teorici o metà dei metri residui
            print(f"\nTest con bobina reale - Metri posati: {metri_posati}")
            update_metri_posati(token, cantiere_id, cavo_id, metri_posati, bobina_id)
        else:
            print("\nNon sono state trovate bobine compatibili con il cavo selezionato.")

    print("\nTest completato con successo!")

if __name__ == "__main__":
    main()
