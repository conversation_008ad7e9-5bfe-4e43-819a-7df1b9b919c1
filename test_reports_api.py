#!/usr/bin/env python3
"""
Test script per verificare che i report API funzionino correttamente.
"""

import requests
import time
import json

def test_reports():
    # Aspetta che il server si avvii
    print('⏳ Aspettando che il server si avvii...')
    time.sleep(10)

    try:
        # Test Bill of Quantities
        print('🧪 Test API Bill of Quantities...')
        response = requests.get(
            'http://localhost:8002/api/reports/10/boq?formato=video',
            timeout=10
        )
        print(f'Status: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print('✅ BOQ Response SUCCESS!')
            content = data.get('content', {})
            print(f'Nome cantiere: {content.get("nome_cantiere", "N/A")}')
            print(f'Cavi per tipo: {len(content.get("cavi_per_tipo", []))}')
            print(f'Bobine per tipo: {len(content.get("bobine_per_tipo", []))}')
        else:
            print(f'❌ BOQ Error: {response.text[:200]}')
        
        print()
        
        # Test Report Bobine
        print('🧪 Test API Report Bobine...')
        response = requests.get(
            'http://localhost:8002/api/reports/10/bobine?formato=video',
            timeout=10
        )
        print(f'Status: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print('✅ Bobine Response SUCCESS!')
            content = data.get('content', {})
            print(f'Nome cantiere: {content.get("nome_cantiere", "N/A")}')
            print(f'Totale bobine: {content.get("totale_bobine", 0)}')
        else:
            print(f'❌ Bobine Error: {response.text[:200]}')
            
    except Exception as e:
        print(f'❌ Errore di connessione: {e}')

if __name__ == "__main__":
    test_reports()
