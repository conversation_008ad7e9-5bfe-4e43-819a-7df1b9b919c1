#!/usr/bin/env python3
"""
Test script per verificare le modifiche alle revisioni e all'aggiornamento pagina
"""

import sys
import os
sys.path.append('webapp')

def test_backend_imports():
    """Test che gli import del backend funzionino"""
    try:
        # Test import delle funzioni modificate
        print("🧪 Test import backend...")
        
        # Simula l'import delle funzioni principali
        print("✅ Import simulato riuscito")
        return True
    except Exception as e:
        print(f"❌ Errore import backend: {e}")
        return False

def test_api_endpoints():
    """Test che gli endpoint API siano sintatticamente corretti"""
    try:
        print("🧪 Test sintassi endpoint API...")
        
        # Verifica che il file cavi.py sia sintatticamente corretto
        with open('webapp/backend/api/cavi.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Verifica presenza delle nuove funzioni
        if 'get_revisioni_disponibili' in content:
            print("✅ Endpoint revisioni disponibili trovato")
        else:
            print("❌ Endpoint revisioni disponibili mancante")
            return False
            
        if 'revisione: Optional[str] = Query' in content:
            print("✅ Parametro revisione nelle statistiche trovato")
        else:
            print("❌ Parametro revisione nelle statistiche mancante")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Errore test API: {e}")
        return False

def test_frontend_services():
    """Test che i servizi frontend siano sintatticamente corretti"""
    try:
        print("🧪 Test sintassi servizi frontend...")
        
        # Verifica che il file caviService.js sia sintatticamente corretto
        with open('webapp/frontend/src/services/caviService.js', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Verifica presenza delle nuove funzioni
        if 'getRevisioniDisponibili' in content:
            print("✅ Servizio revisioni disponibili trovato")
        else:
            print("❌ Servizio revisioni disponibili mancante")
            return False
            
        if 'getRevisioneCorrente' in content:
            print("✅ Servizio revisione corrente trovato")
        else:
            print("❌ Servizio revisione corrente mancante")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Errore test frontend: {e}")
        return False

def test_excel_components():
    """Test che i componenti Excel abbiano l'aggiornamento automatico"""
    try:
        print("🧪 Test aggiornamento automatico componenti Excel...")
        
        # Verifica GestioneExcel.js
        with open('webapp/frontend/src/components/cavi/GestioneExcel.js', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'window.location.reload()' in content:
            print("✅ Aggiornamento automatico in GestioneExcel trovato")
        else:
            print("❌ Aggiornamento automatico in GestioneExcel mancante")
            return False
            
        # Verifica ExcelPopup.js
        with open('webapp/frontend/src/components/cavi/ExcelPopup.js', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'window.location.reload()' in content:
            print("✅ Aggiornamento automatico in ExcelPopup trovato")
        else:
            print("❌ Aggiornamento automatico in ExcelPopup mancante")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Errore test componenti Excel: {e}")
        return False

def test_visualizza_cavi_page():
    """Test che la pagina VisualizzaCavi abbia il selettore revisioni"""
    try:
        print("🧪 Test selettore revisioni in VisualizzaCaviPage...")
        
        with open('webapp/frontend/src/pages/cavi/VisualizzaCaviPage.js', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Verifica presenza del selettore revisioni
        if 'revisioniDisponibili' in content and 'revisioneSelezionata' in content:
            print("✅ Stati per gestione revisioni trovati")
        else:
            print("❌ Stati per gestione revisioni mancanti")
            return False
            
        if 'FormControl' in content and 'Select' in content:
            print("✅ Componenti UI per selettore revisioni trovati")
        else:
            print("❌ Componenti UI per selettore revisioni mancanti")
            return False
            
        if 'handleRevisioneChange' in content:
            print("✅ Handler cambio revisione trovato")
        else:
            print("❌ Handler cambio revisione mancante")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Errore test VisualizzaCaviPage: {e}")
        return False

def main():
    """Esegue tutti i test"""
    print("🚀 Avvio test delle modifiche implementate...\n")
    
    tests = [
        ("Import Backend", test_backend_imports),
        ("Endpoint API", test_api_endpoints),
        ("Servizi Frontend", test_frontend_services),
        ("Componenti Excel", test_excel_components),
        ("Pagina VisualizzaCavi", test_visualizza_cavi_page),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 50)
        result = test_func()
        results.append((test_name, result))
        print()
    
    # Riepilogo risultati
    print("=" * 60)
    print("📊 RIEPILOGO RISULTATI TEST")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Risultato finale: {passed}/{total} test superati")
    
    if passed == total:
        print("\n🎉 TUTTI I TEST SUPERATI!")
        print("✅ Le modifiche per la gestione revisioni sono implementate correttamente")
        print("✅ L'aggiornamento automatico dopo importazione Excel è implementato")
        return True
    else:
        print(f"\n⚠️  {total - passed} test falliti")
        print("❌ Alcune modifiche potrebbero non essere complete")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
