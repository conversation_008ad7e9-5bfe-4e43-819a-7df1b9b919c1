#!/usr/bin/env python3
"""
Test per verificare che il sistema gestisca correttamente le revisioni inserite dall'utente
"""

import sys
import os
sys.path.append('webapp')

def test_backend_revision_handling():
    """Test della gestione revisioni nel backend"""
    print("🧪 Test gestione revisioni backend...")
    
    with open('webapp/backend/api/excel.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("Revisione obbligatoria nel Form", "Form(..., description=" in content),
        ("Validazione revisione obbligatoria", "Codice revisione obbligatorio" in content),
        ("Nessuna generazione automatica", "REV_%Y%m%d_%H%M" not in content or "auto-generated" not in content),
        ("Uso revisione utente", "user_revision = revisione.strip()" in content),
        ("Logica revisioni documentata", "LOGICA REVISIONI:" in content)
    ]
    
    passed = 0
    for check_name, condition in checks:
        status = "✅" if condition else "❌"
        print(f"  {status} {check_name}")
        if condition:
            passed += 1
    
    return passed, len(checks)

def test_frontend_revision_ui():
    """Test dell'interfaccia utente per le revisioni"""
    print("\n🧪 Test interfaccia utente revisioni...")
    
    # Test GestioneExcel.js
    with open('webapp/frontend/src/components/cavi/GestioneExcel.js', 'r', encoding='utf-8') as f:
        gestione_content = f.read()
    
    # Test ExcelPopup.js
    with open('webapp/frontend/src/components/cavi/ExcelPopup.js', 'r', encoding='utf-8') as f:
        popup_content = f.read()
    
    checks = [
        ("Campo revisione obbligatorio (GestioneExcel)", "required" in gestione_content and "revisione" in gestione_content),
        ("Asterisco rosso obbligatorio (GestioneExcel)", "color: 'red'" in gestione_content and "*" in gestione_content),
        ("Validazione errore (GestioneExcel)", "error={" in gestione_content and "helperText=" in gestione_content),
        ("Testo OBBLIGATORIO (GestioneExcel)", "OBBLIGATORIO:" in gestione_content),
        ("Campo revisione obbligatorio (ExcelPopup)", "required" in popup_content and "revisione" in popup_content),
        ("Asterisco rosso obbligatorio (ExcelPopup)", "color: 'red'" in popup_content and "*" in popup_content),
        ("Validazione errore (ExcelPopup)", "error={" in popup_content and "helperText=" in popup_content),
        ("Testo OBBLIGATORIO (ExcelPopup)", "OBBLIGATORIO:" in popup_content)
    ]
    
    passed = 0
    for check_name, condition in checks:
        status = "✅" if condition else "❌"
        print(f"  {status} {check_name}")
        if condition:
            passed += 1
    
    return passed, len(checks)

def test_revision_validation_logic():
    """Test della logica di validazione revisioni"""
    print("\n🧪 Test logica validazione revisioni...")
    
    with open('modules/excel_manager.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("Funzione richiedi_revisione presente", "def richiedi_revisione(" in content),
        ("Modalità non interattiva supportata", "non_interattivo" in content),
        ("Validazione revisione vuota", "if not revisione:" in content),
        ("Accettazione qualsiasi formato", "Accetta qualsiasi formato" in content or "return revisione" in content),
        ("Gestione errori input", "KeyboardInterrupt" in content and "Exception" in content)
    ]
    
    passed = 0
    for check_name, condition in checks:
        status = "✅" if condition else "❌"
        print(f"  {status} {check_name}")
        if condition:
            passed += 1
    
    return passed, len(checks)

def test_revision_examples():
    """Test degli esempi di revisione forniti"""
    print("\n🧪 Test esempi revisioni...")
    
    # Test backend
    with open('webapp/backend/api/excel.py', 'r', encoding='utf-8') as f:
        backend_content = f.read()
    
    # Test frontend
    with open('webapp/frontend/src/components/cavi/GestioneExcel.js', 'r', encoding='utf-8') as f:
        frontend1_content = f.read()
    
    with open('webapp/frontend/src/components/cavi/ExcelPopup.js', 'r', encoding='utf-8') as f:
        frontend2_content = f.read()
    
    checks = [
        ("Esempi nel backend", "REV_001" in backend_content and "V1.0" in backend_content),
        ("Esempi nel frontend (GestioneExcel)", "REV_001" in frontend1_content and "V1.0" in frontend1_content),
        ("Esempi nel frontend (ExcelPopup)", "REV_001" in frontend2_content and "V1.0" in frontend2_content),
        ("Esempio UFFICIALE", "UFFICIALE_01" in backend_content or "UFFICIALE" in frontend1_content or "UFFICIALE" in frontend2_content),
        ("Placeholder informativi", "placeholder=" in frontend1_content and "placeholder=" in frontend2_content)
    ]
    
    passed = 0
    for check_name, condition in checks:
        status = "✅" if condition else "❌"
        print(f"  {status} {check_name}")
        if condition:
            passed += 1
    
    return passed, len(checks)

def test_no_auto_generation():
    """Test che non ci sia generazione automatica di revisioni"""
    print("\n🧪 Test assenza generazione automatica...")
    
    with open('webapp/backend/api/excel.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Cerca pattern di generazione automatica
    auto_patterns = [
        "datetime.now().strftime(\"REV_%Y%m%d_%H%M\")",
        "auto-generated",
        "generate.*revision",
        "default.*revision.*datetime"
    ]
    
    checks = []
    for pattern in auto_patterns:
        found = pattern.lower() in content.lower()
        checks.append((f"Nessun pattern '{pattern}'", not found))
    
    # Verifica che ci sia solo validazione obbligatoria
    checks.append(("Validazione obbligatoria presente", "obbligatorio" in content.lower()))
    checks.append(("Errore se mancante", "HTTPException" in content and "400" in content))
    
    passed = 0
    for check_name, condition in checks:
        status = "✅" if condition else "❌"
        print(f"  {status} {check_name}")
        if condition:
            passed += 1
    
    return passed, len(checks)

def main():
    """Esegue tutti i test"""
    print("🚀 Test Gestione Revisioni Inserite dall'Utente\n")
    
    tests = [
        ("Gestione Revisioni Backend", test_backend_revision_handling),
        ("Interfaccia Utente Revisioni", test_frontend_revision_ui),
        ("Logica Validazione Revisioni", test_revision_validation_logic),
        ("Esempi Revisioni", test_revision_examples),
        ("Assenza Generazione Automatica", test_no_auto_generation)
    ]
    
    total_passed = 0
    total_checks = 0
    
    for nome_test, func_test in tests:
        print(f"📋 {nome_test}")
        print("-" * 50)
        passed, checks = func_test()
        total_passed += passed
        total_checks += checks
        print()
    
    print("=" * 60)
    print("📊 RISULTATO FINALE")
    print("=" * 60)
    
    percentuale = (total_passed / total_checks) * 100 if total_checks > 0 else 0
    
    print(f"✅ Test superati: {total_passed}/{total_checks}")
    print(f"📈 Percentuale successo: {percentuale:.1f}%")
    
    if percentuale >= 95:
        print("\n🎉 ECCELLENTE! Il sistema gestisce correttamente le revisioni utente!")
        print("✅ Le revisioni sono obbligatorie e inserite dall'utente")
        print("✅ Nessuna generazione automatica")
        print("✅ Interfaccia utente chiara e intuitiva")
        print("✅ Validazione corretta e messaggi di errore appropriati")
    elif percentuale >= 85:
        print("\n👍 BUONO! Il sistema gestisce bene le revisioni utente")
        print("⚠️  Alcuni aspetti minori potrebbero essere migliorati")
    else:
        print("\n❌ PROBLEMI! Il sistema non gestisce correttamente le revisioni utente")
        print("🔧 Sono necessarie correzioni")
    
    return percentuale >= 95

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
