#!/usr/bin/env python3
"""
Test per verificare che il nuovo run_system_simple.py funzioni correttamente.
"""

import sys
import os
import time
import subprocess
from pathlib import Path

def test_script_syntax():
    """Testa la sintassi del nuovo script."""
    
    print("🔍 Test sintassi run_system_simple.py...")
    
    script_path = Path("webapp/run_system_simple.py")
    
    if not script_path.exists():
        print(f"❌ Script non trovato: {script_path}")
        return False
    
    try:
        # Test sintassi Python
        result = subprocess.run([
            sys.executable, "-m", "py_compile", str(script_path)
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Sintassi Python corretta")
        else:
            print(f"❌ Errore sintassi: {result.stderr}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Errore durante test sintassi: {e}")
        return False

def test_import_functions():
    """Testa l'importazione delle funzioni del script."""
    
    print("\n🔍 Test importazione funzioni...")
    
    try:
        # Aggiungi il percorso al Python path
        sys.path.insert(0, str(Path("webapp").resolve()))
        
        # Importa le funzioni principali
        from run_system_simple import (
            check_database_connection,
            create_temp_directory,
            run_backend,
            run_frontend
        )
        
        print("✅ Importazione funzioni riuscita")
        
        # Test funzione database
        print("   🔍 Test check_database_connection...")
        db_result = check_database_connection()
        print(f"   Database: {'✅' if db_result else '❌'}")
        
        # Test funzione directory temporanea
        print("   🔍 Test create_temp_directory...")
        temp_result = create_temp_directory()
        print(f"   Directory temp: {'✅' if temp_result else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore durante test importazione: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_structure():
    """Verifica che la struttura dei file sia corretta."""
    
    print("\n🔍 Test struttura file...")
    
    required_files = [
        "webapp/backend/main.py",
        "webapp/frontend/package.json"
    ]
    
    all_exist = True
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - NON TROVATO")
            all_exist = False
    
    return all_exist

def main():
    """Funzione principale."""
    
    print("🚀 TEST NUOVO run_system_simple.py")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Sintassi
    if test_script_syntax():
        tests_passed += 1
        print("✅ Test sintassi: PASSATO")
    else:
        print("❌ Test sintassi: FALLITO")
    
    # Test 2: Struttura file
    if test_file_structure():
        tests_passed += 1
        print("✅ Test struttura file: PASSATO")
    else:
        print("❌ Test struttura file: FALLITO")
    
    # Test 3: Importazione funzioni
    if test_import_functions():
        tests_passed += 1
        print("✅ Test importazione: PASSATO")
    else:
        print("❌ Test importazione: FALLITO")
    
    # Risultato finale
    print("\n" + "=" * 50)
    print(f"🏁 RISULTATO: {tests_passed}/{total_tests} test passati")
    
    if tests_passed == total_tests:
        print("✅ TUTTI I TEST PASSATI!")
        print("🎉 Il nuovo run_system_simple.py è pronto per l'uso!")
        print("\n📋 MIGLIORAMENTI IMPLEMENTATI:")
        print("   • ❌ Rimossi controlli obsoleti (BOBINA_VUOTA, fix_bobina_vuota)")
        print("   • ✅ Aggiunta verifica connessione database PostgreSQL")
        print("   • ✅ Migliorata gestione errori e logging")
        print("   • ✅ Aggiunta verifica file necessari")
        print("   • ✅ Migliorata gestione processi e terminazione")
        print("   • ✅ Aggiunta creazione directory temporanea")
        return True
    else:
        print("❌ ALCUNI TEST FALLITI!")
        print("Controllare i dettagli sopra per risolvere i problemi.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
