#!/usr/bin/env python3
"""
Script di test per verificare il funzionamento delle API degli strumenti certificati.
"""

import requests
import json
from datetime import date, timedelta

# Configurazione
BASE_URL = "http://localhost:8000/api"
CANTIERE_ID = 1  # Assumiamo che esista un cantiere con ID 1

def test_login():
    """Test del login per ottenere il token di autenticazione."""
    login_data = {
        "username": "admin",
        "password": "admin"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
    if response.status_code == 200:
        token_data = response.json()
        return token_data.get("access_token")
    else:
        print(f"Errore nel login: {response.status_code} - {response.text}")
        return None

def get_headers(token):
    """Restituisce gli headers con il token di autenticazione."""
    return {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

def test_get_strumenti(token):
    """Test per ottenere la lista degli strumenti."""
    headers = get_headers(token)
    response = requests.get(f"{BASE_URL}/cantieri/{CANTIERE_ID}/strumenti", headers=headers)
    
    print(f"\n=== GET Strumenti ===")
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        strumenti = response.json()
        print(f"Numero strumenti trovati: {len(strumenti)}")
        for strumento in strumenti:
            print(f"- {strumento['nome']} {strumento['marca']} {strumento['modello']}")
        return strumenti
    else:
        print(f"Errore: {response.text}")
        return []

def test_create_strumento(token):
    """Test per creare un nuovo strumento."""
    headers = get_headers(token)
    
    # Dati del nuovo strumento
    strumento_data = {
        "nome": "Multimetro Test",
        "marca": "Fluke",
        "modello": "87V",
        "numero_serie": "TEST123456",
        "data_calibrazione": str(date.today()),
        "data_scadenza_calibrazione": str(date.today() + timedelta(days=365)),
        "certificato_calibrazione": "/path/to/cert.pdf",
        "note": "Strumento di test creato via API"
    }
    
    response = requests.post(
        f"{BASE_URL}/cantieri/{CANTIERE_ID}/strumenti", 
        headers=headers,
        json=strumento_data
    )
    
    print(f"\n=== CREATE Strumento ===")
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        strumento = response.json()
        print(f"Strumento creato: ID {strumento['id_strumento']}")
        print(f"Nome: {strumento['nome']} {strumento['marca']} {strumento['modello']}")
        return strumento
    else:
        print(f"Errore: {response.text}")
        return None

def test_update_strumento(token, strumento_id):
    """Test per aggiornare uno strumento esistente."""
    headers = get_headers(token)
    
    # Dati di aggiornamento
    update_data = {
        "note": "Strumento aggiornato via API test"
    }
    
    response = requests.put(
        f"{BASE_URL}/cantieri/{CANTIERE_ID}/strumenti/{strumento_id}",
        headers=headers,
        json=update_data
    )
    
    print(f"\n=== UPDATE Strumento ===")
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        strumento = response.json()
        print(f"Strumento aggiornato: {strumento['note']}")
        return strumento
    else:
        print(f"Errore: {response.text}")
        return None

def test_delete_strumento(token, strumento_id):
    """Test per eliminare uno strumento."""
    headers = get_headers(token)
    
    response = requests.delete(
        f"{BASE_URL}/cantieri/{CANTIERE_ID}/strumenti/{strumento_id}",
        headers=headers
    )
    
    print(f"\n=== DELETE Strumento ===")
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        print("Strumento eliminato con successo")
        return True
    else:
        print(f"Errore: {response.text}")
        return False

def main():
    """Funzione principale per eseguire tutti i test."""
    print("=== TEST API STRUMENTI CERTIFICATI ===")
    
    # 1. Login
    print("\n1. Test Login...")
    token = test_login()
    if not token:
        print("Impossibile ottenere il token. Test interrotto.")
        return
    
    print(f"Token ottenuto: {token[:20]}...")
    
    # 2. Get strumenti iniziali
    print("\n2. Lista strumenti iniziale...")
    strumenti_iniziali = test_get_strumenti(token)
    
    # 3. Crea nuovo strumento
    print("\n3. Creazione nuovo strumento...")
    nuovo_strumento = test_create_strumento(token)
    if not nuovo_strumento:
        print("Impossibile creare strumento. Test interrotto.")
        return
    
    strumento_id = nuovo_strumento['id_strumento']
    
    # 4. Get strumenti dopo creazione
    print("\n4. Lista strumenti dopo creazione...")
    test_get_strumenti(token)
    
    # 5. Update strumento
    print("\n5. Aggiornamento strumento...")
    test_update_strumento(token, strumento_id)
    
    # 6. Delete strumento
    print("\n6. Eliminazione strumento...")
    test_delete_strumento(token, strumento_id)
    
    # 7. Get strumenti finali
    print("\n7. Lista strumenti finale...")
    test_get_strumenti(token)
    
    print("\n=== TEST COMPLETATI ===")

if __name__ == "__main__":
    main()
