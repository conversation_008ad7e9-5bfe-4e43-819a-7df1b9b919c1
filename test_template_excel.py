#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per testare la generazione del template Excel.
"""

import os
import sys
import logging

# Aggiungi il percorso del progetto al PYTHONPATH
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Importa la funzione da testare
from modules.excel_manager import crea_template_excel

def test_crea_template_excel():
    """
    Testa la funzione crea_template_excel per verificare che generi un file Excel valido.
    """
    print("🔍 Test della funzione crea_template_excel...")
    
    # Genera un template nella directory corrente
    template_path = crea_template_excel("test_template_cavi.xlsx")
    
    if template_path and os.path.exists(template_path):
        print(f"✅ Template Excel creato con successo: {template_path}")
        print("📊 Verifica manualmente che il file si apra correttamente in Excel.")
        return True
    else:
        print("❌ Errore nella creazione del template Excel.")
        return False

if __name__ == "__main__":
    # Configura il logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Esegui il test
    success = test_crea_template_excel()
    
    # Esci con codice appropriato
    sys.exit(0 if success else 1)