#!/usr/bin/env python
# -*- coding: utf-8 -*-

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Parametri di connessione
conn_params = {
    'host': 'localhost',
    'port': '5432',
    'dbname': 'cantieri',
    'user': 'postgres',
    'password': 'Taranto'
}

try:
    # Connessione al database
    print("Connessione al database...")
    conn = psycopg2.connect(**conn_params)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    
    # Mostra la struttura attuale dei campi n_conduttori e sezione nella tabella Cavi
    print("\nSTRUTTURA ATTUALE DEI CAMPI N_CONDUTTORI E SEZIONE NELLA TABELLA CAVI:")
    cursor.execute("""
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'cavi' AND (column_name = 'n_conduttori' OR column_name = 'sezione')
        ORDER BY ordinal_position
    """)
    columns = cursor.fetchall()
    for col in columns:
        print(f"{col[0]:<20} {col[1]:<15} {col[2]}")
    
    # Modifica il tipo di n_conduttori da INTEGER a TEXT
    print("\nModifica del tipo di n_conduttori da INTEGER a TEXT nella tabella Cavi...")
    cursor.execute("ALTER TABLE cavi ALTER COLUMN n_conduttori TYPE TEXT USING n_conduttori::TEXT")
    
    # Verifica la struttura aggiornata
    print("\nSTRUTTURA AGGIORNATA DEI CAMPI N_CONDUTTORI E SEZIONE NELLA TABELLA CAVI:")
    cursor.execute("""
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'cavi' AND (column_name = 'n_conduttori' OR column_name = 'sezione')
        ORDER BY ordinal_position
    """)
    columns = cursor.fetchall()
    for col in columns:
        print(f"{col[0]:<20} {col[1]:<15} {col[2]}")
    
    # Verifica anche la struttura nella tabella parco_cavi per confronto
    print("\nSTRUTTURA DEI CAMPI N_CONDUTTORI E SEZIONE NELLA TABELLA PARCO_CAVI (per confronto):")
    cursor.execute("""
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'parco_cavi' AND (column_name = 'n_conduttori' OR column_name = 'sezione')
        ORDER BY ordinal_position
    """)
    columns = cursor.fetchall()
    for col in columns:
        print(f"{col[0]:<20} {col[1]:<15} {col[2]}")
    
    print("\nAggiornamento completato con successo!")
    
except Exception as e:
    print(f"Errore durante l'aggiornamento: {e}")
finally:
    # Chiudi la connessione
    if 'conn' in locals() and conn:
        cursor.close()
        conn.close()
        print("Connessione al database chiusa.")
