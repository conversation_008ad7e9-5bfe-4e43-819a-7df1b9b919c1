// Script per aggiornare il menu
// 1. Sostituire tutti i pl: 6 con pl: 3
// 2. Rimuovere tutte le icone dai sottomenu
// 3. Aggiornare tutti i ListItemText con createListItemText

const fs = require('fs');
const path = require('path');

// Percorso del file MainMenu.js
const filePath = path.join(__dirname, 'webapp', 'frontend', 'src', 'components', 'MainMenu.js');

// Leggi il contenuto del file
let content = fs.readFileSync(filePath, 'utf8');

// 1. Sostituire tutti i pl: 6 con pl: 3
content = content.replace(/sx={{ pl: 6 }}/g, 'sx={{ pl: 3 }}');

// 2. Sostituire tutti i pl: 4 con pl: 2
content = content.replace(/sx={{ pl: 4 }}/g, 'sx={{ pl: 2 }}');

// 3. Aggiornare tutti i ListItemText con createListItemText per il livello 3
const regex1 = /<ListItemIcon>[\s\S]*?<\/ListItemIcon>[\s\S]*?<ListItemText primary="([^"]+)" \/>/g;
content = content.replace(regex1, '{createListItemText("$1", 3)}');

// 4. Aggiornare tutti i ListItemText rimanenti con createListItemText per il livello 2
const regex2 = /<ListItemText primary="([^"]+)" \/>/g;
content = content.replace(regex2, '{createListItemText("$1", 2)}');

// 5. Aggiornare tutti i ExpandLess/ExpandMore con fontSize="small"
content = content.replace(/<ExpandLess \/>/g, '<ExpandLess fontSize="small" />');
content = content.replace(/<ExpandMore \/>/g, '<ExpandMore fontSize="small" />');

// Scrivi il contenuto aggiornato nel file
fs.writeFileSync(filePath, content, 'utf8');

console.log('Menu aggiornato con successo!');
