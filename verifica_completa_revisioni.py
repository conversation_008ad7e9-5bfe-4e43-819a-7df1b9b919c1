#!/usr/bin/env python3
"""
VERIFICA COMPLETA DELLA LOGICA REVISIONI
Controlla che ogni aspetto della specifica sia implementato correttamente
"""

import sys
import os
sys.path.append('webapp')

def verifica_acquisizione_codice_revisione():
    """Verifica l'acquisizione del codice revisione"""
    print("🔍 VERIFICA: Acquisizione del Codice Revisione")
    
    with open('modules/excel_manager.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("Modalità interattiva con input()", "input(f\"Inserisci il codice identificativo della revisione:" in content),
        ("Modalità non interattiva", "non_interattivo" in content and "default_revision" in content),
        ("Validazione presenza codice", "if not revisione:" in content),
        ("Errore bloccante se mancante", "return None" in content or "return False" in content),
        ("Campo revisione_ufficiale", "revisione_ufficiale" in content)
    ]
    
    passed = 0
    for check_name, condition in checks:
        status = "✅" if condition else "❌"
        print(f"  {status} {check_name}")
        if condition:
            passed += 1
    
    return passed, len(checks)

def verifica_scrittura_revisione():
    """Verifica la scrittura della revisione nei dati"""
    print("\n🔍 VERIFICA: Scrittura della Revisione nei Dati")
    
    with open('modules/excel_manager.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("Tutti i cavi hanno revisione_ufficiale", "revisione_ufficiale" in content and "INSERT INTO Cavi" in content),
        ("Gestione conflitti con revisione", "revisione" in content and "elabora_cavi_da_excel" in content),
        ("Gestione spare con revisione", "modificato_manualmente = 3" in content),
        ("Gestione reintegri con revisione", "reintegrated" in content)
    ]
    
    passed = 0
    for check_name, condition in checks:
        status = "✅" if condition else "❌"
        print(f"  {status} {check_name}")
        if condition:
            passed += 1
    
    return passed, len(checks)

def verifica_gestione_stati():
    """Verifica la gestione revisione vs stato del cavo"""
    print("\n🔍 VERIFICA: Gestione Revisione vs Stato del Cavo")
    
    with open('modules/excel_manager.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("Stato DA_INSTALLARE - aggiornamento totale", "DA_INSTALLARE" in content or "stato_installazione" in content),
        ("Stato INSTALLATO - caratteristiche bloccate", "INSTALLATO" in content or "installato" in content.lower()),
        ("Stato SPARE - escluso dalla revisione", "SPARE" in content),
        ("modificato_manualmente = 1 - tracciato", "modificato_manualmente" in content and "= 1" in content),
        ("modificato_manualmente = 2 - mantenuto", "modificato_manualmente" in content and "= 2" in content),
        ("modificato_manualmente = 3 - SPARE forzato", "modificato_manualmente = 3" in content)
    ]
    
    passed = 0
    for check_name, condition in checks:
        status = "✅" if condition else "❌"
        print(f"  {status} {check_name}")
        if condition:
            passed += 1
    
    return passed, len(checks)

def verifica_trattamento_cavi():
    """Verifica il trattamento dei cavi in base alla revisione"""
    print("\n🔍 VERIFICA: Trattamento dei Cavi in Base alla Revisione")
    
    with open('modules/excel_manager.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("Cavo presente DB e file - aggiornamento", "cavo_esistente" in content),
        ("Aggiornamenti soft per installati", "installato" in content.lower() and "soft" in content.lower() or "caratteristiche fisiche" in content.lower()),
        ("Aggiornamento pieno per non installati", "non installato" in content.lower() or "aggiornamento pieno" in content.lower()),
        ("Cavo DB assente file - SPARE", "cavi_non_in_revisione" in content),
        ("Marcatura SPARE automatica", "modificato_manualmente = 3" in content),
        ("Reintegrazione SPARE automatica", "reintegrated" in content and "modificato_manualmente" in content)
    ]
    
    passed = 0
    for check_name, condition in checks:
        status = "✅" if condition else "❌"
        print(f"  {status} {check_name}")
        if condition:
            passed += 1
    
    return passed, len(checks)

def verifica_conflitti_confronti():
    """Verifica conflitti e confronti tra revisioni"""
    print("\n🔍 VERIFICA: Conflitti e Confronti tra Revisioni")
    
    with open('modules/excel_manager.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("Conflitti installati - ignorate ma loggate", "physical_conflicts" in content or "conflitti_installati" in content),
        ("Variazioni non installati - accettate e annotate", "non_installed_changes" in content or "variazioni_non_installati" in content),
        ("Report Excel conflitti", "genera_report_conflitti" in content or "report_variazioni" in content),
        ("Audit trail per revisioni", "timestamp" in content and "revisione" in content),
        ("Confronto tra versioni", "confronta_caratteristiche" in content or "_confronta_valori" in content)
    ]
    
    passed = 0
    for check_name, condition in checks:
        status = "✅" if condition else "❌"
        print(f"  {status} {check_name}")
        if condition:
            passed += 1
    
    return passed, len(checks)

def verifica_componenti_coinvolti():
    """Verifica i componenti coinvolti nel codice"""
    print("\n🔍 VERIFICA: Componenti Coinvolti nel Codice")
    
    with open('modules/excel_manager.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("importa_cavi_da_excel - gestisce flusso", "def importa_cavi_da_excel(" in content),
        ("richiedi_revisione - chiede/imposta", "def richiedi_revisione(" in content),
        ("elabora_cavi_da_excel - attribuisce revisione", "def elabora_cavi_da_excel(" in content and "revisione" in content),
        ("inserisci_cavi_nel_database - inserisce con revisione", "def inserisci_cavi_nel_database(" in content and "revisione" in content),
        ("genera_report_importazione - registra revisione", "def genera_report_importazione(" in content and "revisione" in content),
        ("genera_report_conflitti_fisici - registra revisione", "def genera_report_conflitti_fisici(" in content and "revisione" in content)
    ]
    
    passed = 0
    for check_name, condition in checks:
        status = "✅" if condition else "❌"
        print(f"  {status} {check_name}")
        if condition:
            passed += 1
    
    return passed, len(checks)

def verifica_struttura_database():
    """Verifica la struttura del database"""
    print("\n🔍 VERIFICA: Struttura del Database e Revisione")
    
    with open('modules/excel_manager.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("Campo id_cavo - chiave logica", "id_cavo" in content),
        ("Campo id_cantiere - contesto", "id_cantiere" in content),
        ("Campo revisione_ufficiale - revisione corrente", "revisione_ufficiale" in content),
        ("Campo timestamp - data/ora importazione", "timestamp" in content),
        ("Versionamento soft senza duplicare tabelle", "ON CONFLICT" in content and "DO UPDATE SET" in content),
        ("Cronologia logica delle modifiche", "timestamp" in content and "revisione_ufficiale" in content)
    ]
    
    passed = 0
    for check_name, condition in checks:
        status = "✅" if condition else "❌"
        print(f"  {status} {check_name}")
        if condition:
            passed += 1
    
    return passed, len(checks)

def verifica_webapp_integration():
    """Verifica l'integrazione con la webapp"""
    print("\n🔍 VERIFICA: Integrazione WebApp")
    
    # Verifica backend API
    with open('webapp/backend/api/excel.py', 'r', encoding='utf-8') as f:
        api_content = f.read()
    
    # Verifica frontend
    with open('webapp/frontend/src/pages/cavi/VisualizzaCaviPage.js', 'r', encoding='utf-8') as f:
        frontend_content = f.read()
    
    checks = [
        ("API endpoint import con revisione", "revisione" in api_content and "import_cavi" in api_content),
        ("Validazione revisione obbligatoria", "revisione" in api_content and ("required" in api_content or "Form(" in api_content)),
        ("Frontend gestione revisioni", "revisione" in frontend_content and "getRevisioniDisponibili" in frontend_content),
        ("Visualizzazione revisione corrente", "revisione_corrente" in frontend_content or "revisioneCorrente" in frontend_content),
        ("Aggiornamento automatico post-import", "window.location.reload()" in frontend_content or "reload" in frontend_content)
    ]
    
    passed = 0
    for check_name, condition in checks:
        status = "✅" if condition else "❌"
        print(f"  {status} {check_name}")
        if condition:
            passed += 1
    
    return passed, len(checks)

def main():
    """Esegue la verifica completa"""
    print("🚀 VERIFICA COMPLETA DELLA LOGICA REVISIONI")
    print("=" * 60)
    
    verifiche = [
        ("Acquisizione Codice Revisione", verifica_acquisizione_codice_revisione),
        ("Scrittura Revisione nei Dati", verifica_scrittura_revisione),
        ("Gestione Stati Cavo", verifica_gestione_stati),
        ("Trattamento Cavi per Revisione", verifica_trattamento_cavi),
        ("Conflitti e Confronti", verifica_conflitti_confronti),
        ("Componenti Coinvolti", verifica_componenti_coinvolti),
        ("Struttura Database", verifica_struttura_database),
        ("Integrazione WebApp", verifica_webapp_integration)
    ]
    
    total_passed = 0
    total_checks = 0
    
    for nome_verifica, func_verifica in verifiche:
        passed, checks = func_verifica()
        total_passed += passed
        total_checks += checks
    
    print("\n" + "=" * 60)
    print("📊 RISULTATO FINALE VERIFICA REVISIONI")
    print("=" * 60)
    
    percentuale = (total_passed / total_checks) * 100 if total_checks > 0 else 0
    
    print(f"✅ Controlli superati: {total_passed}/{total_checks}")
    print(f"📈 Percentuale conformità: {percentuale:.1f}%")
    
    if percentuale >= 95:
        print("\n🎉 ECCELLENTE! La logica revisioni è COMPLETAMENTE CONFORME!")
        print("✅ Il sistema implementa fedelmente tutte le specifiche")
    elif percentuale >= 85:
        print("\n👍 BUONO! La logica revisioni è LARGAMENTE CONFORME!")
        print("⚠️  Alcuni aspetti minori potrebbero necessitare attenzione")
    elif percentuale >= 70:
        print("\n⚠️  SUFFICIENTE! La logica revisioni è PARZIALMENTE CONFORME!")
        print("❌ Diversi aspetti necessitano correzioni")
    else:
        print("\n❌ INSUFFICIENTE! La logica revisioni NON È CONFORME!")
        print("🔧 Sono necessarie correzioni significative")
    
    return percentuale >= 95

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
