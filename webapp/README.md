# CMS - Sistema di Gestione Cantieri

## Guida all'avvio del sistema

### Prerequisiti
- Python 3.7 o superiore
- Node.js e npm
- PostgreSQL

### Configurazione del database
1. Assicurati che PostgreSQL sia in esecuzione
2. Crea un database chiamato `cms` (o modifica il nome nel file `config.py`)
3. Configura le credenziali del database nel file `backend/config.py`

### Avvio del sistema completo
Il modo più semplice per avviare l'intero sistema è utilizzare lo script `run_system.py`:

```bash
python run_system.py
```

Questo script avvierà sia il backend che il frontend.

### Avvio manuale del backend
Se desideri avviare solo il backend:

```bash
cd backend
python main.py
```

Il backend sarà disponibile all'indirizzo: http://localhost:8001

### Avvio manuale del frontend
Se desideri avviare solo il frontend:

```bash
cd frontend
npm install  # Solo la prima volta o quando vengono aggiunte nuove dipendenze
npm start
```

Il frontend sarà disponibile all'indirizzo: http://localhost:3000

### Verifica dello stato del backend
Per verificare se il backend è in esecuzione, puoi utilizzare lo script `check_backend.py`:

```bash
python check_backend.py
```

Questo script verificherà se il backend è in esecuzione e, se non lo è, ti offrirà la possibilità di avviarlo.

## Risoluzione dei problemi

### Errore "Network Error" nella visualizzazione dei cavi
Se ricevi un errore di rete quando tenti di visualizzare i cavi, è probabile che il backend non sia in esecuzione. Segui questi passaggi:

1. Verifica che il backend sia in esecuzione eseguendo `python check_backend.py`
2. Se il backend non è in esecuzione, avvialo con `python run_system.py` o `cd backend && python main.py`
3. Ricarica la pagina nel browser

### Errore "Nessun cavo trovato" anche se i cavi esistono
Se il sistema mostra "Nessun cavo trovato" anche se sei sicuro che i cavi esistano nel database:

1. Verifica che il cantiere selezionato sia corretto
2. Controlla i log del backend per eventuali errori
3. Verifica direttamente nel database che i cavi esistano per il cantiere selezionato

### Altri problemi
Per altri problemi, controlla i file di log:
- `backend_log.txt` per i log del backend
- `frontend_log.txt` per i log del frontend
- Controlla anche la console del browser per eventuali errori JavaScript
