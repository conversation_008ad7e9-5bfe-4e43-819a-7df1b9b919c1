# Risoluzione Problemi nei Report "Bill of Quantities" e "Utilizzo Bob<PERSON>"

## Problemi Identificati

1. **Campo `bobine_per_tipo` mancante nel report BOQ**:
   - Il frontend si aspettava un campo `bobine_per_tipo` nel report "Bill of Quantities" che non veniva restituito dall'API
   - Questo causava l'errore "Impossibile caricare il report. Riprova più tardi"

2. **Gestione inconsistente dei valori NULL**:
   - Alcuni campi potevano contenere valori NULL che non venivano gestiti correttamente
   - Questo poteva causare errori durante la conversione dei dati

3. **Protezione insufficiente contro la divisione per zero**:
   - Nel calcolo delle percentuali di utilizzo delle bobine mancava una protezione adeguata
   - Questo poteva causare errori quando `metri_totali` era zero o NULL

4. **Messaggi di errore generici**:
   - Gli errori venivano mostrati con messaggi generici che non aiutavano a identificare il problema
   - Mancava una gestione specifica degli errori per ogni tipo di report

## Soluzioni Implementate

### 1. Aggiunta del campo `bobine_per_tipo` nel report BOQ

```python
# Query per ottenere i dati delle bobine disponibili raggruppati per tipologia
bobine_per_tipo = db.execute_query("""
    SELECT
        tipologia, sezione,
        COUNT(*) as num_bobine,
        SUM(metri_residui) as metri_disponibili
    FROM parco_cavi
    WHERE id_cantiere = %s AND stato_bobina != 'TERMINATA'
    GROUP BY tipologia, sezione
    ORDER BY tipologia, sezione
""", (cantiere_id,), fetch_all=True)

# Converti i risultati delle bobine in formato dict
bobine_data = []
for row in bobine_per_tipo:
    try:
        bobine_data.append({
            "tipologia": row[0] or "Non specificato",
            "sezione": row[1] or "Non specificato",
            "num_bobine": int(row[2] or 0),
            "metri_disponibili": round(float(row[3] or 0), 2)
        })
    except (ValueError, TypeError) as conversion_error:
        logging.warning(f"Errore di conversione dati per bobina: {str(row)}: {str(conversion_error)}")

# Includi bobine_per_tipo nel risultato
return {
    "nome_cantiere": nome_cantiere,
    "cavi_per_tipo": cavi_data,
    "bobine_per_tipo": bobine_data
}
```

### 2. Miglioramento della gestione dei valori NULL

```python
# Gestione valori NULL nei dati dei cavi
cavi_data.append({
    "tipologia": row[0] or "Non specificato",
    "sezione": row[1] or "Non specificato",
    "num_cavi": int(row[2] or 0),
    "metri_teorici": round(float(row[3] or 0), 2),
    "metri_reali": round(float(row[4] or 0), 2),
    "metri_da_posare": round(float(row[5] or 0), 2)
})

# Gestione valori NULL nei dati delle bobine
bobine_data.append({
    "id_bobina": row[0] or "ID non specificato",
    "tipologia": row[1] or "Non specificato",
    "sezione": row[2] or "Non specificato",
    "metri_totali": round(float(row[3] or 0), 2),
    "metri_residui": round(float(row[4] or 0), 2),
    "stato": row[5] or "Non specificato",
    "metri_utilizzati": round(float(row[6] or 0), 2),
    "percentuale_utilizzo": round(float(row[7] or 0), 2)
})
```

### 3. Protezione contro la divisione per zero

```python
# Protezione contro divisione per zero nel calcolo percentuale utilizzo
(COALESCE(metri_totali, 0) - COALESCE(metri_residui, 0)) as metri_utilizzati,
CASE
    WHEN COALESCE(metri_totali, 0) > 0 THEN ((COALESCE(metri_totali, 0) - COALESCE(metri_residui, 0)) / COALESCE(metri_totali, 1) * 100)
    ELSE 0
END as percentuale_utilizzo
```

### 4. Miglioramento dei messaggi di errore

#### Backend (reports_simple.py)

```python
try:
    report_data = await generate_boq_data(cantiere_id, db)
    return {
        "success": True,
        "content": report_data,
        "formato": "video"
    }
except Exception as report_error:
    # Cattura errori specifici della generazione del report
    logging.error(f"Errore nella generazione dati BOQ: {str(report_error)}")
    raise HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail=f"Errore nel report Bill of Quantities: {str(report_error)}"
    )
```

#### Frontend (ReportCaviPageNew.js)

```javascript
// Oggetto per tenere traccia degli errori specifici per ogni report
const reportErrors = {
    progress: null,
    boq: null,
    bobine: null,
    caviStato: null
};

// Cattura e memorizza errori specifici
const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video')
    .catch(err => {
        const errorMsg = err.detail || err.message || 'Errore sconosciuto';
        console.error('Error loading BOQ report:', err);
        reportErrors.boq = errorMsg;
        return { content: null };
    });

// Costruisci un messaggio di errore dettagliato
let errorMessage = 'Errore nel caricamento dei report:';
if (reportErrors.boq) {
    errorMessage += `\n- Bill of Quantities: ${reportErrors.boq}`;
}
```

## Risultati Attesi

Con queste modifiche, i report "Bill of Quantities" e "Utilizzo Bobine" dovrebbero ora caricarsi correttamente. In caso di errori, verranno mostrati messaggi più specifici che aiuteranno a identificare e risolvere il problema.

Le modifiche sono state implementate in modo da:

1. Mantenere la compatibilità con il codice esistente
2. Migliorare la robustezza gestendo meglio i casi limite
3. Fornire feedback più utili agli utenti in caso di errore
4. Risolvere il problema specifico del campo mancante nel report BOQ

## Istruzioni per il Test

1. Avviare il sistema con `run_system_simple.py`
2. Accedere all'interfaccia web e navigare alla pagina dei report
3. Provare a generare i report "Bill of Quantities" e "Utilizzo Bobine"
4. Verificare che i report vengano caricati correttamente e che i dati delle bobine siano visualizzati nella sezione "Bobine Disponibili" del report BOQ