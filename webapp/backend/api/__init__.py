from fastapi import APIRouter

from webapp.backend.api.auth import router as auth_router
from webapp.backend.api.users import router as users_router
from webapp.backend.api.admin import router as admin_router
from webapp.backend.api.cantieri import router as cantieri_router
from webapp.backend.api.cavi import router as cavi_router
from webapp.backend.api.cavi_spare import router as cavi_spare_router
from webapp.backend.api.parco_cavi import router as parco_cavi_router
from webapp.backend.api.excel import router as excel_router
from webapp.backend.api.reports_simple import router as reports_router
from webapp.backend.api.strumenti import router as strumenti_router
from webapp.backend.api.certificazioni import router as certificazioni_router

# Crea un router principale per l'API
api_router = APIRouter()

# Includi i router delle varie API
api_router.include_router(auth_router, prefix="/auth", tags=["auth"])
api_router.include_router(users_router, prefix="/users", tags=["users"])
api_router.include_router(admin_router, prefix="/admin", tags=["admin"])
api_router.include_router(cantieri_router, prefix="/cantieri", tags=["cantieri"])
api_router.include_router(cavi_router, prefix="/cavi", tags=["cavi"])
api_router.include_router(cavi_spare_router, prefix="/cavi", tags=["cavi"])
api_router.include_router(parco_cavi_router, prefix="/parco-cavi", tags=["parco-cavi"])
api_router.include_router(excel_router, prefix="/excel", tags=["excel"])
api_router.include_router(reports_router, prefix="/reports", tags=["reports"])
api_router.include_router(strumenti_router, prefix="/cantieri", tags=["strumenti"])
api_router.include_router(certificazioni_router, prefix="/cantieri", tags=["certificazioni"])

# Aggiungi altri router man mano che vengono creati
