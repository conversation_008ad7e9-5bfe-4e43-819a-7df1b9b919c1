from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from webapp.backend.core.security import get_current_active_user
from webapp.backend.database import get_db, engine
from webapp.backend.models.user import User
# Import all models to ensure they are registered with Base.metadata
from webapp.backend.models.cantiere import Cantiere
from webapp.backend.models.cavo import Cavo
from webapp.backend.models.parco_cavi import Parco<PERSON>avo
from webapp.backend.models.strumento_certificato import StrumentoCertificato
from webapp.backend.models.certificazione_cavo import CertificazioneCavo
from webapp.backend.models.comanda import Comanda, ComandaDettaglio

router = APIRouter()

@router.post("/reset-database", response_model=dict)
def reset_database(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Resetta il database.
    Solo gli amministratori possono resettare il database.

    Args:
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        dict: Messaggio di conferma
    """
    # Verifica che l'utente sia un amministratore o un utente impersonato da un amministratore
    token_data = getattr(current_user, "token_data", None)
    is_impersonated = token_data and getattr(token_data, "is_impersonated", False)

    if current_user.ruolo != "owner" and not is_impersonated:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questa risorsa"
        )

    try:
        # Chiudi la sessione corrente
        db.close()

        # Elimina tutte le tabelle
        from webapp.backend.database import Base
        Base.metadata.drop_all(bind=engine)

        # Ricrea tutte le tabelle
        Base.metadata.create_all(bind=engine)

        # Crea l'utente amministratore
        from webapp.backend.core.security import get_password_hash

        admin = User(
            username="admin",
            password=get_password_hash("admin"),
            password_plain="admin",  # Salva anche la password in chiaro
            ruolo="owner",
            abilitato=True
        )

        # Crea una nuova sessione
        new_db = next(get_db())
        try:
            new_db.add(admin)
            new_db.commit()
            new_db.refresh(admin)
            return {"message": "Database resettato con successo"}
        except Exception as db_error:
            new_db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Errore durante la creazione dell'admin: {str(db_error)}"
            )
        finally:
            new_db.close()

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante il reset del database: {str(e)}"
        )
