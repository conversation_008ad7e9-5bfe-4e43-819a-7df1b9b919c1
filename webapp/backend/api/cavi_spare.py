from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy.sql import text
from datetime import datetime

from webapp.backend.database import get_db
from webapp.backend.models.user import User
from webapp.backend.models.cantiere import Cantiere
from webapp.backend.models.cavo import Cavo
from webapp.backend.schemas.cavo import CavoInDB
from webapp.backend.core.security import get_current_active_user

router = APIRouter()

@router.get("/spare/{cantiere_id}", response_model=List[CavoInDB])
def get_cavi_spare(
    cantiere_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Ottiene la lista dei cavi SPARE di un cantiere.
    Questa è una query diretta che filtra per modificato_manualmente = 3.
    
    Args:
        cantiere_id: ID del cantiere
        db: Sessione del database
        current_user: Utente corrente
        
    Returns:
        List[CavoInDB]: Lista dei cavi SPARE
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )
    
    # Ottieni i cavi SPARE del cantiere
    try:
        print(f"Esecuzione query cavi SPARE per cantiere_id={cantiere_id}")
        
        # Esegui una query SQL diretta per ottenere i cavi SPARE
        result = db.execute(
            text("""
            SELECT id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo, tipologia,
                   n_conduttori, sezione, sh, ubicazione_partenza, utenza_partenza, descrizione_utenza_partenza,
                   ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo, metri_teorici, metratura_reale,
                   responsabile_posa, id_bobina, stato_installazione, modificato_manualmente, timestamp,
                   collegamenti, responsabile_partenza, responsabile_arrivo, comanda_posa, comanda_partenza, comanda_arrivo
            FROM cavi
            WHERE id_cantiere = :cantiere_id AND modificato_manualmente = 3
            ORDER BY id_cavo
            """),
            {"cantiere_id": cantiere_id}
        )
        
        # Converti i risultati in dizionari
        cavi_spare = []
        for row in result:
            cavo_dict = {}
            for column, value in row._mapping.items():
                cavo_dict[column] = value
            cavi_spare.append(cavo_dict)
        
        print(f"Trovati {len(cavi_spare)} cavi SPARE")
        return cavi_spare
    
    except Exception as e:
        print(f"Errore durante l'esecuzione della query: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante il caricamento dei cavi SPARE: {str(e)}"
        )
