from typing import Generator

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.orm import Session

from webapp.backend.config import settings
from webapp.backend.database import get_db
from webapp.backend.core.security import get_current_user, get_current_active_user
from webapp.backend.models.user import User

# Riutilizziamo le dipendenze definite in security.py
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_PREFIX}/auth/login")

# Funzione per ottenere l'utente corrente
def get_current_user_dep(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
) -> User:
    """
    Dipendenza per ottenere l'utente corrente.
    Riutilizza la funzione get_current_user definita in security.py.
    """
    return get_current_user(token=token, db=db)

# Funzione per ottenere l'utente corrente attivo
def get_current_active_user_dep(
    current_user: User = Depends(get_current_user_dep)
) -> User:
    """
    Dipendenza per ottenere l'utente corrente attivo.
    Riutilizza la funzione get_current_active_user definita in security.py.
    """
    return get_current_active_user(current_user=current_user)
