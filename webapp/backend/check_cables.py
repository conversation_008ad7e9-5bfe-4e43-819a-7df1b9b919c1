import sys
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import logging

# Configura il logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Ottieni l'ID del cantiere dai parametri della riga di comando
if len(sys.argv) > 1:
    cantiere_id = int(sys.argv[1])
else:
    cantiere_id = 2  # Default a cantiere ID 2

# Configurazione del database
DATABASE_URL = "postgresql://postgres:postgres@localhost:5432/cms"

try:
    # Crea la connessione al database
    engine = create_engine(DATABASE_URL)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    logger.info(f"Connessione al database stabilita. Verifico i cavi per il cantiere ID {cantiere_id}")
    
    # Query per i cavi attivi (modificato_manualmente != 3 OR modificato_manualmente IS NULL)
    query_attivi = text("""
        SELECT id_cavo, sistema, utility, tipologia, n_conduttori, sezione, 
               ubicazione_partenza, ubicazione_arrivo, metri_teorici, stato_installazione, modificato_manualmente
        FROM cavi
        WHERE id_cantiere = :cantiere_id AND (modificato_manualmente != 3 OR modificato_manualmente IS NULL)
        ORDER BY id_cavo
    """)
    
    # Query per i cavi spare (modificato_manualmente = 3)
    query_spare = text("""
        SELECT id_cavo, sistema, utility, tipologia, n_conduttori, sezione, 
               ubicazione_partenza, ubicazione_arrivo, metri_teorici, stato_installazione, modificato_manualmente
        FROM cavi
        WHERE id_cantiere = :cantiere_id AND modificato_manualmente = 3
        ORDER BY id_cavo
    """)
    
    # Esegui le query
    cavi_attivi = session.execute(query_attivi, {"cantiere_id": cantiere_id}).fetchall()
    cavi_spare = session.execute(query_spare, {"cantiere_id": cantiere_id}).fetchall()
    
    # Stampa i risultati
    logger.info(f"Trovati {len(cavi_attivi)} cavi attivi per il cantiere {cantiere_id}")
    for i, cavo in enumerate(cavi_attivi):
        logger.info(f"  {i+1}. ID: {cavo.id_cavo}, Utility: {cavo.utility}, Tipologia: {cavo.tipologia}, Stato: {cavo.stato_installazione}, Modificato: {cavo.modificato_manualmente}")
    
    logger.info(f"Trovati {len(cavi_spare)} cavi spare per il cantiere {cantiere_id}")
    for i, cavo in enumerate(cavi_spare):
        logger.info(f"  {i+1}. ID: {cavo.id_cavo}, Utility: {cavo.utility}, Tipologia: {cavo.tipologia}, Stato: {cavo.stato_installazione}, Modificato: {cavo.modificato_manualmente}")
    
    # Verifica anche se il cantiere esiste
    query_cantiere = text("""
        SELECT id_cantiere, nome, descrizione
        FROM cantieri
        WHERE id_cantiere = :cantiere_id
    """)
    
    cantiere = session.execute(query_cantiere, {"cantiere_id": cantiere_id}).fetchone()
    if cantiere:
        logger.info(f"Cantiere trovato: ID {cantiere.id_cantiere}, Nome: {cantiere.nome}, Descrizione: {cantiere.descrizione}")
    else:
        logger.warning(f"Cantiere con ID {cantiere_id} non trovato nel database!")
    
    # Chiudi la sessione
    session.close()
    
except Exception as e:
    logger.error(f"Errore durante la verifica dei cavi: {str(e)}")
    import traceback
    traceback.print_exc()
