from sqlalchemy import Column, Integer, String, Float, Date, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from webapp.backend.database import Base

class CertificazioneCavo(Base):
    """
    Modello SQLAlchemy per la tabella certificazioni_cavi.
    Corrisponde alla tabella CertificazioniCavi nel database esistente.
    """
    __tablename__ = "certificazionicavi"

    id_certificazione = Column(Integer, primary_key=True, index=True)
    id_cantiere = Column(Integer, ForeignKey("cantieri.id_cantiere"), nullable=False)
    id_cavo = Column(String, nullable=False)
    numero_certificato = Column(String, nullable=False)
    data_certificazione = Column(Date, nullable=False)
    id_operatore = Column(String, nullable=True)
    strumento_utilizzato = Column(String, nullable=True)
    id_strumento = Column(Integer, ForeignKey("strumenticertificati.id_strumento"), nullable=True)
    lunghezza_misurata = Column(Float, nullable=True)
    valore_continuita = Column(String, nullable=True)
    valore_isolamento = Column(String, nullable=True)
    valore_resistenza = Column(String, nullable=True)
    percorso_certificato = Column(String, nullable=True)
    percorso_foto = Column(String, nullable=True)
    note = Column(Text, nullable=True)
    timestamp_creazione = Column(DateTime, default=func.now())
    timestamp_modifica = Column(DateTime, nullable=True)

    # Relazioni
    cantiere = relationship("Cantiere", backref="certificazioni_cavi")
    strumento_certificato = relationship("StrumentoCertificato", back_populates="certificazioni_cavi")
