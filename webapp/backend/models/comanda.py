from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from webapp.backend.database import Base

class Comanda(Base):
    """
    Modello SQLAlchemy per la tabella comande.
    Corrisponde alla tabella Comande nel database esistente.
    """
    __tablename__ = "comande"

    codice_comanda = Column(String, primary_key=True)
    tipo_comanda = Column(String, nullable=False)  # 'POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'
    descrizione = Column(Text, nullable=True)
    data_creazione = Column(Date, nullable=False)
    data_scadenza = Column(Date, nullable=True)
    data_completamento = Column(Date, nullable=True)
    responsabile = Column(String, nullable=True)
    stato = Column(String, nullable=False)  # 'CREATA', 'ASSEGNATA', 'IN_CORSO', 'COMPLETATA', 'ANNULLATA'
    id_cantiere = Column(Integer, ForeignKey("cantieri.id_cantiere"), nullable=False)

    # Relazioni
    cantiere = relationship("Cantiere", backref="comande")


class ComandaDettaglio(Base):
    """
    Modello SQLAlchemy per la tabella comande_dettaglio.
    Corrisponde alla tabella ComandeDettaglio nel database esistente.
    """
    __tablename__ = "comandedettaglio"

    id_dettaglio = Column(Integer, primary_key=True, index=True)
    codice_comanda = Column(String, ForeignKey("comande.codice_comanda"), nullable=False)
    id_cavo = Column(String, nullable=False)
    id_cantiere = Column(Integer, nullable=False)

    # Relazioni
    comanda = relationship("Comanda", backref="dettagli")
