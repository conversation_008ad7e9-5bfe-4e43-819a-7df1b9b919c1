from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from webapp.backend.database import Base

class ParcoCavo(Base):
    """
    Modello SQLAlchemy per la tabella parco_cavi.
    Corrisponde alla tabella parco_cavi nel database esistente.
    """
    __tablename__ = "parco_cavi"

    id_bobina = Column(String, primary_key=True)
    numero_bobina = Column(String, nullable=True)
    utility = Column(String, nullable=True)
    tipologia = Column(String, nullable=True)
    n_conduttori = Column(String, nullable=True)
    sezione = Column(String, nullable=True)
    metri_totali = Column(Float, nullable=True)
    metri_residui = Column(Float, nullable=True)
    stato_bobina = Column(String, nullable=True)
    ubicazione_bobina = Column(String, nullable=True)
    fornitore = Column(String, nullable=True)
    n_DDT = Column(String, name="n_ddt", nullable=True)
    data_DDT = Column(String, name="data_ddt", nullable=True)
    configurazione = Column(String, nullable=True)
    id_cantiere = Column(Integer, ForeignKey("cantieri.id_cantiere"), nullable=False)

    # Relazioni
    cavi = relationship("Cavo", backref="bobina")
