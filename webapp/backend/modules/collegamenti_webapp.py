from typing import Dict, List, Optional, Union
from modules.collegamenti_new import aggiorna_collegamento
from webapp.backend.models.cavo import Cavo
from sqlalchemy.orm import Session

# Costanti per i flag di collegamento
COLLEGAMENTO_PARTENZA = 1  # 0001 in binario
COLLEGAMENTO_ARRIVO = 2    # 0010 in binario

def get_cavi_installati(db: Session, cantiere_id: int) -> List[Cavo]:
    """
    Recupera tutti i cavi installati di un cantiere.

    Args:
        db: Sessione del database
        cantiere_id: ID del cantiere

    Returns:
        List[Cavo]: Lista dei cavi installati
    """
    # Recupera i cavi installati (stato_installazione = 'Installato')
    # Usa una query case-insensitive per stato_installazione
    from sqlalchemy import func
    cavi = db.query(Cavo).filter(
        Cavo.id_cantiere == cantiere_id,
        func.lower(Cavo.stato_installazione) == func.lower('Installato'),
        Cavo.modificato_manualmente != 3  # Escludi i cavi SPARE
    ).all()

    return cavi

def collega_cavo_webapp(db: Session, cantiere_id: int, cavo_id: str, lato: str, responsabile: str) -> Cavo:
    """
    Collega un lato di un cavo utilizzando la logica del modulo CLI.

    Args:
        db: Sessione del database
        cantiere_id: ID del cantiere
        cavo_id: ID del cavo
        lato: 'partenza' o 'arrivo'
        responsabile: Responsabile del collegamento

    Returns:
        Cavo: Il cavo aggiornato
    """
    # Recupera il cavo
    cavo = db.query(Cavo).filter(
        Cavo.id_cantiere == cantiere_id,
        Cavo.id_cavo == cavo_id
    ).first()

    if not cavo:
        raise ValueError(f"Cavo con ID {cavo_id} non trovato nel cantiere {cantiere_id}")

    # Verifica che il cavo sia installato
    if cavo.stato_installazione.lower() != 'installato':
        raise ValueError(f"Il cavo {cavo_id} non è installato")

    # Inizializza il campo collegamenti se è None
    if cavo.collegamenti is None:
        cavo.collegamenti = 0

    # Verifica se il lato è già collegato
    if lato == 'partenza':
        if cavo.collegamenti & COLLEGAMENTO_PARTENZA:
            raise ValueError(f"Il lato partenza del cavo {cavo_id} è già collegato")

        # Aggiorna il flag dei collegamenti (aggiunge 1 per il lato partenza)
        cavo.collegamenti |= COLLEGAMENTO_PARTENZA
        cavo.responsabile_partenza = responsabile

    elif lato == 'arrivo':
        if cavo.collegamenti & COLLEGAMENTO_ARRIVO:
            raise ValueError(f"Il lato arrivo del cavo {cavo_id} è già collegato")

        # Aggiorna il flag dei collegamenti (aggiunge 2 per il lato arrivo)
        cavo.collegamenti |= COLLEGAMENTO_ARRIVO
        cavo.responsabile_arrivo = responsabile

    else:
        raise ValueError(f"Lato non valido: {lato}. Deve essere 'partenza' o 'arrivo'")

    # Salva le modifiche
    db.commit()
    db.refresh(cavo)

    return cavo

def scollega_cavo_webapp(db: Session, cantiere_id: int, cavo_id: str, lato: str) -> Cavo:
    """
    Scollega un lato di un cavo.

    Args:
        db: Sessione del database
        cantiere_id: ID del cantiere
        cavo_id: ID del cavo
        lato: Lato da scollegare ('partenza' o 'arrivo')

    Returns:
        Cavo: Il cavo aggiornato
    """
    # Recupera il cavo
    cavo = db.query(Cavo).filter(
        Cavo.id_cantiere == cantiere_id,
        Cavo.id_cavo == cavo_id
    ).first()

    if not cavo:
        raise ValueError(f"Cavo con ID {cavo_id} non trovato nel cantiere {cantiere_id}")

    # Verifica che il cavo sia installato
    if cavo.stato_installazione.lower() != 'installato':
        raise ValueError(f"Il cavo {cavo_id} non è installato")

    # Inizializza il campo collegamenti se è None
    if cavo.collegamenti is None:
        cavo.collegamenti = 0

    # Aggiorna il campo collegamenti in base al lato
    if lato == 'partenza':
        # Rimuovi il bit 0 (COLLEGAMENTO_PARTENZA = 1)
        cavo.collegamenti &= ~COLLEGAMENTO_PARTENZA
        cavo.responsabile_partenza = None
    elif lato == 'arrivo':
        # Rimuovi il bit 1 (COLLEGAMENTO_ARRIVO = 2)
        cavo.collegamenti &= ~COLLEGAMENTO_ARRIVO
        cavo.responsabile_arrivo = None
    else:
        raise ValueError(f"Lato non valido: {lato}. Deve essere 'partenza' o 'arrivo'")

    # Salva le modifiche
    db.commit()
    db.refresh(cavo)

    return cavo

def cerca_cavi_installati(db: Session, cantiere_id: int, search_term: str) -> List[Cavo]:
    """
    Cerca cavi installati per ID.

    Args:
        db: Sessione del database
        cantiere_id: ID del cantiere
        search_term: Termine di ricerca

    Returns:
        List[Cavo]: Lista dei cavi installati che corrispondono al termine di ricerca
    """
    # Recupera i cavi installati che corrispondono al termine di ricerca
    # Usa una query case-insensitive per stato_installazione
    from sqlalchemy import func
    cavi = db.query(Cavo).filter(
        Cavo.id_cantiere == cantiere_id,
        func.lower(Cavo.stato_installazione) == func.lower('Installato'),
        Cavo.modificato_manualmente != 3,  # Escludi i cavi SPARE
        Cavo.id_cavo.ilike(f"%{search_term}%")
    ).all()

    return cavi
