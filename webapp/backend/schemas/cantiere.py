from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class CantiereBase(BaseModel):
    """Schema base per i cantieri."""
    nome: str
    descrizione: Optional[str] = None

class CantiereCreate(CantiereBase):
    """Schema per la creazione di un cantiere."""
    password_cantiere: str

class CantiereUpdate(BaseModel):
    """Schema per l'aggiornamento di un cantiere."""
    nome: Optional[str] = None
    descrizione: Optional[str] = None
    password_cantiere: Optional[str] = None

class CantiereInDB(CantiereBase):
    """Schema per un cantiere nel database."""
    id_cantiere: int
    data_creazione: datetime
    id_utente: int
    codice_univoco: str
    
    class Config:
        orm_mode = True
