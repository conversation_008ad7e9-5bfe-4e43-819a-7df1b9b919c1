from pydantic import BaseModel, Field
from typing import Optional
from datetime import date, datetime


class StrumentoCertificatoBase(BaseModel):
    """Schema base per gli strumenti certificati."""
    nome: str = Field(..., description="Nome dello strumento")
    marca: str = Field(..., description="Marca dello strumento")
    modello: str = Field(..., description="Modello dello strumento")
    numero_serie: str = Field(..., description="Numero di serie dello strumento")
    data_calibrazione: date = Field(..., description="Data di calibrazione")
    data_scadenza_calibrazione: date = Field(..., description="Data di scadenza calibrazione")
    certificato_calibrazione: Optional[str] = Field(None, description="Percorso del certificato di calibrazione")
    note: Optional[str] = Field(None, description="Note aggiuntive")


class StrumentoCertificatoCreate(StrumentoCertificatoBase):
    """Schema per la creazione di un nuovo strumento certificato."""
    pass


class StrumentoCertificatoUpdate(BaseModel):
    """Schema per l'aggiornamento di uno strumento certificato."""
    nome: Optional[str] = None
    marca: Optional[str] = None
    modello: Optional[str] = None
    numero_serie: Optional[str] = None
    data_calibrazione: Optional[date] = None
    data_scadenza_calibrazione: Optional[date] = None
    certificato_calibrazione: Optional[str] = None
    note: Optional[str] = None


class StrumentoCertificatoInDB(StrumentoCertificatoBase):
    """Schema per lo strumento certificato nel database."""
    id_strumento: int
    id_cantiere: int
    timestamp_creazione: datetime
    timestamp_modifica: Optional[datetime] = None

    class Config:
        from_attributes = True


class StrumentoCertificatoResponse(StrumentoCertificatoInDB):
    """Schema per la risposta API dello strumento certificato."""
    pass
