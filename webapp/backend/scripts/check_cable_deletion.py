#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per verificare il comportamento della cancellazione dei cavi e della marcatura come SPARE.
Questo script esegue test diretti sul database per verificare che i cavi vengano correttamente
marcati come SPARE o eliminati in base alla modalità selezionata.
"""

import sys
import os
import argparse
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from datetime import datetime

# Aggiungi la directory principale al path per importare i moduli
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from webapp.backend.config import settings
from webapp.backend.models.cavo import Cavo
from webapp.backend.models.cantiere import Cantiere

def print_separator():
    print("\n" + "=" * 80 + "\n")

def main():
    parser = argparse.ArgumentParser(description='Verifica il comportamento della cancellazione dei cavi')
    parser.add_argument('--cantiere', type=int, required=True, help='ID del cantiere da verificare')
    parser.add_argument('--cavo', type=str, help='ID del cavo specifico da verificare (opzionale)')
    parser.add_argument('--test', action='store_true', help='Esegui test di marcatura SPARE e cancellazione')
    args = parser.parse_args()

    # Crea la connessione al database
    print(f"Connessione al database: {settings.DATABASE_URL}")
    engine = create_engine(settings.DATABASE_URL)
    Session = sessionmaker(bind=engine)
    session = Session()

    try:
        # Verifica che il cantiere esista
        cantiere = session.query(Cantiere).filter(Cantiere.id_cantiere == args.cantiere).first()
        if not cantiere:
            print(f"Errore: Cantiere con ID {args.cantiere} non trovato")
            return

        print(f"Cantiere trovato: {cantiere.nome} (ID: {cantiere.id_cantiere})")
        print_separator()

        # 1. Verifica se ci sono cavi con modificato_manualmente = 3
        query1 = """
        SELECT id_cavo, stato_installazione, modificato_manualmente, metratura_reale
        FROM cavi
        WHERE id_cantiere = :cantiere_id AND modificato_manualmente = 3
        ORDER BY id_cavo
        """
        result1 = session.execute(text(query1), {"cantiere_id": args.cantiere})
        rows1 = result1.fetchall()
        
        print(f"Cavi con modificato_manualmente = 3 (SPARE): {len(rows1)}")
        for row in rows1:
            print(f"  - ID: {row.id_cavo}, Stato: {row.stato_installazione}, Metri: {row.metratura_reale}")
        
        print_separator()

        # 2. Verifica se ci sono cavi con stato_installazione = 'SPARE'
        query2 = """
        SELECT id_cavo, stato_installazione, modificato_manualmente, metratura_reale
        FROM cavi
        WHERE id_cantiere = :cantiere_id AND stato_installazione = 'SPARE'
        ORDER BY id_cavo
        """
        result2 = session.execute(text(query2), {"cantiere_id": args.cantiere})
        rows2 = result2.fetchall()
        
        print(f"Cavi con stato_installazione = 'SPARE': {len(rows2)}")
        for row in rows2:
            print(f"  - ID: {row.id_cavo}, Modificato: {row.modificato_manualmente}, Metri: {row.metratura_reale}")
        
        print_separator()

        # 3. Se è specificato un cavo specifico, mostra i suoi dettagli
        if args.cavo:
            query3 = """
            SELECT id_cavo, stato_installazione, modificato_manualmente, metratura_reale, timestamp
            FROM cavi
            WHERE id_cantiere = :cantiere_id AND id_cavo = :cavo_id
            """
            result3 = session.execute(text(query3), {"cantiere_id": args.cantiere, "cavo_id": args.cavo})
            row3 = result3.fetchone()
            
            if row3:
                print(f"Dettagli del cavo {args.cavo}:")
                print(f"  - ID: {row3.id_cavo}")
                print(f"  - Stato: {row3.stato_installazione}")
                print(f"  - Modificato manualmente: {row3.modificato_manualmente}")
                print(f"  - Metri reali: {row3.metratura_reale}")
                print(f"  - Timestamp: {row3.timestamp}")
            else:
                print(f"Cavo con ID {args.cavo} non trovato nel cantiere {args.cantiere}")
            
            print_separator()

        # 4. Esegui test di marcatura SPARE e cancellazione se richiesto
        if args.test:
            print("ESECUZIONE TEST DI MARCATURA SPARE E CANCELLAZIONE")
            print("ATTENZIONE: Questo test modificherà il database!")
            
            # Crea un cavo di test
            test_cavo_id = f"TEST_{datetime.now().strftime('%Y%m%d%H%M%S')}"
            print(f"\nCreazione cavo di test con ID: {test_cavo_id}")
            
            new_cavo = Cavo(
                id_cavo=test_cavo_id,
                id_cantiere=args.cantiere,
                revisione_ufficiale="00",
                utility="TEST",
                tipologia="TEST",
                n_conduttori="1",
                sezione="1",
                ubicazione_partenza="TEST",
                ubicazione_arrivo="TEST",
                metri_teorici=10,
                metratura_reale=0,
                stato_installazione="Da installare",
                modificato_manualmente=0,
                timestamp=datetime.now()
            )
            
            session.add(new_cavo)
            session.commit()
            print("Cavo di test creato con successo")
            
            # Verifica che il cavo sia stato creato
            test_cavo = session.query(Cavo).filter(
                Cavo.id_cavo == test_cavo_id,
                Cavo.id_cantiere == args.cantiere
            ).first()
            
            if not test_cavo:
                print("Errore: Cavo di test non trovato dopo la creazione")
                return
            
            print(f"Cavo di test trovato: ID={test_cavo.id_cavo}, Stato={test_cavo.stato_installazione}, Modificato={test_cavo.modificato_manualmente}")
            
            # Test 1: Marca il cavo come SPARE
            print("\nTest 1: Marcatura del cavo come SPARE")
            test_cavo.stato_installazione = "SPARE"
            test_cavo.modificato_manualmente = 3
            test_cavo.timestamp = datetime.now()
            session.commit()
            
            # Verifica che il cavo sia stato marcato come SPARE
            session.refresh(test_cavo)
            print(f"Dopo marcatura SPARE: Stato={test_cavo.stato_installazione}, Modificato={test_cavo.modificato_manualmente}")
            
            # Test 2: Crea un altro cavo di test per l'eliminazione
            test_cavo_id2 = f"TEST_DEL_{datetime.now().strftime('%Y%m%d%H%M%S')}"
            print(f"\nTest 2: Creazione secondo cavo di test con ID: {test_cavo_id2}")
            
            new_cavo2 = Cavo(
                id_cavo=test_cavo_id2,
                id_cantiere=args.cantiere,
                revisione_ufficiale="00",
                utility="TEST",
                tipologia="TEST",
                n_conduttori="1",
                sezione="1",
                ubicazione_partenza="TEST",
                ubicazione_arrivo="TEST",
                metri_teorici=10,
                metratura_reale=0,
                stato_installazione="Da installare",
                modificato_manualmente=0,
                timestamp=datetime.now()
            )
            
            session.add(new_cavo2)
            session.commit()
            
            # Verifica che il secondo cavo sia stato creato
            test_cavo2 = session.query(Cavo).filter(
                Cavo.id_cavo == test_cavo_id2,
                Cavo.id_cantiere == args.cantiere
            ).first()
            
            if not test_cavo2:
                print("Errore: Secondo cavo di test non trovato dopo la creazione")
                return
            
            print(f"Secondo cavo di test trovato: ID={test_cavo2.id_cavo}, Stato={test_cavo2.stato_installazione}")
            
            # Elimina il secondo cavo
            print("\nEliminazione del secondo cavo di test")
            session.delete(test_cavo2)
            session.commit()
            
            # Verifica che il secondo cavo sia stato eliminato
            check_cavo2 = session.query(Cavo).filter(
                Cavo.id_cavo == test_cavo_id2,
                Cavo.id_cantiere == args.cantiere
            ).first()
            
            if check_cavo2:
                print(f"ERRORE: Il secondo cavo di test è ancora presente nel database dopo l'eliminazione")
            else:
                print(f"Secondo cavo di test eliminato con successo")
            
            print_separator()
            
            print("Test completati")

    except Exception as e:
        print(f"Errore durante l'esecuzione: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        session.close()

if __name__ == "__main__":
    main()
