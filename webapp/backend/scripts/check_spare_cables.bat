@echo off
echo Verifica dei cavi SPARE nel database...
echo.

REM Imposta il percorso Python
set PYTHON_PATH=python

REM Verifica se è stato specificato un ID cantiere
if "%1"=="" (
    echo Esecuzione senza parametri - verranno mostrati tutti i cantieri
    %PYTHON_PATH% webapp/backend/scripts/check_spare_cables.py
) else (
    echo Verifica per il cantiere con ID %1
    if "%2"=="" (
        %PYTHON_PATH% webapp/backend/scripts/check_spare_cables.py %1
    ) else (
        echo Verifica per il cavo con ID %2
        %PYTHON_PATH% webapp/backend/scripts/check_spare_cables.py %1 %2
    )
)

echo.
echo Premi un tasto per continuare...
pause > nul
