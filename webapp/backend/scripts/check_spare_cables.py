#!/usr/bin/env python3
"""
Script per verificare lo stato dei cavi SPARE nel database.
Esegui questo script dalla directory principale del progetto.
"""

import sys
import os
import psycopg2
from psycopg2.extras import RealDictCursor
import json
from dotenv import load_dotenv

# Carica le variabili d'ambiente dal file .env
load_dotenv()

# Ottieni le credenziali del database dalle variabili d'ambiente
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = os.getenv("DB_PORT", "5432")
DB_NAME = os.getenv("DB_NAME", "cantieri")
DB_USER = os.getenv("DB_USER", "postgres")
DB_PASS = os.getenv("DB_PASSWORD", "Taranto")

def connect_to_db():
    """Connessione al database PostgreSQL."""
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASS,
            cursor_factory=RealDictCursor
        )
        print(f"Connesso al database {DB_NAME} su {DB_HOST}:{DB_PORT}")
        return conn
    except Exception as e:
        print(f"Errore durante la connessione al database: {e}")
        sys.exit(1)

def execute_query(conn, query, params=None):
    """Esegue una query SQL e restituisce i risultati."""
    try:
        with conn.cursor() as cur:
            cur.execute(query, params)
            results = cur.fetchall()
            return [dict(row) for row in results]
    except Exception as e:
        print(f"Errore durante l'esecuzione della query: {e}")
        print(f"Query: {query}")
        print(f"Parametri: {params}")
        return []

def print_results(title, results):
    """Stampa i risultati in formato leggibile."""
    print("\n" + "=" * 80)
    print(f"{title} ({len(results)} risultati)")
    print("=" * 80)

    if not results:
        print("Nessun risultato trovato.")
        return

    # Stampa i risultati in formato tabellare
    headers = results[0].keys()
    header_row = " | ".join(str(h) for h in headers)
    print(header_row)
    print("-" * len(header_row))

    for row in results:
        row_str = " | ".join(str(row[h]) for h in headers)
        print(row_str)

def check_spare_cables(cantiere_id=None, cavo_id=None):
    """Verifica lo stato dei cavi SPARE nel database."""
    conn = connect_to_db()

    try:
        # Se non è specificato un cantiere, ottieni tutti i cantieri
        if cantiere_id is None:
            cantieri = execute_query(conn, "SELECT id_cantiere, nome FROM cantieri ORDER BY id_cantiere")
            print_results("Cantieri disponibili", cantieri)

            if not cantieri:
                print("Nessun cantiere trovato nel database.")
                return

            # Usa il primo cantiere come default
            cantiere_id = cantieri[0]['id_cantiere']
            print(f"\nUtilizzo il cantiere con ID {cantiere_id} ({cantieri[0]['nome']}) per le verifiche.")

        # 1. Verifica se ci sono cavi con modificato_manualmente = 3
        query1 = """
        SELECT id_cavo, stato_installazione, modificato_manualmente
        FROM cavi
        WHERE id_cantiere = %s AND modificato_manualmente = 3
        ORDER BY id_cavo
        """
        results1 = execute_query(conn, query1, (cantiere_id,))
        print_results("Cavi con modificato_manualmente = 3", results1)

        # 2. Verifica se ci sono cavi con stato_installazione = 'SPARE'
        query2 = """
        SELECT id_cavo, stato_installazione, modificato_manualmente
        FROM cavi
        WHERE id_cantiere = %s AND stato_installazione = 'SPARE'
        ORDER BY id_cavo
        """
        results2 = execute_query(conn, query2, (cantiere_id,))
        print_results("Cavi con stato_installazione = 'SPARE'", results2)

        # 3. Verifica tutti i cavi per un cantiere specifico (limitato a 10 per brevità)
        query3 = """
        SELECT id_cavo, stato_installazione, modificato_manualmente
        FROM cavi
        WHERE id_cantiere = %s
        ORDER BY id_cavo
        LIMIT 10
        """
        results3 = execute_query(conn, query3, (cantiere_id,))
        print_results("Primi 10 cavi nel cantiere", results3)

        # 4. Se è specificato un cavo, verifica i suoi dettagli
        if cavo_id:
            query4 = """
            SELECT *
            FROM cavi
            WHERE id_cantiere = %s AND id_cavo = %s
            """
            results4 = execute_query(conn, query4, (cantiere_id, cavo_id))
            print_results(f"Dettagli del cavo {cavo_id}", results4)

            # Verifica anche la cronologia delle modifiche se esiste una tabella per questo
            try:
                query5 = """
                SELECT *
                FROM cavi_history
                WHERE id_cantiere = %s AND id_cavo = %s
                ORDER BY timestamp DESC
                """
                results5 = execute_query(conn, query5, (cantiere_id, cavo_id))
                print_results(f"Cronologia modifiche del cavo {cavo_id}", results5)
            except:
                print("\nNessuna tabella di cronologia trovata per i cavi.")

        # 5. Verifica la struttura della tabella cavi
        query6 = """
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'cavi'
        ORDER BY ordinal_position
        """
        results6 = execute_query(conn, query6)
        print_results("Struttura della tabella cavi", results6)

    finally:
        conn.close()
        print("\nConnessione al database chiusa.")

if __name__ == "__main__":
    # Ottieni l'ID del cantiere e del cavo dai parametri della riga di comando
    cantiere_id = None
    cavo_id = None

    if len(sys.argv) > 1:
        try:
            cantiere_id = int(sys.argv[1])
        except ValueError:
            print(f"ID cantiere non valido: {sys.argv[1]}")
            sys.exit(1)

    if len(sys.argv) > 2:
        cavo_id = sys.argv[2]

    check_spare_cables(cantiere_id, cavo_id)
