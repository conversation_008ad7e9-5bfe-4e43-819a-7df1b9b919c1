from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth2Pass<PERSON><PERSON>ear<PERSON>, OAuth2PasswordRequestForm
from pydantic import BaseModel
import uvicorn
import psycopg2
from psycopg2.extras import RealDictCursor
import bcrypt
import os
from dotenv import load_dotenv

# Carica le variabili d'ambiente
load_dotenv()

# Configurazione
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = os.getenv("DB_PORT", "5432")
DB_NAME = os.getenv("DB_NAME", "cantieri")
DB_USER = os.getenv("DB_USER", "postgres")
DB_PASSWORD = os.getenv("DB_PASSWORD", "Taranto")

# Crea l'app FastAPI
app = FastAPI(title="CMS API Test")

# Configurazione CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In produzione, specificare l'origine esatta
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Schema per il token
class Token(BaseModel):
    access_token: str
    token_type: str
    user_id: int
    username: str
    role: str

# Schema per il login cantiere
class CantiereLogin(BaseModel):
    codice_univoco: str
    password: str

# Funzione per ottenere una connessione al database
def get_db_connection():
    conn = psycopg2.connect(
        host=DB_HOST,
        port=DB_PORT,
        dbname=DB_NAME,
        user=DB_USER,
        password=DB_PASSWORD
    )
    conn.autocommit = True
    return conn

# Endpoint di base
@app.get("/")
async def root():
    return {"message": "CMS API Test è in esecuzione"}

# Endpoint per verificare la connessione al database
@app.get("/api/test-db")
async def test_db():
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute("SELECT version()")
        version = cursor.fetchone()
        cursor.close()
        conn.close()
        return {"status": "success", "db_version": version["version"]}
    except Exception as e:
        return {"status": "error", "message": str(e)}

# Endpoint per ottenere la lista dei cantieri
@app.get("/api/cantieri")
async def get_cantieri():
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute("""
            SELECT id_cantiere, nome, codice_univoco
            FROM cantieri
            ORDER BY nome
        """)
        cantieri = cursor.fetchall()
        cursor.close()
        conn.close()
        return {"cantieri": cantieri}
    except Exception as e:
        return {"status": "error", "message": str(e)}

# Endpoint per il login standard
@app.post("/api/auth/login")
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # Cerca l'utente nel database
        cursor.execute("""
            SELECT id_utente, username, password, ruolo, abilitato
            FROM utenti
            WHERE username = %s
        """, (form_data.username,))

        user = cursor.fetchone()
        cursor.close()
        conn.close()

        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Username o password non corretti",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Verifica la password
        stored_password = user["password"]
        if isinstance(stored_password, str):
            stored_password = stored_password.encode('utf-8')

        try:
            password_correct = bcrypt.checkpw(form_data.password.encode('utf-8'), stored_password)
        except ValueError:
            # Nessun fallback per utenti migrati
            print("Errore nel formato della password")
            password_correct = False

        if not password_correct:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Username o password non corretti",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Verifica che l'utente sia abilitato
        if not user["abilitato"]:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Utente disabilitato",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Genera un vero token JWT
        import jwt
        from datetime import datetime, timedelta

        # Definisci la chiave segreta e l'algoritmo
        SECRET_KEY = "your-secret-key"  # Usa una chiave segreta sicura in produzione
        ALGORITHM = "HS256"
        ACCESS_TOKEN_EXPIRE_MINUTES = 30

        # Crea i dati per il token
        data = {
            "sub": user["username"],
            "user_id": user["id_utente"],
            "role": user["ruolo"]
        }

        # Imposta la scadenza del token
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        data.update({"exp": expire})

        # Genera il token JWT
        encoded_jwt = jwt.encode(data, SECRET_KEY, algorithm=ALGORITHM)

        return {
            "access_token": encoded_jwt,
            "token_type": "bearer",
            "user_id": user["id_utente"],
            "username": user["username"],
            "role": user["ruolo"]
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore del server: {str(e)}"
        )

# Endpoint per il login cantiere
@app.post("/api/auth/login/cantiere")
async def login_cantiere(cantiere_login: CantiereLogin):
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # Cerca il cantiere nel database
        cursor.execute("""
            SELECT id_cantiere, nome, password_cantiere, id_utente, codice_univoco
            FROM cantieri
            WHERE codice_univoco = %s
        """, (cantiere_login.codice_univoco,))

        cantiere = cursor.fetchone()

        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Codice univoco o password non corretti",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Nessun caso speciale per i cantieri

        # Verifica la password
        stored_password = cantiere["password_cantiere"]
        password_correct = False

        # Prova diversi metodi di verifica della password
        try:
            # 1. Verifica con bcrypt se la password è in formato hash
            if isinstance(stored_password, str) and stored_password.startswith('$2'):
                stored_password_bytes = stored_password.encode('utf-8')
                password_correct = bcrypt.checkpw(cantiere_login.password.encode('utf-8'), stored_password_bytes)
            # 2. Verifica diretta (per password in plaintext o casi speciali)
            elif cantiere_login.password == stored_password:
                password_correct = True
            # Nessun caso speciale per i cantieri
        except Exception as e:
            # Log dell'errore per debug
            print(f"Errore nella verifica della password: {str(e)}")
            # Nessun fallback per casi speciali
            password_correct = False

        if not password_correct:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Codice univoco o password non corretti",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Ottieni l'utente proprietario del cantiere
        cursor.execute("""
            SELECT username
            FROM utenti
            WHERE id_utente = %s
        """, (cantiere["id_utente"],))

        user = cursor.fetchone()
        cursor.close()
        conn.close()

        # Per il test, non generiamo un vero token JWT
        return {
            "access_token": "test_token_cantiere",
            "token_type": "bearer",
            "user_id": cantiere["id_utente"],
            "username": f"Cantiere: {cantiere['nome']}",
            "role": "cantieri_user",
            "cantiere_id": cantiere["id_cantiere"],
            "cantiere_name": cantiere["nome"]
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore del server: {str(e)}"
        )

if __name__ == "__main__":
    uvicorn.run("test_api:app", host="0.0.0.0", port=8000, reload=True)
