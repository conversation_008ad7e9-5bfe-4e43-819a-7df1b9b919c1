#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script di test per verificare il recupero dei cavi.
Questo script testa direttamente la funzionalità di recupero dei cavi
per identificare e risolvere il problema "dictionary update sequence element #0 has length 15; 2 is required".
"""

import sys
import os
import traceback
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session

# Assicurati che il percorso del progetto sia nel PYTHONPATH
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Importa le configurazioni e i modelli
from webapp.backend.config import settings
from webapp.backend.models.cavo import Cavo

def test_cavi_query(cantiere_id: int, tipo_cavo: int = None):
    """
    Testa la query per recuperare i cavi di un cantiere.

    Args:
        cantiere_id: ID del cantiere
        tipo_cavo: Tipo di cavo (0 = attivo, 3 = spare)
    """
    print(f"\n=== Test recupero cavi per cantiere {cantiere_id} (tipo: {tipo_cavo}) ===")

    # Crea la connessione al database
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()

    try:
        # Costruisci la query SQL
        sql_query = """
            SELECT id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo, tipologia,
                   n_conduttori, sezione, sh, ubicazione_partenza, utenza_partenza, descrizione_utenza_partenza,
                   ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo, metri_teorici, metratura_reale,
                   responsabile_posa, id_bobina, stato_installazione, modificato_manualmente, timestamp,
                   collegamenti, responsabile_partenza, responsabile_arrivo, comanda_posa, comanda_partenza, comanda_arrivo
            FROM cavi
            WHERE id_cantiere = :cantiere_id
        """

        params = {"cantiere_id": cantiere_id}

        if tipo_cavo is not None:
            if tipo_cavo == 0:
                sql_query += " AND (modificato_manualmente != 3 OR modificato_manualmente IS NULL)"
            elif tipo_cavo == 3:
                sql_query += " AND modificato_manualmente = 3"

        sql_query += " ORDER BY id_cavo"

        print(f"Query SQL: {sql_query}")
        print(f"Parametri: {params}")

        # Esegui la query
        result = db.execute(text(sql_query), params)

        # Ottieni i risultati
        rows = result.fetchall()
        print(f"Trovati {len(rows)} cavi")

        if not rows:
            print("Nessun cavo trovato")
            return

        # Stampa informazioni sulla prima riga
        first_row = rows[0]
        print(f"Tipo della prima riga: {type(first_row)}")
        print(f"Contenuto della prima riga: {first_row}")
        print(f"Attributi della prima riga: {dir(first_row) if hasattr(first_row, '__dict__') else 'N/A'}")

        # Ottieni i nomi delle colonne
        column_names = result.keys()
        print(f"Nomi delle colonne: {column_names}")
        print(f"Numero di colonne: {len(column_names)}")

        if isinstance(first_row, (list, tuple)):
            print(f"Lunghezza della prima riga: {len(first_row)}")
            if len(column_names) != len(first_row):
                print(f"ATTENZIONE: Il numero di colonne ({len(column_names)}) non corrisponde al numero di valori ({len(first_row)})")

        # Prova a convertire la prima riga in un dizionario
        print("\nTest di conversione in dizionario:")
        try:
            # Metodo 1: dict(row)
            try:
                dict_direct = dict(first_row)
                print(f"Metodo 1 (dict(row)): Successo - {len(dict_direct)} elementi")
            except Exception as e:
                print(f"Metodo 1 (dict(row)): Fallito - {str(e)}")

            # Metodo 2: _mapping
            if hasattr(first_row, '_mapping'):
                try:
                    dict_mapping = dict(first_row._mapping)
                    print(f"Metodo 2 (_mapping): Successo - {len(dict_mapping)} elementi")
                except Exception as e:
                    print(f"Metodo 2 (_mapping): Fallito - {str(e)}")
            else:
                print("Metodo 2 (_mapping): Non applicabile - l'oggetto non ha l'attributo '_mapping'")

            # Metodo 3: _asdict()
            if hasattr(first_row, '_asdict'):
                try:
                    dict_asdict = first_row._asdict()
                    print(f"Metodo 3 (_asdict): Successo - {len(dict_asdict)} elementi")
                except Exception as e:
                    print(f"Metodo 3 (_asdict): Fallito - {str(e)}")
            else:
                print("Metodo 3 (_asdict): Non applicabile - l'oggetto non ha il metodo '_asdict'")

            # Metodo 4: zip con column_names
            if isinstance(first_row, (list, tuple)):
                try:
                    dict_zip = dict(zip(column_names, first_row))
                    print(f"Metodo 4 (zip): Successo - {len(dict_zip)} elementi")
                except Exception as e:
                    print(f"Metodo 4 (zip): Fallito - {str(e)}")
            else:
                print("Metodo 4 (zip): Non applicabile - l'oggetto non è una lista o tupla")

        except Exception as e:
            print(f"Errore durante i test di conversione: {str(e)}")
            traceback.print_exc()

        # Prova a convertire tutte le righe in oggetti Cavo
        print("\nTest di conversione in oggetti Cavo:")
        cavi = []
        for i, row in enumerate(rows[:5]):  # Limita a 5 righe per brevità
            try:
                # Converti la riga in dizionario
                if hasattr(row, '_mapping'):
                    cavo_dict = dict(row._mapping)  # Usa _mapping per la conversione (metodo più affidabile)
                elif isinstance(row, tuple) and hasattr(row, '_fields'):
                    cavo_dict = row._asdict()
                elif isinstance(row, (list, tuple)):
                    if len(column_names) == len(row):
                        cavo_dict = dict(zip(column_names, row))
                    else:
                        print(f"Riga {i}: Il numero di colonne ({len(column_names)}) non corrisponde al numero di valori ({len(row)})")
                        continue
                else:
                    print(f"Riga {i}: Tipo non supportato - {type(row)}")
                    continue

                # Crea un oggetto Cavo
                cavo_params = {}
                for key, value in cavo_dict.items():
                    if hasattr(Cavo, key) or key in ['id_cavo', 'id_cantiere', 'revisione_ufficiale', 'sistema',
                                                    'utility', 'colore_cavo', 'tipologia', 'n_conduttori', 'sezione',
                                                    'sh', 'ubicazione_partenza', 'utenza_partenza', 'descrizione_utenza_partenza',
                                                    'ubicazione_arrivo', 'utenza_arrivo', 'descrizione_utenza_arrivo',
                                                    'metri_teorici', 'metratura_reale', 'responsabile_posa', 'id_bobina',
                                                    'stato_installazione', 'modificato_manualmente', 'timestamp',
                                                    'collegamenti', 'responsabile_partenza', 'responsabile_arrivo',
                                                    'comanda_posa', 'comanda_partenza', 'comanda_arrivo']:
                        cavo_params[key] = value

                cavo = Cavo(**cavo_params)
                cavi.append(cavo)
                print(f"Riga {i}: Conversione riuscita - ID: {cavo.id_cavo}")

            except Exception as e:
                print(f"Riga {i}: Errore durante la conversione - {str(e)}")
                traceback.print_exc()

        print(f"\nConversione completata: {len(cavi)}/{min(5, len(rows))} righe convertite con successo")

    except Exception as e:
        print(f"Errore durante il test: {str(e)}")
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    # Verifica se è stato fornito un ID cantiere come argomento
    if len(sys.argv) > 1:
        cantiere_id = int(sys.argv[1])
    else:
        # Usa un ID cantiere di default per il test
        cantiere_id = 1

    # Testa il recupero dei cavi attivi
    test_cavi_query(cantiere_id, 0)

    # Testa il recupero dei cavi spare
    test_cavi_query(cantiere_id, 3)

    # Testa il recupero di tutti i cavi
    test_cavi_query(cantiere_id)
