#!/usr/bin/env python3
"""
Test script per verificare il funzionamento dell'importazione Excel ottimizzata.
Questo script testa le funzioni di importazione senza dover utilizzare l'interfaccia web.
"""

import sys
import os
from pathlib import Path
import tempfile
import logging

# Aggiungi il percorso del progetto al Python path
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root))

# Configura il logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_excel_import_functions():
    """Test delle funzioni di importazione Excel ottimizzate."""
    
    print("🔄 Test delle funzioni di importazione Excel ottimizzate")
    print("=" * 60)
    
    try:
        # Import delle funzioni ottimizzate
        from webapp.backend.api.excel import (
            importa_cavi_da_excel_webapp,
            importa_parco_bobine_da_excel_webapp
        )
        
        print("✅ Import delle funzioni ottimizzate riuscito")
        
        # Test delle funzioni CLI originali
        from modules.excel_manager import (
            is_file_safe,
            leggi_file_excel,
            valida_colonne_excel,
            crea_template_excel,
            crea_template_parco_bobine
        )
        
        print("✅ Import delle funzioni CLI originali riuscito")
        
        # Test creazione template cavi
        print("\n🔄 Test creazione template cavi...")
        temp_dir = tempfile.mkdtemp()
        template_cavi_path = os.path.join(temp_dir, "test_template_cavi.xlsx")
        
        result = crea_template_excel(template_cavi_path)
        if result and os.path.exists(result):
            print(f"✅ Template cavi creato: {result}")
            os.unlink(result)
        else:
            print("❌ Errore nella creazione del template cavi")
        
        # Test creazione template parco bobine
        print("\n🔄 Test creazione template parco bobine...")
        template_bobine_path = os.path.join(temp_dir, "test_template_bobine.xlsx")
        
        result = crea_template_parco_bobine(template_bobine_path)
        if result and os.path.exists(result):
            print(f"✅ Template parco bobine creato: {result}")
            os.unlink(result)
        else:
            print("❌ Errore nella creazione del template parco bobine")
        
        # Cleanup
        os.rmdir(temp_dir)
        
        print("\n✅ Tutti i test delle funzioni sono passati!")
        return True
        
    except ImportError as e:
        print(f"❌ Errore di import: {e}")
        return False
    except Exception as e:
        print(f"❌ Errore durante i test: {e}")
        return False

def test_field_compatibility():
    """Test della compatibilità dei campi tra CLI e webapp."""
    
    print("\n🔄 Test compatibilità campi tra CLI e webapp")
    print("=" * 60)
    
    try:
        # Test mapping formazione -> sezione
        from modules.excel_manager import valida_colonne_excel
        import pandas as pd
        
        # Crea un DataFrame di test con colonna 'formazione'
        test_data = {
            'id_cavo': ['C001', 'C002'],
            'utility': ['UT1', 'UT2'],
            'tipologia': ['TIP1', 'TIP2'],
            'formazione': ['3x2.5', '4x1.5'],  # Usa 'formazione' invece di 'sezione'
            'metri_teorici': [100, 200]
        }
        
        df_test = pd.DataFrame(test_data)
        print(f"📊 DataFrame di test creato con colonne: {list(df_test.columns)}")
        
        # Test validazione colonne
        df_validato = valida_colonne_excel(df_test)
        
        if df_validato is not None:
            print(f"✅ Validazione riuscita. Colonne risultanti: {list(df_validato.columns)}")
            
            # Verifica che 'formazione' sia stata mappata a 'sezione'
            if 'sezione' in df_validato.columns:
                print("✅ Mapping formazione -> sezione funziona correttamente")
                print(f"   Valori sezione: {df_validato['sezione'].tolist()}")
            else:
                print("❌ Mapping formazione -> sezione non funziona")
                
            # Verifica derivazione n_conduttori
            if 'n_conduttori' in df_validato.columns:
                print("✅ Derivazione n_conduttori funziona correttamente")
                print(f"   Valori n_conduttori: {df_validato['n_conduttori'].tolist()}")
            else:
                print("❌ Derivazione n_conduttori non funziona")
                
        else:
            print("❌ Validazione colonne fallita")
            return False
            
        print("\n✅ Test compatibilità campi completato!")
        return True
        
    except Exception as e:
        print(f"❌ Errore durante test compatibilità: {e}")
        return False

def test_database_connection():
    """Test della connessione al database PostgreSQL."""
    
    print("\n🔄 Test connessione database PostgreSQL")
    print("=" * 60)
    
    try:
        from modules.database_pg import database_connection
        
        # Test connessione semplice
        with database_connection() as (conn, cursor):
            cursor.execute("SELECT version();")
            version = cursor.fetchone()
            print(f"✅ Connessione PostgreSQL riuscita")
            print(f"   Versione: {version[0] if version else 'N/A'}")
            
        # Test connessione con dict cursor
        with database_connection(dict_cursor=True) as (conn, cursor):
            cursor.execute("SELECT COUNT(*) as count FROM Cantieri;")
            result = cursor.fetchone()
            print(f"✅ Connessione con dict cursor riuscita")
            print(f"   Numero cantieri: {result['count'] if result else 'N/A'}")
            
        print("\n✅ Test connessione database completato!")
        return True
        
    except Exception as e:
        print(f"❌ Errore connessione database: {e}")
        return False

def main():
    """Funzione principale per eseguire tutti i test."""
    
    print("🚀 INIZIO TEST SISTEMA IMPORTAZIONE EXCEL OTTIMIZZATO")
    print("=" * 80)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Funzioni di importazione
    if test_excel_import_functions():
        tests_passed += 1
    
    # Test 2: Compatibilità campi
    if test_field_compatibility():
        tests_passed += 1
    
    # Test 3: Connessione database
    if test_database_connection():
        tests_passed += 1
    
    # Risultato finale
    print("\n" + "=" * 80)
    print(f"🏁 RISULTATO FINALE: {tests_passed}/{total_tests} test passati")
    
    if tests_passed == total_tests:
        print("✅ Tutti i test sono passati! Il sistema è pronto per l'uso.")
        return True
    else:
        print("❌ Alcuni test sono falliti. Controllare i log per i dettagli.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
