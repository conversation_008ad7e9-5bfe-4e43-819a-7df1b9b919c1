import requests
import sys
import os
import subprocess
import time

def check_backend_running():
    """Verifica se il backend è in esecuzione"""
    try:
        response = requests.get("http://localhost:8001/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ Il backend è in esecuzione e risponde correttamente!")
            return True
        else:
            print(f"❌ Il backend è in esecuzione ma risponde con codice di errore: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Impossibile connettersi al backend. Il server non è in esecuzione.")
        return False
    except Exception as e:
        print(f"❌ Errore durante la verifica del backend: {str(e)}")
        return False

def start_backend():
    """Avvia il backend"""
    try:
        print("🚀 Avvio del backend in corso...")
        
        # Cambia directory alla cartella backend
        backend_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "backend")
        os.chdir(backend_dir)
        
        # Avvia il backend
        if sys.platform == "win32":
            # Su Windows, usa subprocess.Popen
            process = subprocess.Popen(
                [sys.executable, "main.py"],
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT
            )
            print(f"Backend avviato con PID: {process.pid}")
        else:
            # Su Linux/Mac, usa subprocess.Popen
            process = subprocess.Popen(
                [sys.executable, "main.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT
            )
            print(f"Backend avviato con PID: {process.pid}")
        
        # Attendi che il backend si avvii
        print("Attesa avvio del backend...")
        for i in range(10):
            time.sleep(1)
            print(f"Attesa: {i+1}/10", end="\r")
            try:
                response = requests.get("http://localhost:8001/api/health", timeout=2)
                if response.status_code == 200:
                    print("\n✅ Backend avviato con successo!")
                    return True
            except:
                pass
        
        print("\n⚠️ Il backend potrebbe non essere stato avviato correttamente.")
        return False
    except Exception as e:
        print(f"❌ Errore durante l'avvio del backend: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔍 Verifica dello stato del backend...")
    if check_backend_running():
        print("✅ Il backend è già in esecuzione. Puoi utilizzare l'applicazione normalmente.")
    else:
        print("❌ Il backend non è in esecuzione.")
        choice = input("Vuoi avviare il backend? (s/n): ")
        if choice.lower() == "s":
            if start_backend():
                print("✅ Backend avviato con successo. Ora puoi utilizzare l'applicazione.")
                print("📊 Accedi all'applicazione all'indirizzo: http://localhost:3000")
            else:
                print("❌ Impossibile avviare il backend. Prova ad avviarlo manualmente.")
                print("📝 Esegui il seguente comando nella cartella backend:")
                print("   python main.py")
        else:
            print("⚠️ Il backend non è stato avviato. L'applicazione non funzionerà correttamente.")
            print("📝 Per avviare il backend, esegui il seguente comando nella cartella backend:")
            print("   python main.py")
