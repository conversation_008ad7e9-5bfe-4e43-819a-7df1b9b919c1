
> cms-frontend@0.1.0 start
> react-scripts start

(node:9076) [DEP_WEBPACK_DEV_SERVER_ON_AFTER_SETUP_MIDDLEWARE] DeprecationWarning: 'onAfterSetupMiddleware' option is deprecated. Please use the 'setupMiddlewares' option.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:9076) [DEP_WEBPACK_DEV_SERVER_ON_BEFORE_SETUP_MIDDLEWARE] DeprecationWarning: 'onBeforeSetupMiddleware' option is deprecated. Please use the 'setupMiddlewares' option.
Starting the development server...

Compiled with warnings.

[eslint] 
src\components\TopNavbar.js
  Line 13:3:   'Avatar' is defined but never used                         no-unused-vars
  Line 20:25:  'AdminIcon' is defined but never used                      no-unused-vars
  Line 21:19:  'ConstructionIcon' is defined but never used               no-unused-vars
  Line 22:12:  'CableIcon' is defined but never used                      no-unused-vars
  Line 23:18:  'ReportIcon' is defined but never used                     no-unused-vars
  Line 56:10:  'homeAnchorEl' is assigned a value but never used          no-unused-vars
  Line 57:10:  'adminAnchorEl' is assigned a value but never used         no-unused-vars
  Line 58:10:  'cantieriAnchorEl' is assigned a value but never used      no-unused-vars
  Line 59:10:  'caviAnchorEl' is assigned a value but never used          no-unused-vars
  Line 68:9:   'selectedCantiereName' is assigned a value but never used  no-unused-vars

src\components\admin\UserExpirationChecker.js
  Line 11:10:  'lastCheck' is assigned a value but never used  no-unused-vars

src\components\cavi\CavoForm.js
  Line 6:3:   'Stack' is defined but never used    no-unused-vars
  Line 11:3:  'Divider' is defined but never used  no-unused-vars

src\components\cavi\CertificazioneCavi.js
  Line 8:3:    'Card' is defined but never used                                                                                       no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                                no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                               no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                             no-unused-vars
  Line 37:10:  'AddIcon' is defined but never used                                                                                    no-unused-vars
  Line 38:11:  'EditIcon' is defined but never used                                                                                   no-unused-vars
  Line 45:15:  'ViewListIcon' is defined but never used                                                                               no-unused-vars
  Line 46:12:  'BuildIcon' is defined but never used                                                                                  no-unused-vars
  Line 122:6:  React Hook useEffect has a missing dependency: 'loadCertificazioni'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\cavi\CollegamentiCavo.js
  Line 8:3:   'List' is defined but never used                                                                             no-unused-vars
  Line 9:3:   'ListItem' is defined but never used                                                                         no-unused-vars
  Line 10:3:  'ListItemText' is defined but never used                                                                     no-unused-vars
  Line 46:6:  React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\cavi\ExcelPopup.js
  Line 10:3:   'TextField' is defined but never used          no-unused-vars
  Line 14:3:   'Divider' is defined but never used            no-unused-vars
  Line 27:10:  'filePath' is assigned a value but never used  no-unused-vars

src\components\cavi\GestioneComande.js
  Line 8:3:    'Card' is defined but never used                                                                                no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                         no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                         no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                        no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                      no-unused-vars
  Line 25:3:   'Divider' is defined but never used                                                                             no-unused-vars
  Line 37:10:  'AddIcon' is defined but never used                                                                             no-unused-vars
  Line 41:13:  'SearchIcon' is defined but never used                                                                          no-unused-vars
  Line 43:15:  'ViewListIcon' is defined but never used                                                                        no-unused-vars
  Line 44:17:  'AssignmentIcon' is defined but never used                                                                      no-unused-vars
  Line 98:6:   React Hook useEffect has a missing dependency: 'loadComande'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 101:9:  'handleOptionSelect' is assigned a value but never used                                                         no-unused-vars

src\components\cavi\IncompatibleReelDialog.js
  Line 17:3:  'Alert' is defined but never used  no-unused-vars

src\components\cavi\InserisciMetriForm.js
  Line 17:3:     'FormHelperText' is defined but never used                                                                     no-unused-vars
  Line 34:3:     'ListItemText' is defined but never used                                                                       no-unused-vars
  Line 35:3:     'ListItemSecondaryAction' is defined but never used                                                            no-unused-vars
  Line 39:11:    'SaveIcon' is defined but never used                                                                           no-unused-vars
  Line 51:3:     'CABLE_STATES' is defined but never used                                                                       no-unused-vars
  Line 52:3:     'REEL_STATES' is defined but never used                                                                        no-unused-vars
  Line 54:3:     'determineReelState' is defined but never used                                                                 no-unused-vars
  Line 55:3:     'canModifyCable' is defined but never used                                                                     no-unused-vars
  Line 62:10:    'redirectToVisualizzaCavi' is defined but never used                                                           no-unused-vars
  Line 105:10:   'incompatibleReel' is assigned a value but never used                                                          no-unused-vars
  Line 105:28:   'setIncompatibleReel' is assigned a value but never used                                                       no-unused-vars
  Line 145:6:    React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 701:9:    'handleBack' is assigned a value but never used                                                                no-unused-vars
  Line 1311:11:  'buildFullBobinaId' is assigned a value but never used                                                         no-unused-vars
  Line 1316:11:  'hasSufficientMeters' is assigned a value but never used                                                       no-unused-vars
  Line 1883:9:   'getStepContent' is assigned a value but never used                                                            no-unused-vars

src\components\cavi\MetriPosatiSemplificatoForm.js
  Line 33:3:   'CABLE_STATES' is defined but never used                                                                                        no-unused-vars
  Line 34:3:   'REEL_STATES' is defined but never used                                                                                         no-unused-vars
  Line 35:3:   'determineCableState' is defined but never used                                                                                 no-unused-vars
  Line 36:3:   'determineReelState' is defined but never used                                                                                  no-unused-vars
  Line 37:3:   'canModifyCable' is defined but never used                                                                                      no-unused-vars
  Line 41:3:   'getReelStateColor' is defined but never used                                                                                   no-unused-vars
  Line 43:10:  'redirectToVisualizzaCavi' is defined but never used                                                                            no-unused-vars
  Line 69:10:  'loading' is assigned a value but never used                                                                                    no-unused-vars
  Line 69:19:  'setLoading' is assigned a value but never used                                                                                 no-unused-vars
  Line 88:6:   React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 448:6:  React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array       react-hooks/exhaustive-deps

src\components\cavi\ModificaBobinaForm.js
  Line 9:3:     'FormControl' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 10:3:    'InputLabel' is defined but never used                                                                                                                                                                                         no-unused-vars
  Line 11:3:    'Select' is defined but never used                                                                                                                                                                                             no-unused-vars
  Line 12:3:    'MenuItem' is defined but never used                                                                                                                                                                                           no-unused-vars
  Line 33:3:    'ListItemText' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 35:3:    'ListItemSecondaryAction' is defined but never used                                                                                                                                                                            no-unused-vars
  Line 42:14:   'WarningIcon' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 52:3:    'CABLE_STATES' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 53:3:    'REEL_STATES' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 54:3:    'determineCableState' is defined but never used                                                                                                                                                                                no-unused-vars
  Line 55:3:    'determineReelState' is defined but never used                                                                                                                                                                                 no-unused-vars
  Line 56:3:    'canModifyCable' is defined but never used                                                                                                                                                                                     no-unused-vars
  Line 57:3:    'isCableSpare' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 58:3:    'isCableInstalled' is defined but never used                                                                                                                                                                                   no-unused-vars
  Line 59:3:    'getCableStateColor' is defined but never used                                                                                                                                                                                 no-unused-vars
  Line 72:9:    'navigate' is assigned a value but never used                                                                                                                                                                                  no-unused-vars
  Line 79:10:   'searchResults' is assigned a value but never used                                                                                                                                                                             no-unused-vars
  Line 79:25:   'setSearchResults' is assigned a value but never used                                                                                                                                                                          no-unused-vars
  Line 80:10:   'showSearchResults' is assigned a value but never used                                                                                                                                                                         no-unused-vars
  Line 85:10:   'compatibleBobine' is assigned a value but never used                                                                                                                                                                          no-unused-vars
  Line 105:6:   React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array                                                                                                                    react-hooks/exhaustive-deps
  Line 112:6:   React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array                                                                                                                  react-hooks/exhaustive-deps
  Line 127:6:   React Hook useEffect has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 283:13:  'bobina' is assigned a value but never used                                                                                                                                                                                    no-unused-vars

src\components\cavi\ParcoCavi.js
  Line 8:3:    'Card' is defined but never used                                                                                                                            no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                                                     no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                                                                     no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                                                                    no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                                                                  no-unused-vars
  Line 25:3:   'Divider' is defined but never used                                                                                                                         no-unused-vars
  Line 29:3:   'IconButton' is defined but never used                                                                                                                      no-unused-vars
  Line 39:11:  'EditIcon' is defined but never used                                                                                                                        no-unused-vars
  Line 43:15:  'ViewListIcon' is defined but never used                                                                                                                    no-unused-vars
  Line 44:14:  'WarningIcon' is defined but never used                                                                                                                     no-unused-vars
  Line 50:69:  'isEmpty' is defined but never used                                                                                                                         no-unused-vars
  Line 79:10:  'isFirstInsertion' is assigned a value but never used                                                                                                       no-unused-vars
  Line 145:6:  React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 689:9:  'renderBobineCards' is assigned a value but never used                                                                                                      no-unused-vars

src\components\cavi\PosaCaviCollegamenti.js
  Line 20:3:   'InputLabel' is defined but never used             no-unused-vars
  Line 21:3:   'Select' is defined but never used                 no-unused-vars
  Line 22:3:   'MenuItem' is defined but never used               no-unused-vars
  Line 23:3:   'Grid' is defined but never used                   no-unused-vars
  Line 26:3:   'FormHelperText' is defined but never used         no-unused-vars
  Line 69:10:  'formWarnings' is assigned a value but never used  no-unused-vars
  Line 466:9:  Unreachable code                                   no-unreachable

src\components\cavi\QuickAddCablesDialog.js
  Line 12:3:    'FormControlLabel' is defined but never used                                                                 no-unused-vars
  Line 27:10:   'AddIcon' is defined but never used                                                                          no-unused-vars
  Line 30:11:   'InfoIcon' is defined but never used                                                                         no-unused-vars
  Line 34:10:   'determineCableState' is defined but never used                                                              no-unused-vars
  Line 49:10:   'loading' is assigned a value but never used                                                                 no-unused-vars
  Line 49:19:   'setLoading' is assigned a value but never used                                                              no-unused-vars
  Line 64:6:    React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 270:17:  'result' is assigned a value but never used                                                                  no-unused-vars

src\components\cavi\ReportCavi.js
  Line 7:3:    'Grid' is defined but never used                         no-unused-vars
  Line 20:3:   'Divider' is defined but never used                      no-unused-vars
  Line 21:3:   'List' is defined but never used                         no-unused-vars
  Line 22:3:   'ListItem' is defined but never used                     no-unused-vars
  Line 23:3:   'ListItemText' is defined but never used                 no-unused-vars
  Line 24:3:   'ListItemIcon' is defined but never used                 no-unused-vars
  Line 25:3:   'ListItemButton' is defined but never used               no-unused-vars
  Line 29:15:  'BarChartIcon' is defined but never used                 no-unused-vars
  Line 34:16:  'DateRangeIcon' is defined but never used                no-unused-vars
  Line 53:9:   'handleOptionSelect' is assigned a value but never used  no-unused-vars

src\components\cavi\SelezionaCavoForm.js
  Line 4:3:  'TextField' is defined but never used  no-unused-vars

src\components\certificazioni\CertificazioneForm.jsx
  Line 54:6:  React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\certificazioni\CertificazioniList.jsx
  Line 25:13:  'DownloadIcon' is defined but never used  no-unused-vars

src\components\charts\BoqChart.js
  Line 14:3:  'ComposedChart' is defined but never used  no-unused-vars
  Line 15:3:  'Line' is defined but never used           no-unused-vars
  Line 16:3:  'LineChart' is defined but never used      no-unused-vars

src\components\charts\CaviStatoChart.js
  Line 14:3:  'ComposedChart' is defined but never used  no-unused-vars
  Line 15:3:  'Line' is defined but never used           no-unused-vars

src\components\charts\TimelineChart.js
  Line 3:3:  'LineChart' is defined but never used  no-unused-vars

src\components\common\ExcelLikeFilter.js
  Line 16:3:  'MenuItem' is defined but never used    no-unused-vars
  Line 17:3:  'Select' is defined but never used      no-unused-vars
  Line 19:3:  'InputLabel' is defined but never used  no-unused-vars

src\pages\CaviPage.js
  Line 1:8:  'React' is defined but never used  no-unused-vars

src\pages\CertificazioniPageDebug.jsx
  Line 49:19:  'useAuth' is assigned a value but never used  no-unused-vars

src\pages\Dashboard.js
  Line 38:8:   'RedirectToCaviVisualizza' is defined but never used  no-unused-vars
  Line 44:11:  'user' is assigned a value but never used             no-unused-vars

src\pages\HomePage.js
  Line 2:27:  'Grid' is defined but never used                 no-unused-vars
  Line 2:33:  'Card' is defined but never used                 no-unused-vars
  Line 2:39:  'CardContent' is defined but never used          no-unused-vars
  Line 2:52:  'CardActionArea' is defined but never used       no-unused-vars
  Line 2:68:  'Avatar' is defined but never used               no-unused-vars
  Line 5:25:  'AdminIcon' is defined but never used            no-unused-vars
  Line 6:19:  'ConstructionIcon' is defined but never used     no-unused-vars
  Line 7:12:  'CableIcon' is defined but never used            no-unused-vars
  Line 8:18:  'ReportIcon' is defined but never used           no-unused-vars
  Line 43:9:  'navigateTo' is assigned a value but never used  no-unused-vars

src\pages\cantieri\CantierePage.js
  Line 24:11:  'isImpersonating' is assigned a value but never used                                                               no-unused-vars
  Line 53:6:   React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\pages\cavi\CertificazioneCaviPage.js
  Line 5:3:    'Paper' is defined but never used                     no-unused-vars
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars
  Line 28:9:   'cantiereName' is assigned a value but never used     no-unused-vars

src\pages\cavi\GestioneComandeePage.js
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars

src\pages\cavi\ParcoCaviPage.js
  Line 9:3:    'Card' is defined but never used                      no-unused-vars
  Line 10:3:   'CardContent' is defined but never used               no-unused-vars
  Line 11:3:   'CardActions' is defined but never used               no-unused-vars
  Line 12:3:   'Grid' is defined but never used                      no-unused-vars
  Line 13:3:   'Divider' is defined but never used                   no-unused-vars
  Line 18:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 19:15:  'ViewListIcon' is defined but never used              no-unused-vars
  Line 20:10:  'AddIcon' is defined but never used                   no-unused-vars
  Line 21:11:  'EditIcon' is defined but never used                  no-unused-vars
  Line 22:13:  'DeleteIcon' is defined but never used                no-unused-vars
  Line 23:14:  'HistoryIcon' is defined but never used               no-unused-vars
  Line 28:8:   'ParcoCavi' is defined but never used                 no-unused-vars
  Line 31:11:  'isImpersonating' is assigned a value but never used  no-unused-vars
  Line 51:9:   'handleSuccess' is assigned a value but never used    no-unused-vars
  Line 56:9:   'handleError' is assigned a value but never used      no-unused-vars

src\pages\cavi\ReportCaviPage.js
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars

src\pages\cavi\ReportCaviPageNew.js
  Line 16:3:   'Tooltip' is defined but never used                       no-unused-vars
  Line 35:15:  'PieChartIcon' is defined but never used                  no-unused-vars
  Line 42:16:  'DateRangeIcon' is defined but never used                 no-unused-vars
  Line 64:11:  'user' is assigned a value but never used                 no-unused-vars
  Line 161:9:  'reportTypes' is assigned a value but never used          no-unused-vars
  Line 279:9:  'handleReportSelect' is assigned a value but never used   no-unused-vars
  Line 321:9:  'renderReportContent' is assigned a value but never used  no-unused-vars

src\pages\cavi\TestCaviPage.js
  Line 1:27:  'useEffect' is defined but never used  no-unused-vars

src\pages\cavi\VisualizzaCaviPage.js
  Line 8:3:    'Card' is defined but never used                                                                                       no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                no-unused-vars
  Line 11:3:   'IconButton' is defined but never used                                                                                 no-unused-vars
  Line 21:8:   'InfoIcon' is defined but never used                                                                                   no-unused-vars
  Line 28:8:   'CavoForm' is defined but never used                                                                                   no-unused-vars
  Line 34:11:  'isImpersonating' is assigned a value but never used                                                                   no-unused-vars
  Line 36:9:   'navigate' is assigned a value but never used                                                                          no-unused-vars
  Line 38:10:  'cantiereName' is assigned a value but never used                                                                      no-unused-vars
  Line 76:10:  'statiInstallazione' is assigned a value but never used                                                                no-unused-vars
  Line 77:10:  'tipologieCavi' is assigned a value but never used                                                                     no-unused-vars
  Line 484:6:  React Hook useEffect has missing dependencies: 'error' and 'user'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 489:9:  'handleOpenDetails' is assigned a value but never used                                                                 no-unused-vars
  Line 583:9:  'renderStatsPanel' is assigned a value but never used                                                                  no-unused-vars

src\pages\cavi\parco\EliminaBobinaPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\ModificaBobinaPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\StoricoUtilizzoPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\posa\CollegamentiPage.js
  Line 5:3:    'Paper' is defined but never used                          no-unused-vars
  Line 6:3:    'Button' is defined but never used                         no-unused-vars
  Line 14:11:  'HomeIcon' is defined but never used                       no-unused-vars
  Line 23:11:  'isImpersonating' is assigned a value but never used       no-unused-vars
  Line 30:9:   'cantiereName' is assigned a value but never used          no-unused-vars
  Line 33:9:   'handleBackToCantieri' is assigned a value but never used  no-unused-vars
  Line 38:9:   'handleBackToAdmin' is assigned a value but never used     no-unused-vars

src\pages\cavi\posa\MetriPosatiSemplificatoPage.js
  Line 27:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\posa\ModificaBobinaPage.js
  Line 5:3:    'Paper' is defined but never used                          no-unused-vars
  Line 6:3:    'Button' is defined but never used                         no-unused-vars
  Line 14:11:  'HomeIcon' is defined but never used                       no-unused-vars
  Line 23:11:  'isImpersonating' is assigned a value but never used       no-unused-vars
  Line 30:9:   'cantiereName' is assigned a value but never used          no-unused-vars
  Line 33:9:   'handleBackToCantieri' is assigned a value but never used  no-unused-vars
  Line 38:9:   'handleBackToAdmin' is assigned a value but never used     no-unused-vars

src\pages\cavi\posa\ModificaCavoPage.js
  Line 20:11:  'isImpersonating' is assigned a value but never used    no-unused-vars
  Line 27:9:   'cantiereName' is assigned a value but never used       no-unused-vars
  Line 35:9:   'handleBackToAdmin' is assigned a value but never used  no-unused-vars

src\services\adminService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\authService.js
  Line 78:11:  Expected an error object to be thrown  no-throw-literal
  Line 80:11:  Expected an error object to be thrown  no-throw-literal
  Line 86:9:   Expected an error object to be thrown  no-throw-literal
  Line 89:9:   Expected an error object to be thrown  no-throw-literal

src\services\cantieriService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\caviService.js
  Line 260:9:   Expected an error object to be thrown       no-throw-literal
  Line 274:9:   Expected an error object to be thrown       no-throw-literal
  Line 278:9:   Expected an error object to be thrown       no-throw-literal
  Line 333:11:  Expected an error object to be thrown       no-throw-literal
  Line 435:9:   Expected an error object to be thrown       no-throw-literal
  Line 451:9:   Expected an error object to be thrown       no-throw-literal
  Line 668:9:   Expected an error object to be thrown       no-throw-literal
  Line 677:9:   Expected an error object to be thrown       no-throw-literal
  Line 681:9:   Expected an error object to be thrown       no-throw-literal
  Line 755:17:  'token' is assigned a value but never used  no-unused-vars
  Line 775:9:   Expected an error object to be thrown       no-throw-literal
  Line 794:11:  Expected an error object to be thrown       no-throw-literal
  Line 801:9:   Expected an error object to be thrown       no-throw-literal
  Line 810:11:  Expected an error object to be thrown       no-throw-literal
  Line 817:9:   Expected an error object to be thrown       no-throw-literal
  Line 885:9:   Expected an error object to be thrown       no-throw-literal
  Line 955:3:   Duplicate key 'updateCavoForCompatibility'  no-dupe-keys
  Line 1200:3:  Duplicate key 'getCaviInstallati'           no-dupe-keys
  Line 1249:9:  Expected an error object to be thrown       no-throw-literal
  Line 1279:9:  Expected an error object to be thrown       no-throw-literal
  Line 1332:9:  Expected an error object to be thrown       no-throw-literal
  Line 1374:9:  Expected an error object to be thrown       no-throw-literal

src\services\certificazioneService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\comandeService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\parcoCaviService.js
  Line 1:8:    'axios' is defined but never used              no-unused-vars
  Line 5:7:    'API_URL' is assigned a value but never used   no-unused-vars
  Line 83:13:  'sentData' is assigned a value but never used  no-unused-vars
  Line 109:9:  Expected an error object to be thrown          no-throw-literal
  Line 123:9:  Expected an error object to be thrown          no-throw-literal
  Line 127:9:  Expected an error object to be thrown          no-throw-literal
  Line 212:9:  Expected an error object to be thrown          no-throw-literal
  Line 226:9:  Expected an error object to be thrown          no-throw-literal
  Line 230:9:  Expected an error object to be thrown          no-throw-literal
  Line 271:9:  Expected an error object to be thrown          no-throw-literal
  Line 280:9:  Expected an error object to be thrown          no-throw-literal
  Line 284:9:  Expected an error object to be thrown          no-throw-literal
  Line 320:9:  Expected an error object to be thrown          no-throw-literal
  Line 324:9:  Expected an error object to be thrown          no-throw-literal
  Line 360:9:  Expected an error object to be thrown          no-throw-literal
  Line 369:9:  Expected an error object to be thrown          no-throw-literal
  Line 373:9:  Expected an error object to be thrown          no-throw-literal
  Line 450:9:  Expected an error object to be thrown          no-throw-literal
  Line 459:9:  Expected an error object to be thrown          no-throw-literal
  Line 463:9:  Expected an error object to be thrown          no-throw-literal

src\services\reportService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\userService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

[1m[33mWARNING[39m[22m in [1m[eslint] 
src\components\TopNavbar.js
  Line 13:3:   'Avatar' is defined but never used                         no-unused-vars
  Line 20:25:  'AdminIcon' is defined but never used                      no-unused-vars
  Line 21:19:  'ConstructionIcon' is defined but never used               no-unused-vars
  Line 22:12:  'CableIcon' is defined but never used                      no-unused-vars
  Line 23:18:  'ReportIcon' is defined but never used                     no-unused-vars
  Line 56:10:  'homeAnchorEl' is assigned a value but never used          no-unused-vars
  Line 57:10:  'adminAnchorEl' is assigned a value but never used         no-unused-vars
  Line 58:10:  'cantieriAnchorEl' is assigned a value but never used      no-unused-vars
  Line 59:10:  'caviAnchorEl' is assigned a value but never used          no-unused-vars
  Line 68:9:   'selectedCantiereName' is assigned a value but never used  no-unused-vars

src\components\admin\UserExpirationChecker.js
  Line 11:10:  'lastCheck' is assigned a value but never used  no-unused-vars

src\components\cavi\CavoForm.js
  Line 6:3:   'Stack' is defined but never used    no-unused-vars
  Line 11:3:  'Divider' is defined but never used  no-unused-vars

src\components\cavi\CertificazioneCavi.js
  Line 8:3:    'Card' is defined but never used                                                                                       no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                                no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                               no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                             no-unused-vars
  Line 37:10:  'AddIcon' is defined but never used                                                                                    no-unused-vars
  Line 38:11:  'EditIcon' is defined but never used                                                                                   no-unused-vars
  Line 45:15:  'ViewListIcon' is defined but never used                                                                               no-unused-vars
  Line 46:12:  'BuildIcon' is defined but never used                                                                                  no-unused-vars
  Line 122:6:  React Hook useEffect has a missing dependency: 'loadCertificazioni'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\cavi\CollegamentiCavo.js
  Line 8:3:   'List' is defined but never used                                                                             no-unused-vars
  Line 9:3:   'ListItem' is defined but never used                                                                         no-unused-vars
  Line 10:3:  'ListItemText' is defined but never used                                                                     no-unused-vars
  Line 46:6:  React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\cavi\ExcelPopup.js
  Line 10:3:   'TextField' is defined but never used          no-unused-vars
  Line 14:3:   'Divider' is defined but never used            no-unused-vars
  Line 27:10:  'filePath' is assigned a value but never used  no-unused-vars

src\components\cavi\GestioneComande.js
  Line 8:3:    'Card' is defined but never used                                                                                no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                         no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                         no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                        no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                      no-unused-vars
  Line 25:3:   'Divider' is defined but never used                                                                             no-unused-vars
  Line 37:10:  'AddIcon' is defined but never used                                                                             no-unused-vars
  Line 41:13:  'SearchIcon' is defined but never used                                                                          no-unused-vars
  Line 43:15:  'ViewListIcon' is defined but never used                                                                        no-unused-vars
  Line 44:17:  'AssignmentIcon' is defined but never used                                                                      no-unused-vars
  Line 98:6:   React Hook useEffect has a missing dependency: 'loadComande'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 101:9:  'handleOptionSelect' is assigned a value but never used                                                         no-unused-vars

src\components\cavi\IncompatibleReelDialog.js
  Line 17:3:  'Alert' is defined but never used  no-unused-vars

src\components\cavi\InserisciMetriForm.js
  Line 17:3:     'FormHelperText' is defined but never used                                                                     no-unused-vars
  Line 34:3:     'ListItemText' is defined but never used                                                                       no-unused-vars
  Line 35:3:     'ListItemSecondaryAction' is defined but never used                                                            no-unused-vars
  Line 39:11:    'SaveIcon' is defined but never used                                                                           no-unused-vars
  Line 51:3:     'CABLE_STATES' is defined but never used                                                                       no-unused-vars
  Line 52:3:     'REEL_STATES' is defined but never used                                                                        no-unused-vars
  Line 54:3:     'determineReelState' is defined but never used                                                                 no-unused-vars
  Line 55:3:     'canModifyCable' is defined but never used                                                                     no-unused-vars
  Line 62:10:    'redirectToVisualizzaCavi' is defined but never used                                                           no-unused-vars
  Line 105:10:   'incompatibleReel' is assigned a value but never used                                                          no-unused-vars
  Line 105:28:   'setIncompatibleReel' is assigned a value but never used                                                       no-unused-vars
  Line 145:6:    React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 701:9:    'handleBack' is assigned a value but never used                                                                no-unused-vars
  Line 1311:11:  'buildFullBobinaId' is assigned a value but never used                                                         no-unused-vars
  Line 1316:11:  'hasSufficientMeters' is assigned a value but never used                                                       no-unused-vars
  Line 1883:9:   'getStepContent' is assigned a value but never used                                                            no-unused-vars

src\components\cavi\MetriPosatiSemplificatoForm.js
  Line 33:3:   'CABLE_STATES' is defined but never used                                                                                        no-unused-vars
  Line 34:3:   'REEL_STATES' is defined but never used                                                                                         no-unused-vars
  Line 35:3:   'determineCableState' is defined but never used                                                                                 no-unused-vars
  Line 36:3:   'determineReelState' is defined but never used                                                                                  no-unused-vars
  Line 37:3:   'canModifyCable' is defined but never used                                                                                      no-unused-vars
  Line 41:3:   'getReelStateColor' is defined but never used                                                                                   no-unused-vars
  Line 43:10:  'redirectToVisualizzaCavi' is defined but never used                                                                            no-unused-vars
  Line 69:10:  'loading' is assigned a value but never used                                                                                    no-unused-vars
  Line 69:19:  'setLoading' is assigned a value but never used                                                                                 no-unused-vars
  Line 88:6:   React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 448:6:  React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array       react-hooks/exhaustive-deps

src\components\cavi\ModificaBobinaForm.js
  Line 9:3:     'FormControl' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 10:3:    'InputLabel' is defined but never used                                                                                                                                                                                         no-unused-vars
  Line 11:3:    'Select' is defined but never used                                                                                                                                                                                             no-unused-vars
  Line 12:3:    'MenuItem' is defined but never used                                                                                                                                                                                           no-unused-vars
  Line 33:3:    'ListItemText' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 35:3:    'ListItemSecondaryAction' is defined but never used                                                                                                                                                                            no-unused-vars
  Line 42:14:   'WarningIcon' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 52:3:    'CABLE_STATES' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 53:3:    'REEL_STATES' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 54:3:    'determineCableState' is defined but never used                                                                                                                                                                                no-unused-vars
  Line 55:3:    'determineReelState' is defined but never used                                                                                                                                                                                 no-unused-vars
  Line 56:3:    'canModifyCable' is defined but never used                                                                                                                                                                                     no-unused-vars
  Line 57:3:    'isCableSpare' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 58:3:    'isCableInstalled' is defined but never used                                                                                                                                                                                   no-unused-vars
  Line 59:3:    'getCableStateColor' is defined but never used                                                                                                                                                                                 no-unused-vars
  Line 72:9:    'navigate' is assigned a value but never used                                                                                                                                                                                  no-unused-vars
  Line 79:10:   'searchResults' is assigned a value but never used                                                                                                                                                                             no-unused-vars
  Line 79:25:   'setSearchResults' is assigned a value but never used                                                                                                                                                                          no-unused-vars
  Line 80:10:   'showSearchResults' is assigned a value but never used                                                                                                                                                                         no-unused-vars
  Line 85:10:   'compatibleBobine' is assigned a value but never used                                                                                                                                                                          no-unused-vars
  Line 105:6:   React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array                                                                                                                    react-hooks/exhaustive-deps
  Line 112:6:   React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array                                                                                                                  react-hooks/exhaustive-deps
  Line 127:6:   React Hook useEffect has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 283:13:  'bobina' is assigned a value but never used                                                                                                                                                                                    no-unused-vars

src\components\cavi\ParcoCavi.js
  Line 8:3:    'Card' is defined but never used                                                                                                                            no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                                                     no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                                                                     no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                                                                    no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                                                                  no-unused-vars
  Line 25:3:   'Divider' is defined but never used                                                                                                                         no-unused-vars
  Line 29:3:   'IconButton' is defined but never used                                                                                                                      no-unused-vars
  Line 39:11:  'EditIcon' is defined but never used                                                                                                                        no-unused-vars
  Line 43:15:  'ViewListIcon' is defined but never used                                                                                                                    no-unused-vars
  Line 44:14:  'WarningIcon' is defined but never used                                                                                                                     no-unused-vars
  Line 50:69:  'isEmpty' is defined but never used                                                                                                                         no-unused-vars
  Line 79:10:  'isFirstInsertion' is assigned a value but never used                                                                                                       no-unused-vars
  Line 145:6:  React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 689:9:  'renderBobineCards' is assigned a value but never used                                                                                                      no-unused-vars

src\components\cavi\PosaCaviCollegamenti.js
  Line 20:3:   'InputLabel' is defined but never used             no-unused-vars
  Line 21:3:   'Select' is defined but never used                 no-unused-vars
  Line 22:3:   'MenuItem' is defined but never used               no-unused-vars
  Line 23:3:   'Grid' is defined but never used                   no-unused-vars
  Line 26:3:   'FormHelperText' is defined but never used         no-unused-vars
  Line 69:10:  'formWarnings' is assigned a value but never used  no-unused-vars
  Line 466:9:  Unreachable code                                   no-unreachable

src\components\cavi\QuickAddCablesDialog.js
  Line 12:3:    'FormControlLabel' is defined but never used                                                                 no-unused-vars
  Line 27:10:   'AddIcon' is defined but never used                                                                          no-unused-vars
  Line 30:11:   'InfoIcon' is defined but never used                                                                         no-unused-vars
  Line 34:10:   'determineCableState' is defined but never used                                                              no-unused-vars
  Line 49:10:   'loading' is assigned a value but never used                                                                 no-unused-vars
  Line 49:19:   'setLoading' is assigned a value but never used                                                              no-unused-vars
  Line 64:6:    React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 270:17:  'result' is assigned a value but never used                                                                  no-unused-vars

src\components\cavi\ReportCavi.js
  Line 7:3:    'Grid' is defined but never used                         no-unused-vars
  Line 20:3:   'Divider' is defined but never used                      no-unused-vars
  Line 21:3:   'List' is defined but never used                         no-unused-vars
  Line 22:3:   'ListItem' is defined but never used                     no-unused-vars
  Line 23:3:   'ListItemText' is defined but never used                 no-unused-vars
  Line 24:3:   'ListItemIcon' is defined but never used                 no-unused-vars
  Line 25:3:   'ListItemButton' is defined but never used               no-unused-vars
  Line 29:15:  'BarChartIcon' is defined but never used                 no-unused-vars
  Line 34:16:  'DateRangeIcon' is defined but never used                no-unused-vars
  Line 53:9:   'handleOptionSelect' is assigned a value but never used  no-unused-vars

src\components\cavi\SelezionaCavoForm.js
  Line 4:3:  'TextField' is defined but never used  no-unused-vars

src\components\certificazioni\CertificazioneForm.jsx
  Line 54:6:  React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\certificazioni\CertificazioniList.jsx
  Line 25:13:  'DownloadIcon' is defined but never used  no-unused-vars

src\components\charts\BoqChart.js
  Line 14:3:  'ComposedChart' is defined but never used  no-unused-vars
  Line 15:3:  'Line' is defined but never used           no-unused-vars
  Line 16:3:  'LineChart' is defined but never used      no-unused-vars

src\components\charts\CaviStatoChart.js
  Line 14:3:  'ComposedChart' is defined but never used  no-unused-vars
  Line 15:3:  'Line' is defined but never used           no-unused-vars

src\components\charts\TimelineChart.js
  Line 3:3:  'LineChart' is defined but never used  no-unused-vars

src\components\common\ExcelLikeFilter.js
  Line 16:3:  'MenuItem' is defined but never used    no-unused-vars
  Line 17:3:  'Select' is defined but never used      no-unused-vars
  Line 19:3:  'InputLabel' is defined but never used  no-unused-vars

src\pages\CaviPage.js
  Line 1:8:  'React' is defined but never used  no-unused-vars

src\pages\CertificazioniPageDebug.jsx
  Line 49:19:  'useAuth' is assigned a value but never used  no-unused-vars

src\pages\Dashboard.js
  Line 38:8:   'RedirectToCaviVisualizza' is defined but never used  no-unused-vars
  Line 44:11:  'user' is assigned a value but never used             no-unused-vars

src\pages\HomePage.js
  Line 2:27:  'Grid' is defined but never used                 no-unused-vars
  Line 2:33:  'Card' is defined but never used                 no-unused-vars
  Line 2:39:  'CardContent' is defined but never used          no-unused-vars
  Line 2:52:  'CardActionArea' is defined but never used       no-unused-vars
  Line 2:68:  'Avatar' is defined but never used               no-unused-vars
  Line 5:25:  'AdminIcon' is defined but never used            no-unused-vars
  Line 6:19:  'ConstructionIcon' is defined but never used     no-unused-vars
  Line 7:12:  'CableIcon' is defined but never used            no-unused-vars
  Line 8:18:  'ReportIcon' is defined but never used           no-unused-vars
  Line 43:9:  'navigateTo' is assigned a value but never used  no-unused-vars

src\pages\cantieri\CantierePage.js
  Line 24:11:  'isImpersonating' is assigned a value but never used                                                               no-unused-vars
  Line 53:6:   React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\pages\cavi\CertificazioneCaviPage.js
  Line 5:3:    'Paper' is defined but never used                     no-unused-vars
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars
  Line 28:9:   'cantiereName' is assigned a value but never used     no-unused-vars

src\pages\cavi\GestioneComandeePage.js
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars

src\pages\cavi\ParcoCaviPage.js
  Line 9:3:    'Card' is defined but never used                      no-unused-vars
  Line 10:3:   'CardContent' is defined but never used               no-unused-vars
  Line 11:3:   'CardActions' is defined but never used               no-unused-vars
  Line 12:3:   'Grid' is defined but never used                      no-unused-vars
  Line 13:3:   'Divider' is defined but never used                   no-unused-vars
  Line 18:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 19:15:  'ViewListIcon' is defined but never used              no-unused-vars
  Line 20:10:  'AddIcon' is defined but never used                   no-unused-vars
  Line 21:11:  'EditIcon' is defined but never used                  no-unused-vars
  Line 22:13:  'DeleteIcon' is defined but never used                no-unused-vars
  Line 23:14:  'HistoryIcon' is defined but never used               no-unused-vars
  Line 28:8:   'ParcoCavi' is defined but never used                 no-unused-vars
  Line 31:11:  'isImpersonating' is assigned a value but never used  no-unused-vars
  Line 51:9:   'handleSuccess' is assigned a value but never used    no-unused-vars
  Line 56:9:   'handleError' is assigned a value but never used      no-unused-vars

src\pages\cavi\ReportCaviPage.js
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars

src\pages\cavi\ReportCaviPageNew.js
  Line 16:3:   'Tooltip' is defined but never used                       no-unused-vars
  Line 35:15:  'PieChartIcon' is defined but never used                  no-unused-vars
  Line 42:16:  'DateRangeIcon' is defined but never used                 no-unused-vars
  Line 64:11:  'user' is assigned a value but never used                 no-unused-vars
  Line 161:9:  'reportTypes' is assigned a value but never used          no-unused-vars
  Line 279:9:  'handleReportSelect' is assigned a value but never used   no-unused-vars
  Line 321:9:  'renderReportContent' is assigned a value but never used  no-unused-vars

src\pages\cavi\TestCaviPage.js
  Line 1:27:  'useEffect' is defined but never used  no-unused-vars

src\pages\cavi\VisualizzaCaviPage.js
  Line 8:3:    'Card' is defined but never used                                                                                       no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                no-unused-vars
  Line 11:3:   'IconButton' is defined but never used                                                                                 no-unused-vars
  Line 21:8:   'InfoIcon' is defined but never used                                                                                   no-unused-vars
  Line 28:8:   'CavoForm' is defined but never used                                                                                   no-unused-vars
  Line 34:11:  'isImpersonating' is assigned a value but never used                                                                   no-unused-vars
  Line 36:9:   'navigate' is assigned a value but never used                                                                          no-unused-vars
  Line 38:10:  'cantiereName' is assigned a value but never used                                                                      no-unused-vars
  Line 76:10:  'statiInstallazione' is assigned a value but never used                                                                no-unused-vars
  Line 77:10:  'tipologieCavi' is assigned a value but never used                                                                     no-unused-vars
  Line 484:6:  React Hook useEffect has missing dependencies: '[1m[31merror[39m[22m[1m' and 'user'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 489:9:  'handleOpenDetails' is assigned a value but never used                                                                 no-unused-vars
  Line 583:9:  'renderStatsPanel' is assigned a value but never used                                                                  no-unused-vars

src\pages\cavi\parco\EliminaBobinaPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\ModificaBobinaPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\StoricoUtilizzoPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\posa\CollegamentiPage.js
  Line 5:3:    'Paper' is defined but never used                          no-unused-vars
  Line 6:3:    'Button' is defined but never used                         no-unused-vars
  Line 14:11:  'HomeIcon' is defined but never used                       no-unused-vars
  Line 23:11:  'isImpersonating' is assigned a value but never used       no-unused-vars
  Line 30:9:   'cantiereName' is assigned a value but never used          no-unused-vars
  Line 33:9:   'handleBackToCantieri' is assigned a value but never used  no-unused-vars
  Line 38:9:   'handleBackToAdmin' is assigned a value but never used     no-unused-vars

src\pages\cavi\posa\MetriPosatiSemplificatoPage.js
  Line 27:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\posa\ModificaBobinaPage.js
  Line 5:3:    'Paper' is defined but never used                          no-unused-vars
  Line 6:3:    'Button' is defined but never used                         no-unused-vars
  Line 14:11:  'HomeIcon' is defined but never used                       no-unused-vars
  Line 23:11:  'isImpersonating' is assigned a value but never used       no-unused-vars
  Line 30:9:   'cantiereName' is assigned a value but never used          no-unused-vars
  Line 33:9:   'handleBackToCantieri' is assigned a value but never used  no-unused-vars
  Line 38:9:   'handleBackToAdmin' is assigned a value but never used     no-unused-vars

src\pages\cavi\posa\ModificaCavoPage.js
  Line 20:11:  'isImpersonating' is assigned a value but never used    no-unused-vars
  Line 27:9:   'cantiereName' is assigned a value but never used       no-unused-vars
  Line 35:9:   'handleBackToAdmin' is assigned a value but never used  no-unused-vars

src\services\adminService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\authService.js
  Line 78:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown  no-throw-literal
  Line 80:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown  no-throw-literal
  Line 86:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown  no-throw-literal
  Line 89:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown  no-throw-literal

src\services\cantieriService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\caviService.js
  Line 260:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 274:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 278:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 333:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 435:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 451:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 668:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 677:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 681:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 755:17:  'token' is assigned a value but never used  no-unused-vars
  Line 775:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 794:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 801:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 810:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 817:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 885:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 955:3:   [1m[31mDuplicate[39m[22m[1m key 'updateCavoForCompatibility'  no-dupe-keys
  Line 1200:3:  [1m[31mDuplicate[39m[22m[1m key 'getCaviInstallati'           no-dupe-keys
  Line 1249:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 1279:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 1332:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 1374:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal

src\services\certificazioneService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\comandeService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\parcoCaviService.js
  Line 1:8:    'axios' is defined but never used              no-unused-vars
  Line 5:7:    'API_URL' is assigned a value but never used   no-unused-vars
  Line 83:13:  'sentData' is assigned a value but never used  no-unused-vars
  Line 109:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 123:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 127:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 212:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 226:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 230:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 271:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 280:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 284:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 320:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 324:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 360:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 369:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 373:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 450:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 459:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 463:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal

src\services\reportService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\userService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

[39m[22m

webpack compiled with [1m[33m1 warning[39m[22m
Compiling...
Compiled with warnings.

[eslint] 
src\components\TopNavbar.js
  Line 13:3:   'Avatar' is defined but never used                         no-unused-vars
  Line 20:25:  'AdminIcon' is defined but never used                      no-unused-vars
  Line 21:19:  'ConstructionIcon' is defined but never used               no-unused-vars
  Line 22:12:  'CableIcon' is defined but never used                      no-unused-vars
  Line 23:18:  'ReportIcon' is defined but never used                     no-unused-vars
  Line 56:10:  'homeAnchorEl' is assigned a value but never used          no-unused-vars
  Line 57:10:  'adminAnchorEl' is assigned a value but never used         no-unused-vars
  Line 58:10:  'cantieriAnchorEl' is assigned a value but never used      no-unused-vars
  Line 59:10:  'caviAnchorEl' is assigned a value but never used          no-unused-vars
  Line 68:9:   'selectedCantiereName' is assigned a value but never used  no-unused-vars

src\components\admin\UserExpirationChecker.js
  Line 11:10:  'lastCheck' is assigned a value but never used  no-unused-vars

src\components\cavi\CavoForm.js
  Line 6:3:   'Stack' is defined but never used    no-unused-vars
  Line 11:3:  'Divider' is defined but never used  no-unused-vars

src\components\cavi\CertificazioneCavi.js
  Line 8:3:    'Card' is defined but never used                                                                                       no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                                no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                               no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                             no-unused-vars
  Line 37:10:  'AddIcon' is defined but never used                                                                                    no-unused-vars
  Line 38:11:  'EditIcon' is defined but never used                                                                                   no-unused-vars
  Line 45:15:  'ViewListIcon' is defined but never used                                                                               no-unused-vars
  Line 46:12:  'BuildIcon' is defined but never used                                                                                  no-unused-vars
  Line 122:6:  React Hook useEffect has a missing dependency: 'loadCertificazioni'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\cavi\CollegamentiCavo.js
  Line 8:3:   'List' is defined but never used                                                                             no-unused-vars
  Line 9:3:   'ListItem' is defined but never used                                                                         no-unused-vars
  Line 10:3:  'ListItemText' is defined but never used                                                                     no-unused-vars
  Line 46:6:  React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\cavi\ExcelPopup.js
  Line 10:3:   'TextField' is defined but never used          no-unused-vars
  Line 14:3:   'Divider' is defined but never used            no-unused-vars
  Line 27:10:  'filePath' is assigned a value but never used  no-unused-vars

src\components\cavi\GestioneComande.js
  Line 8:3:    'Card' is defined but never used                                                                                no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                         no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                         no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                        no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                      no-unused-vars
  Line 25:3:   'Divider' is defined but never used                                                                             no-unused-vars
  Line 37:10:  'AddIcon' is defined but never used                                                                             no-unused-vars
  Line 41:13:  'SearchIcon' is defined but never used                                                                          no-unused-vars
  Line 43:15:  'ViewListIcon' is defined but never used                                                                        no-unused-vars
  Line 44:17:  'AssignmentIcon' is defined but never used                                                                      no-unused-vars
  Line 98:6:   React Hook useEffect has a missing dependency: 'loadComande'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 101:9:  'handleOptionSelect' is assigned a value but never used                                                         no-unused-vars

src\components\cavi\IncompatibleReelDialog.js
  Line 17:3:  'Alert' is defined but never used  no-unused-vars

src\components\cavi\InserisciMetriForm.js
  Line 17:3:     'FormHelperText' is defined but never used                                                                     no-unused-vars
  Line 34:3:     'ListItemText' is defined but never used                                                                       no-unused-vars
  Line 35:3:     'ListItemSecondaryAction' is defined but never used                                                            no-unused-vars
  Line 39:11:    'SaveIcon' is defined but never used                                                                           no-unused-vars
  Line 51:3:     'CABLE_STATES' is defined but never used                                                                       no-unused-vars
  Line 52:3:     'REEL_STATES' is defined but never used                                                                        no-unused-vars
  Line 54:3:     'determineReelState' is defined but never used                                                                 no-unused-vars
  Line 55:3:     'canModifyCable' is defined but never used                                                                     no-unused-vars
  Line 62:10:    'redirectToVisualizzaCavi' is defined but never used                                                           no-unused-vars
  Line 105:10:   'incompatibleReel' is assigned a value but never used                                                          no-unused-vars
  Line 105:28:   'setIncompatibleReel' is assigned a value but never used                                                       no-unused-vars
  Line 145:6:    React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 701:9:    'handleBack' is assigned a value but never used                                                                no-unused-vars
  Line 1311:11:  'buildFullBobinaId' is assigned a value but never used                                                         no-unused-vars
  Line 1316:11:  'hasSufficientMeters' is assigned a value but never used                                                       no-unused-vars
  Line 1883:9:   'getStepContent' is assigned a value but never used                                                            no-unused-vars

src\components\cavi\MetriPosatiSemplificatoForm.js
  Line 33:3:   'CABLE_STATES' is defined but never used                                                                                        no-unused-vars
  Line 34:3:   'REEL_STATES' is defined but never used                                                                                         no-unused-vars
  Line 35:3:   'determineCableState' is defined but never used                                                                                 no-unused-vars
  Line 36:3:   'determineReelState' is defined but never used                                                                                  no-unused-vars
  Line 37:3:   'canModifyCable' is defined but never used                                                                                      no-unused-vars
  Line 41:3:   'getReelStateColor' is defined but never used                                                                                   no-unused-vars
  Line 43:10:  'redirectToVisualizzaCavi' is defined but never used                                                                            no-unused-vars
  Line 69:10:  'loading' is assigned a value but never used                                                                                    no-unused-vars
  Line 69:19:  'setLoading' is assigned a value but never used                                                                                 no-unused-vars
  Line 88:6:   React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 448:6:  React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array       react-hooks/exhaustive-deps

src\components\cavi\ModificaBobinaForm.js
  Line 9:3:     'FormControl' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 10:3:    'InputLabel' is defined but never used                                                                                                                                                                                         no-unused-vars
  Line 11:3:    'Select' is defined but never used                                                                                                                                                                                             no-unused-vars
  Line 12:3:    'MenuItem' is defined but never used                                                                                                                                                                                           no-unused-vars
  Line 33:3:    'ListItemText' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 35:3:    'ListItemSecondaryAction' is defined but never used                                                                                                                                                                            no-unused-vars
  Line 42:14:   'WarningIcon' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 52:3:    'CABLE_STATES' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 53:3:    'REEL_STATES' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 54:3:    'determineCableState' is defined but never used                                                                                                                                                                                no-unused-vars
  Line 55:3:    'determineReelState' is defined but never used                                                                                                                                                                                 no-unused-vars
  Line 56:3:    'canModifyCable' is defined but never used                                                                                                                                                                                     no-unused-vars
  Line 57:3:    'isCableSpare' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 58:3:    'isCableInstalled' is defined but never used                                                                                                                                                                                   no-unused-vars
  Line 59:3:    'getCableStateColor' is defined but never used                                                                                                                                                                                 no-unused-vars
  Line 72:9:    'navigate' is assigned a value but never used                                                                                                                                                                                  no-unused-vars
  Line 79:10:   'searchResults' is assigned a value but never used                                                                                                                                                                             no-unused-vars
  Line 79:25:   'setSearchResults' is assigned a value but never used                                                                                                                                                                          no-unused-vars
  Line 80:10:   'showSearchResults' is assigned a value but never used                                                                                                                                                                         no-unused-vars
  Line 85:10:   'compatibleBobine' is assigned a value but never used                                                                                                                                                                          no-unused-vars
  Line 105:6:   React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array                                                                                                                    react-hooks/exhaustive-deps
  Line 112:6:   React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array                                                                                                                  react-hooks/exhaustive-deps
  Line 127:6:   React Hook useEffect has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 283:13:  'bobina' is assigned a value but never used                                                                                                                                                                                    no-unused-vars

src\components\cavi\ParcoCavi.js
  Line 8:3:    'Card' is defined but never used                                                                                                                            no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                                                     no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                                                                     no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                                                                    no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                                                                  no-unused-vars
  Line 25:3:   'Divider' is defined but never used                                                                                                                         no-unused-vars
  Line 29:3:   'IconButton' is defined but never used                                                                                                                      no-unused-vars
  Line 39:11:  'EditIcon' is defined but never used                                                                                                                        no-unused-vars
  Line 43:15:  'ViewListIcon' is defined but never used                                                                                                                    no-unused-vars
  Line 44:14:  'WarningIcon' is defined but never used                                                                                                                     no-unused-vars
  Line 50:69:  'isEmpty' is defined but never used                                                                                                                         no-unused-vars
  Line 79:10:  'isFirstInsertion' is assigned a value but never used                                                                                                       no-unused-vars
  Line 145:6:  React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 689:9:  'renderBobineCards' is assigned a value but never used                                                                                                      no-unused-vars

src\components\cavi\PosaCaviCollegamenti.js
  Line 20:3:   'InputLabel' is defined but never used             no-unused-vars
  Line 21:3:   'Select' is defined but never used                 no-unused-vars
  Line 22:3:   'MenuItem' is defined but never used               no-unused-vars
  Line 23:3:   'Grid' is defined but never used                   no-unused-vars
  Line 26:3:   'FormHelperText' is defined but never used         no-unused-vars
  Line 69:10:  'formWarnings' is assigned a value but never used  no-unused-vars
  Line 466:9:  Unreachable code                                   no-unreachable

src\components\cavi\QuickAddCablesDialog.js
  Line 12:3:    'FormControlLabel' is defined but never used                                                                 no-unused-vars
  Line 27:10:   'AddIcon' is defined but never used                                                                          no-unused-vars
  Line 30:11:   'InfoIcon' is defined but never used                                                                         no-unused-vars
  Line 34:10:   'determineCableState' is defined but never used                                                              no-unused-vars
  Line 49:10:   'loading' is assigned a value but never used                                                                 no-unused-vars
  Line 49:19:   'setLoading' is assigned a value but never used                                                              no-unused-vars
  Line 64:6:    React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 270:17:  'result' is assigned a value but never used                                                                  no-unused-vars

src\components\cavi\ReportCavi.js
  Line 7:3:    'Grid' is defined but never used                         no-unused-vars
  Line 20:3:   'Divider' is defined but never used                      no-unused-vars
  Line 21:3:   'List' is defined but never used                         no-unused-vars
  Line 22:3:   'ListItem' is defined but never used                     no-unused-vars
  Line 23:3:   'ListItemText' is defined but never used                 no-unused-vars
  Line 24:3:   'ListItemIcon' is defined but never used                 no-unused-vars
  Line 25:3:   'ListItemButton' is defined but never used               no-unused-vars
  Line 29:15:  'BarChartIcon' is defined but never used                 no-unused-vars
  Line 34:16:  'DateRangeIcon' is defined but never used                no-unused-vars
  Line 53:9:   'handleOptionSelect' is assigned a value but never used  no-unused-vars

src\components\cavi\SelezionaCavoForm.js
  Line 4:3:  'TextField' is defined but never used  no-unused-vars

src\components\certificazioni\CertificazioneForm.jsx
  Line 54:6:  React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\certificazioni\CertificazioniList.jsx
  Line 25:13:  'DownloadIcon' is defined but never used  no-unused-vars

src\components\charts\BoqChart.js
  Line 14:3:  'ComposedChart' is defined but never used  no-unused-vars
  Line 15:3:  'Line' is defined but never used           no-unused-vars
  Line 16:3:  'LineChart' is defined but never used      no-unused-vars

src\components\charts\CaviStatoChart.js
  Line 14:3:  'ComposedChart' is defined but never used  no-unused-vars
  Line 15:3:  'Line' is defined but never used           no-unused-vars

src\components\charts\TimelineChart.js
  Line 3:3:  'LineChart' is defined but never used  no-unused-vars

src\components\common\ExcelLikeFilter.js
  Line 16:3:  'MenuItem' is defined but never used    no-unused-vars
  Line 17:3:  'Select' is defined but never used      no-unused-vars
  Line 19:3:  'InputLabel' is defined but never used  no-unused-vars

src\pages\CaviPage.js
  Line 1:8:  'React' is defined but never used  no-unused-vars

src\pages\CertificazioniPageDebug.jsx
  Line 49:19:  'useAuth' is assigned a value but never used  no-unused-vars

src\pages\Dashboard.js
  Line 38:8:   'RedirectToCaviVisualizza' is defined but never used  no-unused-vars
  Line 44:11:  'user' is assigned a value but never used             no-unused-vars

src\pages\HomePage.js
  Line 2:27:  'Grid' is defined but never used                 no-unused-vars
  Line 2:33:  'Card' is defined but never used                 no-unused-vars
  Line 2:39:  'CardContent' is defined but never used          no-unused-vars
  Line 2:52:  'CardActionArea' is defined but never used       no-unused-vars
  Line 2:68:  'Avatar' is defined but never used               no-unused-vars
  Line 5:25:  'AdminIcon' is defined but never used            no-unused-vars
  Line 6:19:  'ConstructionIcon' is defined but never used     no-unused-vars
  Line 7:12:  'CableIcon' is defined but never used            no-unused-vars
  Line 8:18:  'ReportIcon' is defined but never used           no-unused-vars
  Line 43:9:  'navigateTo' is assigned a value but never used  no-unused-vars

src\pages\cantieri\CantierePage.js
  Line 24:11:  'isImpersonating' is assigned a value but never used                                                               no-unused-vars
  Line 53:6:   React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\pages\cavi\CertificazioneCaviPage.js
  Line 5:3:    'Paper' is defined but never used                     no-unused-vars
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars
  Line 28:9:   'cantiereName' is assigned a value but never used     no-unused-vars

src\pages\cavi\GestioneComandeePage.js
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars

src\pages\cavi\ParcoCaviPage.js
  Line 9:3:    'Card' is defined but never used                      no-unused-vars
  Line 10:3:   'CardContent' is defined but never used               no-unused-vars
  Line 11:3:   'CardActions' is defined but never used               no-unused-vars
  Line 12:3:   'Grid' is defined but never used                      no-unused-vars
  Line 13:3:   'Divider' is defined but never used                   no-unused-vars
  Line 18:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 19:15:  'ViewListIcon' is defined but never used              no-unused-vars
  Line 20:10:  'AddIcon' is defined but never used                   no-unused-vars
  Line 21:11:  'EditIcon' is defined but never used                  no-unused-vars
  Line 22:13:  'DeleteIcon' is defined but never used                no-unused-vars
  Line 23:14:  'HistoryIcon' is defined but never used               no-unused-vars
  Line 28:8:   'ParcoCavi' is defined but never used                 no-unused-vars
  Line 31:11:  'isImpersonating' is assigned a value but never used  no-unused-vars
  Line 51:9:   'handleSuccess' is assigned a value but never used    no-unused-vars
  Line 56:9:   'handleError' is assigned a value but never used      no-unused-vars

src\pages\cavi\ReportCaviPage.js
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars

src\pages\cavi\ReportCaviPageNew.js
  Line 16:3:   'Tooltip' is defined but never used                       no-unused-vars
  Line 35:15:  'PieChartIcon' is defined but never used                  no-unused-vars
  Line 42:16:  'DateRangeIcon' is defined but never used                 no-unused-vars
  Line 64:11:  'user' is assigned a value but never used                 no-unused-vars
  Line 161:9:  'reportTypes' is assigned a value but never used          no-unused-vars
  Line 279:9:  'handleReportSelect' is assigned a value but never used   no-unused-vars
  Line 321:9:  'renderReportContent' is assigned a value but never used  no-unused-vars

src\pages\cavi\TestCaviPage.js
  Line 1:27:  'useEffect' is defined but never used  no-unused-vars

src\pages\cavi\VisualizzaCaviPage.js
  Line 8:3:    'Card' is defined but never used                                                                                       no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                no-unused-vars
  Line 11:3:   'IconButton' is defined but never used                                                                                 no-unused-vars
  Line 21:8:   'InfoIcon' is defined but never used                                                                                   no-unused-vars
  Line 28:8:   'CavoForm' is defined but never used                                                                                   no-unused-vars
  Line 34:11:  'isImpersonating' is assigned a value but never used                                                                   no-unused-vars
  Line 36:9:   'navigate' is assigned a value but never used                                                                          no-unused-vars
  Line 38:10:  'cantiereName' is assigned a value but never used                                                                      no-unused-vars
  Line 76:10:  'statiInstallazione' is assigned a value but never used                                                                no-unused-vars
  Line 77:10:  'tipologieCavi' is assigned a value but never used                                                                     no-unused-vars
  Line 484:6:  React Hook useEffect has missing dependencies: 'error' and 'user'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 489:9:  'handleOpenDetails' is assigned a value but never used                                                                 no-unused-vars
  Line 583:9:  'renderStatsPanel' is assigned a value but never used                                                                  no-unused-vars

src\pages\cavi\parco\EliminaBobinaPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\ModificaBobinaPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\StoricoUtilizzoPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\posa\CollegamentiPage.js
  Line 5:3:    'Paper' is defined but never used                          no-unused-vars
  Line 6:3:    'Button' is defined but never used                         no-unused-vars
  Line 14:11:  'HomeIcon' is defined but never used                       no-unused-vars
  Line 23:11:  'isImpersonating' is assigned a value but never used       no-unused-vars
  Line 30:9:   'cantiereName' is assigned a value but never used          no-unused-vars
  Line 33:9:   'handleBackToCantieri' is assigned a value but never used  no-unused-vars
  Line 38:9:   'handleBackToAdmin' is assigned a value but never used     no-unused-vars

src\pages\cavi\posa\MetriPosatiSemplificatoPage.js
  Line 27:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\posa\ModificaBobinaPage.js
  Line 5:3:    'Paper' is defined but never used                          no-unused-vars
  Line 6:3:    'Button' is defined but never used                         no-unused-vars
  Line 14:11:  'HomeIcon' is defined but never used                       no-unused-vars
  Line 23:11:  'isImpersonating' is assigned a value but never used       no-unused-vars
  Line 30:9:   'cantiereName' is assigned a value but never used          no-unused-vars
  Line 33:9:   'handleBackToCantieri' is assigned a value but never used  no-unused-vars
  Line 38:9:   'handleBackToAdmin' is assigned a value but never used     no-unused-vars

src\pages\cavi\posa\ModificaCavoPage.js
  Line 20:11:  'isImpersonating' is assigned a value but never used    no-unused-vars
  Line 27:9:   'cantiereName' is assigned a value but never used       no-unused-vars
  Line 35:9:   'handleBackToAdmin' is assigned a value but never used  no-unused-vars

src\services\adminService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\authService.js
  Line 78:11:  Expected an error object to be thrown  no-throw-literal
  Line 80:11:  Expected an error object to be thrown  no-throw-literal
  Line 86:9:   Expected an error object to be thrown  no-throw-literal
  Line 89:9:   Expected an error object to be thrown  no-throw-literal

src\services\cantieriService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\caviService.js
  Line 260:9:   Expected an error object to be thrown       no-throw-literal
  Line 274:9:   Expected an error object to be thrown       no-throw-literal
  Line 278:9:   Expected an error object to be thrown       no-throw-literal
  Line 333:11:  Expected an error object to be thrown       no-throw-literal
  Line 435:9:   Expected an error object to be thrown       no-throw-literal
  Line 451:9:   Expected an error object to be thrown       no-throw-literal
  Line 668:9:   Expected an error object to be thrown       no-throw-literal
  Line 677:9:   Expected an error object to be thrown       no-throw-literal
  Line 681:9:   Expected an error object to be thrown       no-throw-literal
  Line 755:17:  'token' is assigned a value but never used  no-unused-vars
  Line 775:9:   Expected an error object to be thrown       no-throw-literal
  Line 794:11:  Expected an error object to be thrown       no-throw-literal
  Line 801:9:   Expected an error object to be thrown       no-throw-literal
  Line 810:11:  Expected an error object to be thrown       no-throw-literal
  Line 817:9:   Expected an error object to be thrown       no-throw-literal
  Line 885:9:   Expected an error object to be thrown       no-throw-literal
  Line 955:3:   Duplicate key 'updateCavoForCompatibility'  no-dupe-keys
  Line 1200:3:  Duplicate key 'getCaviInstallati'           no-dupe-keys
  Line 1249:9:  Expected an error object to be thrown       no-throw-literal
  Line 1279:9:  Expected an error object to be thrown       no-throw-literal
  Line 1332:9:  Expected an error object to be thrown       no-throw-literal
  Line 1374:9:  Expected an error object to be thrown       no-throw-literal

src\services\certificazioneService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\comandeService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\parcoCaviService.js
  Line 1:8:    'axios' is defined but never used              no-unused-vars
  Line 5:7:    'API_URL' is assigned a value but never used   no-unused-vars
  Line 83:13:  'sentData' is assigned a value but never used  no-unused-vars
  Line 109:9:  Expected an error object to be thrown          no-throw-literal
  Line 123:9:  Expected an error object to be thrown          no-throw-literal
  Line 127:9:  Expected an error object to be thrown          no-throw-literal
  Line 212:9:  Expected an error object to be thrown          no-throw-literal
  Line 226:9:  Expected an error object to be thrown          no-throw-literal
  Line 230:9:  Expected an error object to be thrown          no-throw-literal
  Line 271:9:  Expected an error object to be thrown          no-throw-literal
  Line 280:9:  Expected an error object to be thrown          no-throw-literal
  Line 284:9:  Expected an error object to be thrown          no-throw-literal
  Line 320:9:  Expected an error object to be thrown          no-throw-literal
  Line 324:9:  Expected an error object to be thrown          no-throw-literal
  Line 360:9:  Expected an error object to be thrown          no-throw-literal
  Line 369:9:  Expected an error object to be thrown          no-throw-literal
  Line 373:9:  Expected an error object to be thrown          no-throw-literal
  Line 450:9:  Expected an error object to be thrown          no-throw-literal
  Line 459:9:  Expected an error object to be thrown          no-throw-literal
  Line 463:9:  Expected an error object to be thrown          no-throw-literal

src\services\reportService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\userService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

[1m[33mWARNING[39m[22m in [1m[eslint] 
src\components\TopNavbar.js
  Line 13:3:   'Avatar' is defined but never used                         no-unused-vars
  Line 20:25:  'AdminIcon' is defined but never used                      no-unused-vars
  Line 21:19:  'ConstructionIcon' is defined but never used               no-unused-vars
  Line 22:12:  'CableIcon' is defined but never used                      no-unused-vars
  Line 23:18:  'ReportIcon' is defined but never used                     no-unused-vars
  Line 56:10:  'homeAnchorEl' is assigned a value but never used          no-unused-vars
  Line 57:10:  'adminAnchorEl' is assigned a value but never used         no-unused-vars
  Line 58:10:  'cantieriAnchorEl' is assigned a value but never used      no-unused-vars
  Line 59:10:  'caviAnchorEl' is assigned a value but never used          no-unused-vars
  Line 68:9:   'selectedCantiereName' is assigned a value but never used  no-unused-vars

src\components\admin\UserExpirationChecker.js
  Line 11:10:  'lastCheck' is assigned a value but never used  no-unused-vars

src\components\cavi\CavoForm.js
  Line 6:3:   'Stack' is defined but never used    no-unused-vars
  Line 11:3:  'Divider' is defined but never used  no-unused-vars

src\components\cavi\CertificazioneCavi.js
  Line 8:3:    'Card' is defined but never used                                                                                       no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                                no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                               no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                             no-unused-vars
  Line 37:10:  'AddIcon' is defined but never used                                                                                    no-unused-vars
  Line 38:11:  'EditIcon' is defined but never used                                                                                   no-unused-vars
  Line 45:15:  'ViewListIcon' is defined but never used                                                                               no-unused-vars
  Line 46:12:  'BuildIcon' is defined but never used                                                                                  no-unused-vars
  Line 122:6:  React Hook useEffect has a missing dependency: 'loadCertificazioni'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\cavi\CollegamentiCavo.js
  Line 8:3:   'List' is defined but never used                                                                             no-unused-vars
  Line 9:3:   'ListItem' is defined but never used                                                                         no-unused-vars
  Line 10:3:  'ListItemText' is defined but never used                                                                     no-unused-vars
  Line 46:6:  React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\cavi\ExcelPopup.js
  Line 10:3:   'TextField' is defined but never used          no-unused-vars
  Line 14:3:   'Divider' is defined but never used            no-unused-vars
  Line 27:10:  'filePath' is assigned a value but never used  no-unused-vars

src\components\cavi\GestioneComande.js
  Line 8:3:    'Card' is defined but never used                                                                                no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                         no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                         no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                        no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                      no-unused-vars
  Line 25:3:   'Divider' is defined but never used                                                                             no-unused-vars
  Line 37:10:  'AddIcon' is defined but never used                                                                             no-unused-vars
  Line 41:13:  'SearchIcon' is defined but never used                                                                          no-unused-vars
  Line 43:15:  'ViewListIcon' is defined but never used                                                                        no-unused-vars
  Line 44:17:  'AssignmentIcon' is defined but never used                                                                      no-unused-vars
  Line 98:6:   React Hook useEffect has a missing dependency: 'loadComande'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 101:9:  'handleOptionSelect' is assigned a value but never used                                                         no-unused-vars

src\components\cavi\IncompatibleReelDialog.js
  Line 17:3:  'Alert' is defined but never used  no-unused-vars

src\components\cavi\InserisciMetriForm.js
  Line 17:3:     'FormHelperText' is defined but never used                                                                     no-unused-vars
  Line 34:3:     'ListItemText' is defined but never used                                                                       no-unused-vars
  Line 35:3:     'ListItemSecondaryAction' is defined but never used                                                            no-unused-vars
  Line 39:11:    'SaveIcon' is defined but never used                                                                           no-unused-vars
  Line 51:3:     'CABLE_STATES' is defined but never used                                                                       no-unused-vars
  Line 52:3:     'REEL_STATES' is defined but never used                                                                        no-unused-vars
  Line 54:3:     'determineReelState' is defined but never used                                                                 no-unused-vars
  Line 55:3:     'canModifyCable' is defined but never used                                                                     no-unused-vars
  Line 62:10:    'redirectToVisualizzaCavi' is defined but never used                                                           no-unused-vars
  Line 105:10:   'incompatibleReel' is assigned a value but never used                                                          no-unused-vars
  Line 105:28:   'setIncompatibleReel' is assigned a value but never used                                                       no-unused-vars
  Line 145:6:    React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 701:9:    'handleBack' is assigned a value but never used                                                                no-unused-vars
  Line 1311:11:  'buildFullBobinaId' is assigned a value but never used                                                         no-unused-vars
  Line 1316:11:  'hasSufficientMeters' is assigned a value but never used                                                       no-unused-vars
  Line 1883:9:   'getStepContent' is assigned a value but never used                                                            no-unused-vars

src\components\cavi\MetriPosatiSemplificatoForm.js
  Line 33:3:   'CABLE_STATES' is defined but never used                                                                                        no-unused-vars
  Line 34:3:   'REEL_STATES' is defined but never used                                                                                         no-unused-vars
  Line 35:3:   'determineCableState' is defined but never used                                                                                 no-unused-vars
  Line 36:3:   'determineReelState' is defined but never used                                                                                  no-unused-vars
  Line 37:3:   'canModifyCable' is defined but never used                                                                                      no-unused-vars
  Line 41:3:   'getReelStateColor' is defined but never used                                                                                   no-unused-vars
  Line 43:10:  'redirectToVisualizzaCavi' is defined but never used                                                                            no-unused-vars
  Line 69:10:  'loading' is assigned a value but never used                                                                                    no-unused-vars
  Line 69:19:  'setLoading' is assigned a value but never used                                                                                 no-unused-vars
  Line 88:6:   React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 448:6:  React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array       react-hooks/exhaustive-deps

src\components\cavi\ModificaBobinaForm.js
  Line 9:3:     'FormControl' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 10:3:    'InputLabel' is defined but never used                                                                                                                                                                                         no-unused-vars
  Line 11:3:    'Select' is defined but never used                                                                                                                                                                                             no-unused-vars
  Line 12:3:    'MenuItem' is defined but never used                                                                                                                                                                                           no-unused-vars
  Line 33:3:    'ListItemText' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 35:3:    'ListItemSecondaryAction' is defined but never used                                                                                                                                                                            no-unused-vars
  Line 42:14:   'WarningIcon' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 52:3:    'CABLE_STATES' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 53:3:    'REEL_STATES' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 54:3:    'determineCableState' is defined but never used                                                                                                                                                                                no-unused-vars
  Line 55:3:    'determineReelState' is defined but never used                                                                                                                                                                                 no-unused-vars
  Line 56:3:    'canModifyCable' is defined but never used                                                                                                                                                                                     no-unused-vars
  Line 57:3:    'isCableSpare' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 58:3:    'isCableInstalled' is defined but never used                                                                                                                                                                                   no-unused-vars
  Line 59:3:    'getCableStateColor' is defined but never used                                                                                                                                                                                 no-unused-vars
  Line 72:9:    'navigate' is assigned a value but never used                                                                                                                                                                                  no-unused-vars
  Line 79:10:   'searchResults' is assigned a value but never used                                                                                                                                                                             no-unused-vars
  Line 79:25:   'setSearchResults' is assigned a value but never used                                                                                                                                                                          no-unused-vars
  Line 80:10:   'showSearchResults' is assigned a value but never used                                                                                                                                                                         no-unused-vars
  Line 85:10:   'compatibleBobine' is assigned a value but never used                                                                                                                                                                          no-unused-vars
  Line 105:6:   React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array                                                                                                                    react-hooks/exhaustive-deps
  Line 112:6:   React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array                                                                                                                  react-hooks/exhaustive-deps
  Line 127:6:   React Hook useEffect has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 283:13:  'bobina' is assigned a value but never used                                                                                                                                                                                    no-unused-vars

src\components\cavi\ParcoCavi.js
  Line 8:3:    'Card' is defined but never used                                                                                                                            no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                                                     no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                                                                     no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                                                                    no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                                                                  no-unused-vars
  Line 25:3:   'Divider' is defined but never used                                                                                                                         no-unused-vars
  Line 29:3:   'IconButton' is defined but never used                                                                                                                      no-unused-vars
  Line 39:11:  'EditIcon' is defined but never used                                                                                                                        no-unused-vars
  Line 43:15:  'ViewListIcon' is defined but never used                                                                                                                    no-unused-vars
  Line 44:14:  'WarningIcon' is defined but never used                                                                                                                     no-unused-vars
  Line 50:69:  'isEmpty' is defined but never used                                                                                                                         no-unused-vars
  Line 79:10:  'isFirstInsertion' is assigned a value but never used                                                                                                       no-unused-vars
  Line 145:6:  React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 689:9:  'renderBobineCards' is assigned a value but never used                                                                                                      no-unused-vars

src\components\cavi\PosaCaviCollegamenti.js
  Line 20:3:   'InputLabel' is defined but never used             no-unused-vars
  Line 21:3:   'Select' is defined but never used                 no-unused-vars
  Line 22:3:   'MenuItem' is defined but never used               no-unused-vars
  Line 23:3:   'Grid' is defined but never used                   no-unused-vars
  Line 26:3:   'FormHelperText' is defined but never used         no-unused-vars
  Line 69:10:  'formWarnings' is assigned a value but never used  no-unused-vars
  Line 466:9:  Unreachable code                                   no-unreachable

src\components\cavi\QuickAddCablesDialog.js
  Line 12:3:    'FormControlLabel' is defined but never used                                                                 no-unused-vars
  Line 27:10:   'AddIcon' is defined but never used                                                                          no-unused-vars
  Line 30:11:   'InfoIcon' is defined but never used                                                                         no-unused-vars
  Line 34:10:   'determineCableState' is defined but never used                                                              no-unused-vars
  Line 49:10:   'loading' is assigned a value but never used                                                                 no-unused-vars
  Line 49:19:   'setLoading' is assigned a value but never used                                                              no-unused-vars
  Line 64:6:    React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 270:17:  'result' is assigned a value but never used                                                                  no-unused-vars

src\components\cavi\ReportCavi.js
  Line 7:3:    'Grid' is defined but never used                         no-unused-vars
  Line 20:3:   'Divider' is defined but never used                      no-unused-vars
  Line 21:3:   'List' is defined but never used                         no-unused-vars
  Line 22:3:   'ListItem' is defined but never used                     no-unused-vars
  Line 23:3:   'ListItemText' is defined but never used                 no-unused-vars
  Line 24:3:   'ListItemIcon' is defined but never used                 no-unused-vars
  Line 25:3:   'ListItemButton' is defined but never used               no-unused-vars
  Line 29:15:  'BarChartIcon' is defined but never used                 no-unused-vars
  Line 34:16:  'DateRangeIcon' is defined but never used                no-unused-vars
  Line 53:9:   'handleOptionSelect' is assigned a value but never used  no-unused-vars

src\components\cavi\SelezionaCavoForm.js
  Line 4:3:  'TextField' is defined but never used  no-unused-vars

src\components\certificazioni\CertificazioneForm.jsx
  Line 54:6:  React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\certificazioni\CertificazioniList.jsx
  Line 25:13:  'DownloadIcon' is defined but never used  no-unused-vars

src\components\charts\BoqChart.js
  Line 14:3:  'ComposedChart' is defined but never used  no-unused-vars
  Line 15:3:  'Line' is defined but never used           no-unused-vars
  Line 16:3:  'LineChart' is defined but never used      no-unused-vars

src\components\charts\CaviStatoChart.js
  Line 14:3:  'ComposedChart' is defined but never used  no-unused-vars
  Line 15:3:  'Line' is defined but never used           no-unused-vars

src\components\charts\TimelineChart.js
  Line 3:3:  'LineChart' is defined but never used  no-unused-vars

src\components\common\ExcelLikeFilter.js
  Line 16:3:  'MenuItem' is defined but never used    no-unused-vars
  Line 17:3:  'Select' is defined but never used      no-unused-vars
  Line 19:3:  'InputLabel' is defined but never used  no-unused-vars

src\pages\CaviPage.js
  Line 1:8:  'React' is defined but never used  no-unused-vars

src\pages\CertificazioniPageDebug.jsx
  Line 49:19:  'useAuth' is assigned a value but never used  no-unused-vars

src\pages\Dashboard.js
  Line 38:8:   'RedirectToCaviVisualizza' is defined but never used  no-unused-vars
  Line 44:11:  'user' is assigned a value but never used             no-unused-vars

src\pages\HomePage.js
  Line 2:27:  'Grid' is defined but never used                 no-unused-vars
  Line 2:33:  'Card' is defined but never used                 no-unused-vars
  Line 2:39:  'CardContent' is defined but never used          no-unused-vars
  Line 2:52:  'CardActionArea' is defined but never used       no-unused-vars
  Line 2:68:  'Avatar' is defined but never used               no-unused-vars
  Line 5:25:  'AdminIcon' is defined but never used            no-unused-vars
  Line 6:19:  'ConstructionIcon' is defined but never used     no-unused-vars
  Line 7:12:  'CableIcon' is defined but never used            no-unused-vars
  Line 8:18:  'ReportIcon' is defined but never used           no-unused-vars
  Line 43:9:  'navigateTo' is assigned a value but never used  no-unused-vars

src\pages\cantieri\CantierePage.js
  Line 24:11:  'isImpersonating' is assigned a value but never used                                                               no-unused-vars
  Line 53:6:   React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\pages\cavi\CertificazioneCaviPage.js
  Line 5:3:    'Paper' is defined but never used                     no-unused-vars
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars
  Line 28:9:   'cantiereName' is assigned a value but never used     no-unused-vars

src\pages\cavi\GestioneComandeePage.js
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars

src\pages\cavi\ParcoCaviPage.js
  Line 9:3:    'Card' is defined but never used                      no-unused-vars
  Line 10:3:   'CardContent' is defined but never used               no-unused-vars
  Line 11:3:   'CardActions' is defined but never used               no-unused-vars
  Line 12:3:   'Grid' is defined but never used                      no-unused-vars
  Line 13:3:   'Divider' is defined but never used                   no-unused-vars
  Line 18:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 19:15:  'ViewListIcon' is defined but never used              no-unused-vars
  Line 20:10:  'AddIcon' is defined but never used                   no-unused-vars
  Line 21:11:  'EditIcon' is defined but never used                  no-unused-vars
  Line 22:13:  'DeleteIcon' is defined but never used                no-unused-vars
  Line 23:14:  'HistoryIcon' is defined but never used               no-unused-vars
  Line 28:8:   'ParcoCavi' is defined but never used                 no-unused-vars
  Line 31:11:  'isImpersonating' is assigned a value but never used  no-unused-vars
  Line 51:9:   'handleSuccess' is assigned a value but never used    no-unused-vars
  Line 56:9:   'handleError' is assigned a value but never used      no-unused-vars

src\pages\cavi\ReportCaviPage.js
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars

src\pages\cavi\ReportCaviPageNew.js
  Line 16:3:   'Tooltip' is defined but never used                       no-unused-vars
  Line 35:15:  'PieChartIcon' is defined but never used                  no-unused-vars
  Line 42:16:  'DateRangeIcon' is defined but never used                 no-unused-vars
  Line 64:11:  'user' is assigned a value but never used                 no-unused-vars
  Line 161:9:  'reportTypes' is assigned a value but never used          no-unused-vars
  Line 279:9:  'handleReportSelect' is assigned a value but never used   no-unused-vars
  Line 321:9:  'renderReportContent' is assigned a value but never used  no-unused-vars

src\pages\cavi\TestCaviPage.js
  Line 1:27:  'useEffect' is defined but never used  no-unused-vars

src\pages\cavi\VisualizzaCaviPage.js
  Line 8:3:    'Card' is defined but never used                                                                                       no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                no-unused-vars
  Line 11:3:   'IconButton' is defined but never used                                                                                 no-unused-vars
  Line 21:8:   'InfoIcon' is defined but never used                                                                                   no-unused-vars
  Line 28:8:   'CavoForm' is defined but never used                                                                                   no-unused-vars
  Line 34:11:  'isImpersonating' is assigned a value but never used                                                                   no-unused-vars
  Line 36:9:   'navigate' is assigned a value but never used                                                                          no-unused-vars
  Line 38:10:  'cantiereName' is assigned a value but never used                                                                      no-unused-vars
  Line 76:10:  'statiInstallazione' is assigned a value but never used                                                                no-unused-vars
  Line 77:10:  'tipologieCavi' is assigned a value but never used                                                                     no-unused-vars
  Line 484:6:  React Hook useEffect has missing dependencies: '[1m[31merror[39m[22m[1m' and 'user'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 489:9:  'handleOpenDetails' is assigned a value but never used                                                                 no-unused-vars
  Line 583:9:  'renderStatsPanel' is assigned a value but never used                                                                  no-unused-vars

src\pages\cavi\parco\EliminaBobinaPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\ModificaBobinaPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\StoricoUtilizzoPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\posa\CollegamentiPage.js
  Line 5:3:    'Paper' is defined but never used                          no-unused-vars
  Line 6:3:    'Button' is defined but never used                         no-unused-vars
  Line 14:11:  'HomeIcon' is defined but never used                       no-unused-vars
  Line 23:11:  'isImpersonating' is assigned a value but never used       no-unused-vars
  Line 30:9:   'cantiereName' is assigned a value but never used          no-unused-vars
  Line 33:9:   'handleBackToCantieri' is assigned a value but never used  no-unused-vars
  Line 38:9:   'handleBackToAdmin' is assigned a value but never used     no-unused-vars

src\pages\cavi\posa\MetriPosatiSemplificatoPage.js
  Line 27:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\posa\ModificaBobinaPage.js
  Line 5:3:    'Paper' is defined but never used                          no-unused-vars
  Line 6:3:    'Button' is defined but never used                         no-unused-vars
  Line 14:11:  'HomeIcon' is defined but never used                       no-unused-vars
  Line 23:11:  'isImpersonating' is assigned a value but never used       no-unused-vars
  Line 30:9:   'cantiereName' is assigned a value but never used          no-unused-vars
  Line 33:9:   'handleBackToCantieri' is assigned a value but never used  no-unused-vars
  Line 38:9:   'handleBackToAdmin' is assigned a value but never used     no-unused-vars

src\pages\cavi\posa\ModificaCavoPage.js
  Line 20:11:  'isImpersonating' is assigned a value but never used    no-unused-vars
  Line 27:9:   'cantiereName' is assigned a value but never used       no-unused-vars
  Line 35:9:   'handleBackToAdmin' is assigned a value but never used  no-unused-vars

src\services\adminService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\authService.js
  Line 78:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown  no-throw-literal
  Line 80:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown  no-throw-literal
  Line 86:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown  no-throw-literal
  Line 89:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown  no-throw-literal

src\services\cantieriService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\caviService.js
  Line 260:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 274:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 278:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 333:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 435:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 451:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 668:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 677:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 681:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 755:17:  'token' is assigned a value but never used  no-unused-vars
  Line 775:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 794:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 801:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 810:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 817:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 885:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 955:3:   [1m[31mDuplicate[39m[22m[1m key 'updateCavoForCompatibility'  no-dupe-keys
  Line 1200:3:  [1m[31mDuplicate[39m[22m[1m key 'getCaviInstallati'           no-dupe-keys
  Line 1249:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 1279:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 1332:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 1374:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal

src\services\certificazioneService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\comandeService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\parcoCaviService.js
  Line 1:8:    'axios' is defined but never used              no-unused-vars
  Line 5:7:    'API_URL' is assigned a value but never used   no-unused-vars
  Line 83:13:  'sentData' is assigned a value but never used  no-unused-vars
  Line 109:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 123:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 127:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 212:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 226:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 230:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 271:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 280:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 284:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 320:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 324:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 360:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 369:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 373:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 450:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 459:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 463:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal

src\services\reportService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\userService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

[39m[22m

webpack compiled with [1m[33m1 warning[39m[22m
Compiling...
Compiled with warnings.

[eslint] 
src\components\TopNavbar.js
  Line 13:3:   'Avatar' is defined but never used                         no-unused-vars
  Line 20:25:  'AdminIcon' is defined but never used                      no-unused-vars
  Line 21:19:  'ConstructionIcon' is defined but never used               no-unused-vars
  Line 22:12:  'CableIcon' is defined but never used                      no-unused-vars
  Line 23:18:  'ReportIcon' is defined but never used                     no-unused-vars
  Line 56:10:  'homeAnchorEl' is assigned a value but never used          no-unused-vars
  Line 57:10:  'adminAnchorEl' is assigned a value but never used         no-unused-vars
  Line 58:10:  'cantieriAnchorEl' is assigned a value but never used      no-unused-vars
  Line 59:10:  'caviAnchorEl' is assigned a value but never used          no-unused-vars
  Line 68:9:   'selectedCantiereName' is assigned a value but never used  no-unused-vars

src\components\admin\UserExpirationChecker.js
  Line 11:10:  'lastCheck' is assigned a value but never used  no-unused-vars

src\components\cavi\CavoForm.js
  Line 6:3:   'Stack' is defined but never used    no-unused-vars
  Line 11:3:  'Divider' is defined but never used  no-unused-vars

src\components\cavi\CertificazioneCavi.js
  Line 8:3:    'Card' is defined but never used                                                                                       no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                                no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                               no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                             no-unused-vars
  Line 37:10:  'AddIcon' is defined but never used                                                                                    no-unused-vars
  Line 38:11:  'EditIcon' is defined but never used                                                                                   no-unused-vars
  Line 45:15:  'ViewListIcon' is defined but never used                                                                               no-unused-vars
  Line 46:12:  'BuildIcon' is defined but never used                                                                                  no-unused-vars
  Line 122:6:  React Hook useEffect has a missing dependency: 'loadCertificazioni'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\cavi\CollegamentiCavo.js
  Line 8:3:   'List' is defined but never used                                                                             no-unused-vars
  Line 9:3:   'ListItem' is defined but never used                                                                         no-unused-vars
  Line 10:3:  'ListItemText' is defined but never used                                                                     no-unused-vars
  Line 46:6:  React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\cavi\ExcelPopup.js
  Line 10:3:   'TextField' is defined but never used          no-unused-vars
  Line 14:3:   'Divider' is defined but never used            no-unused-vars
  Line 27:10:  'filePath' is assigned a value but never used  no-unused-vars

src\components\cavi\GestioneComande.js
  Line 8:3:    'Card' is defined but never used                                                                                no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                         no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                         no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                        no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                      no-unused-vars
  Line 25:3:   'Divider' is defined but never used                                                                             no-unused-vars
  Line 37:10:  'AddIcon' is defined but never used                                                                             no-unused-vars
  Line 41:13:  'SearchIcon' is defined but never used                                                                          no-unused-vars
  Line 43:15:  'ViewListIcon' is defined but never used                                                                        no-unused-vars
  Line 44:17:  'AssignmentIcon' is defined but never used                                                                      no-unused-vars
  Line 98:6:   React Hook useEffect has a missing dependency: 'loadComande'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 101:9:  'handleOptionSelect' is assigned a value but never used                                                         no-unused-vars

src\components\cavi\IncompatibleReelDialog.js
  Line 17:3:  'Alert' is defined but never used  no-unused-vars

src\components\cavi\InserisciMetriForm.js
  Line 17:3:     'FormHelperText' is defined but never used                                                                     no-unused-vars
  Line 34:3:     'ListItemText' is defined but never used                                                                       no-unused-vars
  Line 35:3:     'ListItemSecondaryAction' is defined but never used                                                            no-unused-vars
  Line 39:11:    'SaveIcon' is defined but never used                                                                           no-unused-vars
  Line 51:3:     'CABLE_STATES' is defined but never used                                                                       no-unused-vars
  Line 52:3:     'REEL_STATES' is defined but never used                                                                        no-unused-vars
  Line 54:3:     'determineReelState' is defined but never used                                                                 no-unused-vars
  Line 55:3:     'canModifyCable' is defined but never used                                                                     no-unused-vars
  Line 62:10:    'redirectToVisualizzaCavi' is defined but never used                                                           no-unused-vars
  Line 105:10:   'incompatibleReel' is assigned a value but never used                                                          no-unused-vars
  Line 105:28:   'setIncompatibleReel' is assigned a value but never used                                                       no-unused-vars
  Line 145:6:    React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 701:9:    'handleBack' is assigned a value but never used                                                                no-unused-vars
  Line 1311:11:  'buildFullBobinaId' is assigned a value but never used                                                         no-unused-vars
  Line 1316:11:  'hasSufficientMeters' is assigned a value but never used                                                       no-unused-vars
  Line 1883:9:   'getStepContent' is assigned a value but never used                                                            no-unused-vars

src\components\cavi\MetriPosatiSemplificatoForm.js
  Line 33:3:   'CABLE_STATES' is defined but never used                                                                                        no-unused-vars
  Line 34:3:   'REEL_STATES' is defined but never used                                                                                         no-unused-vars
  Line 35:3:   'determineCableState' is defined but never used                                                                                 no-unused-vars
  Line 36:3:   'determineReelState' is defined but never used                                                                                  no-unused-vars
  Line 37:3:   'canModifyCable' is defined but never used                                                                                      no-unused-vars
  Line 41:3:   'getReelStateColor' is defined but never used                                                                                   no-unused-vars
  Line 43:10:  'redirectToVisualizzaCavi' is defined but never used                                                                            no-unused-vars
  Line 69:10:  'loading' is assigned a value but never used                                                                                    no-unused-vars
  Line 69:19:  'setLoading' is assigned a value but never used                                                                                 no-unused-vars
  Line 88:6:   React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 448:6:  React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array       react-hooks/exhaustive-deps

src\components\cavi\ModificaBobinaForm.js
  Line 9:3:     'FormControl' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 10:3:    'InputLabel' is defined but never used                                                                                                                                                                                         no-unused-vars
  Line 11:3:    'Select' is defined but never used                                                                                                                                                                                             no-unused-vars
  Line 12:3:    'MenuItem' is defined but never used                                                                                                                                                                                           no-unused-vars
  Line 33:3:    'ListItemText' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 35:3:    'ListItemSecondaryAction' is defined but never used                                                                                                                                                                            no-unused-vars
  Line 42:14:   'WarningIcon' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 52:3:    'CABLE_STATES' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 53:3:    'REEL_STATES' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 54:3:    'determineCableState' is defined but never used                                                                                                                                                                                no-unused-vars
  Line 55:3:    'determineReelState' is defined but never used                                                                                                                                                                                 no-unused-vars
  Line 56:3:    'canModifyCable' is defined but never used                                                                                                                                                                                     no-unused-vars
  Line 57:3:    'isCableSpare' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 58:3:    'isCableInstalled' is defined but never used                                                                                                                                                                                   no-unused-vars
  Line 59:3:    'getCableStateColor' is defined but never used                                                                                                                                                                                 no-unused-vars
  Line 72:9:    'navigate' is assigned a value but never used                                                                                                                                                                                  no-unused-vars
  Line 79:10:   'searchResults' is assigned a value but never used                                                                                                                                                                             no-unused-vars
  Line 79:25:   'setSearchResults' is assigned a value but never used                                                                                                                                                                          no-unused-vars
  Line 80:10:   'showSearchResults' is assigned a value but never used                                                                                                                                                                         no-unused-vars
  Line 85:10:   'compatibleBobine' is assigned a value but never used                                                                                                                                                                          no-unused-vars
  Line 105:6:   React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array                                                                                                                    react-hooks/exhaustive-deps
  Line 112:6:   React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array                                                                                                                  react-hooks/exhaustive-deps
  Line 127:6:   React Hook useEffect has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 283:13:  'bobina' is assigned a value but never used                                                                                                                                                                                    no-unused-vars

src\components\cavi\ParcoCavi.js
  Line 8:3:    'Card' is defined but never used                                                                                                                            no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                                                     no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                                                                     no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                                                                    no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                                                                  no-unused-vars
  Line 25:3:   'Divider' is defined but never used                                                                                                                         no-unused-vars
  Line 29:3:   'IconButton' is defined but never used                                                                                                                      no-unused-vars
  Line 39:11:  'EditIcon' is defined but never used                                                                                                                        no-unused-vars
  Line 43:15:  'ViewListIcon' is defined but never used                                                                                                                    no-unused-vars
  Line 44:14:  'WarningIcon' is defined but never used                                                                                                                     no-unused-vars
  Line 50:69:  'isEmpty' is defined but never used                                                                                                                         no-unused-vars
  Line 79:10:  'isFirstInsertion' is assigned a value but never used                                                                                                       no-unused-vars
  Line 145:6:  React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 689:9:  'renderBobineCards' is assigned a value but never used                                                                                                      no-unused-vars

src\components\cavi\PosaCaviCollegamenti.js
  Line 20:3:   'InputLabel' is defined but never used             no-unused-vars
  Line 21:3:   'Select' is defined but never used                 no-unused-vars
  Line 22:3:   'MenuItem' is defined but never used               no-unused-vars
  Line 23:3:   'Grid' is defined but never used                   no-unused-vars
  Line 26:3:   'FormHelperText' is defined but never used         no-unused-vars
  Line 69:10:  'formWarnings' is assigned a value but never used  no-unused-vars
  Line 466:9:  Unreachable code                                   no-unreachable

src\components\cavi\QuickAddCablesDialog.js
  Line 12:3:    'FormControlLabel' is defined but never used                                                                 no-unused-vars
  Line 27:10:   'AddIcon' is defined but never used                                                                          no-unused-vars
  Line 30:11:   'InfoIcon' is defined but never used                                                                         no-unused-vars
  Line 34:10:   'determineCableState' is defined but never used                                                              no-unused-vars
  Line 49:10:   'loading' is assigned a value but never used                                                                 no-unused-vars
  Line 49:19:   'setLoading' is assigned a value but never used                                                              no-unused-vars
  Line 64:6:    React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 270:17:  'result' is assigned a value but never used                                                                  no-unused-vars

src\components\cavi\ReportCavi.js
  Line 7:3:    'Grid' is defined but never used                         no-unused-vars
  Line 20:3:   'Divider' is defined but never used                      no-unused-vars
  Line 21:3:   'List' is defined but never used                         no-unused-vars
  Line 22:3:   'ListItem' is defined but never used                     no-unused-vars
  Line 23:3:   'ListItemText' is defined but never used                 no-unused-vars
  Line 24:3:   'ListItemIcon' is defined but never used                 no-unused-vars
  Line 25:3:   'ListItemButton' is defined but never used               no-unused-vars
  Line 29:15:  'BarChartIcon' is defined but never used                 no-unused-vars
  Line 34:16:  'DateRangeIcon' is defined but never used                no-unused-vars
  Line 53:9:   'handleOptionSelect' is assigned a value but never used  no-unused-vars

src\components\cavi\SelezionaCavoForm.js
  Line 4:3:  'TextField' is defined but never used  no-unused-vars

src\components\certificazioni\CertificazioneForm.jsx
  Line 54:6:  React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\certificazioni\CertificazioniList.jsx
  Line 25:13:  'DownloadIcon' is defined but never used  no-unused-vars

src\components\charts\BoqChart.js
  Line 14:3:  'ComposedChart' is defined but never used  no-unused-vars
  Line 15:3:  'Line' is defined but never used           no-unused-vars
  Line 16:3:  'LineChart' is defined but never used      no-unused-vars

src\components\charts\CaviStatoChart.js
  Line 14:3:  'ComposedChart' is defined but never used  no-unused-vars
  Line 15:3:  'Line' is defined but never used           no-unused-vars

src\components\charts\TimelineChart.js
  Line 3:3:  'LineChart' is defined but never used  no-unused-vars

src\components\common\ExcelLikeFilter.js
  Line 16:3:  'MenuItem' is defined but never used    no-unused-vars
  Line 17:3:  'Select' is defined but never used      no-unused-vars
  Line 19:3:  'InputLabel' is defined but never used  no-unused-vars

src\pages\CaviPage.js
  Line 1:8:  'React' is defined but never used  no-unused-vars

src\pages\CertificazioniPageDebug.jsx
  Line 49:19:  'useAuth' is assigned a value but never used  no-unused-vars

src\pages\Dashboard.js
  Line 38:8:   'RedirectToCaviVisualizza' is defined but never used  no-unused-vars
  Line 44:11:  'user' is assigned a value but never used             no-unused-vars

src\pages\HomePage.js
  Line 2:27:  'Grid' is defined but never used                 no-unused-vars
  Line 2:33:  'Card' is defined but never used                 no-unused-vars
  Line 2:39:  'CardContent' is defined but never used          no-unused-vars
  Line 2:52:  'CardActionArea' is defined but never used       no-unused-vars
  Line 2:68:  'Avatar' is defined but never used               no-unused-vars
  Line 5:25:  'AdminIcon' is defined but never used            no-unused-vars
  Line 6:19:  'ConstructionIcon' is defined but never used     no-unused-vars
  Line 7:12:  'CableIcon' is defined but never used            no-unused-vars
  Line 8:18:  'ReportIcon' is defined but never used           no-unused-vars
  Line 43:9:  'navigateTo' is assigned a value but never used  no-unused-vars

src\pages\cantieri\CantierePage.js
  Line 24:11:  'isImpersonating' is assigned a value but never used                                                               no-unused-vars
  Line 53:6:   React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\pages\cavi\CertificazioneCaviPage.js
  Line 5:3:    'Paper' is defined but never used                     no-unused-vars
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars
  Line 28:9:   'cantiereName' is assigned a value but never used     no-unused-vars

src\pages\cavi\GestioneComandeePage.js
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars

src\pages\cavi\ParcoCaviPage.js
  Line 9:3:    'Card' is defined but never used                      no-unused-vars
  Line 10:3:   'CardContent' is defined but never used               no-unused-vars
  Line 11:3:   'CardActions' is defined but never used               no-unused-vars
  Line 12:3:   'Grid' is defined but never used                      no-unused-vars
  Line 13:3:   'Divider' is defined but never used                   no-unused-vars
  Line 18:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 19:15:  'ViewListIcon' is defined but never used              no-unused-vars
  Line 20:10:  'AddIcon' is defined but never used                   no-unused-vars
  Line 21:11:  'EditIcon' is defined but never used                  no-unused-vars
  Line 22:13:  'DeleteIcon' is defined but never used                no-unused-vars
  Line 23:14:  'HistoryIcon' is defined but never used               no-unused-vars
  Line 28:8:   'ParcoCavi' is defined but never used                 no-unused-vars
  Line 31:11:  'isImpersonating' is assigned a value but never used  no-unused-vars
  Line 51:9:   'handleSuccess' is assigned a value but never used    no-unused-vars
  Line 56:9:   'handleError' is assigned a value but never used      no-unused-vars

src\pages\cavi\ReportCaviPage.js
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars

src\pages\cavi\ReportCaviPageNew.js
  Line 16:3:   'Tooltip' is defined but never used                       no-unused-vars
  Line 35:15:  'PieChartIcon' is defined but never used                  no-unused-vars
  Line 42:16:  'DateRangeIcon' is defined but never used                 no-unused-vars
  Line 64:11:  'user' is assigned a value but never used                 no-unused-vars
  Line 161:9:  'reportTypes' is assigned a value but never used          no-unused-vars
  Line 279:9:  'handleReportSelect' is assigned a value but never used   no-unused-vars
  Line 321:9:  'renderReportContent' is assigned a value but never used  no-unused-vars

src\pages\cavi\TestCaviPage.js
  Line 1:27:  'useEffect' is defined but never used  no-unused-vars

src\pages\cavi\VisualizzaCaviPage.js
  Line 8:3:    'Card' is defined but never used                                                                                       no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                no-unused-vars
  Line 11:3:   'IconButton' is defined but never used                                                                                 no-unused-vars
  Line 21:8:   'InfoIcon' is defined but never used                                                                                   no-unused-vars
  Line 28:8:   'CavoForm' is defined but never used                                                                                   no-unused-vars
  Line 34:11:  'isImpersonating' is assigned a value but never used                                                                   no-unused-vars
  Line 36:9:   'navigate' is assigned a value but never used                                                                          no-unused-vars
  Line 38:10:  'cantiereName' is assigned a value but never used                                                                      no-unused-vars
  Line 76:10:  'statiInstallazione' is assigned a value but never used                                                                no-unused-vars
  Line 77:10:  'tipologieCavi' is assigned a value but never used                                                                     no-unused-vars
  Line 484:6:  React Hook useEffect has missing dependencies: 'error' and 'user'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 489:9:  'handleOpenDetails' is assigned a value but never used                                                                 no-unused-vars
  Line 583:9:  'renderStatsPanel' is assigned a value but never used                                                                  no-unused-vars

src\pages\cavi\parco\EliminaBobinaPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\ModificaBobinaPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\StoricoUtilizzoPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\posa\CollegamentiPage.js
  Line 5:3:    'Paper' is defined but never used                          no-unused-vars
  Line 6:3:    'Button' is defined but never used                         no-unused-vars
  Line 14:11:  'HomeIcon' is defined but never used                       no-unused-vars
  Line 23:11:  'isImpersonating' is assigned a value but never used       no-unused-vars
  Line 30:9:   'cantiereName' is assigned a value but never used          no-unused-vars
  Line 33:9:   'handleBackToCantieri' is assigned a value but never used  no-unused-vars
  Line 38:9:   'handleBackToAdmin' is assigned a value but never used     no-unused-vars

src\pages\cavi\posa\MetriPosatiSemplificatoPage.js
  Line 27:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\posa\ModificaBobinaPage.js
  Line 5:3:    'Paper' is defined but never used                          no-unused-vars
  Line 6:3:    'Button' is defined but never used                         no-unused-vars
  Line 14:11:  'HomeIcon' is defined but never used                       no-unused-vars
  Line 23:11:  'isImpersonating' is assigned a value but never used       no-unused-vars
  Line 30:9:   'cantiereName' is assigned a value but never used          no-unused-vars
  Line 33:9:   'handleBackToCantieri' is assigned a value but never used  no-unused-vars
  Line 38:9:   'handleBackToAdmin' is assigned a value but never used     no-unused-vars

src\pages\cavi\posa\ModificaCavoPage.js
  Line 20:11:  'isImpersonating' is assigned a value but never used    no-unused-vars
  Line 27:9:   'cantiereName' is assigned a value but never used       no-unused-vars
  Line 35:9:   'handleBackToAdmin' is assigned a value but never used  no-unused-vars

src\services\adminService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\authService.js
  Line 78:11:  Expected an error object to be thrown  no-throw-literal
  Line 80:11:  Expected an error object to be thrown  no-throw-literal
  Line 86:9:   Expected an error object to be thrown  no-throw-literal
  Line 89:9:   Expected an error object to be thrown  no-throw-literal

src\services\cantieriService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\caviService.js
  Line 260:9:   Expected an error object to be thrown       no-throw-literal
  Line 274:9:   Expected an error object to be thrown       no-throw-literal
  Line 278:9:   Expected an error object to be thrown       no-throw-literal
  Line 333:11:  Expected an error object to be thrown       no-throw-literal
  Line 435:9:   Expected an error object to be thrown       no-throw-literal
  Line 451:9:   Expected an error object to be thrown       no-throw-literal
  Line 668:9:   Expected an error object to be thrown       no-throw-literal
  Line 677:9:   Expected an error object to be thrown       no-throw-literal
  Line 681:9:   Expected an error object to be thrown       no-throw-literal
  Line 755:17:  'token' is assigned a value but never used  no-unused-vars
  Line 775:9:   Expected an error object to be thrown       no-throw-literal
  Line 794:11:  Expected an error object to be thrown       no-throw-literal
  Line 801:9:   Expected an error object to be thrown       no-throw-literal
  Line 810:11:  Expected an error object to be thrown       no-throw-literal
  Line 817:9:   Expected an error object to be thrown       no-throw-literal
  Line 885:9:   Expected an error object to be thrown       no-throw-literal
  Line 955:3:   Duplicate key 'updateCavoForCompatibility'  no-dupe-keys
  Line 1200:3:  Duplicate key 'getCaviInstallati'           no-dupe-keys
  Line 1249:9:  Expected an error object to be thrown       no-throw-literal
  Line 1279:9:  Expected an error object to be thrown       no-throw-literal
  Line 1332:9:  Expected an error object to be thrown       no-throw-literal
  Line 1374:9:  Expected an error object to be thrown       no-throw-literal

src\services\certificazioneService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\comandeService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\parcoCaviService.js
  Line 1:8:    'axios' is defined but never used              no-unused-vars
  Line 5:7:    'API_URL' is assigned a value but never used   no-unused-vars
  Line 83:13:  'sentData' is assigned a value but never used  no-unused-vars
  Line 109:9:  Expected an error object to be thrown          no-throw-literal
  Line 123:9:  Expected an error object to be thrown          no-throw-literal
  Line 127:9:  Expected an error object to be thrown          no-throw-literal
  Line 212:9:  Expected an error object to be thrown          no-throw-literal
  Line 226:9:  Expected an error object to be thrown          no-throw-literal
  Line 230:9:  Expected an error object to be thrown          no-throw-literal
  Line 271:9:  Expected an error object to be thrown          no-throw-literal
  Line 280:9:  Expected an error object to be thrown          no-throw-literal
  Line 284:9:  Expected an error object to be thrown          no-throw-literal
  Line 320:9:  Expected an error object to be thrown          no-throw-literal
  Line 324:9:  Expected an error object to be thrown          no-throw-literal
  Line 360:9:  Expected an error object to be thrown          no-throw-literal
  Line 369:9:  Expected an error object to be thrown          no-throw-literal
  Line 373:9:  Expected an error object to be thrown          no-throw-literal
  Line 450:9:  Expected an error object to be thrown          no-throw-literal
  Line 459:9:  Expected an error object to be thrown          no-throw-literal
  Line 463:9:  Expected an error object to be thrown          no-throw-literal

src\services\reportService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\userService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

[1m[33mWARNING[39m[22m in [1m[eslint] 
src\components\TopNavbar.js
  Line 13:3:   'Avatar' is defined but never used                         no-unused-vars
  Line 20:25:  'AdminIcon' is defined but never used                      no-unused-vars
  Line 21:19:  'ConstructionIcon' is defined but never used               no-unused-vars
  Line 22:12:  'CableIcon' is defined but never used                      no-unused-vars
  Line 23:18:  'ReportIcon' is defined but never used                     no-unused-vars
  Line 56:10:  'homeAnchorEl' is assigned a value but never used          no-unused-vars
  Line 57:10:  'adminAnchorEl' is assigned a value but never used         no-unused-vars
  Line 58:10:  'cantieriAnchorEl' is assigned a value but never used      no-unused-vars
  Line 59:10:  'caviAnchorEl' is assigned a value but never used          no-unused-vars
  Line 68:9:   'selectedCantiereName' is assigned a value but never used  no-unused-vars

src\components\admin\UserExpirationChecker.js
  Line 11:10:  'lastCheck' is assigned a value but never used  no-unused-vars

src\components\cavi\CavoForm.js
  Line 6:3:   'Stack' is defined but never used    no-unused-vars
  Line 11:3:  'Divider' is defined but never used  no-unused-vars

src\components\cavi\CertificazioneCavi.js
  Line 8:3:    'Card' is defined but never used                                                                                       no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                                no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                               no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                             no-unused-vars
  Line 37:10:  'AddIcon' is defined but never used                                                                                    no-unused-vars
  Line 38:11:  'EditIcon' is defined but never used                                                                                   no-unused-vars
  Line 45:15:  'ViewListIcon' is defined but never used                                                                               no-unused-vars
  Line 46:12:  'BuildIcon' is defined but never used                                                                                  no-unused-vars
  Line 122:6:  React Hook useEffect has a missing dependency: 'loadCertificazioni'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\cavi\CollegamentiCavo.js
  Line 8:3:   'List' is defined but never used                                                                             no-unused-vars
  Line 9:3:   'ListItem' is defined but never used                                                                         no-unused-vars
  Line 10:3:  'ListItemText' is defined but never used                                                                     no-unused-vars
  Line 46:6:  React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\cavi\ExcelPopup.js
  Line 10:3:   'TextField' is defined but never used          no-unused-vars
  Line 14:3:   'Divider' is defined but never used            no-unused-vars
  Line 27:10:  'filePath' is assigned a value but never used  no-unused-vars

src\components\cavi\GestioneComande.js
  Line 8:3:    'Card' is defined but never used                                                                                no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                         no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                         no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                        no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                      no-unused-vars
  Line 25:3:   'Divider' is defined but never used                                                                             no-unused-vars
  Line 37:10:  'AddIcon' is defined but never used                                                                             no-unused-vars
  Line 41:13:  'SearchIcon' is defined but never used                                                                          no-unused-vars
  Line 43:15:  'ViewListIcon' is defined but never used                                                                        no-unused-vars
  Line 44:17:  'AssignmentIcon' is defined but never used                                                                      no-unused-vars
  Line 98:6:   React Hook useEffect has a missing dependency: 'loadComande'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 101:9:  'handleOptionSelect' is assigned a value but never used                                                         no-unused-vars

src\components\cavi\IncompatibleReelDialog.js
  Line 17:3:  'Alert' is defined but never used  no-unused-vars

src\components\cavi\InserisciMetriForm.js
  Line 17:3:     'FormHelperText' is defined but never used                                                                     no-unused-vars
  Line 34:3:     'ListItemText' is defined but never used                                                                       no-unused-vars
  Line 35:3:     'ListItemSecondaryAction' is defined but never used                                                            no-unused-vars
  Line 39:11:    'SaveIcon' is defined but never used                                                                           no-unused-vars
  Line 51:3:     'CABLE_STATES' is defined but never used                                                                       no-unused-vars
  Line 52:3:     'REEL_STATES' is defined but never used                                                                        no-unused-vars
  Line 54:3:     'determineReelState' is defined but never used                                                                 no-unused-vars
  Line 55:3:     'canModifyCable' is defined but never used                                                                     no-unused-vars
  Line 62:10:    'redirectToVisualizzaCavi' is defined but never used                                                           no-unused-vars
  Line 105:10:   'incompatibleReel' is assigned a value but never used                                                          no-unused-vars
  Line 105:28:   'setIncompatibleReel' is assigned a value but never used                                                       no-unused-vars
  Line 145:6:    React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 701:9:    'handleBack' is assigned a value but never used                                                                no-unused-vars
  Line 1311:11:  'buildFullBobinaId' is assigned a value but never used                                                         no-unused-vars
  Line 1316:11:  'hasSufficientMeters' is assigned a value but never used                                                       no-unused-vars
  Line 1883:9:   'getStepContent' is assigned a value but never used                                                            no-unused-vars

src\components\cavi\MetriPosatiSemplificatoForm.js
  Line 33:3:   'CABLE_STATES' is defined but never used                                                                                        no-unused-vars
  Line 34:3:   'REEL_STATES' is defined but never used                                                                                         no-unused-vars
  Line 35:3:   'determineCableState' is defined but never used                                                                                 no-unused-vars
  Line 36:3:   'determineReelState' is defined but never used                                                                                  no-unused-vars
  Line 37:3:   'canModifyCable' is defined but never used                                                                                      no-unused-vars
  Line 41:3:   'getReelStateColor' is defined but never used                                                                                   no-unused-vars
  Line 43:10:  'redirectToVisualizzaCavi' is defined but never used                                                                            no-unused-vars
  Line 69:10:  'loading' is assigned a value but never used                                                                                    no-unused-vars
  Line 69:19:  'setLoading' is assigned a value but never used                                                                                 no-unused-vars
  Line 88:6:   React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 448:6:  React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array       react-hooks/exhaustive-deps

src\components\cavi\ModificaBobinaForm.js
  Line 9:3:     'FormControl' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 10:3:    'InputLabel' is defined but never used                                                                                                                                                                                         no-unused-vars
  Line 11:3:    'Select' is defined but never used                                                                                                                                                                                             no-unused-vars
  Line 12:3:    'MenuItem' is defined but never used                                                                                                                                                                                           no-unused-vars
  Line 33:3:    'ListItemText' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 35:3:    'ListItemSecondaryAction' is defined but never used                                                                                                                                                                            no-unused-vars
  Line 42:14:   'WarningIcon' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 52:3:    'CABLE_STATES' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 53:3:    'REEL_STATES' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 54:3:    'determineCableState' is defined but never used                                                                                                                                                                                no-unused-vars
  Line 55:3:    'determineReelState' is defined but never used                                                                                                                                                                                 no-unused-vars
  Line 56:3:    'canModifyCable' is defined but never used                                                                                                                                                                                     no-unused-vars
  Line 57:3:    'isCableSpare' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 58:3:    'isCableInstalled' is defined but never used                                                                                                                                                                                   no-unused-vars
  Line 59:3:    'getCableStateColor' is defined but never used                                                                                                                                                                                 no-unused-vars
  Line 72:9:    'navigate' is assigned a value but never used                                                                                                                                                                                  no-unused-vars
  Line 79:10:   'searchResults' is assigned a value but never used                                                                                                                                                                             no-unused-vars
  Line 79:25:   'setSearchResults' is assigned a value but never used                                                                                                                                                                          no-unused-vars
  Line 80:10:   'showSearchResults' is assigned a value but never used                                                                                                                                                                         no-unused-vars
  Line 85:10:   'compatibleBobine' is assigned a value but never used                                                                                                                                                                          no-unused-vars
  Line 105:6:   React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array                                                                                                                    react-hooks/exhaustive-deps
  Line 112:6:   React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array                                                                                                                  react-hooks/exhaustive-deps
  Line 127:6:   React Hook useEffect has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 283:13:  'bobina' is assigned a value but never used                                                                                                                                                                                    no-unused-vars

src\components\cavi\ParcoCavi.js
  Line 8:3:    'Card' is defined but never used                                                                                                                            no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                                                     no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                                                                     no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                                                                    no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                                                                  no-unused-vars
  Line 25:3:   'Divider' is defined but never used                                                                                                                         no-unused-vars
  Line 29:3:   'IconButton' is defined but never used                                                                                                                      no-unused-vars
  Line 39:11:  'EditIcon' is defined but never used                                                                                                                        no-unused-vars
  Line 43:15:  'ViewListIcon' is defined but never used                                                                                                                    no-unused-vars
  Line 44:14:  'WarningIcon' is defined but never used                                                                                                                     no-unused-vars
  Line 50:69:  'isEmpty' is defined but never used                                                                                                                         no-unused-vars
  Line 79:10:  'isFirstInsertion' is assigned a value but never used                                                                                                       no-unused-vars
  Line 145:6:  React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 689:9:  'renderBobineCards' is assigned a value but never used                                                                                                      no-unused-vars

src\components\cavi\PosaCaviCollegamenti.js
  Line 20:3:   'InputLabel' is defined but never used             no-unused-vars
  Line 21:3:   'Select' is defined but never used                 no-unused-vars
  Line 22:3:   'MenuItem' is defined but never used               no-unused-vars
  Line 23:3:   'Grid' is defined but never used                   no-unused-vars
  Line 26:3:   'FormHelperText' is defined but never used         no-unused-vars
  Line 69:10:  'formWarnings' is assigned a value but never used  no-unused-vars
  Line 466:9:  Unreachable code                                   no-unreachable

src\components\cavi\QuickAddCablesDialog.js
  Line 12:3:    'FormControlLabel' is defined but never used                                                                 no-unused-vars
  Line 27:10:   'AddIcon' is defined but never used                                                                          no-unused-vars
  Line 30:11:   'InfoIcon' is defined but never used                                                                         no-unused-vars
  Line 34:10:   'determineCableState' is defined but never used                                                              no-unused-vars
  Line 49:10:   'loading' is assigned a value but never used                                                                 no-unused-vars
  Line 49:19:   'setLoading' is assigned a value but never used                                                              no-unused-vars
  Line 64:6:    React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 270:17:  'result' is assigned a value but never used                                                                  no-unused-vars

src\components\cavi\ReportCavi.js
  Line 7:3:    'Grid' is defined but never used                         no-unused-vars
  Line 20:3:   'Divider' is defined but never used                      no-unused-vars
  Line 21:3:   'List' is defined but never used                         no-unused-vars
  Line 22:3:   'ListItem' is defined but never used                     no-unused-vars
  Line 23:3:   'ListItemText' is defined but never used                 no-unused-vars
  Line 24:3:   'ListItemIcon' is defined but never used                 no-unused-vars
  Line 25:3:   'ListItemButton' is defined but never used               no-unused-vars
  Line 29:15:  'BarChartIcon' is defined but never used                 no-unused-vars
  Line 34:16:  'DateRangeIcon' is defined but never used                no-unused-vars
  Line 53:9:   'handleOptionSelect' is assigned a value but never used  no-unused-vars

src\components\cavi\SelezionaCavoForm.js
  Line 4:3:  'TextField' is defined but never used  no-unused-vars

src\components\certificazioni\CertificazioneForm.jsx
  Line 54:6:  React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\certificazioni\CertificazioniList.jsx
  Line 25:13:  'DownloadIcon' is defined but never used  no-unused-vars

src\components\charts\BoqChart.js
  Line 14:3:  'ComposedChart' is defined but never used  no-unused-vars
  Line 15:3:  'Line' is defined but never used           no-unused-vars
  Line 16:3:  'LineChart' is defined but never used      no-unused-vars

src\components\charts\CaviStatoChart.js
  Line 14:3:  'ComposedChart' is defined but never used  no-unused-vars
  Line 15:3:  'Line' is defined but never used           no-unused-vars

src\components\charts\TimelineChart.js
  Line 3:3:  'LineChart' is defined but never used  no-unused-vars

src\components\common\ExcelLikeFilter.js
  Line 16:3:  'MenuItem' is defined but never used    no-unused-vars
  Line 17:3:  'Select' is defined but never used      no-unused-vars
  Line 19:3:  'InputLabel' is defined but never used  no-unused-vars

src\pages\CaviPage.js
  Line 1:8:  'React' is defined but never used  no-unused-vars

src\pages\CertificazioniPageDebug.jsx
  Line 49:19:  'useAuth' is assigned a value but never used  no-unused-vars

src\pages\Dashboard.js
  Line 38:8:   'RedirectToCaviVisualizza' is defined but never used  no-unused-vars
  Line 44:11:  'user' is assigned a value but never used             no-unused-vars

src\pages\HomePage.js
  Line 2:27:  'Grid' is defined but never used                 no-unused-vars
  Line 2:33:  'Card' is defined but never used                 no-unused-vars
  Line 2:39:  'CardContent' is defined but never used          no-unused-vars
  Line 2:52:  'CardActionArea' is defined but never used       no-unused-vars
  Line 2:68:  'Avatar' is defined but never used               no-unused-vars
  Line 5:25:  'AdminIcon' is defined but never used            no-unused-vars
  Line 6:19:  'ConstructionIcon' is defined but never used     no-unused-vars
  Line 7:12:  'CableIcon' is defined but never used            no-unused-vars
  Line 8:18:  'ReportIcon' is defined but never used           no-unused-vars
  Line 43:9:  'navigateTo' is assigned a value but never used  no-unused-vars

src\pages\cantieri\CantierePage.js
  Line 24:11:  'isImpersonating' is assigned a value but never used                                                               no-unused-vars
  Line 53:6:   React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\pages\cavi\CertificazioneCaviPage.js
  Line 5:3:    'Paper' is defined but never used                     no-unused-vars
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars
  Line 28:9:   'cantiereName' is assigned a value but never used     no-unused-vars

src\pages\cavi\GestioneComandeePage.js
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars

src\pages\cavi\ParcoCaviPage.js
  Line 9:3:    'Card' is defined but never used                      no-unused-vars
  Line 10:3:   'CardContent' is defined but never used               no-unused-vars
  Line 11:3:   'CardActions' is defined but never used               no-unused-vars
  Line 12:3:   'Grid' is defined but never used                      no-unused-vars
  Line 13:3:   'Divider' is defined but never used                   no-unused-vars
  Line 18:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 19:15:  'ViewListIcon' is defined but never used              no-unused-vars
  Line 20:10:  'AddIcon' is defined but never used                   no-unused-vars
  Line 21:11:  'EditIcon' is defined but never used                  no-unused-vars
  Line 22:13:  'DeleteIcon' is defined but never used                no-unused-vars
  Line 23:14:  'HistoryIcon' is defined but never used               no-unused-vars
  Line 28:8:   'ParcoCavi' is defined but never used                 no-unused-vars
  Line 31:11:  'isImpersonating' is assigned a value but never used  no-unused-vars
  Line 51:9:   'handleSuccess' is assigned a value but never used    no-unused-vars
  Line 56:9:   'handleError' is assigned a value but never used      no-unused-vars

src\pages\cavi\ReportCaviPage.js
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars

src\pages\cavi\ReportCaviPageNew.js
  Line 16:3:   'Tooltip' is defined but never used                       no-unused-vars
  Line 35:15:  'PieChartIcon' is defined but never used                  no-unused-vars
  Line 42:16:  'DateRangeIcon' is defined but never used                 no-unused-vars
  Line 64:11:  'user' is assigned a value but never used                 no-unused-vars
  Line 161:9:  'reportTypes' is assigned a value but never used          no-unused-vars
  Line 279:9:  'handleReportSelect' is assigned a value but never used   no-unused-vars
  Line 321:9:  'renderReportContent' is assigned a value but never used  no-unused-vars

src\pages\cavi\TestCaviPage.js
  Line 1:27:  'useEffect' is defined but never used  no-unused-vars

src\pages\cavi\VisualizzaCaviPage.js
  Line 8:3:    'Card' is defined but never used                                                                                       no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                no-unused-vars
  Line 11:3:   'IconButton' is defined but never used                                                                                 no-unused-vars
  Line 21:8:   'InfoIcon' is defined but never used                                                                                   no-unused-vars
  Line 28:8:   'CavoForm' is defined but never used                                                                                   no-unused-vars
  Line 34:11:  'isImpersonating' is assigned a value but never used                                                                   no-unused-vars
  Line 36:9:   'navigate' is assigned a value but never used                                                                          no-unused-vars
  Line 38:10:  'cantiereName' is assigned a value but never used                                                                      no-unused-vars
  Line 76:10:  'statiInstallazione' is assigned a value but never used                                                                no-unused-vars
  Line 77:10:  'tipologieCavi' is assigned a value but never used                                                                     no-unused-vars
  Line 484:6:  React Hook useEffect has missing dependencies: '[1m[31merror[39m[22m[1m' and 'user'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 489:9:  'handleOpenDetails' is assigned a value but never used                                                                 no-unused-vars
  Line 583:9:  'renderStatsPanel' is assigned a value but never used                                                                  no-unused-vars

src\pages\cavi\parco\EliminaBobinaPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\ModificaBobinaPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\StoricoUtilizzoPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\posa\CollegamentiPage.js
  Line 5:3:    'Paper' is defined but never used                          no-unused-vars
  Line 6:3:    'Button' is defined but never used                         no-unused-vars
  Line 14:11:  'HomeIcon' is defined but never used                       no-unused-vars
  Line 23:11:  'isImpersonating' is assigned a value but never used       no-unused-vars
  Line 30:9:   'cantiereName' is assigned a value but never used          no-unused-vars
  Line 33:9:   'handleBackToCantieri' is assigned a value but never used  no-unused-vars
  Line 38:9:   'handleBackToAdmin' is assigned a value but never used     no-unused-vars

src\pages\cavi\posa\MetriPosatiSemplificatoPage.js
  Line 27:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\posa\ModificaBobinaPage.js
  Line 5:3:    'Paper' is defined but never used                          no-unused-vars
  Line 6:3:    'Button' is defined but never used                         no-unused-vars
  Line 14:11:  'HomeIcon' is defined but never used                       no-unused-vars
  Line 23:11:  'isImpersonating' is assigned a value but never used       no-unused-vars
  Line 30:9:   'cantiereName' is assigned a value but never used          no-unused-vars
  Line 33:9:   'handleBackToCantieri' is assigned a value but never used  no-unused-vars
  Line 38:9:   'handleBackToAdmin' is assigned a value but never used     no-unused-vars

src\pages\cavi\posa\ModificaCavoPage.js
  Line 20:11:  'isImpersonating' is assigned a value but never used    no-unused-vars
  Line 27:9:   'cantiereName' is assigned a value but never used       no-unused-vars
  Line 35:9:   'handleBackToAdmin' is assigned a value but never used  no-unused-vars

src\services\adminService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\authService.js
  Line 78:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown  no-throw-literal
  Line 80:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown  no-throw-literal
  Line 86:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown  no-throw-literal
  Line 89:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown  no-throw-literal

src\services\cantieriService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\caviService.js
  Line 260:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 274:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 278:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 333:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 435:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 451:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 668:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 677:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 681:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 755:17:  'token' is assigned a value but never used  no-unused-vars
  Line 775:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 794:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 801:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 810:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 817:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 885:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 955:3:   [1m[31mDuplicate[39m[22m[1m key 'updateCavoForCompatibility'  no-dupe-keys
  Line 1200:3:  [1m[31mDuplicate[39m[22m[1m key 'getCaviInstallati'           no-dupe-keys
  Line 1249:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 1279:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 1332:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 1374:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal

src\services\certificazioneService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\comandeService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\parcoCaviService.js
  Line 1:8:    'axios' is defined but never used              no-unused-vars
  Line 5:7:    'API_URL' is assigned a value but never used   no-unused-vars
  Line 83:13:  'sentData' is assigned a value but never used  no-unused-vars
  Line 109:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 123:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 127:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 212:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 226:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 230:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 271:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 280:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 284:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 320:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 324:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 360:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 369:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 373:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 450:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 459:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 463:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal

src\services\reportService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\userService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

[39m[22m

webpack compiled with [1m[33m1 warning[39m[22m
Compiling...
Compiled with warnings.

[eslint] 
src\components\TopNavbar.js
  Line 13:3:   'Avatar' is defined but never used                         no-unused-vars
  Line 20:25:  'AdminIcon' is defined but never used                      no-unused-vars
  Line 21:19:  'ConstructionIcon' is defined but never used               no-unused-vars
  Line 22:12:  'CableIcon' is defined but never used                      no-unused-vars
  Line 23:18:  'ReportIcon' is defined but never used                     no-unused-vars
  Line 56:10:  'homeAnchorEl' is assigned a value but never used          no-unused-vars
  Line 57:10:  'adminAnchorEl' is assigned a value but never used         no-unused-vars
  Line 58:10:  'cantieriAnchorEl' is assigned a value but never used      no-unused-vars
  Line 59:10:  'caviAnchorEl' is assigned a value but never used          no-unused-vars
  Line 68:9:   'selectedCantiereName' is assigned a value but never used  no-unused-vars

src\components\admin\UserExpirationChecker.js
  Line 11:10:  'lastCheck' is assigned a value but never used  no-unused-vars

src\components\cavi\CavoForm.js
  Line 6:3:   'Stack' is defined but never used    no-unused-vars
  Line 11:3:  'Divider' is defined but never used  no-unused-vars

src\components\cavi\CertificazioneCavi.js
  Line 8:3:    'Card' is defined but never used                                                                                       no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                                no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                               no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                             no-unused-vars
  Line 37:10:  'AddIcon' is defined but never used                                                                                    no-unused-vars
  Line 38:11:  'EditIcon' is defined but never used                                                                                   no-unused-vars
  Line 45:15:  'ViewListIcon' is defined but never used                                                                               no-unused-vars
  Line 46:12:  'BuildIcon' is defined but never used                                                                                  no-unused-vars
  Line 122:6:  React Hook useEffect has a missing dependency: 'loadCertificazioni'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\cavi\CollegamentiCavo.js
  Line 8:3:   'List' is defined but never used                                                                             no-unused-vars
  Line 9:3:   'ListItem' is defined but never used                                                                         no-unused-vars
  Line 10:3:  'ListItemText' is defined but never used                                                                     no-unused-vars
  Line 46:6:  React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\cavi\ExcelPopup.js
  Line 10:3:   'TextField' is defined but never used          no-unused-vars
  Line 14:3:   'Divider' is defined but never used            no-unused-vars
  Line 27:10:  'filePath' is assigned a value but never used  no-unused-vars

src\components\cavi\GestioneComande.js
  Line 8:3:    'Card' is defined but never used                                                                                no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                         no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                         no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                        no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                      no-unused-vars
  Line 25:3:   'Divider' is defined but never used                                                                             no-unused-vars
  Line 37:10:  'AddIcon' is defined but never used                                                                             no-unused-vars
  Line 41:13:  'SearchIcon' is defined but never used                                                                          no-unused-vars
  Line 43:15:  'ViewListIcon' is defined but never used                                                                        no-unused-vars
  Line 44:17:  'AssignmentIcon' is defined but never used                                                                      no-unused-vars
  Line 98:6:   React Hook useEffect has a missing dependency: 'loadComande'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 101:9:  'handleOptionSelect' is assigned a value but never used                                                         no-unused-vars

src\components\cavi\IncompatibleReelDialog.js
  Line 17:3:  'Alert' is defined but never used  no-unused-vars

src\components\cavi\InserisciMetriForm.js
  Line 17:3:     'FormHelperText' is defined but never used                                                                     no-unused-vars
  Line 34:3:     'ListItemText' is defined but never used                                                                       no-unused-vars
  Line 35:3:     'ListItemSecondaryAction' is defined but never used                                                            no-unused-vars
  Line 39:11:    'SaveIcon' is defined but never used                                                                           no-unused-vars
  Line 51:3:     'CABLE_STATES' is defined but never used                                                                       no-unused-vars
  Line 52:3:     'REEL_STATES' is defined but never used                                                                        no-unused-vars
  Line 54:3:     'determineReelState' is defined but never used                                                                 no-unused-vars
  Line 55:3:     'canModifyCable' is defined but never used                                                                     no-unused-vars
  Line 62:10:    'redirectToVisualizzaCavi' is defined but never used                                                           no-unused-vars
  Line 105:10:   'incompatibleReel' is assigned a value but never used                                                          no-unused-vars
  Line 105:28:   'setIncompatibleReel' is assigned a value but never used                                                       no-unused-vars
  Line 145:6:    React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 701:9:    'handleBack' is assigned a value but never used                                                                no-unused-vars
  Line 1311:11:  'buildFullBobinaId' is assigned a value but never used                                                         no-unused-vars
  Line 1316:11:  'hasSufficientMeters' is assigned a value but never used                                                       no-unused-vars
  Line 1883:9:   'getStepContent' is assigned a value but never used                                                            no-unused-vars

src\components\cavi\MetriPosatiSemplificatoForm.js
  Line 33:3:   'CABLE_STATES' is defined but never used                                                                                        no-unused-vars
  Line 34:3:   'REEL_STATES' is defined but never used                                                                                         no-unused-vars
  Line 35:3:   'determineCableState' is defined but never used                                                                                 no-unused-vars
  Line 36:3:   'determineReelState' is defined but never used                                                                                  no-unused-vars
  Line 37:3:   'canModifyCable' is defined but never used                                                                                      no-unused-vars
  Line 41:3:   'getReelStateColor' is defined but never used                                                                                   no-unused-vars
  Line 43:10:  'redirectToVisualizzaCavi' is defined but never used                                                                            no-unused-vars
  Line 69:10:  'loading' is assigned a value but never used                                                                                    no-unused-vars
  Line 69:19:  'setLoading' is assigned a value but never used                                                                                 no-unused-vars
  Line 88:6:   React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 448:6:  React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array       react-hooks/exhaustive-deps

src\components\cavi\ModificaBobinaForm.js
  Line 9:3:     'FormControl' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 10:3:    'InputLabel' is defined but never used                                                                                                                                                                                         no-unused-vars
  Line 11:3:    'Select' is defined but never used                                                                                                                                                                                             no-unused-vars
  Line 12:3:    'MenuItem' is defined but never used                                                                                                                                                                                           no-unused-vars
  Line 33:3:    'ListItemText' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 35:3:    'ListItemSecondaryAction' is defined but never used                                                                                                                                                                            no-unused-vars
  Line 42:14:   'WarningIcon' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 52:3:    'CABLE_STATES' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 53:3:    'REEL_STATES' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 54:3:    'determineCableState' is defined but never used                                                                                                                                                                                no-unused-vars
  Line 55:3:    'determineReelState' is defined but never used                                                                                                                                                                                 no-unused-vars
  Line 56:3:    'canModifyCable' is defined but never used                                                                                                                                                                                     no-unused-vars
  Line 57:3:    'isCableSpare' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 58:3:    'isCableInstalled' is defined but never used                                                                                                                                                                                   no-unused-vars
  Line 59:3:    'getCableStateColor' is defined but never used                                                                                                                                                                                 no-unused-vars
  Line 72:9:    'navigate' is assigned a value but never used                                                                                                                                                                                  no-unused-vars
  Line 79:10:   'searchResults' is assigned a value but never used                                                                                                                                                                             no-unused-vars
  Line 79:25:   'setSearchResults' is assigned a value but never used                                                                                                                                                                          no-unused-vars
  Line 80:10:   'showSearchResults' is assigned a value but never used                                                                                                                                                                         no-unused-vars
  Line 85:10:   'compatibleBobine' is assigned a value but never used                                                                                                                                                                          no-unused-vars
  Line 105:6:   React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array                                                                                                                    react-hooks/exhaustive-deps
  Line 112:6:   React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array                                                                                                                  react-hooks/exhaustive-deps
  Line 127:6:   React Hook useEffect has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 283:13:  'bobina' is assigned a value but never used                                                                                                                                                                                    no-unused-vars

src\components\cavi\ParcoCavi.js
  Line 8:3:    'Card' is defined but never used                                                                                                                            no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                                                     no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                                                                     no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                                                                    no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                                                                  no-unused-vars
  Line 25:3:   'Divider' is defined but never used                                                                                                                         no-unused-vars
  Line 29:3:   'IconButton' is defined but never used                                                                                                                      no-unused-vars
  Line 39:11:  'EditIcon' is defined but never used                                                                                                                        no-unused-vars
  Line 43:15:  'ViewListIcon' is defined but never used                                                                                                                    no-unused-vars
  Line 44:14:  'WarningIcon' is defined but never used                                                                                                                     no-unused-vars
  Line 50:69:  'isEmpty' is defined but never used                                                                                                                         no-unused-vars
  Line 79:10:  'isFirstInsertion' is assigned a value but never used                                                                                                       no-unused-vars
  Line 145:6:  React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 689:9:  'renderBobineCards' is assigned a value but never used                                                                                                      no-unused-vars

src\components\cavi\PosaCaviCollegamenti.js
  Line 20:3:   'InputLabel' is defined but never used             no-unused-vars
  Line 21:3:   'Select' is defined but never used                 no-unused-vars
  Line 22:3:   'MenuItem' is defined but never used               no-unused-vars
  Line 23:3:   'Grid' is defined but never used                   no-unused-vars
  Line 26:3:   'FormHelperText' is defined but never used         no-unused-vars
  Line 69:10:  'formWarnings' is assigned a value but never used  no-unused-vars
  Line 466:9:  Unreachable code                                   no-unreachable

src\components\cavi\QuickAddCablesDialog.js
  Line 12:3:    'FormControlLabel' is defined but never used                                                                 no-unused-vars
  Line 27:10:   'AddIcon' is defined but never used                                                                          no-unused-vars
  Line 30:11:   'InfoIcon' is defined but never used                                                                         no-unused-vars
  Line 34:10:   'determineCableState' is defined but never used                                                              no-unused-vars
  Line 49:10:   'loading' is assigned a value but never used                                                                 no-unused-vars
  Line 49:19:   'setLoading' is assigned a value but never used                                                              no-unused-vars
  Line 64:6:    React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 270:17:  'result' is assigned a value but never used                                                                  no-unused-vars

src\components\cavi\ReportCavi.js
  Line 7:3:    'Grid' is defined but never used                         no-unused-vars
  Line 20:3:   'Divider' is defined but never used                      no-unused-vars
  Line 21:3:   'List' is defined but never used                         no-unused-vars
  Line 22:3:   'ListItem' is defined but never used                     no-unused-vars
  Line 23:3:   'ListItemText' is defined but never used                 no-unused-vars
  Line 24:3:   'ListItemIcon' is defined but never used                 no-unused-vars
  Line 25:3:   'ListItemButton' is defined but never used               no-unused-vars
  Line 29:15:  'BarChartIcon' is defined but never used                 no-unused-vars
  Line 34:16:  'DateRangeIcon' is defined but never used                no-unused-vars
  Line 53:9:   'handleOptionSelect' is assigned a value but never used  no-unused-vars

src\components\cavi\SelezionaCavoForm.js
  Line 4:3:  'TextField' is defined but never used  no-unused-vars

src\components\certificazioni\CertificazioneForm.jsx
  Line 54:6:  React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\certificazioni\CertificazioniList.jsx
  Line 25:13:  'DownloadIcon' is defined but never used  no-unused-vars

src\components\charts\BoqChart.js
  Line 14:3:  'ComposedChart' is defined but never used  no-unused-vars
  Line 15:3:  'Line' is defined but never used           no-unused-vars
  Line 16:3:  'LineChart' is defined but never used      no-unused-vars

src\components\charts\CaviStatoChart.js
  Line 14:3:  'ComposedChart' is defined but never used  no-unused-vars
  Line 15:3:  'Line' is defined but never used           no-unused-vars

src\components\charts\TimelineChart.js
  Line 3:3:  'LineChart' is defined but never used  no-unused-vars

src\components\common\ExcelLikeFilter.js
  Line 16:3:  'MenuItem' is defined but never used    no-unused-vars
  Line 17:3:  'Select' is defined but never used      no-unused-vars
  Line 19:3:  'InputLabel' is defined but never used  no-unused-vars

src\pages\CaviPage.js
  Line 1:8:  'React' is defined but never used  no-unused-vars

src\pages\CertificazioniPageDebug.jsx
  Line 49:19:  'useAuth' is assigned a value but never used  no-unused-vars

src\pages\Dashboard.js
  Line 38:8:   'RedirectToCaviVisualizza' is defined but never used  no-unused-vars
  Line 44:11:  'user' is assigned a value but never used             no-unused-vars

src\pages\HomePage.js
  Line 2:27:  'Grid' is defined but never used                 no-unused-vars
  Line 2:33:  'Card' is defined but never used                 no-unused-vars
  Line 2:39:  'CardContent' is defined but never used          no-unused-vars
  Line 2:52:  'CardActionArea' is defined but never used       no-unused-vars
  Line 2:68:  'Avatar' is defined but never used               no-unused-vars
  Line 5:25:  'AdminIcon' is defined but never used            no-unused-vars
  Line 6:19:  'ConstructionIcon' is defined but never used     no-unused-vars
  Line 7:12:  'CableIcon' is defined but never used            no-unused-vars
  Line 8:18:  'ReportIcon' is defined but never used           no-unused-vars
  Line 43:9:  'navigateTo' is assigned a value but never used  no-unused-vars

src\pages\cantieri\CantierePage.js
  Line 24:11:  'isImpersonating' is assigned a value but never used                                                               no-unused-vars
  Line 53:6:   React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\pages\cavi\CertificazioneCaviPage.js
  Line 5:3:    'Paper' is defined but never used                     no-unused-vars
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars
  Line 28:9:   'cantiereName' is assigned a value but never used     no-unused-vars

src\pages\cavi\GestioneComandeePage.js
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars

src\pages\cavi\ParcoCaviPage.js
  Line 9:3:    'Card' is defined but never used                      no-unused-vars
  Line 10:3:   'CardContent' is defined but never used               no-unused-vars
  Line 11:3:   'CardActions' is defined but never used               no-unused-vars
  Line 12:3:   'Grid' is defined but never used                      no-unused-vars
  Line 13:3:   'Divider' is defined but never used                   no-unused-vars
  Line 18:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 19:15:  'ViewListIcon' is defined but never used              no-unused-vars
  Line 20:10:  'AddIcon' is defined but never used                   no-unused-vars
  Line 21:11:  'EditIcon' is defined but never used                  no-unused-vars
  Line 22:13:  'DeleteIcon' is defined but never used                no-unused-vars
  Line 23:14:  'HistoryIcon' is defined but never used               no-unused-vars
  Line 28:8:   'ParcoCavi' is defined but never used                 no-unused-vars
  Line 31:11:  'isImpersonating' is assigned a value but never used  no-unused-vars
  Line 51:9:   'handleSuccess' is assigned a value but never used    no-unused-vars
  Line 56:9:   'handleError' is assigned a value but never used      no-unused-vars

src\pages\cavi\ReportCaviPage.js
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars

src\pages\cavi\ReportCaviPageNew.js
  Line 16:3:   'Tooltip' is defined but never used                       no-unused-vars
  Line 35:15:  'PieChartIcon' is defined but never used                  no-unused-vars
  Line 42:16:  'DateRangeIcon' is defined but never used                 no-unused-vars
  Line 64:11:  'user' is assigned a value but never used                 no-unused-vars
  Line 161:9:  'reportTypes' is assigned a value but never used          no-unused-vars
  Line 279:9:  'handleReportSelect' is assigned a value but never used   no-unused-vars
  Line 321:9:  'renderReportContent' is assigned a value but never used  no-unused-vars

src\pages\cavi\TestCaviPage.js
  Line 1:27:  'useEffect' is defined but never used  no-unused-vars

src\pages\cavi\VisualizzaCaviPage.js
  Line 8:3:    'Card' is defined but never used                                                                                       no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                no-unused-vars
  Line 11:3:   'IconButton' is defined but never used                                                                                 no-unused-vars
  Line 21:8:   'InfoIcon' is defined but never used                                                                                   no-unused-vars
  Line 28:8:   'CavoForm' is defined but never used                                                                                   no-unused-vars
  Line 34:11:  'isImpersonating' is assigned a value but never used                                                                   no-unused-vars
  Line 36:9:   'navigate' is assigned a value but never used                                                                          no-unused-vars
  Line 38:10:  'cantiereName' is assigned a value but never used                                                                      no-unused-vars
  Line 76:10:  'statiInstallazione' is assigned a value but never used                                                                no-unused-vars
  Line 77:10:  'tipologieCavi' is assigned a value but never used                                                                     no-unused-vars
  Line 484:6:  React Hook useEffect has missing dependencies: 'error' and 'user'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 489:9:  'handleOpenDetails' is assigned a value but never used                                                                 no-unused-vars
  Line 583:9:  'renderStatsPanel' is assigned a value but never used                                                                  no-unused-vars

src\pages\cavi\parco\EliminaBobinaPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\ModificaBobinaPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\StoricoUtilizzoPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\posa\CollegamentiPage.js
  Line 5:3:    'Paper' is defined but never used                          no-unused-vars
  Line 6:3:    'Button' is defined but never used                         no-unused-vars
  Line 14:11:  'HomeIcon' is defined but never used                       no-unused-vars
  Line 23:11:  'isImpersonating' is assigned a value but never used       no-unused-vars
  Line 30:9:   'cantiereName' is assigned a value but never used          no-unused-vars
  Line 33:9:   'handleBackToCantieri' is assigned a value but never used  no-unused-vars
  Line 38:9:   'handleBackToAdmin' is assigned a value but never used     no-unused-vars

src\pages\cavi\posa\MetriPosatiSemplificatoPage.js
  Line 27:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\posa\ModificaBobinaPage.js
  Line 5:3:    'Paper' is defined but never used                          no-unused-vars
  Line 6:3:    'Button' is defined but never used                         no-unused-vars
  Line 14:11:  'HomeIcon' is defined but never used                       no-unused-vars
  Line 23:11:  'isImpersonating' is assigned a value but never used       no-unused-vars
  Line 30:9:   'cantiereName' is assigned a value but never used          no-unused-vars
  Line 33:9:   'handleBackToCantieri' is assigned a value but never used  no-unused-vars
  Line 38:9:   'handleBackToAdmin' is assigned a value but never used     no-unused-vars

src\pages\cavi\posa\ModificaCavoPage.js
  Line 20:11:  'isImpersonating' is assigned a value but never used    no-unused-vars
  Line 27:9:   'cantiereName' is assigned a value but never used       no-unused-vars
  Line 35:9:   'handleBackToAdmin' is assigned a value but never used  no-unused-vars

src\services\adminService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\authService.js
  Line 78:11:  Expected an error object to be thrown  no-throw-literal
  Line 80:11:  Expected an error object to be thrown  no-throw-literal
  Line 86:9:   Expected an error object to be thrown  no-throw-literal
  Line 89:9:   Expected an error object to be thrown  no-throw-literal

src\services\cantieriService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\caviService.js
  Line 260:9:   Expected an error object to be thrown       no-throw-literal
  Line 274:9:   Expected an error object to be thrown       no-throw-literal
  Line 278:9:   Expected an error object to be thrown       no-throw-literal
  Line 333:11:  Expected an error object to be thrown       no-throw-literal
  Line 435:9:   Expected an error object to be thrown       no-throw-literal
  Line 451:9:   Expected an error object to be thrown       no-throw-literal
  Line 668:9:   Expected an error object to be thrown       no-throw-literal
  Line 677:9:   Expected an error object to be thrown       no-throw-literal
  Line 681:9:   Expected an error object to be thrown       no-throw-literal
  Line 755:17:  'token' is assigned a value but never used  no-unused-vars
  Line 775:9:   Expected an error object to be thrown       no-throw-literal
  Line 794:11:  Expected an error object to be thrown       no-throw-literal
  Line 801:9:   Expected an error object to be thrown       no-throw-literal
  Line 810:11:  Expected an error object to be thrown       no-throw-literal
  Line 817:9:   Expected an error object to be thrown       no-throw-literal
  Line 885:9:   Expected an error object to be thrown       no-throw-literal
  Line 955:3:   Duplicate key 'updateCavoForCompatibility'  no-dupe-keys
  Line 1200:3:  Duplicate key 'getCaviInstallati'           no-dupe-keys
  Line 1249:9:  Expected an error object to be thrown       no-throw-literal
  Line 1279:9:  Expected an error object to be thrown       no-throw-literal
  Line 1332:9:  Expected an error object to be thrown       no-throw-literal
  Line 1374:9:  Expected an error object to be thrown       no-throw-literal

src\services\certificazioneService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\comandeService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\parcoCaviService.js
  Line 1:8:    'axios' is defined but never used              no-unused-vars
  Line 5:7:    'API_URL' is assigned a value but never used   no-unused-vars
  Line 83:13:  'sentData' is assigned a value but never used  no-unused-vars
  Line 109:9:  Expected an error object to be thrown          no-throw-literal
  Line 123:9:  Expected an error object to be thrown          no-throw-literal
  Line 127:9:  Expected an error object to be thrown          no-throw-literal
  Line 212:9:  Expected an error object to be thrown          no-throw-literal
  Line 226:9:  Expected an error object to be thrown          no-throw-literal
  Line 230:9:  Expected an error object to be thrown          no-throw-literal
  Line 271:9:  Expected an error object to be thrown          no-throw-literal
  Line 280:9:  Expected an error object to be thrown          no-throw-literal
  Line 284:9:  Expected an error object to be thrown          no-throw-literal
  Line 320:9:  Expected an error object to be thrown          no-throw-literal
  Line 324:9:  Expected an error object to be thrown          no-throw-literal
  Line 360:9:  Expected an error object to be thrown          no-throw-literal
  Line 369:9:  Expected an error object to be thrown          no-throw-literal
  Line 373:9:  Expected an error object to be thrown          no-throw-literal
  Line 450:9:  Expected an error object to be thrown          no-throw-literal
  Line 459:9:  Expected an error object to be thrown          no-throw-literal
  Line 463:9:  Expected an error object to be thrown          no-throw-literal

src\services\reportService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\userService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

[1m[33mWARNING[39m[22m in [1m[eslint] 
src\components\TopNavbar.js
  Line 13:3:   'Avatar' is defined but never used                         no-unused-vars
  Line 20:25:  'AdminIcon' is defined but never used                      no-unused-vars
  Line 21:19:  'ConstructionIcon' is defined but never used               no-unused-vars
  Line 22:12:  'CableIcon' is defined but never used                      no-unused-vars
  Line 23:18:  'ReportIcon' is defined but never used                     no-unused-vars
  Line 56:10:  'homeAnchorEl' is assigned a value but never used          no-unused-vars
  Line 57:10:  'adminAnchorEl' is assigned a value but never used         no-unused-vars
  Line 58:10:  'cantieriAnchorEl' is assigned a value but never used      no-unused-vars
  Line 59:10:  'caviAnchorEl' is assigned a value but never used          no-unused-vars
  Line 68:9:   'selectedCantiereName' is assigned a value but never used  no-unused-vars

src\components\admin\UserExpirationChecker.js
  Line 11:10:  'lastCheck' is assigned a value but never used  no-unused-vars

src\components\cavi\CavoForm.js
  Line 6:3:   'Stack' is defined but never used    no-unused-vars
  Line 11:3:  'Divider' is defined but never used  no-unused-vars

src\components\cavi\CertificazioneCavi.js
  Line 8:3:    'Card' is defined but never used                                                                                       no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                                no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                               no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                             no-unused-vars
  Line 37:10:  'AddIcon' is defined but never used                                                                                    no-unused-vars
  Line 38:11:  'EditIcon' is defined but never used                                                                                   no-unused-vars
  Line 45:15:  'ViewListIcon' is defined but never used                                                                               no-unused-vars
  Line 46:12:  'BuildIcon' is defined but never used                                                                                  no-unused-vars
  Line 122:6:  React Hook useEffect has a missing dependency: 'loadCertificazioni'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\cavi\CollegamentiCavo.js
  Line 8:3:   'List' is defined but never used                                                                             no-unused-vars
  Line 9:3:   'ListItem' is defined but never used                                                                         no-unused-vars
  Line 10:3:  'ListItemText' is defined but never used                                                                     no-unused-vars
  Line 46:6:  React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\cavi\ExcelPopup.js
  Line 10:3:   'TextField' is defined but never used          no-unused-vars
  Line 14:3:   'Divider' is defined but never used            no-unused-vars
  Line 27:10:  'filePath' is assigned a value but never used  no-unused-vars

src\components\cavi\GestioneComande.js
  Line 8:3:    'Card' is defined but never used                                                                                no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                         no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                         no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                        no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                      no-unused-vars
  Line 25:3:   'Divider' is defined but never used                                                                             no-unused-vars
  Line 37:10:  'AddIcon' is defined but never used                                                                             no-unused-vars
  Line 41:13:  'SearchIcon' is defined but never used                                                                          no-unused-vars
  Line 43:15:  'ViewListIcon' is defined but never used                                                                        no-unused-vars
  Line 44:17:  'AssignmentIcon' is defined but never used                                                                      no-unused-vars
  Line 98:6:   React Hook useEffect has a missing dependency: 'loadComande'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 101:9:  'handleOptionSelect' is assigned a value but never used                                                         no-unused-vars

src\components\cavi\IncompatibleReelDialog.js
  Line 17:3:  'Alert' is defined but never used  no-unused-vars

src\components\cavi\InserisciMetriForm.js
  Line 17:3:     'FormHelperText' is defined but never used                                                                     no-unused-vars
  Line 34:3:     'ListItemText' is defined but never used                                                                       no-unused-vars
  Line 35:3:     'ListItemSecondaryAction' is defined but never used                                                            no-unused-vars
  Line 39:11:    'SaveIcon' is defined but never used                                                                           no-unused-vars
  Line 51:3:     'CABLE_STATES' is defined but never used                                                                       no-unused-vars
  Line 52:3:     'REEL_STATES' is defined but never used                                                                        no-unused-vars
  Line 54:3:     'determineReelState' is defined but never used                                                                 no-unused-vars
  Line 55:3:     'canModifyCable' is defined but never used                                                                     no-unused-vars
  Line 62:10:    'redirectToVisualizzaCavi' is defined but never used                                                           no-unused-vars
  Line 105:10:   'incompatibleReel' is assigned a value but never used                                                          no-unused-vars
  Line 105:28:   'setIncompatibleReel' is assigned a value but never used                                                       no-unused-vars
  Line 145:6:    React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 701:9:    'handleBack' is assigned a value but never used                                                                no-unused-vars
  Line 1311:11:  'buildFullBobinaId' is assigned a value but never used                                                         no-unused-vars
  Line 1316:11:  'hasSufficientMeters' is assigned a value but never used                                                       no-unused-vars
  Line 1883:9:   'getStepContent' is assigned a value but never used                                                            no-unused-vars

src\components\cavi\MetriPosatiSemplificatoForm.js
  Line 33:3:   'CABLE_STATES' is defined but never used                                                                                        no-unused-vars
  Line 34:3:   'REEL_STATES' is defined but never used                                                                                         no-unused-vars
  Line 35:3:   'determineCableState' is defined but never used                                                                                 no-unused-vars
  Line 36:3:   'determineReelState' is defined but never used                                                                                  no-unused-vars
  Line 37:3:   'canModifyCable' is defined but never used                                                                                      no-unused-vars
  Line 41:3:   'getReelStateColor' is defined but never used                                                                                   no-unused-vars
  Line 43:10:  'redirectToVisualizzaCavi' is defined but never used                                                                            no-unused-vars
  Line 69:10:  'loading' is assigned a value but never used                                                                                    no-unused-vars
  Line 69:19:  'setLoading' is assigned a value but never used                                                                                 no-unused-vars
  Line 88:6:   React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 448:6:  React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array       react-hooks/exhaustive-deps

src\components\cavi\ModificaBobinaForm.js
  Line 9:3:     'FormControl' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 10:3:    'InputLabel' is defined but never used                                                                                                                                                                                         no-unused-vars
  Line 11:3:    'Select' is defined but never used                                                                                                                                                                                             no-unused-vars
  Line 12:3:    'MenuItem' is defined but never used                                                                                                                                                                                           no-unused-vars
  Line 33:3:    'ListItemText' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 35:3:    'ListItemSecondaryAction' is defined but never used                                                                                                                                                                            no-unused-vars
  Line 42:14:   'WarningIcon' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 52:3:    'CABLE_STATES' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 53:3:    'REEL_STATES' is defined but never used                                                                                                                                                                                        no-unused-vars
  Line 54:3:    'determineCableState' is defined but never used                                                                                                                                                                                no-unused-vars
  Line 55:3:    'determineReelState' is defined but never used                                                                                                                                                                                 no-unused-vars
  Line 56:3:    'canModifyCable' is defined but never used                                                                                                                                                                                     no-unused-vars
  Line 57:3:    'isCableSpare' is defined but never used                                                                                                                                                                                       no-unused-vars
  Line 58:3:    'isCableInstalled' is defined but never used                                                                                                                                                                                   no-unused-vars
  Line 59:3:    'getCableStateColor' is defined but never used                                                                                                                                                                                 no-unused-vars
  Line 72:9:    'navigate' is assigned a value but never used                                                                                                                                                                                  no-unused-vars
  Line 79:10:   'searchResults' is assigned a value but never used                                                                                                                                                                             no-unused-vars
  Line 79:25:   'setSearchResults' is assigned a value but never used                                                                                                                                                                          no-unused-vars
  Line 80:10:   'showSearchResults' is assigned a value but never used                                                                                                                                                                         no-unused-vars
  Line 85:10:   'compatibleBobine' is assigned a value but never used                                                                                                                                                                          no-unused-vars
  Line 105:6:   React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array                                                                                                                    react-hooks/exhaustive-deps
  Line 112:6:   React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array                                                                                                                  react-hooks/exhaustive-deps
  Line 127:6:   React Hook useEffect has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 283:13:  'bobina' is assigned a value but never used                                                                                                                                                                                    no-unused-vars

src\components\cavi\ParcoCavi.js
  Line 8:3:    'Card' is defined but never used                                                                                                                            no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                                                     no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                                                                     no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                                                                    no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                                                                  no-unused-vars
  Line 25:3:   'Divider' is defined but never used                                                                                                                         no-unused-vars
  Line 29:3:   'IconButton' is defined but never used                                                                                                                      no-unused-vars
  Line 39:11:  'EditIcon' is defined but never used                                                                                                                        no-unused-vars
  Line 43:15:  'ViewListIcon' is defined but never used                                                                                                                    no-unused-vars
  Line 44:14:  'WarningIcon' is defined but never used                                                                                                                     no-unused-vars
  Line 50:69:  'isEmpty' is defined but never used                                                                                                                         no-unused-vars
  Line 79:10:  'isFirstInsertion' is assigned a value but never used                                                                                                       no-unused-vars
  Line 145:6:  React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 689:9:  'renderBobineCards' is assigned a value but never used                                                                                                      no-unused-vars

src\components\cavi\PosaCaviCollegamenti.js
  Line 20:3:   'InputLabel' is defined but never used             no-unused-vars
  Line 21:3:   'Select' is defined but never used                 no-unused-vars
  Line 22:3:   'MenuItem' is defined but never used               no-unused-vars
  Line 23:3:   'Grid' is defined but never used                   no-unused-vars
  Line 26:3:   'FormHelperText' is defined but never used         no-unused-vars
  Line 69:10:  'formWarnings' is assigned a value but never used  no-unused-vars
  Line 466:9:  Unreachable code                                   no-unreachable

src\components\cavi\QuickAddCablesDialog.js
  Line 12:3:    'FormControlLabel' is defined but never used                                                                 no-unused-vars
  Line 27:10:   'AddIcon' is defined but never used                                                                          no-unused-vars
  Line 30:11:   'InfoIcon' is defined but never used                                                                         no-unused-vars
  Line 34:10:   'determineCableState' is defined but never used                                                              no-unused-vars
  Line 49:10:   'loading' is assigned a value but never used                                                                 no-unused-vars
  Line 49:19:   'setLoading' is assigned a value but never used                                                              no-unused-vars
  Line 64:6:    React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 270:17:  'result' is assigned a value but never used                                                                  no-unused-vars

src\components\cavi\ReportCavi.js
  Line 7:3:    'Grid' is defined but never used                         no-unused-vars
  Line 20:3:   'Divider' is defined but never used                      no-unused-vars
  Line 21:3:   'List' is defined but never used                         no-unused-vars
  Line 22:3:   'ListItem' is defined but never used                     no-unused-vars
  Line 23:3:   'ListItemText' is defined but never used                 no-unused-vars
  Line 24:3:   'ListItemIcon' is defined but never used                 no-unused-vars
  Line 25:3:   'ListItemButton' is defined but never used               no-unused-vars
  Line 29:15:  'BarChartIcon' is defined but never used                 no-unused-vars
  Line 34:16:  'DateRangeIcon' is defined but never used                no-unused-vars
  Line 53:9:   'handleOptionSelect' is assigned a value but never used  no-unused-vars

src\components\cavi\SelezionaCavoForm.js
  Line 4:3:  'TextField' is defined but never used  no-unused-vars

src\components\certificazioni\CertificazioneForm.jsx
  Line 54:6:  React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\certificazioni\CertificazioniList.jsx
  Line 25:13:  'DownloadIcon' is defined but never used  no-unused-vars

src\components\charts\BoqChart.js
  Line 14:3:  'ComposedChart' is defined but never used  no-unused-vars
  Line 15:3:  'Line' is defined but never used           no-unused-vars
  Line 16:3:  'LineChart' is defined but never used      no-unused-vars

src\components\charts\CaviStatoChart.js
  Line 14:3:  'ComposedChart' is defined but never used  no-unused-vars
  Line 15:3:  'Line' is defined but never used           no-unused-vars

src\components\charts\TimelineChart.js
  Line 3:3:  'LineChart' is defined but never used  no-unused-vars

src\components\common\ExcelLikeFilter.js
  Line 16:3:  'MenuItem' is defined but never used    no-unused-vars
  Line 17:3:  'Select' is defined but never used      no-unused-vars
  Line 19:3:  'InputLabel' is defined but never used  no-unused-vars

src\pages\CaviPage.js
  Line 1:8:  'React' is defined but never used  no-unused-vars

src\pages\CertificazioniPageDebug.jsx
  Line 49:19:  'useAuth' is assigned a value but never used  no-unused-vars

src\pages\Dashboard.js
  Line 38:8:   'RedirectToCaviVisualizza' is defined but never used  no-unused-vars
  Line 44:11:  'user' is assigned a value but never used             no-unused-vars

src\pages\HomePage.js
  Line 2:27:  'Grid' is defined but never used                 no-unused-vars
  Line 2:33:  'Card' is defined but never used                 no-unused-vars
  Line 2:39:  'CardContent' is defined but never used          no-unused-vars
  Line 2:52:  'CardActionArea' is defined but never used       no-unused-vars
  Line 2:68:  'Avatar' is defined but never used               no-unused-vars
  Line 5:25:  'AdminIcon' is defined but never used            no-unused-vars
  Line 6:19:  'ConstructionIcon' is defined but never used     no-unused-vars
  Line 7:12:  'CableIcon' is defined but never used            no-unused-vars
  Line 8:18:  'ReportIcon' is defined but never used           no-unused-vars
  Line 43:9:  'navigateTo' is assigned a value but never used  no-unused-vars

src\pages\cantieri\CantierePage.js
  Line 24:11:  'isImpersonating' is assigned a value but never used                                                               no-unused-vars
  Line 53:6:   React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\pages\cavi\CertificazioneCaviPage.js
  Line 5:3:    'Paper' is defined but never used                     no-unused-vars
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars
  Line 28:9:   'cantiereName' is assigned a value but never used     no-unused-vars

src\pages\cavi\GestioneComandeePage.js
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars

src\pages\cavi\ParcoCaviPage.js
  Line 9:3:    'Card' is defined but never used                      no-unused-vars
  Line 10:3:   'CardContent' is defined but never used               no-unused-vars
  Line 11:3:   'CardActions' is defined but never used               no-unused-vars
  Line 12:3:   'Grid' is defined but never used                      no-unused-vars
  Line 13:3:   'Divider' is defined but never used                   no-unused-vars
  Line 18:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 19:15:  'ViewListIcon' is defined but never used              no-unused-vars
  Line 20:10:  'AddIcon' is defined but never used                   no-unused-vars
  Line 21:11:  'EditIcon' is defined but never used                  no-unused-vars
  Line 22:13:  'DeleteIcon' is defined but never used                no-unused-vars
  Line 23:14:  'HistoryIcon' is defined but never used               no-unused-vars
  Line 28:8:   'ParcoCavi' is defined but never used                 no-unused-vars
  Line 31:11:  'isImpersonating' is assigned a value but never used  no-unused-vars
  Line 51:9:   'handleSuccess' is assigned a value but never used    no-unused-vars
  Line 56:9:   'handleError' is assigned a value but never used      no-unused-vars

src\pages\cavi\ReportCaviPage.js
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars

src\pages\cavi\ReportCaviPageNew.js
  Line 16:3:   'Tooltip' is defined but never used                       no-unused-vars
  Line 35:15:  'PieChartIcon' is defined but never used                  no-unused-vars
  Line 42:16:  'DateRangeIcon' is defined but never used                 no-unused-vars
  Line 64:11:  'user' is assigned a value but never used                 no-unused-vars
  Line 161:9:  'reportTypes' is assigned a value but never used          no-unused-vars
  Line 279:9:  'handleReportSelect' is assigned a value but never used   no-unused-vars
  Line 321:9:  'renderReportContent' is assigned a value but never used  no-unused-vars

src\pages\cavi\TestCaviPage.js
  Line 1:27:  'useEffect' is defined but never used  no-unused-vars

src\pages\cavi\VisualizzaCaviPage.js
  Line 8:3:    'Card' is defined but never used                                                                                       no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                no-unused-vars
  Line 11:3:   'IconButton' is defined but never used                                                                                 no-unused-vars
  Line 21:8:   'InfoIcon' is defined but never used                                                                                   no-unused-vars
  Line 28:8:   'CavoForm' is defined but never used                                                                                   no-unused-vars
  Line 34:11:  'isImpersonating' is assigned a value but never used                                                                   no-unused-vars
  Line 36:9:   'navigate' is assigned a value but never used                                                                          no-unused-vars
  Line 38:10:  'cantiereName' is assigned a value but never used                                                                      no-unused-vars
  Line 76:10:  'statiInstallazione' is assigned a value but never used                                                                no-unused-vars
  Line 77:10:  'tipologieCavi' is assigned a value but never used                                                                     no-unused-vars
  Line 484:6:  React Hook useEffect has missing dependencies: '[1m[31merror[39m[22m[1m' and 'user'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 489:9:  'handleOpenDetails' is assigned a value but never used                                                                 no-unused-vars
  Line 583:9:  'renderStatsPanel' is assigned a value but never used                                                                  no-unused-vars

src\pages\cavi\parco\EliminaBobinaPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\ModificaBobinaPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\StoricoUtilizzoPage.js
  Line 5:3:   'Paper' is defined but never used                  no-unused-vars
  Line 31:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\posa\CollegamentiPage.js
  Line 5:3:    'Paper' is defined but never used                          no-unused-vars
  Line 6:3:    'Button' is defined but never used                         no-unused-vars
  Line 14:11:  'HomeIcon' is defined but never used                       no-unused-vars
  Line 23:11:  'isImpersonating' is assigned a value but never used       no-unused-vars
  Line 30:9:   'cantiereName' is assigned a value but never used          no-unused-vars
  Line 33:9:   'handleBackToCantieri' is assigned a value but never used  no-unused-vars
  Line 38:9:   'handleBackToAdmin' is assigned a value but never used     no-unused-vars

src\pages\cavi\posa\MetriPosatiSemplificatoPage.js
  Line 27:9:  'cantiereName' is assigned a value but never used  no-unused-vars

src\pages\cavi\posa\ModificaBobinaPage.js
  Line 5:3:    'Paper' is defined but never used                          no-unused-vars
  Line 6:3:    'Button' is defined but never used                         no-unused-vars
  Line 14:11:  'HomeIcon' is defined but never used                       no-unused-vars
  Line 23:11:  'isImpersonating' is assigned a value but never used       no-unused-vars
  Line 30:9:   'cantiereName' is assigned a value but never used          no-unused-vars
  Line 33:9:   'handleBackToCantieri' is assigned a value but never used  no-unused-vars
  Line 38:9:   'handleBackToAdmin' is assigned a value but never used     no-unused-vars

src\pages\cavi\posa\ModificaCavoPage.js
  Line 20:11:  'isImpersonating' is assigned a value but never used    no-unused-vars
  Line 27:9:   'cantiereName' is assigned a value but never used       no-unused-vars
  Line 35:9:   'handleBackToAdmin' is assigned a value but never used  no-unused-vars

src\services\adminService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\authService.js
  Line 78:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown  no-throw-literal
  Line 80:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown  no-throw-literal
  Line 86:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown  no-throw-literal
  Line 89:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown  no-throw-literal

src\services\cantieriService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\caviService.js
  Line 260:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 274:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 278:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 333:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 435:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 451:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 668:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 677:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 681:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 755:17:  'token' is assigned a value but never used  no-unused-vars
  Line 775:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 794:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 801:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 810:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 817:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 885:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 955:3:   [1m[31mDuplicate[39m[22m[1m key 'updateCavoForCompatibility'  no-dupe-keys
  Line 1200:3:  [1m[31mDuplicate[39m[22m[1m key 'getCaviInstallati'           no-dupe-keys
  Line 1249:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 1279:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 1332:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 1374:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal

src\services\certificazioneService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\comandeService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\parcoCaviService.js
  Line 1:8:    'axios' is defined but never used              no-unused-vars
  Line 5:7:    'API_URL' is assigned a value but never used   no-unused-vars
  Line 83:13:  'sentData' is assigned a value but never used  no-unused-vars
  Line 109:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 123:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 127:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 212:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 226:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 230:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 271:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 280:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 284:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 320:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 324:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 360:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 369:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 373:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 450:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 459:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 463:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal

src\services\reportService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\userService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

[39m[22m

webpack compiled with [1m[33m1 warning[39m[22m
