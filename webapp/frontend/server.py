import http.server
import socketserver
import os

# Porta su cui il server ascolterà
PORT = 8081

# Directory corrente
DIRECTORY = os.path.dirname(os.path.abspath(__file__))

class Handler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=DIRECTORY, **kwargs)

    def end_headers(self):
        # Aggiungi header CORS per consentire richieste da qualsiasi origine
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        super().end_headers()

# Crea il server
with socketserver.TCPServer(("", PORT), Handler) as httpd:
    print(f"Server in esecuzione su http://localhost:{PORT}")
    print(f"Per accedere all'applicazione React, apri http://localhost:3000")
    print(f"Nota: I file HTML statici sono stati rinominati con prefisso 'OBSOLETO_' e non sono più utilizzati")
    print("Premi Ctrl+C per terminare il server")

    # Avvia il server
    httpd.serve_forever()
