import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { CircularProgress, Box } from '@mui/material';

const ProtectedRoute = ({ children, requiredRole }) => {
  const { isAuthenticated, user, loading } = useAuth();
  const location = useLocation();

  console.log('ProtectedRoute - Stato autenticazione:', { isAuthenticated, loading, user });
  console.log('ProtectedRoute - Percorso corrente:', location.pathname);

  // Mostra un indicatore di caricamento mentre verifichiamo l'autenticazione
  if (loading) {
    console.log('ProtectedRoute - Caricamento in corso...');
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <CircularProgress />
      </Box>
    );
  }

  // Se l'utente non è autenticato, reindirizza alla pagina di login
  if (!isAuthenticated) {
    console.log('ProtectedRoute - Utente non autenticato, reindirizzamento a /login');
    // Non rimuoviamo il token qui, lo facciamo solo in AuthContext
    return <Navigate to="/login" replace state={{ from: location.pathname }} />;
  }

  // Se è richiesto un ruolo specifico, verifica che l'utente abbia quel ruolo
  if (requiredRole && user.role !== requiredRole) {
    console.log(`ProtectedRoute - Utente non ha il ruolo richiesto: ${requiredRole}, reindirizzamento a /dashboard`);
    // Reindirizza alla dashboard o a una pagina di accesso negato
    return <Navigate to="/dashboard" replace />;
  }

  // Restrizioni per utenti cantiere: possono accedere solo alla gestione cavi
  if (user.role === 'cantieri_user') {
    // Verifica se il percorso corrente non è relativo alla gestione cavi
    if (!location.pathname.includes('/dashboard/cavi')) {
      console.log('ProtectedRoute - Utente cantiere tenta di accedere a una pagina non autorizzata, reindirizzamento a /dashboard/cavi');
      return <Navigate to="/dashboard/cavi" replace />;
    }
  }

  console.log('ProtectedRoute - Accesso consentito');

  // Se l'utente è autenticato e ha il ruolo richiesto, mostra il contenuto protetto
  return children;
};

export default ProtectedRoute;
