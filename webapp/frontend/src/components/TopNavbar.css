.excel-style-menu {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.excel-style-menu .MuiToolbar-root {
  min-height: 60px;
  padding-left: 24px;
  padding-right: 24px;
  flex-wrap: nowrap;
  overflow-x: hidden;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.excel-style-menu .MuiToolbar-root::-webkit-scrollbar {
  display: none;
}

.excel-style-menu .MuiButton-root {
  text-transform: none;
  font-size: 1rem;
  padding: 8px 16px;
  min-width: auto;
  margin-right: 10px;
  font-weight: 500;
}

.excel-style-menu .MuiDivider-root {
  margin: 0 8px;
  height: 30px;
}

/* Stile per i menu a tendina */
.excel-style-submenu .MuiMenuItem-root {
  font-size: 1rem;
  min-height: 42px;
  padding: 8px 20px;
}

/* Stile per i sottomenu */
.excel-style-submenu {
  margin-top: 2px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Effetto hover sui pulsanti */
.excel-style-menu .MuiButton-root:hover {
  background-color: #e9ecef;
}

/* Stile per il pulsante attivo */
.excel-style-menu .active-button {
  background-color: #e9ecef;
  font-weight: 600;
  border-bottom: 2px solid #1976d2;
}

/* Stile per il menu item attivo */
.excel-style-submenu .active-item {
  background-color: #e9ecef;
  font-weight: 500;
}

/* Stile per le icone nei pulsanti */
.excel-style-menu .MuiButton-root .MuiSvgIcon-root {
  font-size: 1.3rem;
  margin-right: 6px;
}

/* Stile per il testo utente nella barra */
.excel-style-menu .MuiTypography-body2 {
  font-size: 1rem;
}
