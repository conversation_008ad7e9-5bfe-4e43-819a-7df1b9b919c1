import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  CircularProgress
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import userService from '../../services/userService';

const DatabaseView = () => {
  const [dbData, setDbData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Carica i dati del database
  const loadDbData = async () => {
    setLoading(true);
    try {
      console.log('Tentativo di caricamento dei dati del database raw...');
      const data = await userService.getDbRaw();
      console.log('Dati ricevuti:', data);
      setDbData(data);
      setError('');
    } catch (err) {
      console.error('Errore durante il caricamento dei dati:', err);
      setError(err.detail || err.message || 'Errore durante il caricamento dei dati del database');
    } finally {
      setLoading(false);
    }
  };

  // Carica i dati all'avvio del componente
  useEffect(() => {
    loadDbData();
  }, []);

  // Funzione per renderizzare una tabella generica
  const renderTable = (title, data, keyField) => {
    if (!data || data.length === 0) {
      return (
        <Accordion key={title}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle1">{title}</Typography>
            <Typography variant="caption" sx={{ ml: 2, color: 'text.secondary' }}>
              (Nessun dato)
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography>Nessun record presente in questa tabella.</Typography>
          </AccordionDetails>
        </Accordion>
      );
    }

    // Estrai le intestazioni dalla prima riga di dati
    const headers = Object.keys(data[0]);

    return (
      <Accordion key={title}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle1">{title}</Typography>
          <Typography variant="caption" sx={{ ml: 2, color: 'text.secondary' }}>
            ({data.length} record)
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
            <Table size="small" stickyHeader>
              <TableHead>
                <TableRow>
                  {headers.map((header) => (
                    <TableCell key={header}>
                      {header}
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {data.map((row, rowIndex) => (
                  <TableRow key={`${keyField ? row[keyField] : rowIndex}`}>
                    {headers.map((header) => {
                      let cellValue = row[header];

                      // Formatta i valori booleani
                      if (typeof cellValue === 'boolean') {
                        cellValue = cellValue ? 'Sì' : 'No';
                      }

                      // Tronca stringhe lunghe
                      const isLongText = typeof cellValue === 'string' && cellValue.length > 50;

                      return (
                        <TableCell key={`${rowIndex}-${header}`}>
                          {isLongText ? (
                            <Tooltip title={cellValue}>
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                {cellValue.substring(0, 50)}...
                                <InfoIcon fontSize="small" sx={{ ml: 0.5, color: 'text.secondary' }} />
                              </Box>
                            </Tooltip>
                          ) : (
                            cellValue === null ? 'NULL' : cellValue
                          )}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </AccordionDetails>
      </Accordion>
    );
  };

  // Definizione delle tabelle da visualizzare
  const tables = [
    { title: 'Tabella Utenti', key: 'users', idField: 'id_utente' },
    { title: 'Tabella Cantieri', key: 'cantieri', idField: 'id_cantiere' },
    { title: 'Tabella Cavi', key: 'cavi', idField: 'id_cavo' },
    { title: 'Tabella Parco Cavi (Bobine)', key: 'parco_cavi', idField: 'id_bobina' },
    { title: 'Tabella Strumenti Certificati', key: 'strumenti_certificati', idField: 'id_strumento' },
    { title: 'Tabella Certificazioni Cavi', key: 'certificazioni_cavi', idField: 'id_certificazione' }
  ];

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Visualizzazione Database Raw</Typography>
        <Button
          variant="outlined"
          startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}
          onClick={loadDbData}
          disabled={loading}
        >
          {loading ? 'Caricamento...' : 'Aggiorna'}
        </Button>
      </Box>

      {error && (
        <Typography color="error" sx={{ mb: 2 }}>
          {error}
        </Typography>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
          <Typography sx={{ ml: 2 }}>Caricamento dati...</Typography>
        </Box>
      ) : !dbData ? (
        <Typography>Nessun dato disponibile</Typography>
      ) : (
        <Box>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Questa visualizzazione mostra tutti i dati grezzi presenti nel database, senza filtri o elaborazioni.
            Espandi le sezioni per visualizzare il contenuto delle tabelle.
          </Typography>

          {tables.map((table) => (
            dbData[table.key] && renderTable(table.title, dbData[table.key], table.idField)
          ))}
        </Box>
      )}
    </Box>
  );
};

export default DatabaseView;
