import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Login as LoginIcon
} from '@mui/icons-material';
import userService from '../../services/userService';
import { useAuth } from '../../context/AuthContext';

const ImpersonateUser = () => {
  const navigate = useNavigate();
  const [users, setUsers] = useState([]);
  const [selectedUserId, setSelectedUserId] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const { impersonateUser } = useAuth();

  // Carica gli utenti
  const loadUsers = async () => {
    setLoading(true);
    try {
      const data = await userService.getUsers();
      // Filtra solo gli utenti attivi e non admin
      const activeUsers = data.filter(user => user.abilitato && user.ruolo !== 'owner');
      setUsers(activeUsers);
      setError('');
    } catch (err) {
      setError(err.detail || 'Errore durante il caricamento degli utenti');
    } finally {
      setLoading(false);
    }
  };

  // Carica gli utenti all'avvio del componente
  useEffect(() => {
    loadUsers();
  }, []);

  // Gestisce il cambio dell'utente selezionato
  const handleUserChange = (event) => {
    setSelectedUserId(event.target.value);
  };

  // Gestisce l'accesso come utente selezionato
  const handleImpersonate = async () => {
    if (!selectedUserId) {
      setError('Seleziona un utente');
      return;
    }

    setLoading(true);
    try {
      // Trova l'utente selezionato per ottenere il ruolo
      const selectedUser = users.find(user => user.id_utente === parseInt(selectedUserId));
      if (!selectedUser) {
        throw new Error('Utente non trovato');
      }

      // Utilizza la funzione impersonateUser dal contesto di autenticazione
      const userData = await impersonateUser(selectedUserId);

      console.log('Impersonificazione utente:', userData.impersonatedUser.username, 'Ruolo:', userData.impersonatedUser.role);

      // Reindirizza in base al ruolo dell'utente impersonato
      // L'amministratore mantiene i suoi privilegi ma accede alle funzionalità dell'utente impersonato
      if (userData.impersonatedUser.role === 'user') {
        // Utente standard - vai alla pagina dei cantieri
        navigate('/dashboard/cantieri');
      } else if (userData.impersonatedUser.role === 'cantieri_user') {
        // Utente cantiere - vai alla pagina di visualizzazione cavi
        navigate('/dashboard/cavi/visualizza');
      } else {
        // Fallback - vai alla dashboard generica
        navigate('/dashboard');
      }
    } catch (err) {
      console.error('Errore durante l\'impersonificazione:', err);
      setError(err.detail || 'Errore durante l\'accesso come utente selezionato');
      setLoading(false);
    }
    // Non mettiamo il finally qui perché il redirect interromperà l'esecuzione
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Accedi come Utente</Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={loadUsers}
          disabled={loading}
        >
          Aggiorna
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <CircularProgress />
      ) : users.length === 0 ? (
        <Typography>Nessun utente disponibile</Typography>
      ) : (
        <Box sx={{ mt: 2 }}>
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel id="user-select-label">Seleziona Utente</InputLabel>
            <Select
              labelId="user-select-label"
              id="user-select"
              value={selectedUserId}
              label="Seleziona Utente"
              onChange={handleUserChange}
            >
              {users.map((user) => (
                <MenuItem key={user.id_utente} value={user.id_utente}>
                  {user.username} ({user.ruolo})
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <Button
            variant="contained"
            color="primary"
            startIcon={<LoginIcon />}
            onClick={handleImpersonate}
            disabled={!selectedUserId || loading}
            fullWidth
          >
            Accedi come Utente Selezionato
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default ImpersonateUser;
