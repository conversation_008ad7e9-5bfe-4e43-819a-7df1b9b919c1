import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  TextField,
  Alert,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  CircularProgress
} from '@mui/material';
import {
  Warning as WarningIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import adminService from '../../services/adminService';

const ResetDatabase = () => {
  const [confirmText, setConfirmText] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Apre il dialog di conferma
  const handleOpenDialog = () => {
    setDialogOpen(true);
    setConfirmText('');
    setError('');
  };

  // Chiude il dialog di conferma
  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  // Gestisce il cambio del testo di conferma
  const handleConfirmTextChange = (event) => {
    setConfirmText(event.target.value);
  };

  // Gestisce il reset del database
  const handleResetDatabase = async () => {
    if (confirmText !== 'RESET') {
      setError('Per confermare, digita esattamente "RESET"');
      return;
    }

    setLoading(true);
    try {
      await adminService.resetDatabase();
      setSuccess('Database resettato con successo. Il sistema verrà riavviato.');
      
      // Chiudi il dialog
      setDialogOpen(false);
      
      // Logout e reindirizza alla pagina di login dopo 3 secondi
      setTimeout(() => {
        localStorage.removeItem('token');
        window.location.href = '/login';
      }, 3000);
    } catch (err) {
      setError(err.detail || 'Errore durante il reset del database');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          Reset Database
        </Typography>
        
        <Alert severity="warning" sx={{ mb: 2, width: '100%' }}>
          <Typography variant="body1">
            ⚠️ ATTENZIONE: Questa operazione è irreversibile!
          </Typography>
          <Typography variant="body2">
            Tutti i dati verranno eliminati permanentemente.
          </Typography>
        </Alert>
        
        <Button
          variant="contained"
          color="error"
          startIcon={<DeleteIcon />}
          onClick={handleOpenDialog}
          sx={{ mt: 2 }}
        >
          Reset Database
        </Button>
      </Box>
      
      {success && (
        <Alert severity="success" sx={{ mt: 2 }}>
          {success}
        </Alert>
      )}
      
      {/* Dialog di conferma */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog}>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <WarningIcon color="error" sx={{ mr: 1 }} />
            Conferma Reset Database
          </Box>
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            ⚠️ ATTENZIONE: Questa operazione è irreversibile!
            <br />
            Tutti i dati verranno eliminati permanentemente.
            <br /><br />
            Per confermare, digita "RESET" nel campo sottostante.
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            id="confirm"
            label="Digita RESET per confermare"
            type="text"
            fullWidth
            variant="outlined"
            value={confirmText}
            onChange={handleConfirmTextChange}
            error={!!error}
            helperText={error}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} disabled={loading}>
            Annulla
          </Button>
          <Button 
            onClick={handleResetDatabase} 
            color="error" 
            disabled={confirmText !== 'RESET' || loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Conferma Reset'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ResetDatabase;
