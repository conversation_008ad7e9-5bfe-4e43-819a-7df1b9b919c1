import { useEffect, useState } from 'react';
import userService from '../../services/userService';
import { useAuth } from '../../context/AuthContext';

/**
 * Componente invisibile che verifica periodicamente gli utenti scaduti.
 * Viene eseguito solo quando un amministratore è loggato.
 */
const UserExpirationChecker = () => {
  const { user } = useAuth();
  const [lastCheck, setLastCheck] = useState(null);

  // Verifica gli utenti scaduti all'avvio e ogni ora
  useEffect(() => {
    // Esegui solo se l'utente è un amministratore
    if (user && user.role === 'owner') {
      // Funzione per verificare gli utenti scaduti
      const checkExpiredUsers = async () => {
        try {
          const result = await userService.checkExpiredUsers();
          console.log('Verifica utenti scaduti:', result);
          setLastCheck(new Date());
        } catch (error) {
          console.error('Errore durante la verifica degli utenti scaduti:', error);
          // Non fare nulla in caso di errore, per evitare di bloccare l'applicazione
          // L'errore potrebbe essere dovuto a un problema di autorizzazione
          // quando si impersona un utente non amministratore
        }
      };

      // Esegui subito la verifica
      checkExpiredUsers();

      // Imposta un intervallo per verificare ogni ora
      const interval = setInterval(checkExpiredUsers, 60 * 60 * 1000);

      // Pulisci l'intervallo quando il componente viene smontato
      return () => clearInterval(interval);
    }
  }, [user]);

  // Questo componente non renderizza nulla
  return null;
};

export default UserExpirationChecker;
