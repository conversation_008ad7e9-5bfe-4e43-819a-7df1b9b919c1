import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Typography,
  Paper,
  Grid,
  Switch,
  FormControlLabel
} from '@mui/material';
// Date picker imports temporarily commented out due to dependency issues
// import { DatePicker } from '@mui/x-date-pickers/DatePicker';
// import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
// import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
// import { it } from 'date-fns/locale';
import userService from '../../services/userService';

const UserForm = ({ user, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    ruolo: 'user', // L'amministratore può creare solo utenti standard
    data_scadenza: null,
    abilitato: true
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Inizializza il form con i dati dell'utente se presente
  useEffect(() => {
    if (user) {
      setFormData({
        username: user.username || '',
        password: '', // Non mostrare la password esistente
        ruolo: user.ruolo || 'user',
        data_scadenza: user.data_scadenza ? new Date(user.data_scadenza) : null,
        abilitato: user.abilitato !== undefined ? user.abilitato : true
      });
    }
  }, [user]);

  // Gestisce il cambio dei campi del form
  const handleChange = (e) => {
    const { name, value, checked } = e.target;
    setFormData({
      ...formData,
      [name]: name === 'abilitato' ? checked : value
    });

    // Resetta l'errore per il campo
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };

  // Gestisce il cambio della data di scadenza
  const handleDateChange = (date) => {
    setFormData({
      ...formData,
      data_scadenza: date
    });

    // Resetta l'errore per il campo
    if (errors.data_scadenza) {
      setErrors({
        ...errors,
        data_scadenza: ''
      });
    }
  };

  // Valida il form
  const validateForm = () => {
    const newErrors = {};

    if (!formData.username.trim()) {
      newErrors.username = 'Username obbligatorio';
    }

    if (!user && !formData.password.trim()) {
      newErrors.password = 'Password obbligatoria';
    }

    if (!formData.ruolo) {
      newErrors.ruolo = 'Ruolo obbligatorio';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Gestisce il salvataggio dell'utente
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Prepara i dati da inviare
      const userData = {
        ...formData
      };

      // Rimuovi la password se è vuota (modifica utente)
      if (user && !userData.password.trim()) {
        delete userData.password;
      }

      // Converti la data in formato ISO
      if (userData.data_scadenza) {
        userData.data_scadenza = userData.data_scadenza.toISOString().split('T')[0];
      }

      let result;
      if (user) {
        // Aggiorna l'utente esistente
        result = await userService.updateUser(user.id_utente, userData);
      } else {
        // Crea un nuovo utente
        result = await userService.createUser(userData);
      }

      onSave(result);
    } catch (err) {
      setError(err.detail || 'Errore durante il salvataggio dell\'utente');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        {user ? 'Modifica Utente' : 'Nuovo Utente'}
      </Typography>

      {error && (
        <Typography color="error" sx={{ mb: 2 }}>
          {error}
        </Typography>
      )}

      <Box component="form" onSubmit={handleSubmit} noValidate>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="username"
              label="Username"
              name="username"
              autoComplete="username"
              value={formData.username}
              onChange={handleChange}
              error={!!errors.username}
              helperText={errors.username}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              margin="normal"
              required={!user}
              fullWidth
              name="password"
              label={user ? 'Nuova Password (lascia vuoto per non modificare)' : 'Password'}
              type="password"
              id="password"
              autoComplete="new-password"
              value={formData.password}
              onChange={handleChange}
              error={!!errors.password}
              helperText={errors.password}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth margin="normal" error={!!errors.ruolo}>
              <InputLabel id="ruolo-label">Ruolo</InputLabel>
              <Select
                labelId="ruolo-label"
                id="ruolo"
                name="ruolo"
                value={formData.ruolo}
                onChange={handleChange}
                label="Ruolo"
                disabled={true} /* Il ruolo è sempre 'user' per i nuovi utenti creati dall'amministratore */
              >
                <MenuItem value="user">Utente Standard</MenuItem>
                {/* Rimossa opzione Utente Cantiere poiché gli utenti cantiere vengono creati dagli utenti standard */}
              </Select>
              {errors.ruolo && <FormHelperText>{errors.ruolo}</FormHelperText>}
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            {/* Date picker temporarily replaced with a text field */}
            <TextField
              margin="normal"
              fullWidth
              id="data_scadenza"
              label="Data Scadenza (YYYY-MM-DD) (opzionale)"
              name="data_scadenza"
              value={formData.data_scadenza ? formData.data_scadenza.toISOString().split('T')[0] : ''}
              onChange={(e) => {
                try {
                  const dateStr = e.target.value;
                  const date = dateStr ? new Date(dateStr) : null;
                  handleDateChange(date);
                } catch (err) {
                  console.error('Invalid date format', err);
                }
              }}
              error={!!errors.data_scadenza}
              helperText={errors.data_scadenza || 'Formato: YYYY-MM-DD'}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.abilitato}
                  onChange={handleChange}
                  name="abilitato"
                  color="primary"
                  disabled={loading || (user && user.ruolo === 'owner')}
                />
              }
              label="Utente abilitato"
            />
          </Grid>

          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <Button
                onClick={onCancel}
                sx={{ mr: 1 }}
                disabled={loading}
              >
                Annulla
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={loading}
              >
                {loading ? 'Salvataggio...' : 'Salva'}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Box>
    </Paper>
  );
};

export default UserForm;
