import React, { useState, useEffect } from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Typography,
  Chip,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Edit as EditIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import userService from '../../services/userService';

const UsersList = ({ onEditUser }) => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);

  // Carica gli utenti
  const loadUsers = async () => {
    setLoading(true);
    try {
      const data = await userService.getUsers();
      setUsers(data);
      setError('');
    } catch (err) {
      setError(err.detail || 'Errore durante il caricamento degli utenti');
    } finally {
      setLoading(false);
    }
  };

  // Carica gli utenti all'avvio del componente
  useEffect(() => {
    loadUsers();
  }, []);

  // Gestisce l'abilitazione/disabilitazione di un utente
  const handleToggleStatus = async (userId) => {
    try {
      await userService.toggleUserStatus(userId);
      loadUsers(); // Ricarica gli utenti
    } catch (err) {
      setError(err.detail || 'Errore durante la modifica dello stato dell\'utente');
    }
  };

  // Apre il dialog di conferma per l'eliminazione
  const handleOpenDeleteDialog = (user) => {
    setUserToDelete(user);
    setDeleteDialogOpen(true);
  };

  // Chiude il dialog di conferma per l'eliminazione
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setUserToDelete(null);
  };

  // Gestisce l'eliminazione di un utente
  const handleDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      await userService.deleteUser(userToDelete.id_utente);
      loadUsers(); // Ricarica gli utenti
      handleCloseDeleteDialog();
    } catch (err) {
      setError(err.detail || 'Errore durante l\'eliminazione dell\'utente');
      handleCloseDeleteDialog();
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Lista Utenti</Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={loadUsers}
          disabled={loading}
        >
          Aggiorna
        </Button>
      </Box>

      {error && (
        <Typography color="error" sx={{ mb: 2 }}>
          {error}
        </Typography>
      )}

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>ID</TableCell>
              <TableCell>Username</TableCell>
              <TableCell>Password</TableCell>
              <TableCell>Ruolo</TableCell>
              <TableCell>Scadenza</TableCell>
              <TableCell>Stato</TableCell>
              <TableCell>Azioni</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  Caricamento...
                </TableCell>
              </TableRow>
            ) : users.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  Nessun utente trovato
                </TableCell>
              </TableRow>
            ) : (
              users.map((user) => (
                <TableRow key={user.id_utente}>
                  <TableCell>{user.id_utente}</TableCell>
                  <TableCell>{user.username}</TableCell>
                  <TableCell>
                    {/* Mostra la password in chiaro per l'amministratore */}
                    {user.password_plain || '********'}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={user.ruolo === 'owner' ? 'Admin' : user.ruolo === 'user' ? 'Standard' : 'Cantiere'}
                      color={user.ruolo === 'owner' ? 'primary' : user.ruolo === 'user' ? 'secondary' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {user.data_scadenza ? format(new Date(user.data_scadenza), 'dd/MM/yyyy') : 'N/A'}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={user.abilitato ? 'Attivo' : 'Disabilitato'}
                      color={user.abilitato ? 'success' : 'error'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Tooltip title="Modifica">
                      <IconButton
                        color="primary"
                        onClick={() => onEditUser(user)}
                        disabled={false} /* Abilitato per tutti gli utenti */
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>

                    <Tooltip title={user.abilitato ? 'Disabilita' : 'Abilita'}>
                      <IconButton
                        color={user.abilitato ? 'error' : 'success'}
                        onClick={() => handleToggleStatus(user.id_utente)}
                        disabled={user.ruolo === 'owner'}
                      >
                        {user.abilitato ? <BlockIcon /> : <CheckCircleIcon />}
                      </IconButton>
                    </Tooltip>

                    <Tooltip title="Elimina">
                      <IconButton
                        color="error"
                        onClick={() => handleOpenDeleteDialog(user)}
                        disabled={user.ruolo === 'owner'}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Dialog di conferma eliminazione */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
      >
        <DialogTitle>Conferma eliminazione</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Sei sicuro di voler eliminare l'utente {userToDelete?.username}?
            Questa azione non può essere annullata.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Annulla</Button>
          <Button onClick={handleDeleteUser} color="error" autoFocus>
            Elimina
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UsersList;
