import React, { useState } from 'react';
import { Box, Alert, CircularProgress } from '@mui/material';
import CavoForm from './CavoForm';
import caviService from '../../services/caviService';

/**
 * Componente per l'aggiunta di un nuovo cavo.
 * Utilizza il componente CavoForm in modalità 'add'.
 */
const AggiungiCavoForm = ({ cantiereId, onSuccess, onError }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Gestisce l'invio del form
  const handleSubmit = async (cavoData) => {
    setLoading(true);
    setError(null);
    
    try {
      // Assicura che il cavo sia associato al cantiere corretto
      const dataToSubmit = {
        ...cavoData,
        id_cantiere: cantiereId
      };
      
      // Chiama il servizio per aggiungere il cavo
      const response = await caviService.addCavo(dataToSubmit);
      
      if (onSuccess) {
        onSuccess(`Cavo ${cavoData.id_cavo} aggiunto con successo.`);
      }
      
      return response;
    } catch (err) {
      console.error('Errore durante l\'aggiunta del cavo:', err);
      const errorMessage = err.response?.data?.detail || err.message || 'Errore durante l\'aggiunta del cavo';
      
      setError(errorMessage);
      
      if (onError) {
        onError(errorMessage);
      }
      
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
          <CircularProgress />
        </Box>
      )}
      
      <CavoForm
        mode="add"
        onSubmit={handleSubmit}
        onSuccess={onSuccess}
        onError={onError}
      />
    </Box>
  );
};

export default AggiungiCavoForm;