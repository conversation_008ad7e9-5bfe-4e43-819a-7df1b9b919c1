import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Divider,
  Chip
} from '@mui/material';
import { getCableStateColor } from '../../utils/stateUtils';

/**
 * Component for displaying detailed information about a cable
 * Implements the same visualization as _visualizza_dettagli_cavo in the CLI
 *
 * @param {Object} props - Component props
 * @param {Object} props.cavo - The cable object to display
 * @param {boolean} props.compact - Whether to display in compact mode (default: false)
 * @param {string} props.title - Optional title for the component
 */
const CavoDetailsView = ({ cavo, compact = false, title = "Dettagli Cavo" }) => {
  if (!cavo) return null;

  // Helper function to format a field value
  const formatValue = (value) => {
    if (value === null || value === undefined || value === '') {
      return 'N/A';
    }
    return value;
  };

  // Define the fields to display
  const fields = [
    { label: 'ID Cavo', value: cavo.id_cavo, primary: true },
    { label: 'Sistema', value: cavo.sistema },
    { label: 'Utility', value: cavo.utility },
    { label: 'Colore Cavo', value: cavo.colore_cavo },
    { label: 'Tipologia', value: cavo.tipologia },
    // n_conduttori field is now a spare field (kept in DB but hidden in UI)
    { label: 'Formazione', value: cavo.sezione },
    // sh field is now a spare field (kept in DB but hidden in UI)
    { label: 'Ubicazione Partenza', value: cavo.ubicazione_partenza },
    { label: 'Utenza Partenza', value: cavo.utenza_partenza },
    { label: 'Descrizione Utenza Partenza', value: cavo.descrizione_utenza_partenza },
    { label: 'Ubicazione Arrivo', value: cavo.ubicazione_arrivo },
    { label: 'Utenza Arrivo', value: cavo.utenza_arrivo },
    { label: 'Descrizione Utenza Arrivo', value: cavo.descrizione_utenza_arrivo },
    { label: 'Metri Teorici', value: cavo.metri_teorici },
    {
      label: 'Stato Installazione',
      value: cavo.stato_installazione,
      chip: true,
      chipColor: getCableStateColor(cavo.stato_installazione)
    }
  ];

  // Add metratura_reale only if it's greater than 0
  if (cavo.metratura_reale && parseFloat(cavo.metratura_reale) > 0) {
    fields.push({ label: 'Metratura Reale', value: cavo.metratura_reale + ' m' });
  }

  // Add bobina information if available
  if (cavo.id_bobina && cavo.id_bobina !== 'TBD') {
    fields.push({ label: 'Bobina Associata', value: cavo.id_bobina });
  } else if (cavo.id_bobina === 'BOBINA_VUOTA') {
    // Quando id_bobina è 'BOBINA_VUOTA', mostra 'BOBINA VUOTA'
    fields.push({ label: 'Bobina Associata', value: 'BOBINA VUOTA' });
  } else if (cavo.id_bobina === null) {
    // Quando id_bobina è null (cavi non posati), non mostrare la bobina
    // Non aggiungere il campo alla lista
  }

  // Add data_posa if available
  if (cavo.data_posa) {
    // Format the date
    const date = new Date(cavo.data_posa);
    const formattedDate = date.toLocaleDateString('it-IT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
    fields.push({ label: 'Data Posa', value: formattedDate });
  }

  return (
    <Paper sx={{ p: compact ? 2 : 3, mb: 3 }}>
      <Typography variant={compact ? "h6" : "h5"} gutterBottom>
        {title}
      </Typography>

      <Divider sx={{ mb: 2 }} />

      <Grid container spacing={compact ? 1 : 2}>
        {fields.map((field, index) => (
          <Grid item xs={12} sm={compact ? 6 : 4} key={index}>
            <Box sx={{ mb: compact ? 1 : 2 }}>
              <Typography variant="caption" color="text.secondary">
                {field.label}
              </Typography>
              {field.chip ? (
                <Box sx={{ mt: 0.5 }}>
                  <Chip
                    label={formatValue(field.value)}
                    color={field.chipColor || 'default'}
                    size={compact ? "small" : "medium"}
                  />
                </Box>
              ) : (
                <Typography
                  variant={field.primary ? "subtitle1" : "body2"}
                  sx={{ fontWeight: field.primary ? 'bold' : 'regular' }}
                >
                  {formatValue(field.value)}
                </Typography>
              )}
            </Box>
          </Grid>
        ))}
      </Grid>
    </Paper>
  );
};

export default CavoDetailsView;
