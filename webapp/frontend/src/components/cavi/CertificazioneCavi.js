import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemButton,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Divider,
  IconButton,
  Chip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PictureAsPdf as PdfIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Save as SaveIcon,
  Clear as ClearIcon,
  ViewList as ViewListIcon,
  Build as BuildIcon
} from '@mui/icons-material';
import certificazioneService from '../../services/certificazioneService';
import caviService from '../../services/caviService';

const CertificazioneCavi = forwardRef(({ cantiereId, onSuccess, onError }, ref) => {
  const [loading, setLoading] = useState(false);
  const [certificazioni, setCertificazioni] = useState([]);
  const [cavi, setCavi] = useState([]);
  const [strumenti, setStrumenti] = useState([]);
  const [selectedOption, setSelectedOption] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [selectedCertificazione, setSelectedCertificazione] = useState(null);
  const [selectedCavo, setSelectedCavo] = useState(null);
  const [filtroCavo, setFiltroCavo] = useState('');
  const [selectedStrumento, setSelectedStrumento] = useState(null);
  const [strumentoFormData, setStrumentoFormData] = useState({
    nome: '',
    marca: '',
    modello: '',
    numero_serie: '',
    data_calibrazione: '',
    data_scadenza_calibrazione: '',
    certificato_calibrazione: '',
    note: ''
  });
  const [formData, setFormData] = useState({
    id_cavo: '',
    id_strumento: '',
    lunghezza_misurata: '',
    valore_continuita: 'OK',
    valore_isolamento: '',
    valore_resistenza: 'OK',
    note: ''
  });

  // Carica le certificazioni
  const loadCertificazioni = async (filtroCavo = '') => {
    try {
      setLoading(true);
      const data = await certificazioneService.getCertificazioni(cantiereId, filtroCavo);
      setCertificazioni(data);
    } catch (error) {
      onError('Errore nel caricamento delle certificazioni');
      console.error('Errore nel caricamento delle certificazioni:', error);
    } finally {
      setLoading(false);
    }
  };

  // Carica i cavi disponibili
  const loadCavi = async () => {
    try {
      setLoading(true);
      const data = await caviService.getCavi(cantiereId);
      setCavi(data);
    } catch (error) {
      onError('Errore nel caricamento dei cavi');
      console.error('Errore nel caricamento dei cavi:', error);
    } finally {
      setLoading(false);
    }
  };

  // Carica gli strumenti certificati
  const loadStrumenti = async () => {
    try {
      setLoading(true);
      const data = await certificazioneService.getStrumenti(cantiereId);
      setStrumenti(data);
    } catch (error) {
      onError('Errore nel caricamento degli strumenti');
      console.error('Errore nel caricamento degli strumenti:', error);
    } finally {
      setLoading(false);
    }
  };

  // Espone i metodi tramite ref
  useImperativeHandle(ref, () => ({
    handleOptionSelect
  }));

  // Carica i dati all'avvio del componente
  useEffect(() => {
    loadCertificazioni();
  }, [cantiereId]);

  // Gestisce la selezione di un'opzione dal menu
  const handleOptionSelect = (option) => {
    setSelectedOption(option);

    if (option === 'visualizzaCertificazioni') {
      loadCertificazioni();
    } else if (option === 'filtraCertificazioni') {
      setDialogType('filtraCertificazioni');
      setOpenDialog(true);
    } else if (option === 'creaCertificazione') {
      loadCavi();
      loadStrumenti();
      setDialogType('selezionaCavo');
      setOpenDialog(true);
    } else if (option === 'dettagliCertificazione') {
      loadCertificazioni();
      setDialogType('selezionaCertificazione');
      setOpenDialog(true);
    } else if (option === 'generaPdf') {
      loadCertificazioni();
      setDialogType('selezionaCertificazionePdf');
      setOpenDialog(true);
    } else if (option === 'eliminaCertificazione') {
      loadCertificazioni();
      setDialogType('eliminaCertificazione');
      setOpenDialog(true);
    } else if (option === 'gestioneStrumenti') {
      loadStrumenti();
      setDialogType('gestioneStrumenti');
      setOpenDialog(true);
    }
  };

  // Gestisce la chiusura del dialog
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedCertificazione(null);
    setSelectedCavo(null);
    setSelectedStrumento(null);
    setFormData({
      id_cavo: '',
      id_strumento: '',
      lunghezza_misurata: '',
      valore_continuita: 'OK',
      valore_isolamento: '',
      valore_resistenza: 'OK',
      note: ''
    });
    setStrumentoFormData({
      nome: '',
      marca: '',
      modello: '',
      numero_serie: '',
      data_calibrazione: '',
      data_scadenza_calibrazione: '',
      certificato_calibrazione: '',
      note: ''
    });
  };

  // Gestisce la selezione di una certificazione
  const handleCertificazioneSelect = (certificazione) => {
    setSelectedCertificazione(certificazione);

    if (dialogType === 'selezionaCertificazione') {
      setDialogType('dettagliCertificazione');
    } else if (dialogType === 'selezionaCertificazionePdf') {
      handleGeneraPdf(certificazione.id_certificazione);
    }
  };

  // Gestisce la selezione di un cavo
  const handleCavoSelect = (cavo) => {
    setSelectedCavo(cavo);
    setFormData({
      ...formData,
      id_cavo: cavo.id_cavo,
      lunghezza_misurata: cavo.metratura_reale || '0'
    });
    setDialogType('creaCertificazione');
  };

  // Gestisce il cambio dei valori nel form
  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Gestisce il filtro per cavo
  const handleFiltroCavo = () => {
    loadCertificazioni(filtroCavo);
    handleCloseDialog();
  };

  // Gestisce la creazione di una certificazione
  const handleCreaCertificazione = async () => {
    try {
      if (!formData.id_cavo || !formData.id_strumento || !formData.valore_isolamento) {
        onError('Compila tutti i campi obbligatori');
        return;
      }

      setLoading(true);
      await certificazioneService.createCertificazione(cantiereId, formData);
      onSuccess('Certificazione creata con successo');
      handleCloseDialog();
      loadCertificazioni();
    } catch (error) {
      onError('Errore nella creazione della certificazione: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nella creazione della certificazione:', error);
    } finally {
      setLoading(false);
    }
  };

  // Gestisce l'eliminazione di una certificazione
  const handleEliminaCertificazione = async () => {
    try {
      if (!selectedCertificazione) {
        onError('Seleziona una certificazione da eliminare');
        return;
      }

      setLoading(true);
      await certificazioneService.deleteCertificazione(cantiereId, selectedCertificazione.id_certificazione);
      onSuccess('Certificazione eliminata con successo');
      handleCloseDialog();
      loadCertificazioni();
    } catch (error) {
      onError('Errore nell\'eliminazione della certificazione: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nell\'eliminazione della certificazione:', error);
    } finally {
      setLoading(false);
    }
  };

  // Gestisce la generazione del PDF
  const handleGeneraPdf = async (idCertificazione) => {
    try {
      setLoading(true);
      const response = await certificazioneService.generatePdf(cantiereId, idCertificazione);

      // Apri il PDF in una nuova finestra
      window.open(response.file_url, '_blank');

      onSuccess('PDF generato con successo');
      handleCloseDialog();
    } catch (error) {
      onError('Errore nella generazione del PDF: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nella generazione del PDF:', error);
    } finally {
      setLoading(false);
    }
  };

  // Gestisce la selezione di uno strumento
  const handleStrumentoSelect = (strumento) => {
    setSelectedStrumento(strumento);
    setStrumentoFormData({
      nome: strumento.nome || '',
      marca: strumento.marca || '',
      modello: strumento.modello || '',
      numero_serie: strumento.numero_serie || '',
      data_calibrazione: strumento.data_calibrazione || '',
      data_scadenza_calibrazione: strumento.data_scadenza_calibrazione || '',
      certificato_calibrazione: strumento.certificato_calibrazione || '',
      note: strumento.note || ''
    });
    setDialogType('modificaStrumento');
  };

  // Gestisce il cambio dei valori nel form strumento
  const handleStrumentoFormChange = (e) => {
    const { name, value } = e.target;
    setStrumentoFormData({
      ...strumentoFormData,
      [name]: value
    });
  };

  // Gestisce la creazione di un nuovo strumento
  const handleCreaStrumento = () => {
    setSelectedStrumento(null);
    setStrumentoFormData({
      nome: '',
      marca: '',
      modello: '',
      numero_serie: '',
      data_calibrazione: '',
      data_scadenza_calibrazione: '',
      certificato_calibrazione: '',
      note: ''
    });
    setDialogType('creaStrumento');
  };

  // Gestisce il salvataggio dello strumento
  const handleSalvaStrumento = async () => {
    try {
      if (!strumentoFormData.nome || !strumentoFormData.marca || !strumentoFormData.modello || !strumentoFormData.numero_serie) {
        onError('Compila tutti i campi obbligatori');
        return;
      }

      if (!strumentoFormData.data_calibrazione || !strumentoFormData.data_scadenza_calibrazione) {
        onError('Le date di calibrazione e scadenza sono obbligatorie');
        return;
      }

      setLoading(true);

      if (selectedStrumento) {
        await certificazioneService.updateStrumento(cantiereId, selectedStrumento.id_strumento, strumentoFormData);
        onSuccess('Strumento aggiornato con successo');
      } else {
        await certificazioneService.createStrumento(cantiereId, strumentoFormData);
        onSuccess('Strumento creato con successo');
      }

      handleCloseDialog();
      loadStrumenti();
    } catch (error) {
      onError('Errore nel salvataggio dello strumento: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nel salvataggio dello strumento:', error);
    } finally {
      setLoading(false);
    }
  };

  // Gestisce l'eliminazione di uno strumento
  const handleEliminaStrumento = async (strumento) => {
    try {
      if (window.confirm(`Sei sicuro di voler eliminare lo strumento "${strumento.nome} ${strumento.marca} ${strumento.modello}"?`)) {
        setLoading(true);
        await certificazioneService.deleteStrumento(cantiereId, strumento.id_strumento);
        onSuccess('Strumento eliminato con successo');
        loadStrumenti();
      }
    } catch (error) {
      onError('Errore nell\'eliminazione dello strumento: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nell\'eliminazione dello strumento:', error);
    } finally {
      setLoading(false);
    }
  };

  // Renderizza le certificazioni in formato tabella
  const renderCertificazioniTable = () => {
    if (certificazioni.length === 0) {
      return (
        <Alert severity="info">Nessuna certificazione trovata</Alert>
      );
    }

    return (
      <TableContainer component={Paper}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>ID</TableCell>
              <TableCell>Cavo</TableCell>
              <TableCell>Data</TableCell>
              <TableCell>Operatore</TableCell>
              <TableCell>Strumento</TableCell>
              <TableCell>Lunghezza</TableCell>
              <TableCell>Isolamento</TableCell>
              <TableCell>Azioni</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {certificazioni.map((cert) => (
              <TableRow key={cert.id_certificazione}>
                <TableCell>{cert.id_certificazione}</TableCell>
                <TableCell>{cert.id_cavo}</TableCell>
                <TableCell>{new Date(cert.data_certificazione).toLocaleDateString()}</TableCell>
                <TableCell>{cert.operatore}</TableCell>
                <TableCell>{cert.strumento}</TableCell>
                <TableCell>{cert.lunghezza_misurata} m</TableCell>
                <TableCell>{cert.valore_isolamento} MΩ</TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    onClick={() => {
                      setSelectedCertificazione(cert);
                      setDialogType('dettagliCertificazione');
                      setOpenDialog(true);
                    }}
                  >
                    <SearchIcon fontSize="small" />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => handleGeneraPdf(cert.id_certificazione)}
                  >
                    <PdfIcon fontSize="small" />
                  </IconButton>
                  <IconButton
                    size="small"
                    color="error"
                    onClick={() => {
                      setSelectedCertificazione(cert);
                      setDialogType('eliminaCertificazione');
                      setOpenDialog(true);
                    }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  // Renderizza il dialog in base al tipo
  const renderDialog = () => {
    if (dialogType === 'filtraCertificazioni') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>Filtra Certificazioni per Cavo</DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <TextField
                fullWidth
                label="ID Cavo"
                variant="outlined"
                value={filtroCavo}
                onChange={(e) => setFiltroCavo(e.target.value)}
                placeholder="Inserisci l'ID del cavo"
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => {
                setFiltroCavo('');
                loadCertificazioni('');
                handleCloseDialog();
              }}
              startIcon={<ClearIcon />}
            >
              Rimuovi Filtro
            </Button>
            <Button onClick={handleCloseDialog}>Annulla</Button>
            <Button
              onClick={handleFiltroCavo}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <FilterIcon />}
            >
              Filtra
            </Button>
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'selezionaCavo') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
          <DialogTitle>Seleziona Cavo per Certificazione</DialogTitle>
          <DialogContent>
            {loading ? (
              <CircularProgress />
            ) : cavi.length === 0 ? (
              <Alert severity="info">Nessun cavo disponibile</Alert>
            ) : (
              <List>
                {cavi.map((cavo) => (
                  <ListItem
                    button
                    key={cavo.id_cavo}
                    onClick={() => handleCavoSelect(cavo)}
                  >
                    <ListItemText
                      primary={cavo.id_cavo}
                      secondary={`${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`}
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'creaCertificazione') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
          <DialogTitle>Crea Nuova Certificazione</DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Cavo selezionato: {selectedCavo?.id_cavo}
              </Typography>
              <Typography variant="body2" gutterBottom>
                Tipologia: {selectedCavo?.tipologia || 'N/A'}
              </Typography>
              <Typography variant="body2" gutterBottom>
                Metratura: {selectedCavo?.metratura_reale || '0'} m
              </Typography>

              <Divider sx={{ my: 2 }} />

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel>Strumento Utilizzato</InputLabel>
                    <Select
                      name="id_strumento"
                      value={formData.id_strumento}
                      onChange={handleFormChange}
                      label="Strumento Utilizzato"
                      required
                    >
                      {strumenti.map((strumento) => (
                        <MenuItem key={strumento.id_strumento} value={strumento.id_strumento}>
                          {strumento.nome} - {strumento.modello}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="lunghezza_misurata"
                    label="Lunghezza Misurata (m)"
                    type="number"
                    fullWidth
                    variant="outlined"
                    value={formData.lunghezza_misurata}
                    onChange={handleFormChange}
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel>Test Continuità</InputLabel>
                    <Select
                      name="valore_continuita"
                      value={formData.valore_continuita}
                      onChange={handleFormChange}
                      label="Test Continuità"
                    >
                      <MenuItem value="OK">OK</MenuItem>
                      <MenuItem value="NON OK">NON OK</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="valore_isolamento"
                    label="Valore Isolamento (MΩ)"
                    fullWidth
                    variant="outlined"
                    value={formData.valore_isolamento}
                    onChange={handleFormChange}
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel>Test Resistenza</InputLabel>
                    <Select
                      name="valore_resistenza"
                      value={formData.valore_resistenza}
                      onChange={handleFormChange}
                      label="Test Resistenza"
                    >
                      <MenuItem value="OK">OK</MenuItem>
                      <MenuItem value="NON OK">NON OK</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    name="note"
                    label="Note"
                    fullWidth
                    multiline
                    rows={3}
                    variant="outlined"
                    value={formData.note}
                    onChange={handleFormChange}
                  />
                </Grid>
              </Grid>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
            <Button
              onClick={handleCreaCertificazione}
              disabled={loading || !formData.id_strumento || !formData.valore_isolamento}
              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
            >
              Salva
            </Button>
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'selezionaCertificazione' || dialogType === 'selezionaCertificazionePdf') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
          <DialogTitle>
            {dialogType === 'selezionaCertificazione'
              ? 'Seleziona Certificazione da Visualizzare'
              : 'Seleziona Certificazione per PDF'}
          </DialogTitle>
          <DialogContent>
            {loading ? (
              <CircularProgress />
            ) : certificazioni.length === 0 ? (
              <Alert severity="info">Nessuna certificazione trovata</Alert>
            ) : (
              <List>
                {certificazioni.map((cert) => (
                  <ListItem
                    button
                    key={cert.id_certificazione}
                    onClick={() => handleCertificazioneSelect(cert)}
                  >
                    <ListItemText
                      primary={`ID: ${cert.id_certificazione} - Cavo: ${cert.id_cavo}`}
                      secondary={`Data: ${new Date(cert.data_certificazione).toLocaleDateString()} - Operatore: ${cert.operatore}`}
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'dettagliCertificazione') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
          <DialogTitle>Dettagli Certificazione</DialogTitle>
          <DialogContent>
            {!selectedCertificazione ? (
              <CircularProgress />
            ) : (
              <Box sx={{ mt: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2">ID Certificazione:</Typography>
                    <Typography variant="body1" gutterBottom>{selectedCertificazione.id_certificazione}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2">Cavo:</Typography>
                    <Typography variant="body1" gutterBottom>{selectedCertificazione.id_cavo}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2">Data Certificazione:</Typography>
                    <Typography variant="body1" gutterBottom>
                      {new Date(selectedCertificazione.data_certificazione).toLocaleDateString()}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2">Operatore:</Typography>
                    <Typography variant="body1" gutterBottom>{selectedCertificazione.operatore}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2">Strumento:</Typography>
                    <Typography variant="body1" gutterBottom>{selectedCertificazione.strumento}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2">ID Strumento:</Typography>
                    <Typography variant="body1" gutterBottom>{selectedCertificazione.id_strumento}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2">Lunghezza Misurata:</Typography>
                    <Typography variant="body1" gutterBottom>{selectedCertificazione.lunghezza_misurata} m</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2">Test Continuità:</Typography>
                    <Typography variant="body1" gutterBottom>{selectedCertificazione.valore_continuita}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2">Valore Isolamento:</Typography>
                    <Typography variant="body1" gutterBottom>{selectedCertificazione.valore_isolamento} MΩ</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2">Test Resistenza:</Typography>
                    <Typography variant="body1" gutterBottom>{selectedCertificazione.valore_resistenza}</Typography>
                  </Grid>
                  {selectedCertificazione.note && (
                    <Grid item xs={12}>
                      <Typography variant="subtitle2">Note:</Typography>
                      <Typography variant="body1" gutterBottom>{selectedCertificazione.note}</Typography>
                    </Grid>
                  )}
                </Grid>

                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                  <Button
                    variant="contained"
                    startIcon={<PdfIcon />}
                    onClick={() => handleGeneraPdf(selectedCertificazione.id_certificazione)}
                    sx={{ mr: 1 }}
                  >
                    Genera PDF
                  </Button>
                </Box>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Chiudi</Button>
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'eliminaCertificazione') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>Elimina Certificazione</DialogTitle>
          <DialogContent>
            {!selectedCertificazione ? (
              loading ? (
                <CircularProgress />
              ) : certificazioni.length === 0 ? (
                <Alert severity="info">Nessuna certificazione disponibile</Alert>
              ) : (
                <List>
                  {certificazioni.map((cert) => (
                    <ListItem
                      button
                      key={cert.id_certificazione}
                      onClick={() => setSelectedCertificazione(cert)}
                    >
                      <ListItemText
                        primary={`ID: ${cert.id_certificazione} - Cavo: ${cert.id_cavo}`}
                        secondary={`Data: ${new Date(cert.data_certificazione).toLocaleDateString()}`}
                      />
                    </ListItem>
                  ))}
                </List>
              )
            ) : (
              <Box>
                <Alert severity="warning" sx={{ mb: 2 }}>
                  Sei sicuro di voler eliminare la certificazione {selectedCertificazione.id_certificazione}?
                </Alert>
                <Typography variant="body1">
                  Questa operazione non può essere annullata.
                </Typography>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
            {selectedCertificazione && (
              <Button
                onClick={handleEliminaCertificazione}
                disabled={loading}
                color="error"
                startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}
              >
                Elimina
              </Button>
            )}
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'gestioneStrumenti') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
          <DialogTitle>Gestione Strumenti Certificati</DialogTitle>
          <DialogContent>
            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleCreaStrumento}
              >
                Nuovo Strumento
              </Button>
            </Box>

            {loading ? (
              <CircularProgress />
            ) : strumenti.length === 0 ? (
              <Alert severity="info">Nessuno strumento certificato trovato</Alert>
            ) : (
              <TableContainer component={Paper}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Nome</TableCell>
                      <TableCell>Marca</TableCell>
                      <TableCell>Modello</TableCell>
                      <TableCell>N° Serie</TableCell>
                      <TableCell>Calibrazione</TableCell>
                      <TableCell>Scadenza</TableCell>
                      <TableCell>Stato</TableCell>
                      <TableCell>Azioni</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {strumenti.map((strumento) => {
                      const scadenza = new Date(strumento.data_scadenza_calibrazione);
                      const oggi = new Date();
                      const giorni = Math.ceil((scadenza - oggi) / (1000 * 60 * 60 * 24));

                      let statoColor = 'success';
                      let statoLabel = 'Valido';

                      if (giorni < 0) {
                        statoColor = 'error';
                        statoLabel = 'Scaduto';
                      } else if (giorni <= 30) {
                        statoColor = 'warning';
                        statoLabel = 'In scadenza';
                      }

                      return (
                        <TableRow key={strumento.id_strumento}>
                          <TableCell>{strumento.nome}</TableCell>
                          <TableCell>{strumento.marca}</TableCell>
                          <TableCell>{strumento.modello}</TableCell>
                          <TableCell>{strumento.numero_serie}</TableCell>
                          <TableCell>{new Date(strumento.data_calibrazione).toLocaleDateString()}</TableCell>
                          <TableCell>{scadenza.toLocaleDateString()}</TableCell>
                          <TableCell>
                            <Chip
                              label={statoLabel}
                              color={statoColor}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <IconButton
                              size="small"
                              onClick={() => handleStrumentoSelect(strumento)}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleEliminaStrumento(strumento)}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Chiudi</Button>
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'creaStrumento' || dialogType === 'modificaStrumento') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
          <DialogTitle>
            {dialogType === 'creaStrumento' ? 'Nuovo Strumento' : 'Modifica Strumento'}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="nome"
                    label="Nome Strumento"
                    fullWidth
                    variant="outlined"
                    value={strumentoFormData.nome}
                    onChange={handleStrumentoFormChange}
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="marca"
                    label="Marca"
                    fullWidth
                    variant="outlined"
                    value={strumentoFormData.marca}
                    onChange={handleStrumentoFormChange}
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="modello"
                    label="Modello"
                    fullWidth
                    variant="outlined"
                    value={strumentoFormData.modello}
                    onChange={handleStrumentoFormChange}
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="numero_serie"
                    label="Numero di Serie"
                    fullWidth
                    variant="outlined"
                    value={strumentoFormData.numero_serie}
                    onChange={handleStrumentoFormChange}
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="data_calibrazione"
                    label="Data Calibrazione"
                    type="date"
                    fullWidth
                    variant="outlined"
                    value={strumentoFormData.data_calibrazione}
                    onChange={handleStrumentoFormChange}
                    InputLabelProps={{ shrink: true }}
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="data_scadenza_calibrazione"
                    label="Data Scadenza Calibrazione"
                    type="date"
                    fullWidth
                    variant="outlined"
                    value={strumentoFormData.data_scadenza_calibrazione}
                    onChange={handleStrumentoFormChange}
                    InputLabelProps={{ shrink: true }}
                    required
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    name="certificato_calibrazione"
                    label="Percorso Certificato Calibrazione"
                    fullWidth
                    variant="outlined"
                    value={strumentoFormData.certificato_calibrazione}
                    onChange={handleStrumentoFormChange}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    name="note"
                    label="Note"
                    fullWidth
                    multiline
                    rows={3}
                    variant="outlined"
                    value={strumentoFormData.note}
                    onChange={handleStrumentoFormChange}
                  />
                </Grid>
              </Grid>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
            <Button
              onClick={handleSalvaStrumento}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
            >
              Salva
            </Button>
          </DialogActions>
        </Dialog>
      );
    }

    return null;
  };

  return (
    <Box>

      {selectedOption === 'visualizzaCertificazioni' && !openDialog && (
        <Box sx={{ mt: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Certificazioni
              {filtroCavo && ` - Filtro: ${filtroCavo}`}
            </Typography>
            {filtroCavo && (
              <Button
                variant="outlined"
                startIcon={<ClearIcon />}
                onClick={() => {
                  setFiltroCavo('');
                  loadCertificazioni('');
                }}
              >
                Rimuovi Filtro
              </Button>
            )}
          </Box>

          {loading ? (
            <CircularProgress />
          ) : (
            renderCertificazioniTable()
          )}
        </Box>
      )}

      {!selectedOption && (
        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Typography variant="body1">
            Seleziona un'opzione dal menu principale per iniziare.
          </Typography>
        </Paper>
      )}

      {renderDialog()}
    </Box>
  );
});

export default CertificazioneCavi;
