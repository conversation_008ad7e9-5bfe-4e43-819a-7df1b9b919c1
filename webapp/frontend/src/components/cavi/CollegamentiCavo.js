import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  List,
  ListItem,
  ListItemText,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import caviService from '../../services/caviService';

const CollegamentiCavo = ({ cantiereId, onSuccess, onError }) => {
  const [loading, setLoading] = useState(false);
  const [cavi, setCavi] = useState([]);
  const [filteredCavi, setFilteredCavi] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCavo, setSelectedCavo] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [formData, setFormData] = useState({
    lato: 'partenza',
    responsabile: 'cantiere'
  });

  // Carica i cavi installati
  useEffect(() => {
    loadCavi();
  }, [cantiereId]);

  const loadCavi = async () => {
    try {
      setLoading(true);
      console.log(`Caricamento cavi installati per cantiere ${cantiereId}...`);

      // Ottieni solo i cavi installati
      const response = await caviService.getCaviInstallati(cantiereId);

      if (response && Array.isArray(response)) {
        console.log(`Ricevuti ${response.length} cavi installati`);
        setCavi(response);
        setFilteredCavi(response);

        if (response.length === 0) {
          console.log('Nessun cavo installato trovato per questo cantiere');
          // Non mostriamo un errore qui, l'interfaccia mostrerà già un messaggio appropriato
        }
      } else {
        console.error('Risposta non valida dal server:', response);
        setCavi([]);
        setFilteredCavi([]);
        onError('Formato risposta non valido dal server. Contattare l\'amministratore.');
      }
    } catch (error) {
      console.error('Errore nel caricamento dei cavi:', error);
      setCavi([]);
      setFilteredCavi([]);

      // Messaggio di errore più dettagliato e user-friendly
      let errorMessage = 'Errore nel caricamento dei cavi: ';

      if (error.detail) {
        errorMessage += error.detail;
      } else if (error.message) {
        // Rimuovi dettagli tecnici dal messaggio di errore
        const cleanMessage = error.message
          .replace(/http:\/\/localhost:\d+/g, 'server')
          .replace(/network error/i, 'errore di connessione');
        errorMessage += cleanMessage;
      } else {
        errorMessage += 'Errore sconosciuto';
      }

      onError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Filtra i cavi in base al termine di ricerca
  const handleSearch = (event) => {
    const term = event.target.value;
    setSearchTerm(term);

    if (!term.trim()) {
      setFilteredCavi(cavi);
    } else {
      const filtered = cavi.filter(cavo => 
        cavo.id_cavo.toLowerCase().includes(term.toLowerCase())
      );
      setFilteredCavi(filtered);
    }
  };

  // Gestisce la selezione di un cavo
  const handleCavoSelect = (cavo) => {
    setSelectedCavo(cavo);
    setOpenDialog(true);
  };

  // Gestisce il cambio dei valori nel form
  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Gestisce il salvataggio del collegamento
  const handleSaveCollegamento = async () => {
    try {
      setLoading(true);

      // Determina se il lato è già collegato
      const collegamenti = selectedCavo.collegamenti || 0;
      const latoPartenzaCollegato = (collegamenti & 1) === 1;
      const latoArrivoCollegato = (collegamenti & 2) === 2;

      // Verifica se il lato selezionato è già collegato
      if ((formData.lato === 'partenza' && latoPartenzaCollegato) || 
          (formData.lato === 'arrivo' && latoArrivoCollegato)) {
        // Se è già collegato, chiedi se vuole scollegarlo
        await caviService.scollegaCavo(
          cantiereId,
          selectedCavo.id_cavo,
          formData.lato
        );
        onSuccess(`Lato ${formData.lato} del cavo ${selectedCavo.id_cavo} scollegato con successo`);
      } else {
        // Altrimenti collega il lato
        await caviService.collegaCavo(
          cantiereId,
          selectedCavo.id_cavo,
          formData.lato,
          formData.responsabile
        );
        onSuccess(`Lato ${formData.lato} del cavo ${selectedCavo.id_cavo} collegato con successo`);
      }

      // Ricarica i cavi per aggiornare lo stato
      await loadCavi();
      setOpenDialog(false);
      setSelectedCavo(null);
    } catch (error) {
      onError('Errore durante l\'operazione: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore durante l\'operazione:', error);
    } finally {
      setLoading(false);
    }
  };

  // Formatta lo stato dei collegamenti
  const formatStatoCollegamenti = (collegamenti) => {
    collegamenti = collegamenti || 0;

    if (collegamenti === 0) return "Non collegato";
    if (collegamenti === 1) return "Solo partenza";
    if (collegamenti === 2) return "Solo arrivo";
    if (collegamenti === 3) return "Completo";
    return `Sconosciuto (${collegamenti})`;
  };

  // Formatta lo stato di un singolo lato
  const formatStatoLato = (collegamenti, lato) => {
    collegamenti = collegamenti || 0;

    if (lato === 'partenza') {
      return (collegamenti & 1) ? "Collegato" : "Non collegato";
    } else {
      return (collegamenti & 2) ? "Collegato" : "Non collegato";
    }
  };

  return (
    <Box>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="body2" paragraph>
          Visualizza e gestisci i collegamenti dei cavi installati.
        </Typography>

        <TextField
          label="Cerca cavo per ID"
          variant="outlined"
          fullWidth
          margin="normal"
          value={searchTerm}
          onChange={handleSearch}
          placeholder="Inserisci l'ID del cavo da cercare"
        />

        <Button 
          variant="contained" 
          color="primary" 
          onClick={loadCavi}
          disabled={loading}
          sx={{ mt: 2, mb: 3 }}
        >
          {loading ? <CircularProgress size={24} /> : "Aggiorna lista"}
        </Button>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : filteredCavi.length === 0 ? (
          <Box sx={{ mt: 2 }}>
            <Alert severity="info" sx={{ mb: 2 }}>
              {searchTerm ? 
                `Nessun cavo installato trovato con ID contenente "${searchTerm}".` : 
                "Nessun cavo installato trovato in questo cantiere."}
            </Alert>
            <Typography variant="body2" sx={{ mt: 2, mb: 1 }}>
              Possibili motivi:
            </Typography>
            <ul>
              <li>
                <Typography variant="body2">
                  Non ci sono cavi nello stato "INSTALLATO" in questo cantiere.
                </Typography>
              </li>
              <li>
                <Typography variant="body2">
                  Il cavo che stai cercando potrebbe essere in uno stato diverso da "INSTALLATO" (es. "DA INSTALLARE", "POSATO").
                </Typography>
              </li>
              <li>
                <Typography variant="body2">
                  Il cavo potrebbe essere marcato come SPARE.
                </Typography>
              </li>
            </ul>
            <Typography variant="body2" sx={{ mt: 2 }}>
              Suggerimenti:
            </Typography>
            <ul>
              <li>
                <Typography variant="body2">
                  Verifica lo stato del cavo nella pagina di gestione cavi.
                </Typography>
              </li>
              <li>
                <Typography variant="body2">
                  Assicurati che il cavo sia stato installato correttamente.
                </Typography>
              </li>
            </ul>
          </Box>
        ) : (
          <TableContainer component={Paper} sx={{ mt: 2 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>ID Cavo</TableCell>
                  <TableCell>Stato</TableCell>
                  <TableCell>Lato Partenza</TableCell>
                  <TableCell>Lato Arrivo</TableCell>
                  <TableCell>Resp. Partenza</TableCell>
                  <TableCell>Resp. Arrivo</TableCell>
                  <TableCell>Azioni</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredCavi.map((cavo) => (
                  <TableRow key={cavo.id_cavo}>
                    <TableCell>{cavo.id_cavo}</TableCell>
                    <TableCell>{formatStatoCollegamenti(cavo.collegamenti)}</TableCell>
                    <TableCell>{formatStatoLato(cavo.collegamenti, 'partenza')}</TableCell>
                    <TableCell>{formatStatoLato(cavo.collegamenti, 'arrivo')}</TableCell>
                    <TableCell>{cavo.responsabile_partenza || '-'}</TableCell>
                    <TableCell>{cavo.responsabile_arrivo || '-'}</TableCell>
                    <TableCell>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => handleCavoSelect(cavo)}
                      >
                        Gestisci
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>

      {/* Dialog per la gestione dei collegamenti */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Gestione Collegamenti Cavo</DialogTitle>
        <DialogContent>
          {selectedCavo && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Cavo selezionato: {selectedCavo.id_cavo}
              </Typography>

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle2" gutterBottom>
                Informazioni Cavo:
              </Typography>
              <Typography variant="body2">
                Partenza (FROM): {selectedCavo.ubicazione_partenza || 'N/A'}
              </Typography>
              <Typography variant="body2">
                Arrivo (TO): {selectedCavo.ubicazione_arrivo || 'N/A'}
              </Typography>

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle2" gutterBottom>
                Stato Collegamenti:
              </Typography>
              <Typography variant="body2">
                Lato Partenza: {formatStatoLato(selectedCavo.collegamenti, 'partenza')}
                {(selectedCavo.collegamenti & 1) ? ` (Responsabile: ${selectedCavo.responsabile_partenza || 'N/A'})` : ''}
              </Typography>
              <Typography variant="body2">
                Lato Arrivo: {formatStatoLato(selectedCavo.collegamenti, 'arrivo')}
                {(selectedCavo.collegamenti & 2) ? ` (Responsabile: ${selectedCavo.responsabile_arrivo || 'N/A'})` : ''}
              </Typography>

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle2" gutterBottom>
                Gestisci Collegamenti:
              </Typography>

              <FormControl component="fieldset" sx={{ mt: 2 }}>
                <Typography variant="body2" gutterBottom>
                  Seleziona il lato da gestire:
                </Typography>
                <RadioGroup
                  name="lato"
                  value={formData.lato}
                  onChange={handleFormChange}
                  row
                >
                  <FormControlLabel value="partenza" control={<Radio />} label="Lato Partenza" />
                  <FormControlLabel value="arrivo" control={<Radio />} label="Lato Arrivo" />
                </RadioGroup>
              </FormControl>

              <TextField
                margin="dense"
                name="responsabile"
                label="Responsabile del collegamento"
                fullWidth
                variant="outlined"
                value={formData.responsabile}
                onChange={handleFormChange}
                sx={{ mt: 2 }}
                helperText="Lascia vuoto per usare 'cantiere' come valore predefinito"
              />

              <Typography variant="body2" color="primary" sx={{ mt: 2 }}>
                {formData.lato === 'partenza' && (selectedCavo.collegamenti & 1) ? 
                  "Attenzione: Il lato partenza è già collegato. Procedendo verrà scollegato." : 
                  formData.lato === 'arrivo' && (selectedCavo.collegamenti & 2) ? 
                  "Attenzione: Il lato arrivo è già collegato. Procedendo verrà scollegato." : 
                  `Procedendo verrà collegato il lato ${formData.lato}.`}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Annulla</Button>
          <Button
            onClick={handleSaveCollegamento}
            disabled={loading || !selectedCavo}
            variant="contained"
            color="primary"
          >
            {loading ? <CircularProgress size={24} /> : 
              ((formData.lato === 'partenza' && (selectedCavo?.collegamenti & 1)) || 
               (formData.lato === 'arrivo' && (selectedCavo?.collegamenti & 2))) ? 
                "Scollega" : "Collega"}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CollegamentiCavo;
