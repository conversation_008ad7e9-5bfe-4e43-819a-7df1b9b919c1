import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  Box,
  CircularProgress
} from '@mui/material';

/**
 * Dialog per la configurazione della numerazione delle bobine.
 * Mostrato solo per il primo inserimento di una bobina in un cantiere.
 *
 * @param {Object} props - Proprietà del componente
 * @param {boolean} props.open - Indica se il dialog è aperto
 * @param {Function} props.onClose - Funzione chiamata alla chiusura del dialog
 * @param {Function} props.onConfirm - Funzione chiamata alla conferma della configurazione
 */
const ConfigurazioneDialog = ({ open, onClose, onConfirm }) => {
  const [configValue, setConfigValue] = useState('s');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset dello stato quando il dialog viene aperto
  useEffect(() => {
    if (open) {
      setIsSubmitting(false);
      setConfigValue('s'); // Reset al valore di default
      console.log('ConfigurazioneDialog: Dialog aperto');
    }
  }, [open]);

  const handleConfirm = () => {
    console.log('ConfigurazioneDialog: Confermando con valore:', configValue);
    setIsSubmitting(true);
    // Previene click multipli
    setTimeout(() => {
      onConfirm(configValue);
      setIsSubmitting(false);
    }, 300);
  };

  const handleClose = () => {
    if (isSubmitting) return; // Previene chiusura durante l'invio
    console.log('ConfigurazioneDialog: Chiudendo dialog');
    onClose();
  };

  // Aggiungiamo un log per verificare quando il componente viene renderizzato
  console.log('Rendering ConfigurazioneDialog, open:', open);

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        // Impedisce la chiusura cliccando fuori
        if (reason !== 'backdropClick') {
          handleClose();
        }
      }}
      maxWidth="sm"
      fullWidth
      disableEscapeKeyDown={true}  // Impedisce la chiusura con ESC
      style={{ zIndex: 9999 }}  // Assicura che il dialog sia in primo piano
    >
      <DialogTitle sx={{ bgcolor: 'primary.main', color: 'white', fontWeight: 'bold' }}>
        Configurazione Numerazione Bobine
      </DialogTitle>
      <DialogContent sx={{ py: 3 }}>
        <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
          <Typography variant="h6" gutterBottom color="primary.main">
            Questa è la prima bobina per questo cantiere.
          </Typography>
          <Typography variant="body1" gutterBottom fontWeight="medium">
            Scegli come vuoi gestire la numerazione delle bobine:
          </Typography>
        </Box>
        <FormControl component="fieldset" sx={{ mt: 2, width: '100%' }}>
          <RadioGroup
            value={configValue}
            onChange={(e) => setConfigValue(e.target.value)}
          >
            <FormControlLabel
              value="s"
              control={<Radio color="primary" size="medium" />}
              label={
                <Box>
                  <Typography variant="subtitle1" fontWeight="bold">Usa numeri progressivi</Typography>
                  <Typography variant="body2" color="text.secondary">Es. 1, 2, 3, ...</Typography>
                </Box>
              }
              sx={{ mb: 2, p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}
            />
            <FormControlLabel
              value="n"
              control={<Radio color="primary" size="medium" />}
              label={
                <Box>
                  <Typography variant="subtitle1" fontWeight="bold">Inserisci manualmente l'ID della bobina</Typography>
                  <Typography variant="body2" color="text.secondary">Es. A123, SPEC01, ecc.</Typography>
                </Box>
              }
              sx={{ p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}
            />
          </RadioGroup>
        </FormControl>
        <Box sx={{ mt: 3, p: 2, bgcolor: '#fff3e0', borderRadius: 1, border: '1px solid #ffe0b2' }}>
          <Typography variant="body2" color="warning.dark">
            <strong>Nota importante:</strong> Questa configurazione sarà utilizzata per tutte le bobine di questo cantiere e non potrà essere modificata in seguito.
          </Typography>
        </Box>
      </DialogContent>
      <DialogActions sx={{ px: 3, py: 2, bgcolor: '#f9f9f9' }}>
        <Button
          onClick={handleClose}
          disabled={isSubmitting}
          variant="outlined"
          sx={{ px: 3 }}
        >
          Annulla
        </Button>
        <Button
          onClick={handleConfirm}
          variant="contained"
          color="primary"
          disabled={isSubmitting}
          size="large"
          sx={{ px: 4, fontWeight: 'bold' }}
        >
          {isSubmitting ? (
            <>
              <CircularProgress size={20} sx={{ mr: 1 }} color="inherit" />
              Elaborazione...
            </>
          ) : (
            'Conferma Scelta'
          )}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfigurazioneDialog;
