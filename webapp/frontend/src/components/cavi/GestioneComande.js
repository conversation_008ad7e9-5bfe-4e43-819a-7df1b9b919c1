import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemButton,
  Divider,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Search as SearchIcon,
  Print as PrintIcon,
  ViewList as ViewListIcon,
  Assignment as AssignmentIcon
} from '@mui/icons-material';
import comandeService from '../../services/comandeService';
import caviService from '../../services/caviService';

const GestioneComande = ({ cantiereId, onSuccess, onError }) => {
  const [loading, setLoading] = useState(false);
  const [comande, setComande] = useState([]);
  const [cavi, setCavi] = useState([]);
  const [selectedOption, setSelectedOption] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [selectedComanda, setSelectedComanda] = useState(null);
  const [selectedCavo, setSelectedCavo] = useState(null);
  const [formData, setFormData] = useState({
    numero_comanda: '',
    data_comanda: '',
    tipo_comanda: 'POSA',
    id_cavo: '',
    operatore: '',
    note: ''
  });

  // Carica le comande
  const loadComande = async () => {
    try {
      setLoading(true);
      const data = await comandeService.getComande(cantiereId);
      setComande(data);
    } catch (error) {
      onError('Errore nel caricamento delle comande');
      console.error('Errore nel caricamento delle comande:', error);
    } finally {
      setLoading(false);
    }
  };

  // Carica i cavi disponibili
  const loadCavi = async () => {
    try {
      setLoading(true);
      const data = await caviService.getCavi(cantiereId);
      setCavi(data);
    } catch (error) {
      onError('Errore nel caricamento dei cavi');
      console.error('Errore nel caricamento dei cavi:', error);
    } finally {
      setLoading(false);
    }
  };

  // Carica i dati all'avvio del componente
  useEffect(() => {
    loadComande();
  }, [cantiereId]);

  // Gestisce la selezione di un'opzione dal menu
  const handleOptionSelect = (option) => {
    setSelectedOption(option);

    if (option === 'visualizzaComande') {
      loadComande();
    } else if (option === 'creaComanda') {
      loadCavi();
      setDialogType('creaComanda');
      // Imposta la data di oggi come default
      const today = new Date().toISOString().split('T')[0];
      setFormData({
        ...formData,
        data_comanda: today
      });
      setOpenDialog(true);
    } else if (option === 'modificaComanda') {
      loadComande();
      setDialogType('selezionaComanda');
      setOpenDialog(true);
    } else if (option === 'eliminaComanda') {
      loadComande();
      setDialogType('eliminaComanda');
      setOpenDialog(true);
    } else if (option === 'stampaComanda') {
      loadComande();
      setDialogType('stampaComanda');
      setOpenDialog(true);
    } else if (option === 'assegnaComanda') {
      loadComande();
      loadCavi();
      setDialogType('selezionaCavoComanda');
      setOpenDialog(true);
    }
  };

  // Gestisce la chiusura del dialog
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedComanda(null);
    setSelectedCavo(null);
    setFormData({
      numero_comanda: '',
      data_comanda: '',
      tipo_comanda: 'POSA',
      id_cavo: '',
      operatore: '',
      note: ''
    });
  };

  // Gestisce la selezione di una comanda
  const handleComandaSelect = (comanda) => {
    setSelectedComanda(comanda);

    if (dialogType === 'selezionaComanda') {
      setDialogType('modificaComanda');
      setFormData({
        numero_comanda: comanda.numero_comanda,
        data_comanda: comanda.data_comanda.split('T')[0],
        tipo_comanda: comanda.tipo_comanda,
        id_cavo: comanda.id_cavo || '',
        operatore: comanda.operatore || '',
        note: comanda.note || ''
      });
    } else if (dialogType === 'stampaComanda') {
      handleStampaComanda(comanda.id_comanda);
    }
  };

  // Gestisce la selezione di un cavo
  const handleCavoSelect = (cavo) => {
    setSelectedCavo(cavo);
    setFormData({
      ...formData,
      id_cavo: cavo.id_cavo
    });

    if (dialogType === 'selezionaCavoComanda') {
      setDialogType('selezionaComandaPerCavo');
    }
  };

  // Gestisce il cambio dei valori nel form
  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Gestisce la creazione di una comanda
  const handleCreaComanda = async () => {
    try {
      if (!formData.numero_comanda || !formData.data_comanda || !formData.tipo_comanda) {
        onError('Compila tutti i campi obbligatori');
        return;
      }

      setLoading(true);
      await comandeService.createComanda(cantiereId, formData);
      onSuccess('Comanda creata con successo');
      handleCloseDialog();
      loadComande();
    } catch (error) {
      onError('Errore nella creazione della comanda: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nella creazione della comanda:', error);
    } finally {
      setLoading(false);
    }
  };

  // Gestisce la modifica di una comanda
  const handleModificaComanda = async () => {
    try {
      if (!formData.numero_comanda || !formData.data_comanda || !formData.tipo_comanda) {
        onError('Compila tutti i campi obbligatori');
        return;
      }

      setLoading(true);
      await comandeService.updateComanda(cantiereId, selectedComanda.id_comanda, formData);
      onSuccess('Comanda modificata con successo');
      handleCloseDialog();
      loadComande();
    } catch (error) {
      onError('Errore nella modifica della comanda: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nella modifica della comanda:', error);
    } finally {
      setLoading(false);
    }
  };

  // Gestisce l'eliminazione di una comanda
  const handleEliminaComanda = async () => {
    try {
      if (!selectedComanda) {
        onError('Seleziona una comanda da eliminare');
        return;
      }

      setLoading(true);
      await comandeService.deleteComanda(cantiereId, selectedComanda.id_comanda);
      onSuccess('Comanda eliminata con successo');
      handleCloseDialog();
      loadComande();
    } catch (error) {
      onError('Errore nell\'eliminazione della comanda: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nell\'eliminazione della comanda:', error);
    } finally {
      setLoading(false);
    }
  };

  // Gestisce l'assegnazione di una comanda a un cavo
  const handleAssegnaComanda = async () => {
    try {
      if (!selectedCavo || !selectedComanda) {
        onError('Seleziona un cavo e una comanda');
        return;
      }

      setLoading(true);
      await comandeService.assignComandaToCavo(cantiereId, selectedComanda.id_comanda, selectedCavo.id_cavo);
      onSuccess('Comanda assegnata al cavo con successo');
      handleCloseDialog();
      loadComande();
    } catch (error) {
      onError('Errore nell\'assegnazione della comanda: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nell\'assegnazione della comanda:', error);
    } finally {
      setLoading(false);
    }
  };

  // Gestisce la stampa di una comanda
  const handleStampaComanda = async (idComanda) => {
    try {
      setLoading(true);
      const response = await comandeService.printComanda(cantiereId, idComanda);

      // Apri il PDF in una nuova finestra
      window.open(response.file_url, '_blank');

      onSuccess('PDF della comanda generato con successo');
      handleCloseDialog();
    } catch (error) {
      onError('Errore nella generazione del PDF della comanda: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nella generazione del PDF della comanda:', error);
    } finally {
      setLoading(false);
    }
  };

  // Renderizza le comande in formato tabella
  const renderComandeTable = () => {
    if (comande.length === 0) {
      return (
        <Alert severity="info">Nessuna comanda trovata</Alert>
      );
    }

    return (
      <TableContainer component={Paper}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>ID</TableCell>
              <TableCell>Numero</TableCell>
              <TableCell>Data</TableCell>
              <TableCell>Tipo</TableCell>
              <TableCell>Cavo</TableCell>
              <TableCell>Operatore</TableCell>
              <TableCell>Azioni</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {comande.map((comanda) => (
              <TableRow key={comanda.id_comanda}>
                <TableCell>{comanda.id_comanda}</TableCell>
                <TableCell>{comanda.numero_comanda}</TableCell>
                <TableCell>{new Date(comanda.data_comanda).toLocaleDateString()}</TableCell>
                <TableCell>{comanda.tipo_comanda}</TableCell>
                <TableCell>{comanda.id_cavo || '-'}</TableCell>
                <TableCell>{comanda.operatore || '-'}</TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    onClick={() => {
                      setSelectedComanda(comanda);
                      setDialogType('modificaComanda');
                      setFormData({
                        numero_comanda: comanda.numero_comanda,
                        data_comanda: comanda.data_comanda.split('T')[0],
                        tipo_comanda: comanda.tipo_comanda,
                        id_cavo: comanda.id_cavo || '',
                        operatore: comanda.operatore || '',
                        note: comanda.note || ''
                      });
                      setOpenDialog(true);
                    }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => handleStampaComanda(comanda.id_comanda)}
                  >
                    <PrintIcon fontSize="small" />
                  </IconButton>
                  <IconButton
                    size="small"
                    color="error"
                    onClick={() => {
                      setSelectedComanda(comanda);
                      setDialogType('eliminaComanda');
                      setOpenDialog(true);
                    }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  // Renderizza il dialog in base al tipo
  const renderDialog = () => {
    if (dialogType === 'creaComanda' || dialogType === 'modificaComanda') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
          <DialogTitle>
            {dialogType === 'creaComanda' ? 'Crea Nuova Comanda' : 'Modifica Comanda'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="numero_comanda"
                  label="Numero Comanda"
                  fullWidth
                  variant="outlined"
                  value={formData.numero_comanda}
                  onChange={handleFormChange}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="data_comanda"
                  label="Data Comanda"
                  type="date"
                  fullWidth
                  variant="outlined"
                  value={formData.data_comanda}
                  onChange={handleFormChange}
                  InputLabelProps={{ shrink: true }}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel>Tipo Comanda</InputLabel>
                  <Select
                    name="tipo_comanda"
                    value={formData.tipo_comanda}
                    onChange={handleFormChange}
                    label="Tipo Comanda"
                    required
                  >
                    <MenuItem value="POSA">POSA</MenuItem>
                    <MenuItem value="PARTENZA">PARTENZA</MenuItem>
                    <MenuItem value="ARRIVO">ARRIVO</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="id_cavo"
                  label="ID Cavo (opzionale)"
                  fullWidth
                  variant="outlined"
                  value={formData.id_cavo}
                  onChange={handleFormChange}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="operatore"
                  label="Operatore"
                  fullWidth
                  variant="outlined"
                  value={formData.operatore}
                  onChange={handleFormChange}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  name="note"
                  label="Note"
                  fullWidth
                  multiline
                  rows={3}
                  variant="outlined"
                  value={formData.note}
                  onChange={handleFormChange}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
            <Button
              onClick={dialogType === 'creaComanda' ? handleCreaComanda : handleModificaComanda}
              disabled={loading || !formData.numero_comanda || !formData.data_comanda || !formData.tipo_comanda}
              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
            >
              Salva
            </Button>
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'selezionaComanda') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
          <DialogTitle>Seleziona Comanda da Modificare</DialogTitle>
          <DialogContent>
            {loading ? (
              <CircularProgress />
            ) : comande.length === 0 ? (
              <Alert severity="info">Nessuna comanda disponibile</Alert>
            ) : (
              <List>
                {comande.map((comanda) => (
                  <ListItem
                    button
                    key={comanda.id_comanda}
                    onClick={() => handleComandaSelect(comanda)}
                  >
                    <ListItemText
                      primary={`${comanda.numero_comanda} - ${comanda.tipo_comanda}`}
                      secondary={`Data: ${new Date(comanda.data_comanda).toLocaleDateString()} - Cavo: ${comanda.id_cavo || 'Non assegnato'}`}
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'eliminaComanda') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>Elimina Comanda</DialogTitle>
          <DialogContent>
            {!selectedComanda ? (
              loading ? (
                <CircularProgress />
              ) : comande.length === 0 ? (
                <Alert severity="info">Nessuna comanda disponibile</Alert>
              ) : (
                <List>
                  {comande.map((comanda) => (
                    <ListItem
                      button
                      key={comanda.id_comanda}
                      onClick={() => setSelectedComanda(comanda)}
                    >
                      <ListItemText
                        primary={`${comanda.numero_comanda} - ${comanda.tipo_comanda}`}
                        secondary={`Data: ${new Date(comanda.data_comanda).toLocaleDateString()}`}
                      />
                    </ListItem>
                  ))}
                </List>
              )
            ) : (
              <Box>
                <Alert severity="warning" sx={{ mb: 2 }}>
                  Sei sicuro di voler eliminare la comanda {selectedComanda.numero_comanda}?
                </Alert>
                <Typography variant="body1">
                  Questa operazione non può essere annullata.
                </Typography>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
            {selectedComanda && (
              <Button
                onClick={handleEliminaComanda}
                disabled={loading}
                color="error"
                startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}
              >
                Elimina
              </Button>
            )}
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'stampaComanda') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
          <DialogTitle>Seleziona Comanda da Stampare</DialogTitle>
          <DialogContent>
            {loading ? (
              <CircularProgress />
            ) : comande.length === 0 ? (
              <Alert severity="info">Nessuna comanda disponibile</Alert>
            ) : (
              <List>
                {comande.map((comanda) => (
                  <ListItem
                    button
                    key={comanda.id_comanda}
                    onClick={() => handleComandaSelect(comanda)}
                  >
                    <ListItemText
                      primary={`${comanda.numero_comanda} - ${comanda.tipo_comanda}`}
                      secondary={`Data: ${new Date(comanda.data_comanda).toLocaleDateString()} - Cavo: ${comanda.id_cavo || 'Non assegnato'}`}
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'selezionaCavoComanda') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
          <DialogTitle>Seleziona Cavo per Assegnare Comanda</DialogTitle>
          <DialogContent>
            {loading ? (
              <CircularProgress />
            ) : cavi.length === 0 ? (
              <Alert severity="info">Nessun cavo disponibile</Alert>
            ) : (
              <List>
                {cavi.map((cavo) => (
                  <ListItem
                    button
                    key={cavo.id_cavo}
                    onClick={() => handleCavoSelect(cavo)}
                  >
                    <ListItemText
                      primary={cavo.id_cavo}
                      secondary={`${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`}
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'selezionaComandaPerCavo') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
          <DialogTitle>Seleziona Comanda da Assegnare al Cavo {selectedCavo?.id_cavo}</DialogTitle>
          <DialogContent>
            {loading ? (
              <CircularProgress />
            ) : comande.length === 0 ? (
              <Alert severity="info">Nessuna comanda disponibile</Alert>
            ) : (
              <List>
                {comande.map((comanda) => (
                  <ListItem
                    button
                    key={comanda.id_comanda}
                    onClick={() => setSelectedComanda(comanda)}
                  >
                    <ListItemText
                      primary={`${comanda.numero_comanda} - ${comanda.tipo_comanda}`}
                      secondary={`Data: ${new Date(comanda.data_comanda).toLocaleDateString()}`}
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
            {selectedComanda && (
              <Button
                onClick={handleAssegnaComanda}
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
              >
                Assegna
              </Button>
            )}
          </DialogActions>
        </Dialog>
      );
    }

    return null;
  };

  return (
    <Box>
      {selectedOption === 'visualizzaComande' && !openDialog ? (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Comande
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            renderComandeTable()
          )}
        </Paper>
      ) : !openDialog ? (
        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          {!selectedOption ? (
            <Typography variant="body1">
              Seleziona un'opzione dal menu principale per iniziare.
            </Typography>
          ) : (
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                {selectedOption === 'creaComanda' && 'Crea nuova comanda'}
                {selectedOption === 'modificaComanda' && 'Modifica comanda'}
                {selectedOption === 'eliminaComanda' && 'Elimina comanda'}
                {selectedOption === 'stampaComanda' && 'Stampa comanda'}
                {selectedOption === 'assegnaComanda' && 'Assegna comanda a cavo'}
              </Typography>
              <CircularProgress sx={{ mt: 2 }} />
            </Box>
          )}
        </Paper>
      ) : null}

      {renderDialog()}
    </Box>
  );
};

export default GestioneComande;
