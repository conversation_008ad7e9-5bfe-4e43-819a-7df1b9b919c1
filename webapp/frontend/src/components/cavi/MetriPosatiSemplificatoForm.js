import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  Chip,
  Divider,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import caviService from '../../services/caviService';
import parcoCaviService from '../../services/parcoCaviService';
import {
  CABLE_STATES,
  REEL_STATES,
  determineCableState,
  determineReelState,
  canModifyCable,
  isCableSpare,
  isCableInstalled,
  getCableStateColor,
  getReelStateColor
} from '../../utils/stateUtils';
import { redirectToVisualizzaCavi } from '../../utils/navigationUtils';
import IncompatibleReelDialog from './IncompatibleReelDialog';

/**
 * Componente per l'inserimento dei metri posati di un cavo
 * Versione ultra-semplificata con workflow compresso in un'unica pagina
 *
 * @param {Object} props - Proprietà del componente
 * @param {string} props.cantiereId - ID del cantiere
 * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione
 * @param {Function} props.onError - Funzione chiamata in caso di errore
 */
const MetriPosatiSemplificatoForm = ({ cantiereId, onSuccess, onError }) => {
  const navigate = useNavigate();

  // Stati per i dati
  const [cavi, setCavi] = useState([]);
  const [bobine, setBobine] = useState([]);
  const [selectedCavo, setSelectedCavo] = useState(null);
  const [formData, setFormData] = useState({
    id_cavo: '',
    metri_posati: '',
    id_bobina: ''
  });

  // Stati per il caricamento
  const [loading, setLoading] = useState(false);
  const [caviLoading, setCaviLoading] = useState(false);
  const [bobineLoading, setBobineLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // Stati per la validazione
  const [formErrors, setFormErrors] = useState({});
  const [formWarnings, setFormWarnings] = useState({});

  // Stati per i dialoghi speciali
  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);
  const [incompatibleReelData, setIncompatibleReelData] = useState({ cavo: null, bobina: null });
  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);
  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);

  // Carica la lista dei cavi e delle bobine all'avvio
  useEffect(() => {
    loadCavi();
    loadBobine();
  }, [cantiereId]);

  // Carica la lista dei cavi
  const loadCavi = async () => {
    try {
      setCaviLoading(true);
      const caviData = await caviService.getCavi(cantiereId);

      // Filtra i cavi che non sono SPARE
      const caviAttivi = caviData.filter(cavo => !isCableSpare(cavo));

      setCavi(caviAttivi);
    } catch (error) {
      console.error('Errore nel caricamento dei cavi:', error);
      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));
    } finally {
      setCaviLoading(false);
    }
  };

  // Carica la lista delle bobine
  const loadBobine = async () => {
    try {
      setBobineLoading(true);
      console.log('Caricamento bobine per cantiere:', cantiereId);
      const bobineData = await parcoCaviService.getBobine(cantiereId);
      console.log('Bobine caricate:', bobineData);
      console.log('Dettaglio bobine:');
      bobineData.forEach(bobina => {
        console.log(`Bobina ${bobina.id_bobina}:`, {
          tipologia: bobina.tipologia,
          sezione: bobina.sezione,
          metri_residui: bobina.metri_residui,
          stato_bobina: bobina.stato_bobina
        });
      });
      console.log('IMPORTANTE: Impostazione delle bobine nello stato...');
      setBobine(bobineData);
      console.log('Bobine impostate nello stato:', bobineData.length);
    } catch (error) {
      console.error('Errore nel caricamento delle bobine:', error);
      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));
    } finally {
      setBobineLoading(false);
    }
  };

  // Gestisce la selezione di un cavo
  const handleCavoSelect = (cavo) => {
    console.log('Cavo selezionato:', cavo);
    console.log('Dettaglio cavo:', {
      id_cavo: cavo.id_cavo,
      tipologia: cavo.tipologia,
      sezione: cavo.sezione,
      metri_teorici: cavo.metri_teorici,
      stato_installazione: cavo.stato_installazione
    });

    // Verifica se il cavo è già posato
    if (isCableInstalled(cavo)) {
      console.log('Cavo già posato, mostro dialog');
      setAlreadyLaidCavo(cavo);
      setShowAlreadyLaidDialog(true);
      return;
    }

    console.log('Impostazione selectedCavo...');
    setSelectedCavo(cavo);
    console.log('selectedCavo impostato a:', cavo.id_cavo);

    setFormData({
      id_cavo: cavo.id_cavo,
      metri_posati: '',
      id_bobina: ''
    });
    setFormErrors({});
    setFormWarnings({});

    // Log per debug - verifica se ci sono bobine compatibili
    console.log('Verifica bobine compatibili per il cavo selezionato...');

    // Forza il filtraggio delle bobine compatibili
    const compatibleBobineList = filterCompatibleBobine(cavo);
    console.log('Bobine compatibili trovate (forzato):', compatibleBobineList.length);
    setCompatibleBobine(compatibleBobineList);
  };

  // Gestisce la modifica dei campi del form
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Validazione in tempo reale
    validateField(name, value);
  };

  // Validazione di un singolo campo
  const validateField = (name, value) => {
    const newErrors = { ...formErrors };
    const newWarnings = { ...formWarnings };

    if (name === 'metri_posati') {
      // Validazione metri posati
      if (value === '') {
        newErrors.metri_posati = 'I metri posati sono obbligatori';
      } else if (isNaN(value) || parseFloat(value) < 0) {
        newErrors.metri_posati = 'I metri posati devono essere un numero positivo';
      } else {
        delete newErrors.metri_posati;

        // Avvisi sui metri posati
        const metriPosati = parseFloat(value);
        if (selectedCavo && metriPosati > selectedCavo.metri_teorici) {
          newWarnings.metri_posati = `I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`;
        } else {
          delete newWarnings.metri_posati;
        }

        // Avvisi sulla bobina selezionata
        if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {
          const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);
          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {
            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;
          } else {
            delete newWarnings.id_bobina;
          }
        }
      }
    }

    if (name === 'id_bobina') {
      // Validazione bobina
      if (value === '') {
        newErrors.id_bobina = 'La bobina è obbligatoria';
      } else {
        delete newErrors.id_bobina;

        // Avvisi sulla bobina selezionata
        if (value !== 'BOBINA_VUOTA' && formData.metri_posati) {
          const metriPosati = parseFloat(formData.metri_posati);
          const selectedBobina = bobine.find(b => b.id_bobina === value);
          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {
            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;
          } else {
            delete newWarnings.id_bobina;
          }
        }
      }
    }

    setFormErrors(newErrors);
    setFormWarnings(newWarnings);
  };

  // Validazione completa del form
  const validateForm = () => {
    const newErrors = {};

    // Validazione metri posati
    if (!formData.metri_posati) {
      newErrors.metri_posati = 'I metri posati sono obbligatori';
    } else if (isNaN(formData.metri_posati) || parseFloat(formData.metri_posati) < 0) {
      newErrors.metri_posati = 'I metri posati devono essere un numero positivo';
    }

    // Validazione bobina
    if (!formData.id_bobina) {
      newErrors.id_bobina = 'La bobina è obbligatoria';
    }

    setFormErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Verifica la compatibilità tra cavo e bobina
  const checkCompatibility = () => {
    if (formData.id_bobina === 'BOBINA_VUOTA') {
      return true; // BOBINA_VUOTA è sempre compatibile
    }

    const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);
    if (!selectedBobina) {
      return false;
    }

    // Verifica compatibilità tipologia
    const tipologiaCompatibile = selectedCavo.tipologia === selectedBobina.tipologia;

    // Verifica compatibilità sezione
    const sezioneCompatibile = String(selectedCavo.sezione) === String(selectedBobina.sezione);

    return tipologiaCompatibile && sezioneCompatibile;
  };

  // Gestisce il salvataggio dei dati
  const handleSave = async () => {
    // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA
    if (bobine.length === 0 && !bobineLoading) {
      if (!formData.metri_posati || isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {
        setFormErrors({
          ...formErrors,
          metri_posati: 'I metri posati sono obbligatori e devono essere maggiori di zero'
        });
        return;
      }

      // Imposta BOBINA_VUOTA e procedi con il salvataggio
      formData.id_bobina = 'BOBINA_VUOTA';
    } else {
      // Validazione completa
      if (!validateForm()) {
        return;
      }

      // Verifica compatibilità
      if (!checkCompatibility()) {
        // Mostra dialog per incompatibilità
        const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);
        setIncompatibleReelData({
          cavo: selectedCavo,
          bobina: selectedBobina
        });
        setShowIncompatibleReelDialog(true);
        return;
      }
    }

    // Procedi con il salvataggio
    try {
      setSaving(true);

      // Converti metri posati in numero
      const metriPosati = parseFloat(formData.metri_posati);

      // Chiamata API
      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');
      console.log('- cantiereId:', cantiereId);
      console.log('- id_cavo:', formData.id_cavo);
      console.log('- metri_posati:', metriPosati);
      console.log('- id_bobina:', formData.id_bobina);

      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER
      await caviService.updateMetriPosati(
        cantiereId,
        formData.id_cavo,
        metriPosati,
        formData.id_bobina,
        true // Forza sempre a true per evitare blocchi
      );

      // Mostra messaggio di successo
      onSuccess('Metri posati aggiornati con successo');

      // Resetta il form
      setSelectedCavo(null);
      setFormData({
        id_cavo: '',
        metri_posati: '',
        id_bobina: ''
      });

      // Ricarica i dati
      loadCavi();
      loadBobine();
    } catch (error) {
      console.error('Errore durante il salvataggio:', error);
      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));
    } finally {
      setSaving(false);
    }
  };

  // Gestisce l'aggiornamento del cavo per compatibilità
  const handleUpdateCavoForCompatibility = async () => {
    try {
      setSaving(true);
      setShowIncompatibleReelDialog(false);

      const { cavo, bobina } = incompatibleReelData;

      // Aggiorna il cavo per renderlo compatibile con la bobina
      await caviService.updateCavoForCompatibility(
        cantiereId,
        cavo.id_cavo,
        {
          id_bobina: bobina.id_bobina,
          tipologia: bobina.tipologia,
          sezione: bobina.sezione
        }
      );

      // Procedi con l'aggiornamento dei metri posati
      await caviService.updateMetriPosati(
        cantiereId,
        formData.id_cavo,
        parseFloat(formData.metri_posati),
        formData.id_bobina,
        true // Forza sempre a true per evitare blocchi
      );

      // Mostra messaggio di successo
      onSuccess('Cavo aggiornato e metri posati registrati con successo');

      // Resetta il form
      setSelectedCavo(null);
      setFormData({
        id_cavo: '',
        metri_posati: '',
        id_bobina: ''
      });

      // Ricarica i dati
      loadCavi();
      loadBobine();
    } catch (error) {
      console.error('Errore durante l\'aggiornamento del cavo:', error);
      onError('Errore durante l\'aggiornamento del cavo: ' + (error.message || 'Errore sconosciuto'));
    } finally {
      setSaving(false);
    }
  };

  // Gestisce la chiusura del dialogo per cavi già posati
  const handleCloseAlreadyLaidDialog = () => {
    setShowAlreadyLaidDialog(false);
    setAlreadyLaidCavo(null);
  };

  // Gestisce la selezione di un altro cavo
  const handleSelectAnotherCable = () => {
    handleCloseAlreadyLaidDialog();
  };

  // Gestisce l'opzione di modificare la bobina di un cavo già posato
  const handleModifyReel = () => {
    if (alreadyLaidCavo) {
      navigate(`/dashboard/cavi/posa/modifica-bobina?cavoId=${alreadyLaidCavo.id_cavo}`);
    }
    handleCloseAlreadyLaidDialog();
  };

  // Stato per le bobine compatibili
  const [compatibleBobine, setCompatibleBobine] = useState([]);

  // Aggiorna le bobine compatibili quando viene selezionato un cavo o cambiano le bobine disponibili
  useEffect(() => {
    console.log('useEffect per bobine compatibili - selectedCavo:', selectedCavo ? selectedCavo.id_cavo : 'nessuno');
    console.log('useEffect per bobine compatibili - bobine:', bobine.length);

    if (selectedCavo) {
      // Filtra le bobine compatibili localmente
      console.log('Chiamata a filterCompatibleBobine...');
      const filtered = filterCompatibleBobine(selectedCavo);
      console.log('Risultato di filterCompatibleBobine:', filtered.length);
      setCompatibleBobine(filtered);
      console.log('compatibleBobine impostato a:', filtered.length);
    } else {
      setCompatibleBobine([]);
      console.log('compatibleBobine impostato a vuoto');
    }
  }, [selectedCavo, bobine]);

  // Filtra le bobine compatibili localmente
  const filterCompatibleBobine = (cavo) => {
    if (!cavo) return [];

    console.log('Filtrando bobine compatibili per cavo:', cavo);
    console.log('Bobine disponibili:', bobine);
    console.log('Numero di bobine disponibili:', bobine.length);

    // Verifica se ci sono bobine disponibili
    if (bobine.length === 0) {
      console.log('ATTENZIONE: Nessuna bobina disponibile nel cantiere!');
      return [];
    }

    // Filtra le bobine compatibili con il cavo
    const filtered = bobine.filter(bobina => {
      // Normalizza i valori per un confronto più robusto
      // 1. Normalizza tipologia (trim e lowercase)
      // 2. Normalizza sezione (trim e conversione in stringa)
      // 3. Verifica stato bobina e metri residui
      const cavoTipologiaNorm = String(cavo.tipologia || '').trim().toLowerCase();
      const cavoSezioneNorm = String(cavo.sezione || '').trim();

      const bobinaTipologiaNorm = String(bobina.tipologia || '').trim().toLowerCase();
      const bobinaSezioneNorm = String(bobina.sezione || '').trim();

      // Verifica compatibilità
      const tipologiaMatch = bobinaTipologiaNorm === cavoTipologiaNorm;
      const sezioneMatch = bobinaSezioneNorm === cavoSezioneNorm;

      // Verifica stato bobina direttamente invece di usare determineReelState
      // Questo è più affidabile e corrisponde alla logica del backend
      const stateOk = bobina.stato_bobina !== 'Terminata' && bobina.stato_bobina !== 'Over';

      // Verifica che i metri residui siano positivi
      const metriOk = bobina.metri_residui > 0;

      const isCompatible = tipologiaMatch && sezioneMatch && stateOk && metriOk;

      // Log dettagliati per debug
      console.log(`Confronto dettagliato per bobina ${bobina.id_bobina}:`);
      console.log(`- Tipologia bobina (originale): "${bobina.tipologia}", tipo: ${typeof bobina.tipologia}`);
      console.log(`- Tipologia cavo (originale): "${cavo.tipologia}", tipo: ${typeof cavo.tipologia}`);
      console.log(`- Tipologia bobina (normalizzata): "${bobinaTipologiaNorm}"`);
      console.log(`- Tipologia cavo (normalizzata): "${cavoTipologiaNorm}"`);
      console.log(`- Sezione bobina (originale): "${bobina.sezione}", tipo: ${typeof bobina.sezione}`);
      console.log(`- Sezione cavo (originale): "${cavo.sezione}", tipo: ${typeof cavo.sezione}`);
      console.log(`- Sezione bobina (normalizzata): "${bobinaSezioneNorm}"`);
      console.log(`- Sezione cavo (normalizzata): "${cavoSezioneNorm}"`);
      console.log(`- Stato bobina: ${bobina.stato_bobina}`);
      console.log(`- Metri residui: ${bobina.metri_residui}`);
      console.log(`- Stato OK? ${stateOk}`);
      console.log(`- Metri OK? ${metriOk}`);

      // Log di riepilogo
      console.log(`Bobina ${bobina.id_bobina}:`, {
        'Tipologia bobina': `"${bobina.tipologia}"`,
        'Tipologia cavo': `"${cavo.tipologia}"`,
        'Tipologie uguali?': tipologiaMatch,
        'Sezione bobina': `"${String(bobina.sezione)}"`,
        'Sezione cavo': `"${String(cavo.sezione)}"`,
        'Sezioni uguali?': sezioneMatch,
        'Stato bobina': bobina.stato_bobina,
        'Metri residui': bobina.metri_residui,
        'Stato OK?': stateOk,
        'Metri OK?': metriOk,
        'Compatibile?': isCompatible
      });

      return isCompatible;
    });

    console.log('Bobine compatibili trovate:', filtered.length);
    if (filtered.length > 0) {
      console.log('Prima bobina compatibile:', filtered[0]);
    } else {
      console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');
    }

    return filtered;
  };

  // Funzione di utilità per ottenere le bobine compatibili (usata nel rendering)
  const getCompatibleBobine = () => {
    return compatibleBobine;
  };

  // Renderizza la tabella dei cavi
  const renderCaviTable = () => {
    if (caviLoading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      );
    }

    if (cavi.length === 0) {
      return (
        <Alert severity="info" sx={{ my: 2 }}>
          Nessun cavo disponibile per questo cantiere.
        </Alert>
      );
    }

    return (
      <>
        <Alert severity="info" sx={{ mb: 2 }}>
          Seleziona un cavo dalla tabella per inserire i metri posati. I cavi già installati sono disabilitati.
        </Alert>

        <TableContainer component={Paper} sx={{ mb: 3 }}>
          <Table size="small">
            <TableHead>
              <TableRow sx={{ bgcolor: '#e3f2fd' }}>
                <TableCell>ID Cavo</TableCell>
                <TableCell>Tipologia</TableCell>
                <TableCell>Ubicazione</TableCell>
                <TableCell>Metri Teorici</TableCell>
                <TableCell>Stato</TableCell>
                <TableCell>Azioni</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {cavi.map((cavo) => {
                const isInstalled = isCableInstalled(cavo);
                return (
                  <TableRow
                    key={cavo.id_cavo}
                    sx={{
                      bgcolor: isInstalled ? '#f5f5f5' : 'inherit',
                      '&:hover': { bgcolor: isInstalled ? '#f5f5f5' : '#f1f8e9' }
                    }}
                  >
                    <TableCell><strong>{cavo.id_cavo}</strong></TableCell>
                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>
                    <TableCell>Da: {cavo.ubicazione_partenza || 'N/A'}<br/>A: {cavo.ubicazione_arrivo || 'N/A'}</TableCell>
                    <TableCell>{cavo.metri_teorici || 'N/A'} m</TableCell>
                    <TableCell>
                      <Chip
                        label={cavo.stato_installazione || 'N/D'}
                        size="small"
                        color={getCableStateColor(cavo.stato_installazione)}
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Button
                        size="small"
                        variant="contained"
                        color="primary"
                        onClick={() => handleCavoSelect(cavo)}
                        disabled={isInstalled}
                      >
                        {isInstalled ? 'Già installato' : 'Seleziona'}
                      </Button>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      </>
    );
  };

  // Renderizza il form per inserimento metri e selezione bobina
  const renderForm = () => {
    if (!selectedCavo) return null;

    // Log per debug - verifica le bobine compatibili nel rendering
    const compatibleBobineList = getCompatibleBobine();
    console.log('Rendering form - Bobine compatibili:', compatibleBobineList);
    console.log('Rendering form - Numero di bobine compatibili:', compatibleBobineList.length);

    // Verifica se ci sono bobine disponibili
    if (bobine.length === 0 && !bobineLoading) {
      return (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Inserimento metri posati
          </Typography>

          <Alert severity="warning" sx={{ mb: 3 }}>
            Non ci sono bobine disponibili nel cantiere. Puoi comunque registrare i metri posati utilizzando l'opzione "BOBINA VUOTA".
          </Alert>

          <Paper sx={{ p: 1.5, mb: 3, width: '100%' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 2, fontSize: '0.9rem' }}>
                Cavo selezionato: <span style={{ color: '#1976d2' }}>{selectedCavo.id_cavo}</span>
              </Typography>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'wrap', ml: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                  <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Tipo:</Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>{selectedCavo.tipologia || 'N/A'}</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                  <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Form:</Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>{selectedCavo.sezione || 'N/A'}</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                  <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Metri:</Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>{selectedCavo.metri_teorici || 'N/A'} m</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                  <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Stato:</Typography>
                  <Chip
                    size="small"
                    label={selectedCavo.stato_installazione || 'N/D'}
                    color={getCableStateColor(selectedCavo.stato_installazione)}
                    sx={{ height: '20px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.8rem' } }}
                  />
                </Box>
              </Box>
            </Box>
          </Paper>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Metri posati"
                name="metri_posati"
                value={formData.metri_posati}
                onChange={handleInputChange}
                type="number"
                InputProps={{
                  inputProps: { min: 0, step: 0.1 }
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Bobina</InputLabel>
                <Select
                  name="id_bobina"
                  value="BOBINA_VUOTA"
                  disabled
                >
                  <MenuItem value="BOBINA_VUOTA" sx={{ fontWeight: 'bold', color: '#2e7d32', bgcolor: '#f1f8e9' }}>
                    BOBINA VUOTA (Cavo posato senza bobina)
                  </MenuItem>
                </Select>
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                  Non ci sono bobine disponibili. Verrà utilizzata l'opzione BOBINA VUOTA.
                </Typography>
              </FormControl>
            </Grid>
          </Grid>

          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>
            <Button
              variant="outlined"
              color="secondary"
              onClick={() => {
                setSelectedCavo(null);
                setFormData({
                  id_cavo: '',
                  metri_posati: '',
                  id_bobina: ''
                });
              }}
              disabled={saving}
            >
              Annulla
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={() => handleSave()}
              disabled={saving || !formData.metri_posati}
            >
              {saving ? <CircularProgress size={24} /> : 'Salva'}
            </Button>
          </Box>
        </Paper>
      );
    }

    const compatibleBobine = getCompatibleBobine();

    return (
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Inserimento metri posati
        </Typography>

        <Alert severity="info" sx={{ mb: 3 }}>
          Inserisci i metri posati per il cavo selezionato e associa una bobina. Se il cavo è stato posato senza una bobina specifica, seleziona <strong>BOBINA VUOTA</strong>.
        </Alert>

        <Paper sx={{ p: 1.5, mb: 3, width: '100%' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 2, fontSize: '0.9rem' }}>
              Cavo selezionato: <span style={{ color: '#1976d2' }}>{selectedCavo.id_cavo}</span>
            </Typography>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'wrap', ml: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Tipo:</Typography>
                <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>{selectedCavo.tipologia || 'N/A'}</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Form:</Typography>
                <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>{selectedCavo.sezione || 'N/A'}</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Metri:</Typography>
                <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>{selectedCavo.metri_teorici || 'N/A'} m</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Stato:</Typography>
                <Chip
                  size="small"
                  label={selectedCavo.stato_installazione || 'N/D'}
                  color={getCableStateColor(selectedCavo.stato_installazione)}
                  sx={{ height: '20px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.8rem' } }}
                />
              </Box>
            </Box>
          </Box>
        </Paper>

        <Divider sx={{ mb: 3 }} />

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Metri posati"
              name="metri_posati"
              value={formData.metri_posati}
              onChange={handleInputChange}
              type="number"
              error={!!formErrors.metri_posati}
              helperText={formErrors.metri_posati || (formWarnings.metri_posati && (
                <span style={{ color: 'orange' }}>{formWarnings.metri_posati}</span>
              ))}
              disabled={saving}
              InputProps={{
                inputProps: { min: 0, step: 0.1 }
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth error={!!formErrors.id_bobina}>
              <InputLabel>Bobina (Seleziona una bobina da associare al cavo o usa "BOBINA VUOTA" se non desideri associare una bobina specifica)</InputLabel>
              <Select
                name="id_bobina"
                value={formData.id_bobina}
                onChange={handleInputChange}
                disabled={saving || bobineLoading}
              >
                {/* Opzione BOBINA VUOTA sempre disponibile e in evidenza */}
                <MenuItem value="BOBINA_VUOTA" sx={{ fontWeight: 'bold', color: '#2e7d32', bgcolor: '#f1f8e9' }}>
                  BOBINA VUOTA (Cavo posato senza bobina)
                </MenuItem>

                {/* Separatore */}
                <Divider />

                {/* Messaggio informativo */}
                {bobineLoading ? (
                  <MenuItem disabled>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <CircularProgress size={20} sx={{ mr: 1 }} />
                      <Typography variant="caption">
                        Caricamento bobine...
                      </Typography>
                    </Box>
                  </MenuItem>
                ) : compatibleBobine.length === 0 ? (
                  <MenuItem disabled>
                    <Typography variant="caption" color="text.secondary">
                      Nessuna bobina compatibile disponibile. Utilizzare BOBINA VUOTA.
                    </Typography>
                  </MenuItem>
                ) : (
                  <MenuItem disabled>
                    <Typography variant="caption">
                      Bobine compatibili ({compatibleBobine.length})
                    </Typography>
                  </MenuItem>
                )}

                {/* Bobine compatibili */}
                {!bobineLoading && (() => {
                  console.log('Rendering Select - Bobine compatibili:', compatibleBobine);
                  console.log('Rendering Select - Numero di bobine compatibili:', compatibleBobine.length);

                  return compatibleBobine.map((bobina) => {
                    console.log('Rendering bobina compatibile:', bobina);
                    return (
                      <MenuItem key={bobina.id_bobina} value={bobina.id_bobina}>
                        {bobina.id_bobina} - {bobina.tipologia} - {bobina.metri_residui}m
                      </MenuItem>
                    );
                  });
                })()
                }

                {/* Separatore per tutte le bobine */}
                <Divider />

                {/* Titolo per tutte le bobine */}
                <MenuItem disabled>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', color: '#ff9800' }}>
                    TUTTE LE BOBINE DISPONIBILI (Ignora compatibilità)
                  </Typography>
                </MenuItem>

                {/* Mostra tutte le bobine disponibili */}
                {bobine.filter(bobina => bobina.stato_bobina !== 'Terminata').map((bobina) => (
                  <MenuItem key={bobina.id_bobina} value={bobina.id_bobina}>
                    {bobina.id_bobina} - {bobina.tipologia} - {bobina.sezione} - {bobina.metri_residui}m
                  </MenuItem>
                ))}
              </Select>
              {formErrors.id_bobina && (
                <Typography variant="caption" color="error">
                  {formErrors.id_bobina}
                </Typography>
              )}
              {formWarnings.id_bobina && (
                <Typography variant="caption" sx={{ color: 'orange' }}>
                  {formWarnings.id_bobina}
                </Typography>
              )}
              {/* Messaggio informativo sotto il campo */}
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                Seleziona una bobina o usa BOBINA VUOTA se il cavo è stato posato senza una bobina specifica.
              </Typography>
            </FormControl>
          </Grid>
        </Grid>

        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>
          <Button
            variant="outlined"
            color="secondary"
            onClick={() => {
              setSelectedCavo(null);
              setFormData({
                id_cavo: '',
                metri_posati: '',
                id_bobina: ''
              });
            }}
            disabled={saving}
          >
            Annulla
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSave}
            disabled={saving || Object.keys(formErrors).length > 0}
          >
            {saving ? <CircularProgress size={24} /> : 'Salva'}
          </Button>
        </Box>
      </Paper>
    );
  };

  return (
    <Box>
      {/* Tabella cavi */}
      {!selectedCavo && renderCaviTable()}

      {/* Form per inserimento metri e selezione bobina */}
      {renderForm()}

      {/* Dialog per bobine incompatibili */}
      <IncompatibleReelDialog
        open={showIncompatibleReelDialog}
        onClose={() => setShowIncompatibleReelDialog(false)}
        cavo={incompatibleReelData.cavo}
        bobina={incompatibleReelData.bobina}
        onUpdateCavo={handleUpdateCavoForCompatibility}
        onSelectAnotherReel={() => {
          setShowIncompatibleReelDialog(false);
          setFormData(prev => ({ ...prev, id_bobina: '' }));
        }}
      />

      {/* Dialog per cavi già posati */}
      <Dialog open={showAlreadyLaidDialog} onClose={handleCloseAlreadyLaidDialog}>
        <DialogTitle>Cavo già posato</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Il cavo {alreadyLaidCavo?.id_cavo} è già stato posato.
          </DialogContentText>
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" gutterBottom>
              Puoi:
            </Typography>
            <Typography component="ul" variant="body2">
              <li>Modificare la bobina associata</li>
              <li>Selezionare un altro cavo</li>
              <li>Annullare l'operazione</li>
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>
          <Button onClick={handleCloseAlreadyLaidDialog} color="secondary">
            Annulla operazione
          </Button>
          <Box>
            <Button onClick={handleSelectAnotherCable} color="primary" sx={{ mr: 1 }}>
              Seleziona altro cavo
            </Button>
            <Button onClick={handleModifyReel} variant="contained" color="primary">
              Modifica bobina
            </Button>
          </Box>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MetriPosatiSemplificatoForm;
