import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Radio,
  RadioGroup,
  FormControlLabel,
  IconButton,
  InputAdornment,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  ListItemSecondaryAction,
  Chip
} from '@mui/material';
import {
  Search as SearchIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  AddCircleOutline as AddCircleOutlineIcon
} from '@mui/icons-material';
import IncompatibleReelDialog from './IncompatibleReelDialog';
import { useNavigate, useParams } from 'react-router-dom';
import caviService from '../../services/caviService';
import parcoCaviService from '../../services/parcoCaviService';
import CavoDetailsView from './CavoDetailsView';
import {
  CABLE_STATES,
  REEL_STATES,
  determineCableState,
  determineReelState,
  canModifyCable,
  isCableSpare,
  isCableInstalled,
  getCableStateColor,
  getReelStateColor
} from '../../utils/stateUtils';

/**
 * Componente per la modifica della bobina di un cavo già posato
 *
 * @param {Object} props - Proprietà del componente
 * @param {string} props.cantiereId - ID del cantiere
 * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione
 * @param {Function} props.onError - Funzione chiamata in caso di errore
 */
const ModificaBobinaForm = ({ cantiereId, onSuccess, onError }) => {
  const navigate = useNavigate();
  const { cavoId } = useParams();

  // Stati per la gestione del form
  const [loading, setLoading] = useState(false);
  const [caviLoading, setCaviLoading] = useState(false);
  const [bobineLoading, setBobineLoading] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [showSearchResults, setShowSearchResults] = useState(false);

  // Stati per i dati
  const [cavi, setCavi] = useState([]);
  const [bobine, setBobine] = useState([]);
  const [compatibleBobine, setCompatibleBobine] = useState([]);
  const [selectedCavo, setSelectedCavo] = useState(null);
  const [cavoIdInput, setCavoIdInput] = useState('');
  const [selectedOption, setSelectedOption] = useState('');
  const [selectedBobinaId, setSelectedBobinaId] = useState('');
  const [bobinaSearchText, setBobinaSearchText] = useState('');

  // Stati per gestire il dialog di incompatibilità
  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);
  const [incompatibleReelData, setIncompatibleReelData] = useState({ cavo: null, bobina: null });

  // Stati per i dialoghi
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');
  const [confirmDialogAction, setConfirmDialogAction] = useState(null);
  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);

  // Carica i cavi all'avvio
  useEffect(() => {
    loadCavi();
  }, [cantiereId]);

  // Carica le bobine quando viene selezionato un cavo
  useEffect(() => {
    if (selectedCavo) {
      loadBobine();
    }
  }, [selectedCavo]);

  // Pre-seleziona il cavo se viene fornito l'ID del cavo nell'URL
  useEffect(() => {
    if (cavoId && cavi.length > 0 && !selectedCavo) {
      const cavoFromUrl = cavi.find(cavo => cavo.id_cavo === cavoId);
      if (cavoFromUrl) {
        console.log('Pre-selezione cavo dall\'URL:', cavoFromUrl);
        setSelectedCavo(cavoFromUrl);
        setCavoIdInput(cavoId);
      } else {
        console.warn(`Cavo con ID ${cavoId} non trovato nella lista dei cavi installati`);
        onError(`Cavo ${cavoId} non trovato o non installato`);
      }
    }
  }, [cavoId, cavi, selectedCavo]);

  // Funzione per caricare i cavi
  const loadCavi = async () => {
    try {
      setCaviLoading(true);
      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);

      // Carica solo i cavi installati (con metratura_reale > 0)
      const caviData = await caviService.getCavi(cantiereId, null, 'Installato');
      console.log(`Caricati ${caviData.length} cavi installati`);

      // Filtra i cavi che hanno metratura_reale > 0
      const caviInstallati = caviData.filter(cavo =>
        parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato'
      );

      setCavi(caviInstallati);
      console.log(`Filtrati ${caviInstallati.length} cavi effettivamente installati`);
    } catch (error) {
      console.error('Errore durante il caricamento dei cavi:', error);
      onError('Errore durante il caricamento dei cavi: ' + (error.detail || error.message || 'Errore sconosciuto'));
    } finally {
      setCaviLoading(false);
    }
  };

  // Funzione per caricare le bobine
  const loadBobine = async () => {
    if (!selectedCavo) return;

    try {
      setBobineLoading(true);
      console.log(`Caricamento bobine per il cantiere ${cantiereId}...`);

      // Carica tutte le bobine disponibili
      const bobineData = await parcoCaviService.getBobine(cantiereId);
      console.log(`Caricati ${bobineData.length} bobine`);

      // Filtra le bobine compatibili
      const compatibleBobineData = bobineData.filter(bobina =>
        bobina.tipologia === selectedCavo.tipologia &&
        bobina.sezione === selectedCavo.sezione &&
        (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso')
      );

      setBobine(bobineData);
      setCompatibleBobine(compatibleBobineData);
      console.log(`Filtrate ${compatibleBobineData.length} bobine compatibili`);
    } catch (error) {
      console.error('Errore durante il caricamento delle bobine:', error);
      onError('Errore durante il caricamento delle bobine: ' + (error.detail || error.message || 'Errore sconosciuto'));
    } finally {
      setBobineLoading(false);
    }
  };

  // Gestisce la ricerca di un cavo per ID
  const handleSearchCavoById = async () => {
    if (!cavoIdInput.trim()) {
      onError('Inserisci un ID cavo valido');
      return;
    }

    try {
      setLoading(true);
      const cavo = await caviService.getCavoById(cantiereId, cavoIdInput.trim());

      // Verifica che il cavo sia installato
      if (parseFloat(cavo.metratura_reale) <= 0 && cavo.stato_installazione !== 'Installato') {
        onError('Il cavo selezionato non risulta installato');
        setLoading(false);
        return;
      }

      setSelectedCavo(cavo);
      setSelectedOption(''); // Reset dell'opzione selezionata
      setSelectedBobinaId(''); // Reset della bobina selezionata
      setShowSearchResults(false);
    } catch (error) {
      console.error('Errore durante la ricerca del cavo:', error);
      onError('Errore durante la ricerca del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));
    } finally {
      setLoading(false);
    }
  };

  // Gestisce la selezione di un cavo dalla lista
  const handleSelectCavo = (cavo) => {
    setSelectedCavo(cavo);
    setSelectedOption(''); // Reset dell'opzione selezionata
    setSelectedBobinaId(''); // Reset della bobina selezionata
    setShowSearchResults(false);
  };

  // Gestisce il cambio dell'opzione selezionata
  const handleOptionChange = (event) => {
    setSelectedOption(event.target.value);
    setSelectedBobinaId(''); // Reset della bobina selezionata quando cambia l'opzione
  };

  // Gestisce la selezione di una bobina
  const handleSelectBobina = (bobinaId) => {
    console.log('Bobina selezionata:', bobinaId);

    // Trova la bobina selezionata
    const bobina = bobine.find(b => b.id_bobina === bobinaId);

    if (bobina && selectedCavo) {
      // Verifica compatibilità
      const isCompatible =
        String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&
        String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim();

      if (!isCompatible) {
        console.log('Bobina incompatibile selezionata:', bobina);
        console.log('Cavo corrente:', selectedCavo);

        // Aggiorna automaticamente le caratteristiche del cavo per renderlo compatibile
        handleUpdateCavoForCompatibility(selectedCavo, bobina);

        // Imposta la bobina selezionata per mostrare i dettagli
        setSelectedBobinaId(bobinaId);
        return;
      }
    }

    // Se compatibile o nessun cavo selezionato, procedi normalmente
    setSelectedBobinaId(bobinaId);
  };



  // Gestisce il salvataggio delle modifiche
  const handleSave = () => {
    if (!selectedCavo) {
      onError('Seleziona un cavo prima di procedere');
      return;
    }

    if (!selectedOption) {
      onError('Seleziona un\'opzione prima di procedere');
      return;
    }

    // Verifica che sia stata selezionata una bobina se l'opzione è "assegnaNuova"
    if (selectedOption === 'assegnaNuova' && !selectedBobinaId) {
      onError('Seleziona una bobina prima di procedere');
      return;
    }

    // Prepara il messaggio di conferma in base all'opzione selezionata
    let message = '';
    let action = null;

    if (selectedOption === 'assegnaNuova') {
      const bobina = bobine.find(b => b.id_bobina === selectedBobinaId);
      message = `Sei sicuro di voler assegnare la bobina ${getBobinaNumber(selectedBobinaId)} al cavo ${selectedCavo.id_cavo}?`;
      action = () => updateBobina(selectedBobinaId);
    } else if (selectedOption === 'rimuoviBobina') {
      message = `Sei sicuro di voler rimuovere la bobina attuale dal cavo ${selectedCavo.id_cavo}?`;
      action = () => updateBobina('BOBINA_VUOTA');
    } else if (selectedOption === 'annullaInstallazione') {
      message = `ATTENZIONE: Questa operazione annullerà l'installazione del cavo ${selectedCavo.id_cavo}. Tutti i metri posati saranno restituiti alla bobina originale. Sei sicuro di voler procedere?`;
      action = () => annullaInstallazione();
    }

    setConfirmDialogMessage(message);
    setConfirmDialogAction(() => action);
    setShowConfirmDialog(true);
  };

  // Funzione per aggiornare la bobina di un cavo
  const updateBobina = async (bobinaId) => {
    try {
      setLoading(true);
      await caviService.updateBobina(cantiereId, selectedCavo.id_cavo, bobinaId);

      onSuccess(`Bobina ${bobinaId === 'BOBINA_VUOTA' ? 'vuota assegnata' : 'assegnata'} con successo`);

      // Reset del form
      setSelectedCavo(null);
      setSelectedOption('');
      setSelectedBobinaId('');
      setCavoIdInput('');

      // Ricarica i dati
      loadCavi();
    } catch (error) {
      console.error('Errore durante l\'aggiornamento della bobina:', error);
      onError('Errore durante l\'aggiornamento della bobina: ' + (error.detail || error.message || 'Errore sconosciuto'));
    } finally {
      setLoading(false);
    }
  };

  // Funzione per annullare l'installazione di un cavo
  const annullaInstallazione = async () => {
    try {
      setLoading(true);

      // Chiamata all'API per annullare l'installazione
      await caviService.cancelInstallation(cantiereId, selectedCavo.id_cavo);

      onSuccess(`Installazione del cavo ${selectedCavo.id_cavo} annullata con successo`);

      // Reset del form
      setSelectedCavo(null);
      setSelectedOption('');
      setSelectedBobinaId('');
      setCavoIdInput('');

      // Ricarica i dati
      loadCavi();
    } catch (error) {
      console.error('Errore durante l\'annullamento dell\'installazione:', error);
      onError('Errore durante l\'annullamento dell\'installazione: ' + (error.detail || error.message || 'Errore sconosciuto'));
    } finally {
      setLoading(false);
    }
  };

  // Funzione per chiudere il form e resettare tutto
  const handleCloseForm = () => {
    // Reset di tutti gli stati
    setSelectedCavo(null);
    setSelectedOption('');
    setSelectedBobinaId('');
    setCavoIdInput('');
    setShowSearchResults(false);

    // Messaggio di conferma
    onSuccess('Operazione annullata');
  };

  // Gestisce la chiusura del dialog di incompatibilità
  const handleCloseIncompatibleReelDialog = () => {
    setShowIncompatibleReelDialog(false);
    setIncompatibleReelData({ cavo: null, bobina: null });
  };

  // Gestisce l'aggiornamento delle caratteristiche del cavo per compatibilità
  const handleUpdateCavoForCompatibility = async (cavo = null, bobina = null) => {
    // Se non vengono passati parametri, usa i dati dal dialog di incompatibilità
    if (!cavo || !bobina) {
      const dialogData = incompatibleReelData;
      cavo = dialogData.cavo;
      bobina = dialogData.bobina;
    }

    if (!cavo || !bobina) {
      console.error('Dati mancanti per l\'aggiornamento del cavo:', { cavo, bobina });
      onError('Dati mancanti per l\'aggiornamento del cavo');
      return;
    }

    try {
      setLoading(true);
      console.log(`Aggiornamento caratteristiche del cavo ${cavo.id_cavo} per compatibilità con bobina ${bobina.id_bobina}`);

      // Aggiorna le caratteristiche del cavo
      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, bobina.id_bobina);

      // Aggiorna il cavo selezionato con le nuove caratteristiche
      const updatedCavo = await caviService.getCavoById(cantiereId, cavo.id_cavo);
      setSelectedCavo(updatedCavo);

      // Mantieni la bobina selezionata
      setSelectedBobinaId(bobina.id_bobina);

      onSuccess(`Caratteristiche del cavo ${cavo.id_cavo} aggiornate automaticamente per compatibilità con bobina ${getBobinaNumber(bobina.id_bobina)}`);

      // Chiudi il dialog solo se è aperto
      if (showIncompatibleReelDialog) {
        handleCloseIncompatibleReelDialog();
      }

      // Ricarica le bobine per aggiornare la compatibilità
      loadBobine();
    } catch (error) {
      console.error('Errore durante l\'aggiornamento del cavo:', error);
      onError('Errore durante l\'aggiornamento del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));
    } finally {
      setLoading(false);
    }
  };

  // Gestisce l'utilizzo di una bobina incompatibile senza aggiornare le caratteristiche del cavo
  const handleContinueWithIncompatible = async () => {
    const { cavo, bobina } = incompatibleReelData;
    if (!cavo || !bobina) {
      console.error('Dati mancanti per utilizzare la bobina incompatibile:', { cavo, bobina });
      onError('Dati mancanti per utilizzare la bobina incompatibile');
      return;
    }

    try {
      setLoading(true);
      console.log(`Utilizzo bobina incompatibile ${bobina.id_bobina} con cavo ${cavo.id_cavo} senza aggiornare le caratteristiche`);

      // Mantieni la bobina selezionata
      setSelectedBobinaId(bobina.id_bobina);

      onSuccess(`Bobina incompatibile ${getBobinaNumber(bobina.id_bobina)} selezionata per il cavo ${cavo.id_cavo}`);
      handleCloseIncompatibleReelDialog();
    } catch (error) {
      console.error('Errore durante la selezione della bobina incompatibile:', error);
      onError('Errore durante la selezione della bobina incompatibile: ' + (error.detail || error.message || 'Errore sconosciuto'));
    } finally {
      setLoading(false);
    }
  };

  // Funzione per estrarre il numero della bobina dall'ID completo
  const getBobinaNumber = (idBobina) => {
    if (idBobina === 'BOBINA_VUOTA') return 'BOBINA VUOTA';

    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}
    if (idBobina && idBobina.includes('_B')) {
      return idBobina.split('_B')[1];
    }
    return idBobina;
  };

  // Renderizza il form per la selezione del cavo
  const renderCavoSelectionForm = () => (
    <Box>
      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
        Seleziona un cavo posato
      </Typography>

      {/* Ricerca per ID - Versione compatta con dettagli cavo selezionato */}
      <Paper sx={{ p: 1.5, mb: 2, width: '100%' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
          <Typography variant="subtitle2" sx={{ mr: 1, minWidth: '80px' }}>
            Cerca cavo
          </Typography>
          <TextField
            size="small"
            label="ID Cavo"
            variant="outlined"
            value={cavoIdInput}
            onChange={(e) => setCavoIdInput(e.target.value)}
            placeholder="Inserisci l'ID del cavo"
            sx={{ flexGrow: 0, width: '200px', mr: 1 }}
          />
          <Button
            variant="contained"
            color="primary"
            onClick={handleSearchCavoById}
            disabled={caviLoading || !cavoIdInput.trim()}
            startIcon={caviLoading ? <CircularProgress size={16} /> : <SearchIcon fontSize="small" />}
            size="small"
            sx={{ minWidth: '80px', height: '36px', mr: 2 }}
          >
            CERCA
          </Button>

          {/* Dettagli cavo selezionato in riga singola */}
          {selectedCavo && (
            <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1, flexWrap: 'nowrap', overflow: 'hidden', ml: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 1, fontSize: '0.95rem' }}>
                  Cavo: <span style={{ color: '#1976d2' }}>{selectedCavo.id_cavo}</span>
                </Typography>
                <Divider orientation="vertical" flexItem sx={{ mx: 1.5 }} />
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'nowrap', overflow: 'hidden' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                    <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Tipo:</Typography>
                    <Typography variant="body2" sx={{ fontSize: '0.9rem' }}>{selectedCavo.tipologia || 'N/A'}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                    <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Form:</Typography>
                    <Typography variant="body2" sx={{ fontSize: '0.9rem' }}>{selectedCavo.sezione || 'N/A'}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                    <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Metri:</Typography>
                    <Typography variant="body2" sx={{ fontSize: '0.9rem' }}>{selectedCavo.metratura_reale || 'N/A'} m</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                    <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Stato:</Typography>
                    <Chip
                      size="small"
                      label={selectedCavo.stato_installazione || 'N/D'}
                      color="success"
                      sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}
                    />
                  </Box>
                </Box>
              </Box>

              {/* Dettagli bobina attuale */}
              {selectedCavo.id_bobina && (
                <>
                  <Divider orientation="vertical" flexItem sx={{ mx: 2 }} />
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 1, fontSize: '0.95rem', color: '#2e7d32' }}>
                      Bobina attuale: <span>{selectedCavo.id_bobina === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(selectedCavo.id_bobina)}</span>
                    </Typography>
                    {(() => {
                      if (selectedCavo.id_bobina === 'BOBINA_VUOTA') {
                        return (
                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                            <Typography variant="body2" sx={{ fontSize: '0.9rem', color: 'text.secondary', fontStyle: 'italic' }}>
                              (Cavo posato senza bobina specifica)
                            </Typography>
                          </Box>
                        );
                      }

                      const bobina = bobine.find(b => b.id_bobina === selectedCavo.id_bobina);
                      return bobina ? (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'nowrap', overflow: 'hidden' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                            <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Residui:</Typography>
                            <Typography variant="body2" sx={{ fontSize: '0.9rem', color: 'success.main', fontWeight: 'bold' }}>
                              {bobina.metri_residui || 0} m
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                            <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Stato:</Typography>
                            <Chip
                              size="small"
                              label={bobina.stato_bobina || 'N/D'}
                              color={bobina.stato_bobina === 'Disponibile' ? 'success' : 'warning'}
                              sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}
                            />
                          </Box>
                        </Box>
                      ) : null;
                    })()}
                  </Box>
                </>
              )}

              {/* Dettagli bobina selezionata (se diversa da quella attuale) */}
              {selectedBobinaId && selectedBobinaId !== selectedCavo?.id_bobina && (
                <>
                  <Divider orientation="vertical" flexItem sx={{ mx: 2 }} />
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 1, fontSize: '0.95rem', color: '#1976d2' }}>
                      Bobina selezionata: <span>{selectedBobinaId === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(selectedBobinaId)}</span>
                    </Typography>
                    {(() => {
                      if (selectedBobinaId === 'BOBINA_VUOTA') {
                        return (
                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                            <Typography variant="body2" sx={{ fontSize: '0.9rem', color: 'text.secondary', fontStyle: 'italic' }}>
                              (Rimozione bobina attuale)
                            </Typography>
                          </Box>
                        );
                      }

                      const bobina = bobine.find(b => b.id_bobina === selectedBobinaId);
                      if (!bobina) return null;

                      // Verifica compatibilità
                      const isCompatible = selectedCavo &&
                        String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&
                        String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim();

                      return (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'nowrap', overflow: 'hidden' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                            <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Tipo:</Typography>
                            <Typography variant="body2" sx={{ fontSize: '0.9rem', color: isCompatible ? 'text.primary' : 'error.main' }}>
                              {bobina.tipologia || 'N/A'}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                            <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Form:</Typography>
                            <Typography variant="body2" sx={{ fontSize: '0.9rem', color: isCompatible ? 'text.primary' : 'error.main' }}>
                              {bobina.sezione || 'N/A'}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                            <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Residui:</Typography>
                            <Typography variant="body2" sx={{ fontSize: '0.9rem', color: 'primary.main', fontWeight: 'bold' }}>
                              {bobina.metri_residui || 0} m
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                            <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Stato:</Typography>
                            <Chip
                              size="small"
                              label={bobina.stato_bobina || 'N/D'}
                              color={bobina.stato_bobina === 'Disponibile' ? 'success' : 'warning'}
                              sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}
                            />
                          </Box>
                          {!isCompatible && (
                            <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                              <Chip
                                size="small"
                                label="Non compatibile"
                                color="error"
                                variant="outlined"
                                sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}
                              />
                            </Box>
                          )}
                        </Box>
                      );
                    })()}
                  </Box>
                </>
              )}
            </Box>
          )}
        </Box>
      </Paper>

      {/* Lista cavi - versione compatta */}
      <Paper sx={{ p: 1.5, width: '100%' }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>
          Seleziona dalla lista
        </Typography>

        {caviLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
            <CircularProgress size={24} />
          </Box>
        ) : cavi.length === 0 ? (
          <Alert severity="info" sx={{ py: 0.5 }}>
            <Typography variant="caption">Non ci sono cavi posati disponibili.</Typography>
          </Alert>
        ) : (
          <TableContainer component={Paper} variant="outlined" sx={{ maxHeight: '300px', overflow: 'auto', width: '100%' }}>
            <Table size="small" stickyHeader>
              <TableHead>
                <TableRow sx={{ '& th': { fontWeight: 'bold', py: 1, bgcolor: '#f5f5f5' } }}>
                  <TableCell>ID Cavo</TableCell>
                  <TableCell>Tipologia</TableCell>
                  <TableCell>Formazione</TableCell>
                  <TableCell>Metri</TableCell>
                  <TableCell>Bobina</TableCell>
                  <TableCell>Stato</TableCell>
                  <TableCell align="center" sx={{ width: '40px' }}>Info</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {cavi.map((cavo) => (
                  <TableRow
                    key={cavo.id_cavo}
                    hover
                    onClick={() => handleSelectCavo(cavo)}
                    sx={{
                      cursor: 'pointer',
                      '&:hover': { bgcolor: '#f1f8e9' },
                      '& td': { py: 0.5 }
                    }}
                  >
                    <TableCell sx={{ fontWeight: 'medium' }}>{cavo.id_cavo}</TableCell>
                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>
                    <TableCell>{cavo.sezione || 'N/A'}</TableCell>
                    <TableCell>{cavo.metratura_reale || 'N/A'} m</TableCell>
                    <TableCell>
                      {cavo.id_bobina ? (cavo.id_bobina === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(cavo.id_bobina)) : 'VUOTA'}
                    </TableCell>
                    <TableCell>
                      <Chip
                        size="small"
                        label="Installato"
                        color="success"
                        sx={{ height: '20px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.7rem' } }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedCavo(cavo);
                          setShowCavoDetailsDialog(true);
                        }}
                      >
                        <InfoIcon fontSize="small" />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>
    </Box>
  );



  // Renderizza le opzioni di modifica
  const renderModificaOptions = () => {
    if (!selectedCavo) return null;

    return (
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Opzioni di modifica
        </Typography>

        <RadioGroup
          value={selectedOption}
          onChange={handleOptionChange}
        >
          <FormControlLabel
            value="assegnaNuova"
            control={<Radio />}
            label="Assegna nuova bobina"
          />
          <FormControlLabel
            value="rimuoviBobina"
            control={<Radio />}
            label="Rimuovi bobina attuale"
          />
          <FormControlLabel
            value="annullaInstallazione"
            control={<Radio />}
            label="Annulla installazione"
          />
        </RadioGroup>

        {selectedOption === 'assegnaNuova' && renderBobineSelection()}

        {selectedOption === 'rimuoviBobina' && (
          <Alert severity="info" sx={{ mt: 2 }}>
            Questa operazione rimuoverà l'associazione con la bobina attuale, assegnando una "BOBINA_VUOTA" al cavo.
            Il cavo rimarrà nello stato posato e i metri posati rimarranno invariati.
            La bobina attuale (se presente) riavrà i suoi metri restituiti.
          </Alert>
        )}

        {selectedOption === 'annullaInstallazione' && (
          <Alert severity="warning" sx={{ mt: 2 }}>
            <Typography variant="body2" fontWeight="bold">
              ATTENZIONE: Questa operazione annullerà completamente l'installazione del cavo.
            </Typography>
            <Typography variant="body2">
              - Il cavo tornerà allo stato "Da installare"<br />
              - La metratura reale sarà azzerata<br />
              - L'associazione con la bobina sarà rimossa<br />
              - I metri posati saranno restituiti alla bobina originale (se presente)
            </Typography>
          </Alert>
        )}

        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            variant="outlined"
            color="secondary"
            startIcon={<CancelIcon />}
            onClick={handleCloseForm}
            disabled={loading}
          >
            Annulla operazione
          </Button>
          <Button
            variant="contained"
            color="primary"
            startIcon={<SaveIcon />}
            onClick={handleSave}
            disabled={loading || !selectedOption || (selectedOption === 'assegnaNuova' && !selectedBobinaId)}
          >
            Salva modifiche
          </Button>
        </Box>
      </Paper>
    );
  };

  // Renderizza la selezione delle bobine
  const renderBobineSelection = () => {
    if (bobineLoading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
          <CircularProgress />
        </Box>
      );
    }

    // Filtra le bobine in base al testo di ricerca
    const bobineFiltrate = bobine.filter(bobina => {
      const searchLower = bobinaSearchText.toLowerCase();
      return !bobinaSearchText ||
        getBobinaNumber(bobina.id_bobina).toLowerCase().includes(searchLower) ||
        String(bobina.tipologia || '').toLowerCase().includes(searchLower) ||
        String(bobina.sezione || '').toLowerCase().includes(searchLower);
    });

    // Separa le bobine compatibili e non compatibili
    const bobineCompatibili = selectedCavo
      ? bobineFiltrate.filter(bobina =>
          String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&
          String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim() &&
          (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso'))
      : [];

    const bobineNonCompatibili = selectedCavo
      ? bobineFiltrate.filter(bobina =>
          !(String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&
            String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim()) &&
          (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso'))
      : [];

    // Ordina per metri residui (decrescente)
    bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);
    bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);

    return (
      <Box sx={{ mt: 2 }}>
        {/* Campo di ricerca rapida */}
        <Box sx={{ mb: 2 }}>
          <TextField
            fullWidth
            label="Ricerca bobina (ID, tipologia, formazione)"
            value={bobinaSearchText}
            onChange={(e) => setBobinaSearchText(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              )
            }}
            sx={{ mb: 2 }}
          />
        </Box>



        {/* Bobina selezionata */}
        {selectedBobinaId && (
          <Alert severity="success" sx={{ mb: 2 }}>
            Bobina selezionata: <strong>{getBobinaNumber(selectedBobinaId)}</strong>
          </Alert>
        )}

        {/* Griglia per le due liste di bobine */}
        <Grid container spacing={2}>
          {/* Colonna sinistra: Bobine compatibili */}
          <Grid item xs={12} md={6}>
            <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                ELENCO BOBINE COMPATIBILI
              </Typography>

              {bobineCompatibili.length > 0 ? (
                <>
                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8, px: 1.8, bgcolor: '#f5f5f5', borderRadius: 1, mb: 1 }}>
                    <Box sx={{ width: '60px', mr: 2 }}>
                      <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>ID</Typography>
                    </Box>
                    <Box sx={{ width: '120px', mr: 2 }}>
                      <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Tipo</Typography>
                    </Box>
                    <Box sx={{ width: '100px', mr: 2 }}>
                      <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Form.</Typography>
                    </Box>
                    <Box sx={{ width: '100px', mr: 2 }}>
                      <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Residui</Typography>
                    </Box>
                    <Box sx={{ flexGrow: 0 }}>
                      <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Stato</Typography>
                    </Box>
                  </Box>
                  <List sx={{ maxHeight: bobineCompatibili.length > 6 ? '300px' : 'auto', overflowY: bobineCompatibili.length > 6 ? 'auto' : 'visible', overflowX: 'hidden', bgcolor: 'background.paper' }}>
                    {bobineCompatibili.map((bobina) => (
                      <ListItem
                        key={bobina.id_bobina}
                        disablePadding
                        secondaryAction={
                          <IconButton
                            edge="end"
                            size="small"
                            onClick={() => handleSelectBobina(bobina.id_bobina)}
                          >
                            <AddCircleOutlineIcon color="primary" />
                          </IconButton>
                        }
                        sx={{
                          bgcolor: selectedBobinaId === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',
                          borderRadius: '4px',
                          mb: 0.5,
                          border: selectedBobinaId === bobina.id_bobina ? '1px solid #4caf50' : 'none',
                        }}
                      >
                        <ListItemButton
                          dense
                          onClick={() => handleSelectBobina(bobina.id_bobina)}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8 }}>
                            <Box sx={{ width: '60px', mr: 2 }}>
                              <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.9rem' }}>
                                {getBobinaNumber(bobina.id_bobina)}
                              </Typography>
                            </Box>
                            <Box sx={{ width: '120px', mr: 2 }}>
                              <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                                {bobina.tipologia || 'N/A'}
                              </Typography>
                            </Box>
                            <Box sx={{ width: '100px', mr: 2 }}>
                              <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                                {bobina.sezione || 'N/A'}
                              </Typography>
                            </Box>
                            <Box sx={{ width: '100px', mr: 2 }}>
                              <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.85rem', color: 'success.main' }}>
                                {bobina.metri_residui || 0} m
                              </Typography>
                            </Box>
                            <Box sx={{ flexGrow: 0 }}>
                              <Chip
                                size="small"
                                label={bobina.stato_bobina || 'N/D'}
                                color={getReelStateColor(bobina.stato_bobina)}
                                variant="outlined"
                                sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}
                              />
                            </Box>
                          </Box>
                        </ListItemButton>
                      </ListItem>
                    ))}
                  </List>
                </>
              ) : (
                <Alert severity="info" sx={{ mt: 1 }}>
                  Nessuna bobina compatibile disponibile. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.
                </Alert>
              )}
            </Paper>
          </Grid>

          {/* Colonna destra: Bobine non compatibili */}
          <Grid item xs={12} md={6}>
            <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                ELENCO BOBINE NON COMPATIBILI
              </Typography>

              {bobineNonCompatibili.length > 0 ? (
                <>
                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8, px: 1.8, bgcolor: '#f5f5f5', borderRadius: 1, mb: 1 }}>
                    <Box sx={{ width: '60px', mr: 2 }}>
                      <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>ID</Typography>
                    </Box>
                    <Box sx={{ width: '120px', mr: 2 }}>
                      <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Tipo</Typography>
                    </Box>
                    <Box sx={{ width: '100px', mr: 2 }}>
                      <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Form.</Typography>
                    </Box>
                    <Box sx={{ width: '100px', mr: 2 }}>
                      <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Residui</Typography>
                    </Box>
                    <Box sx={{ flexGrow: 0 }}>
                      <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Stato</Typography>
                    </Box>
                  </Box>
                  <List sx={{ maxHeight: bobineNonCompatibili.length > 6 ? '300px' : 'auto', overflowY: bobineNonCompatibili.length > 6 ? 'auto' : 'visible', overflowX: 'hidden', bgcolor: 'background.paper' }}>
                    {bobineNonCompatibili.map((bobina) => (
                      <ListItem
                        key={bobina.id_bobina}
                        disablePadding
                        secondaryAction={
                          <IconButton
                            edge="end"
                            size="small"
                            onClick={() => handleSelectBobina(bobina.id_bobina)}
                          >
                            <AddCircleOutlineIcon color="primary" />
                          </IconButton>
                        }
                        sx={{
                          bgcolor: selectedBobinaId === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',
                          borderRadius: '4px',
                          mb: 0.5,
                          border: selectedBobinaId === bobina.id_bobina ? '1px solid #4caf50' : 'none',
                        }}
                      >
                        <ListItemButton
                          dense
                          onClick={() => handleSelectBobina(bobina.id_bobina)}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8 }}>
                            <Box sx={{ width: '60px', mr: 2 }}>
                              <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.9rem' }}>
                                {getBobinaNumber(bobina.id_bobina)}
                              </Typography>
                            </Box>
                            <Box sx={{ width: '120px', mr: 2 }}>
                              <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                                {bobina.tipologia || 'N/A'}
                              </Typography>
                            </Box>
                            <Box sx={{ width: '100px', mr: 2 }}>
                              <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                                {bobina.sezione || 'N/A'}
                              </Typography>
                            </Box>
                            <Box sx={{ width: '100px', mr: 2 }}>
                              <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.85rem', color: 'success.main' }}>
                                {bobina.metri_residui || 0} m
                              </Typography>
                            </Box>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Chip
                                size="small"
                                label={bobina.stato_bobina || 'N/D'}
                                color={getReelStateColor(bobina.stato_bobina)}
                                variant="outlined"
                                sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}
                              />
                              <Chip
                                size="small"
                                label="Non comp."
                                color="warning"
                                variant="outlined"
                                sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}
                              />
                            </Box>
                          </Box>
                        </ListItemButton>
                      </ListItem>
                    ))}
                  </List>
                </>
              ) : (
                <Alert severity="info" sx={{ mt: 1 }}>
                  Nessuna bobina non compatibile disponibile con i filtri attuali.
                </Alert>
              )}
            </Paper>
          </Grid>
        </Grid>

        {/* Messaggio se non ci sono bobine */}
        {bobineCompatibili.length === 0 && bobineNonCompatibili.length === 0 && (
          <Alert severity="info" sx={{ mt: 2 }}>
            {bobinaSearchText ? 'Nessuna bobina trovata con i criteri di ricerca specificati.' : 'Non ci sono bobine disponibili.'}
          </Alert>
        )}
      </Box>
    );
  };

  return (
    <Box>
      {/* Form per la selezione del cavo */}
      {renderCavoSelectionForm()}

      {/* Opzioni di modifica */}
      {renderModificaOptions()}

      {/* Dialog per la visualizzazione dei dettagli completi del cavo */}
      <Dialog
        open={showCavoDetailsDialog}
        onClose={() => setShowCavoDetailsDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Dettagli completi del cavo</DialogTitle>
        <DialogContent>
          {selectedCavo && <CavoDetailsView cavo={selectedCavo} />}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowCavoDetailsDialog(false)}>Chiudi</Button>
        </DialogActions>
      </Dialog>

      {/* Dialog di conferma */}
      <Dialog
        open={showConfirmDialog}
        onClose={() => setShowConfirmDialog(false)}
      >
        <DialogTitle>Conferma operazione</DialogTitle>
        <DialogContent>
          <Typography>{confirmDialogMessage}</Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setShowConfirmDialog(false)}
            color="primary"
          >
            Annulla
          </Button>
          <Button
            onClick={() => {
              setShowConfirmDialog(false);
              if (confirmDialogAction) confirmDialogAction();
            }}
            color="primary"
            variant="contained"
            autoFocus
          >
            Conferma
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog per bobine incompatibili */}
      <IncompatibleReelDialog
        open={showIncompatibleReelDialog}
        onClose={handleCloseIncompatibleReelDialog}
        cavo={incompatibleReelData.cavo}
        bobina={incompatibleReelData.bobina}
        onUpdateCavo={handleUpdateCavoForCompatibility}
        onSelectAnotherReel={handleCloseIncompatibleReelDialog}
        onContinueWithIncompatible={handleContinueWithIncompatible}
      />
    </Box>
  );
};

export default ModificaBobinaForm;
