import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  TextField,
  Checkbox,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  IconButton,
  Chip,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Info as InfoIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import caviService from '../../services/caviService';
import { determineCableState, getCableStateColor, isCableInstalled } from '../../utils/stateUtils';

/**
 * Componente per aggiungere rapidamente più cavi a una bobina
 *
 * @param {Object} props - Proprietà del componente
 * @param {boolean} props.open - Indica se il dialog è aperto
 * @param {Function} props.onClose - Funzione chiamata alla chiusura del dialog
 * @param {Object} props.bobina - <PERSON><PERSON> selezionata
 * @param {string} props.cantiereId - ID del cantiere
 * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione
 * @param {Function} props.onError - Funzione chiamata in caso di errore
 */
const QuickAddCablesDialog = ({ open, onClose, bobina, cantiereId, onSuccess, onError }) => {
  // Stati per la gestione dei dati
  const [loading, setLoading] = useState(false);
  const [caviLoading, setCaviLoading] = useState(false);
  const [cavi, setCavi] = useState([]);
  const [selectedCavi, setSelectedCavi] = useState([]);
  const [caviMetri, setCaviMetri] = useState({});
  const [searchTerm, setSearchTerm] = useState('');
  const [errors, setErrors] = useState({});
  const [warnings, setWarnings] = useState({});
  const [saving, setSaving] = useState(false);

  // Carica i cavi disponibili quando il dialog viene aperto
  useEffect(() => {
    if (open && bobina) {
      loadCavi();
    }
  }, [open, bobina, cantiereId]);

  // Funzione per caricare i cavi
  const loadCavi = async () => {
    try {
      setCaviLoading(true);
      const caviData = await caviService.getCavi(cantiereId);

      // Filtra i cavi compatibili con la bobina e non ancora installati
      const caviCompatibili = caviData.filter(cavo =>
        // Verifica compatibilità con la bobina
        cavo.tipologia === bobina.tipologia &&
        String(cavo.n_conduttori) === String(bobina.n_conduttori) &&
        String(cavo.sezione) === String(bobina.sezione) &&
        // Verifica che il cavo non sia già installato
        !isCableInstalled(cavo) &&
        // Verifica che il cavo non sia SPARE
        cavo.modificato_manualmente !== 3
      );

      setCavi(caviCompatibili);
    } catch (error) {
      console.error('Errore nel caricamento dei cavi:', error);
      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));
    } finally {
      setCaviLoading(false);
    }
  };

  // Gestisce la selezione/deselezione di un cavo
  const handleCavoSelect = (cavo) => {
    setSelectedCavi(prev => {
      const isSelected = prev.some(c => c.id_cavo === cavo.id_cavo);

      if (isSelected) {
        // Rimuovi il cavo dalla selezione
        const newSelected = prev.filter(c => c.id_cavo !== cavo.id_cavo);

        // Rimuovi anche i metri associati
        const newCaviMetri = { ...caviMetri };
        delete newCaviMetri[cavo.id_cavo];
        setCaviMetri(newCaviMetri);

        return newSelected;
      } else {
        // Aggiungi il cavo alla selezione
        return [...prev, cavo];
      }
    });
  };

  // Gestisce l'input dei metri posati per un cavo
  const handleMetriChange = (cavoId, value) => {
    // Aggiorna i metri per il cavo
    setCaviMetri(prev => ({
      ...prev,
      [cavoId]: value
    }));

    // Valida il valore inserito
    validateMetri(cavoId, value);
  };

  // Valida i metri inseriti per un cavo
  const validateMetri = (cavoId, value) => {
    const cavo = cavi.find(c => c.id_cavo === cavoId);
    if (!cavo) return;

    // Resetta gli errori e gli avvisi per questo cavo
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[cavoId];
      return newErrors;
    });

    setWarnings(prev => {
      const newWarnings = { ...prev };
      delete newWarnings[cavoId];
      return newWarnings;
    });

    // Controllo input vuoto
    if (!value || value.trim() === '') {
      setErrors(prev => ({
        ...prev,
        [cavoId]: 'Inserire un valore per i metri posati'
      }));
      return false;
    }

    // Controllo formato numerico
    if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {
      setErrors(prev => ({
        ...prev,
        [cavoId]: 'Inserire un valore numerico positivo'
      }));
      return false;
    }

    const metriPosati = parseFloat(value);

    // Controllo metri teorici cavo
    if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {
      setWarnings(prev => ({
        ...prev,
        [cavoId]: `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`
      }));
    }

    // Controllo metri residui bobina
    const metriTotaliRichiesti = Object.entries(caviMetri)
      .filter(([id, _]) => id !== cavoId) // Escludi il cavo corrente
      .reduce((sum, [_, metri]) => sum + parseFloat(metri || 0), 0) + metriPosati;

    if (metriTotaliRichiesti > bobina.metri_residui) {
      setWarnings(prev => ({
        ...prev,
        [cavoId]: `I metri totali richiesti (${metriTotaliRichiesti}m) superano i metri residui della bobina (${bobina.metri_residui}m)`
      }));
    }

    return true;
  };

  // Valida tutti i metri inseriti
  const validateAllMetri = () => {
    let isValid = true;
    const newErrors = {};
    const newWarnings = {};

    // Verifica che ci siano cavi selezionati
    if (selectedCavi.length === 0) {
      onError('Seleziona almeno un cavo');
      return false;
    }

    // Verifica che tutti i cavi selezionati abbiano metri inseriti
    for (const cavo of selectedCavi) {
      const metri = caviMetri[cavo.id_cavo];

      // Controllo input vuoto
      if (!metri || metri.trim() === '') {
        newErrors[cavo.id_cavo] = 'Inserire un valore per i metri posati';
        isValid = false;
        continue;
      }

      // Controllo formato numerico
      if (isNaN(parseFloat(metri)) || parseFloat(metri) <= 0) {
        newErrors[cavo.id_cavo] = 'Inserire un valore numerico positivo';
        isValid = false;
        continue;
      }

      const metriPosati = parseFloat(metri);

      // Controllo metri teorici cavo
      if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {
        newWarnings[cavo.id_cavo] = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`;
        // Non mostrare popup di conferma, solo l'avviso nel form
      }
    }

    // Verifica che i metri totali richiesti non superino i metri residui della bobina
    const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => sum + parseFloat(metri || 0), 0);
    if (metriTotaliRichiesti > bobina.metri_residui) {
      // Questo è un avviso globale, non specifico per un cavo
      if (!window.confirm(`ATTENZIONE: I metri totali richiesti (${metriTotaliRichiesti}m) superano i metri residui della bobina (${bobina.metri_residui}m).\n\nQuesto porterà la bobina in stato OVER.\n\nVuoi continuare?`)) {
        isValid = false;
      }
    }

    setErrors(newErrors);
    setWarnings(newWarnings);
    return isValid;
  };

  // Gestisce il salvataggio dei dati
  const handleSave = async () => {
    try {
      // Validazione
      if (!validateAllMetri()) {
        return;
      }

      setSaving(true);

      // Conferma finale
      if (!window.confirm(`Confermi l'aggiornamento di ${selectedCavi.length} cavi con la bobina ${bobina.id_bobina}?`)) {
        setSaving(false);
        return;
      }

      // Aggiorna ogni cavo selezionato
      const results = [];
      let errors = [];

      for (const cavo of selectedCavi) {
        try {
          const metriPosati = parseFloat(caviMetri[cavo.id_cavo]);

          // Determina se è necessario forzare lo stato OVER della bobina
          const metriGiàUtilizzati = results.reduce((sum, r) => sum + r.metriPosati, 0);
          const forceOver = (metriGiàUtilizzati + metriPosati) > bobina.metri_residui;

          // Aggiorna i metri posati del cavo
          const result = await caviService.updateMetriPosati(
            cantiereId,
            cavo.id_cavo,
            metriPosati,
            bobina.id_bobina,
            forceOver
          );

          results.push({
            cavo: cavo.id_cavo,
            metriPosati,
            success: true
          });
        } catch (error) {
          console.error(`Errore nell'aggiornamento del cavo ${cavo.id_cavo}:`, error);
          errors.push({
            cavo: cavo.id_cavo,
            error: error.message || 'Errore sconosciuto'
          });
        }
      }

      // Gestione del risultato
      if (errors.length === 0) {
        // Tutti i cavi sono stati aggiornati con successo
        onSuccess(`${results.length} cavi aggiornati con successo`);
        onClose();
      } else if (results.length > 0) {
        // Alcuni cavi sono stati aggiornati, altri no
        onSuccess(`${results.length} cavi aggiornati con successo, ${errors.length} falliti`);
        onError(`Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);
        onClose();
      } else {
        // Nessun cavo è stato aggiornato
        onError(`Nessun cavo aggiornato. Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);
      }
    } catch (error) {
      console.error('Errore durante il salvataggio:', error);
      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));
    } finally {
      setSaving(false);
    }
  };

  // Filtra i cavi in base al termine di ricerca
  const filteredCavi = cavi.filter(cavo =>
    cavo.id_cavo.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (cavo.tipologia && cavo.tipologia.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (cavo.ubicazione_partenza && cavo.ubicazione_partenza.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (cavo.ubicazione_arrivo && cavo.ubicazione_arrivo.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        Aggiungi cavi alla bobina {bobina?.numero_bobina || ''}
      </DialogTitle>
      <DialogContent>
        {!bobina ? (
          <Alert severity="error">Nessuna bobina selezionata</Alert>
        ) : (
          <>
            {/* Informazioni sulla bobina */}
            <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
              <Typography variant="subtitle1" gutterBottom>
                Dettagli bobina
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
                <Box>
                  <Typography variant="body2" color="text.secondary">ID Bobina</Typography>
                  <Typography variant="body1">{bobina.id_bobina}</Typography>
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary">Tipologia</Typography>
                  <Typography variant="body1">{bobina.tipologia || 'N/A'}</Typography>
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary">Conduttori</Typography>
                  <Typography variant="body1">{bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}</Typography>
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary">Metri residui</Typography>
                  <Typography variant="body1">{bobina.metri_residui?.toFixed(1) || '0'} m</Typography>
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary">Stato</Typography>
                  <Chip
                    label={bobina.stato_bobina || 'N/D'}
                    size="small"
                    color={
                      bobina.stato_bobina === 'Disponibile' ? 'success' :
                      bobina.stato_bobina === 'In uso' ? 'primary' :
                      bobina.stato_bobina === 'Over' ? 'error' :
                      bobina.stato_bobina === 'Terminata' ? 'warning' : 'default'
                    }
                  />
                </Box>
              </Box>
            </Box>

            {/* Ricerca cavi */}
            <TextField
              fullWidth
              label="Cerca cavi"
              variant="outlined"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Cerca per ID, tipologia, ubicazione..."
              sx={{ mb: 2 }}
            />

            {/* Tabella cavi */}
            {caviLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                <CircularProgress />
              </Box>
            ) : filteredCavi.length === 0 ? (
              <Alert severity="info">
                Nessun cavo compatibile disponibile per questa bobina.
              </Alert>
            ) : (
              <TableContainer component={Paper} sx={{ mb: 3 }}>
                <Table size="small">
                  <TableHead>
                    <TableRow sx={{ bgcolor: '#f5f5f5' }}>
                      <TableCell padding="checkbox"></TableCell>
                      <TableCell>ID Cavo</TableCell>
                      <TableCell>Tipologia</TableCell>
                      <TableCell>Ubicazione</TableCell>
                      <TableCell>Metri Teorici</TableCell>
                      <TableCell>Metri Posati</TableCell>
                      <TableCell>Stato</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredCavi.map((cavo) => {
                      const isSelected = selectedCavi.some(c => c.id_cavo === cavo.id_cavo);
                      return (
                        <TableRow
                          key={cavo.id_cavo}
                          hover
                          selected={isSelected}
                          onClick={() => handleCavoSelect(cavo)}
                          sx={{ cursor: 'pointer' }}
                        >
                          <TableCell padding="checkbox">
                            <Checkbox
                              checked={isSelected}
                              onChange={(e) => {
                                e.stopPropagation();
                                handleCavoSelect(cavo);
                              }}
                            />
                          </TableCell>
                          <TableCell>{cavo.id_cavo}</TableCell>
                          <TableCell>{cavo.tipologia || 'N/A'}</TableCell>
                          <TableCell>
                            <Tooltip title={`Da: ${cavo.ubicazione_partenza || 'N/A'} - A: ${cavo.ubicazione_arrivo || 'N/A'}`}>
                              <Box sx={{ maxWidth: 150, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                {cavo.ubicazione_partenza || 'N/A'} → {cavo.ubicazione_arrivo || 'N/A'}
                              </Box>
                            </Tooltip>
                          </TableCell>
                          <TableCell>{cavo.metri_teorici || 'N/A'}</TableCell>
                          <TableCell>
                            {isSelected ? (
                              <TextField
                                size="small"
                                type="number"
                                value={caviMetri[cavo.id_cavo] || ''}
                                onChange={(e) => {
                                  e.stopPropagation();
                                  handleMetriChange(cavo.id_cavo, e.target.value);
                                }}
                                onClick={(e) => e.stopPropagation()}
                                error={!!errors[cavo.id_cavo]}
                                helperText={errors[cavo.id_cavo] || warnings[cavo.id_cavo]}
                                FormHelperTextProps={{
                                  sx: { color: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? 'warning.main' : 'error.main' }
                                }}
                                InputProps={{
                                  endAdornment: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? (
                                    <Tooltip title={warnings[cavo.id_cavo]}>
                                      <WarningIcon color="warning" fontSize="small" />
                                    </Tooltip>
                                  ) : null
                                }}
                              />
                            ) : (
                              'N/A'
                            )}
                          </TableCell>
                          <TableCell>
                            <Chip
                              size="small"
                              label={cavo.stato_installazione || 'Da installare'}
                              color={getCableStateColor(cavo.stato_installazione)}
                            />
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* Riepilogo selezione */}
            {selectedCavi.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Riepilogo selezione ({selectedCavi.length} cavi)
                </Typography>
                <TableContainer component={Paper}>
                  <Table size="small">
                    <TableHead>
                      <TableRow sx={{ bgcolor: '#f5f5f5' }}>
                        <TableCell>ID Cavo</TableCell>
                        <TableCell>Metri Posati</TableCell>
                        <TableCell>Azioni</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {selectedCavi.map((cavo) => (
                        <TableRow key={cavo.id_cavo}>
                          <TableCell>{cavo.id_cavo}</TableCell>
                          <TableCell>
                            <TextField
                              size="small"
                              type="number"
                              value={caviMetri[cavo.id_cavo] || ''}
                              onChange={(e) => handleMetriChange(cavo.id_cavo, e.target.value)}
                              error={!!errors[cavo.id_cavo]}
                              helperText={errors[cavo.id_cavo] || warnings[cavo.id_cavo]}
                              FormHelperTextProps={{
                                sx: { color: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? 'warning.main' : 'error.main' }
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleCavoSelect(cavo)}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={3} align="right">
                          <Typography variant="body2">
                            <strong>Metri totali richiesti:</strong> {
                              Object.values(caviMetri).reduce((sum, metri) => {
                                const value = parseFloat(metri || 0);
                                return isNaN(value) ? sum : sum + value;
                              }, 0).toFixed(1)
                            } m
                          </Typography>
                          <Typography variant="body2">
                            <strong>Metri residui bobina:</strong> {bobina.metri_residui?.toFixed(1) || '0'} m
                          </Typography>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            )}

            {/* Avvisi */}
            {Object.keys(warnings).length > 0 && (
              <Alert severity="warning" sx={{ mb: 2 }}>
                <Typography variant="subtitle2">Attenzione:</Typography>
                <ul>
                  {Object.entries(warnings).map(([cavoId, warning]) => (
                    <li key={cavoId}>{cavoId}: {warning}</li>
                  ))}
                </ul>
              </Alert>
            )}

            {/* Istruzioni */}
            <Alert severity="info" sx={{ mb: 2 }}>
              Seleziona i cavi che vuoi associare a questa bobina e inserisci i metri posati per ciascuno.
              I metri posati verranno sottratti dai metri residui della bobina.
            </Alert>
          </>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={saving}>
          Annulla
        </Button>
        <Button
          onClick={handleSave}
          color="primary"
          variant="contained"
          disabled={saving || selectedCavi.length === 0 || Object.keys(errors).length > 0}
          startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
        >
          Salva
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default QuickAddCablesDialog;
