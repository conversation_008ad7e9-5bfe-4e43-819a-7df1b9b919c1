import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Alert,
  CircularProgress,
  Link,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemButton
} from '@mui/material';
import {
  Assessment as AssessmentIcon,
  BarChart as BarChartIcon,
  PieChart as PieChartIcon,
  Timeline as TimelineIcon,
  List as ListIcon,
  Download as DownloadIcon,
  DateRange as DateRangeIcon
} from '@mui/icons-material';
import reportService from '../../services/reportService';

const ReportCavi = ({ cantiereId, onSuccess, onError }) => {
  const [loading, setLoading] = useState(false);
  const [selectedOption, setSelectedOption] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [formData, setFormData] = useState({
    formato: 'pdf',
    id_bobina: '',
    data_inizio: '',
    data_fine: ''
  });
  const [downloadLink, setDownloadLink] = useState('');
  const [reportContent, setReportContent] = useState('');

  // Gestisce la selezione di un'opzione dal menu
  const handleOptionSelect = (option) => {
    setSelectedOption(option);

    if (option === 'reportAvanzamento') {
      setDialogType('reportAvanzamento');
      setOpenDialog(true);
    } else if (option === 'billOfQuantities') {
      setDialogType('billOfQuantities');
      setOpenDialog(true);
    } else if (option === 'reportUtilizzoBobine') {
      setDialogType('reportUtilizzoBobine');
      setOpenDialog(true);
    } else if (option === 'reportPosaPeriodo') {
      // Imposta le date di default (ultimo mese)
      const today = new Date();
      const lastMonth = new Date();
      lastMonth.setMonth(today.getMonth() - 1);

      setFormData({
        ...formData,
        data_inizio: lastMonth.toISOString().split('T')[0],
        data_fine: today.toISOString().split('T')[0]
      });

      setDialogType('reportPosaPeriodo');
      setOpenDialog(true);
    }
  };

  // Gestisce la chiusura del dialog
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setFormData({
      formato: 'pdf',
      id_bobina: '',
      data_inizio: '',
      data_fine: ''
    });
    setDownloadLink('');
    setReportContent('');
  };

  // Gestisce il cambio dei valori nel form
  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Gestisce la generazione del report di avanzamento
  const handleGeneraReportAvanzamento = async () => {
    try {
      setLoading(true);

      if (formData.formato === 'video') {
        const response = await reportService.getProgressReport(cantiereId, 'video');
        setReportContent(response.content);
      } else {
        const response = await reportService.getProgressReport(cantiereId, formData.formato);
        setDownloadLink(response.file_url);
      }

      setDialogType(formData.formato === 'video' ? 'visualizzaReport' : 'downloadReport');
      onSuccess('Report di avanzamento generato con successo');
    } catch (error) {
      onError('Errore nella generazione del report di avanzamento: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nella generazione del report di avanzamento:', error);
    } finally {
      setLoading(false);
    }
  };

  // Gestisce la generazione della distinta materiali
  const handleGeneraBillOfQuantities = async () => {
    try {
      setLoading(true);

      if (formData.formato === 'video') {
        const response = await reportService.getBillOfQuantities(cantiereId, 'video');
        setReportContent(response.content);
      } else {
        const response = await reportService.getBillOfQuantities(cantiereId, formData.formato);
        setDownloadLink(response.file_url);
      }

      setDialogType(formData.formato === 'video' ? 'visualizzaReport' : 'downloadReport');
      onSuccess('Distinta materiali generata con successo');
    } catch (error) {
      onError('Errore nella generazione della distinta materiali: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nella generazione della distinta materiali:', error);
    } finally {
      setLoading(false);
    }
  };

  // Gestisce la generazione del report utilizzo bobine
  const handleGeneraReportUtilizzoBobine = async () => {
    try {
      setLoading(true);

      if (formData.id_bobina) {
        // Report per bobina specifica
        if (formData.formato === 'video') {
          const response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, 'video');
          setReportContent(response.content);
        } else {
          const response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, formData.formato);
          setDownloadLink(response.file_url);
        }
      } else {
        // Report completo bobine
        if (formData.formato === 'video') {
          const response = await reportService.getBobineReport(cantiereId, 'video');
          setReportContent(response.content);
        } else {
          const response = await reportService.getBobineReport(cantiereId, formData.formato);
          setDownloadLink(response.file_url);
        }
      }

      setDialogType(formData.formato === 'video' ? 'visualizzaReport' : 'downloadReport');
      onSuccess('Report utilizzo bobine generato con successo');
    } catch (error) {
      onError('Errore nella generazione del report utilizzo bobine: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nella generazione del report utilizzo bobine:', error);
    } finally {
      setLoading(false);
    }
  };

  // Gestisce la generazione del report posa per periodo
  const handleGeneraReportPosaPeriodo = async () => {
    try {
      if (!formData.data_inizio || !formData.data_fine) {
        onError('Seleziona le date di inizio e fine periodo');
        return;
      }

      setLoading(true);

      if (formData.formato === 'video') {
        const response = await reportService.getPosaPerPeriodoReport(
          cantiereId,
          formData.data_inizio,
          formData.data_fine,
          'video'
        );
        setReportContent(response.content);
      } else {
        const response = await reportService.getPosaPerPeriodoReport(
          cantiereId,
          formData.data_inizio,
          formData.data_fine,
          formData.formato
        );
        setDownloadLink(response.file_url);
      }

      setDialogType(formData.formato === 'video' ? 'visualizzaReport' : 'downloadReport');
      onSuccess('Report posa per periodo generato con successo');
    } catch (error) {
      onError('Errore nella generazione del report posa per periodo: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nella generazione del report posa per periodo:', error);
    } finally {
      setLoading(false);
    }
  };

  // Renderizza il dialog in base al tipo
  const renderDialog = () => {
    if (dialogType === 'reportAvanzamento') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>Report Avanzamento</DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
                <InputLabel>Formato</InputLabel>
                <Select
                  name="formato"
                  value={formData.formato}
                  onChange={handleFormChange}
                  label="Formato"
                >
                  <MenuItem value="pdf">PDF</MenuItem>
                  <MenuItem value="excel">Excel</MenuItem>
                  <MenuItem value="video">Visualizza a schermo</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
            <Button
              onClick={handleGeneraReportAvanzamento}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <AssessmentIcon />}
            >
              Genera Report
            </Button>
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'billOfQuantities') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>Bill of Quantities (Distinta Materiali)</DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
                <InputLabel>Formato</InputLabel>
                <Select
                  name="formato"
                  value={formData.formato}
                  onChange={handleFormChange}
                  label="Formato"
                >
                  <MenuItem value="pdf">PDF</MenuItem>
                  <MenuItem value="excel">Excel</MenuItem>
                  <MenuItem value="video">Visualizza a schermo</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
            <Button
              onClick={handleGeneraBillOfQuantities}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <ListIcon />}
            >
              Genera Report
            </Button>
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'reportUtilizzoBobine') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>Report Utilizzo Bobine</DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <TextField
                name="id_bobina"
                label="ID Bobina (opzionale)"
                fullWidth
                variant="outlined"
                value={formData.id_bobina}
                onChange={handleFormChange}
                helperText="Lascia vuoto per un report completo di tutte le bobine"
                sx={{ mb: 2 }}
              />

              <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
                <InputLabel>Formato</InputLabel>
                <Select
                  name="formato"
                  value={formData.formato}
                  onChange={handleFormChange}
                  label="Formato"
                >
                  <MenuItem value="pdf">PDF</MenuItem>
                  <MenuItem value="excel">Excel</MenuItem>
                  <MenuItem value="video">Visualizza a schermo</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
            <Button
              onClick={handleGeneraReportUtilizzoBobine}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <PieChartIcon />}
            >
              Genera Report
            </Button>
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'reportPosaPeriodo') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>Report Posa per Periodo</DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <TextField
                name="data_inizio"
                label="Data Inizio"
                type="date"
                fullWidth
                variant="outlined"
                value={formData.data_inizio}
                onChange={handleFormChange}
                InputLabelProps={{ shrink: true }}
                sx={{ mb: 2 }}
                required
              />

              <TextField
                name="data_fine"
                label="Data Fine"
                type="date"
                fullWidth
                variant="outlined"
                value={formData.data_fine}
                onChange={handleFormChange}
                InputLabelProps={{ shrink: true }}
                sx={{ mb: 2 }}
                required
              />

              <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
                <InputLabel>Formato</InputLabel>
                <Select
                  name="formato"
                  value={formData.formato}
                  onChange={handleFormChange}
                  label="Formato"
                >
                  <MenuItem value="pdf">PDF</MenuItem>
                  <MenuItem value="excel">Excel</MenuItem>
                  <MenuItem value="video">Visualizza a schermo</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
            <Button
              onClick={handleGeneraReportPosaPeriodo}
              disabled={loading || !formData.data_inizio || !formData.data_fine}
              startIcon={loading ? <CircularProgress size={20} /> : <TimelineIcon />}
            >
              Genera Report
            </Button>
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'downloadReport') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>Report Generato</DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <Alert severity="success" sx={{ mb: 2 }}>
                Il report è stato generato con successo.
              </Alert>
              <Typography variant="body1" gutterBottom>
                Clicca sul link sottostante per scaricare il file:
              </Typography>
              <Link
                href={downloadLink}
                target="_blank"
                rel="noopener noreferrer"
                download
                sx={{ display: 'flex', alignItems: 'center', mt: 1 }}
              >
                <DownloadIcon sx={{ mr: 1 }} />
                Scarica report
              </Link>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Chiudi</Button>
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'visualizzaReport') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
          <DialogTitle>Visualizzazione Report</DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2, mb: 2 }}>
              <Paper sx={{ p: 2, maxHeight: '60vh', overflow: 'auto' }}>
                <pre style={{ whiteSpace: 'pre-wrap', fontFamily: 'monospace' }}>
                  {reportContent}
                </pre>
              </Paper>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Chiudi</Button>
          </DialogActions>
        </Dialog>
      );
    }

    return null;
  };

  return (
    <Box>
      {downloadLink ? (
        <Paper sx={{ p: 3, minHeight: '200px', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
          <Typography variant="h6" gutterBottom>
            Download pronto
          </Typography>
          <Link href={downloadLink} download target="_blank" rel="noopener">
            <Button variant="contained" color="primary" startIcon={<DownloadIcon />}>
              Scarica report
            </Button>
          </Link>
        </Paper>
      ) : (
        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          {!selectedOption ? (
            <Typography variant="body1">
              Seleziona un'opzione dal menu principale per iniziare.
            </Typography>
          ) : loading ? (
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                {selectedOption === 'reportAvanzamento' && 'Report Avanzamento'}
                {selectedOption === 'billOfQuantities' && 'Bill of Quantities'}
                {selectedOption === 'reportUtilizzoBobine' && 'Report Utilizzo Bobine'}
                {selectedOption === 'reportPosaPeriodo' && 'Report Posa per Periodo'}
              </Typography>
              <CircularProgress sx={{ mt: 2 }} />
            </Box>
          ) : (
            <Typography variant="body1">
              Seleziona il formato e le opzioni per generare il report.
            </Typography>
          )}
        </Paper>
      )}

      {renderDialog()}
    </Box>
  );
};

export default ReportCavi;
