import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert
} from '@mui/material';
import parcoCaviService from '../../services/parcoCaviService';
import caviService from '../../services/caviService';

/**
 * Componente di test per visualizzare tutte le bobine e i cavi disponibili
 */
const TestBobineComponent = ({ cantiereId }) => {
  const [bobine, setBobine] = useState([]);
  const [cavi, setCavi] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Carica bobine e cavi all'avvio
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        // Carica bobine
        console.log('Caricamento bobine per cantiere:', cantiereId);
        const bobineData = await parcoCaviService.getBobine(cantiereId);
        console.log('Bobine caricate:', bobineData);
        setBobine(bobineData);
        
        // Carica cavi
        console.log('Caricamento cavi per cantiere:', cantiereId);
        const caviData = await caviService.getCavi(cantiereId);
        console.log('Cavi caricati:', caviData);
        setCavi(caviData);
      } catch (error) {
        console.error('Errore nel caricamento dei dati:', error);
        setError('Errore nel caricamento dei dati: ' + (error.message || 'Errore sconosciuto'));
      } finally {
        setLoading(false);
      }
    };
    
    loadData();
  }, [cantiereId]);

  // Mostra loading
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Mostra errore
  if (error) {
    return (
      <Alert severity="error" sx={{ my: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom>
        Test Visualizzazione Bobine e Cavi
      </Typography>
      
      {/* Tabella Bobine */}
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Bobine Disponibili ({bobine.length})
      </Typography>
      
      {bobine.length === 0 ? (
        <Alert severity="info" sx={{ my: 2 }}>
          Nessuna bobina disponibile per questo cantiere.
        </Alert>
      ) : (
        <TableContainer component={Paper} sx={{ mb: 3 }}>
          <Table size="small">
            <TableHead>
              <TableRow sx={{ bgcolor: '#e3f2fd' }}>
                <TableCell>ID Bobina</TableCell>
                <TableCell>Tipologia</TableCell>
                <TableCell>Sezione</TableCell>
                <TableCell>Metri Residui</TableCell>
                <TableCell>Stato</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {bobine.map((bobina) => (
                <TableRow key={bobina.id_bobina}>
                  <TableCell><strong>{bobina.id_bobina}</strong></TableCell>
                  <TableCell>{bobina.tipologia || 'N/A'}</TableCell>
                  <TableCell>{bobina.sezione || 'N/A'}</TableCell>
                  <TableCell>{bobina.metri_residui || 'N/A'} m</TableCell>
                  <TableCell>{bobina.stato_bobina || 'N/A'}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
      
      {/* Tabella Cavi */}
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Cavi Disponibili ({cavi.length})
      </Typography>
      
      {cavi.length === 0 ? (
        <Alert severity="info" sx={{ my: 2 }}>
          Nessun cavo disponibile per questo cantiere.
        </Alert>
      ) : (
        <TableContainer component={Paper} sx={{ mb: 3 }}>
          <Table size="small">
            <TableHead>
              <TableRow sx={{ bgcolor: '#e3f2fd' }}>
                <TableCell>ID Cavo</TableCell>
                <TableCell>Tipologia</TableCell>
                <TableCell>Sezione</TableCell>
                <TableCell>Metri Teorici</TableCell>
                <TableCell>Stato</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {cavi.map((cavo) => (
                <TableRow key={cavo.id_cavo}>
                  <TableCell><strong>{cavo.id_cavo}</strong></TableCell>
                  <TableCell>{cavo.tipologia || 'N/A'}</TableCell>
                  <TableCell>{cavo.sezione || 'N/A'}</TableCell>
                  <TableCell>{cavo.metri_teorici || 'N/A'} m</TableCell>
                  <TableCell>{cavo.stato_installazione || 'N/A'}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
      
      {/* Sezione di debug */}
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Debug - Verifica Compatibilità
      </Typography>
      
      <TableContainer component={Paper} sx={{ mb: 3 }}>
        <Table size="small">
          <TableHead>
            <TableRow sx={{ bgcolor: '#e3f2fd' }}>
              <TableCell>Cavo</TableCell>
              <TableCell>Bobina</TableCell>
              <TableCell>Tipologia Match</TableCell>
              <TableCell>Sezione Match</TableCell>
              <TableCell>Compatibile</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {cavi.filter(cavo => cavo.stato_installazione === 'Da installare').map((cavo) => (
              bobine.filter(bobina => bobina.stato_bobina !== 'Terminata').map((bobina) => {
                const tipologiaMatch = String(cavo.tipologia || '').trim().toLowerCase() === String(bobina.tipologia || '').trim().toLowerCase();
                const sezioneMatch = String(cavo.sezione || '').trim() === String(bobina.sezione || '').trim();
                const isCompatible = tipologiaMatch && sezioneMatch;
                
                return (
                  <TableRow key={`${cavo.id_cavo}-${bobina.id_bobina}`} sx={{ bgcolor: isCompatible ? '#f1f8e9' : 'inherit' }}>
                    <TableCell>{cavo.id_cavo} ({cavo.tipologia}/{cavo.sezione})</TableCell>
                    <TableCell>{bobina.id_bobina} ({bobina.tipologia}/{bobina.sezione})</TableCell>
                    <TableCell>{tipologiaMatch ? '✅' : '❌'}</TableCell>
                    <TableCell>{sezioneMatch ? '✅' : '❌'}</TableCell>
                    <TableCell>{isCompatible ? '✅ Compatibile' : '❌ Non compatibile'}</TableCell>
                  </TableRow>
                );
              })
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Paper>
  );
};

export default TestBobineComponent;
