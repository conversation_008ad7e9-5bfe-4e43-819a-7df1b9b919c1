import React, { useState } from 'react';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Typography,
  Box,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  GetApp as DownloadIcon
} from '@mui/icons-material';

import { apiService } from '../../services/apiService';

function CertificazioniList({ certificazioni, onEdit, onDelete, cantiereId }) {
  const [selectedCertificazione, setSelectedCertificazione] = useState(null);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [certificazioneToDelete, setCertificazioneToDelete] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleViewDetails = async (certificazione) => {
    try {
      setLoading(true);
      const details = await apiService.getCertificazione(cantiereId, certificazione.id_certificazione);
      setSelectedCertificazione(details);
      setShowDetailsDialog(true);
    } catch (error) {
      console.error('Errore nel caricamento dei dettagli:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClick = (certificazione) => {
    setCertificazioneToDelete(certificazione);
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      setLoading(true);
      await apiService.deleteCertificazione(cantiereId, certificazioneToDelete.id_certificazione);
      setShowDeleteDialog(false);
      setCertificazioneToDelete(null);
      onDelete();
    } catch (error) {
      console.error('Errore nell\'eliminazione:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('it-IT');
  };

  const getIsolamentoColor = (valore) => {
    if (!valore) return 'default';
    const numValue = parseFloat(valore);
    if (numValue >= 500) return 'success';
    if (numValue >= 100) return 'warning';
    return 'error';
  };

  if (certificazioni.length === 0) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          Nessuna certificazione trovata
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Clicca su "Nuova Certificazione" per aggiungere la prima certificazione
        </Typography>
      </Paper>
    );
  }

  return (
    <>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell><strong>N° Certificato</strong></TableCell>
              <TableCell><strong>ID Cavo</strong></TableCell>
              <TableCell><strong>Tipologia</strong></TableCell>
              <TableCell><strong>Sezione</strong></TableCell>
              <TableCell><strong>Data</strong></TableCell>
              <TableCell><strong>Operatore</strong></TableCell>
              <TableCell><strong>Isolamento (MΩ)</strong></TableCell>
              <TableCell><strong>Lunghezza (m)</strong></TableCell>
              <TableCell><strong>Azioni</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {certificazioni.map((cert) => (
              <TableRow key={cert.id_certificazione} hover>
                <TableCell>
                  <Typography variant="body2" fontWeight="bold">
                    {cert.numero_certificato}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" fontFamily="monospace">
                    {cert.id_cavo}
                  </Typography>
                </TableCell>
                <TableCell>{cert.cavo_tipologia || '-'}</TableCell>
                <TableCell>{cert.cavo_sezione || '-'}</TableCell>
                <TableCell>{formatDate(cert.data_certificazione)}</TableCell>
                <TableCell>{cert.id_operatore || '-'}</TableCell>
                <TableCell>
                  <Chip
                    label={cert.valore_isolamento || '-'}
                    color={getIsolamentoColor(cert.valore_isolamento)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  {cert.lunghezza_misurata ? cert.lunghezza_misurata.toFixed(2) : '-'}
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 0.5 }}>
                    <IconButton
                      size="small"
                      onClick={() => handleViewDetails(cert)}
                      title="Visualizza dettagli"
                    >
                      <VisibilityIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => onEdit(cert)}
                      title="Modifica"
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDeleteClick(cert)}
                      title="Elimina"
                      color="error"
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Dialog Dettagli Certificazione */}
      <Dialog
        open={showDetailsDialog}
        onClose={() => setShowDetailsDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Dettagli Certificazione {selectedCertificazione?.numero_certificato}
        </DialogTitle>
        <DialogContent>
          {selectedCertificazione && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Informazioni Cavo
                </Typography>
                <Typography><strong>ID Cavo:</strong> {selectedCertificazione.id_cavo}</Typography>
                <Typography><strong>Tipologia:</strong> {selectedCertificazione.cavo_tipologia || '-'}</Typography>
                <Typography><strong>Sezione:</strong> {selectedCertificazione.cavo_sezione || '-'}</Typography>
                <Typography><strong>Partenza:</strong> {selectedCertificazione.cavo_ubicazione_partenza || '-'}</Typography>
                <Typography><strong>Arrivo:</strong> {selectedCertificazione.cavo_ubicazione_arrivo || '-'}</Typography>
                <Typography><strong>Metri Teorici:</strong> {selectedCertificazione.cavo_metri_teorici || '-'}</Typography>
                <Typography><strong>Stato:</strong> {selectedCertificazione.cavo_stato_installazione || '-'}</Typography>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Informazioni Certificazione
                </Typography>
                <Typography><strong>N° Certificato:</strong> {selectedCertificazione.numero_certificato}</Typography>
                <Typography><strong>Data:</strong> {formatDate(selectedCertificazione.data_certificazione)}</Typography>
                <Typography><strong>Operatore:</strong> {selectedCertificazione.id_operatore || '-'}</Typography>
                <Typography><strong>Lunghezza Misurata:</strong> {selectedCertificazione.lunghezza_misurata ? `${selectedCertificazione.lunghezza_misurata.toFixed(2)} m` : '-'}</Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Valori di Test
                </Typography>
                <Typography><strong>Continuità:</strong> {selectedCertificazione.valore_continuita || '-'}</Typography>
                <Typography><strong>Isolamento:</strong> {selectedCertificazione.valore_isolamento || '-'} MΩ</Typography>
                <Typography><strong>Resistenza:</strong> {selectedCertificazione.valore_resistenza || '-'}</Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Strumento Utilizzato
                </Typography>
                {selectedCertificazione.strumento_nome ? (
                  <>
                    <Typography><strong>Nome:</strong> {selectedCertificazione.strumento_nome}</Typography>
                    <Typography><strong>Marca:</strong> {selectedCertificazione.strumento_marca || '-'}</Typography>
                    <Typography><strong>Modello:</strong> {selectedCertificazione.strumento_modello || '-'}</Typography>
                  </>
                ) : (
                  <Typography>{selectedCertificazione.strumento_utilizzato || 'Non specificato'}</Typography>
                )}
              </Grid>

              {selectedCertificazione.note && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Note
                  </Typography>
                  <Typography>{selectedCertificazione.note}</Typography>
                </Grid>
              )}
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDetailsDialog(false)}>
            Chiudi
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog Conferma Eliminazione */}
      <Dialog
        open={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
      >
        <DialogTitle>Conferma Eliminazione</DialogTitle>
        <DialogContent>
          <Typography>
            Sei sicuro di voler eliminare la certificazione {certificazioneToDelete?.numero_certificato}?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Questa operazione non può essere annullata.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDeleteDialog(false)}>
            Annulla
          </Button>
          <Button 
            onClick={handleDeleteConfirm} 
            color="error" 
            disabled={loading}
          >
            Elimina
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default CertificazioniList;
