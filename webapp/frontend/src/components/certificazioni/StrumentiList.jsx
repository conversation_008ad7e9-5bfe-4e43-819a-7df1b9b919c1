import React, { useState } from 'react';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Typography,
  Box,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Warning as WarningIcon
} from '@mui/icons-material';

import { apiService } from '../../services/apiService';

function StrumentiList({ strumenti, onEdit, onDelete, cantiereId }) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [strumentoToDelete, setStrumentoToDelete] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleDeleteClick = (strumento) => {
    setStrumentoToDelete(strumento);
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      setLoading(true);
      await apiService.deleteStrumento(cantiereId, strumentoToDelete.id_strumento);
      setShowDeleteDialog(false);
      setStrumentoToDelete(null);
      onDelete();
    } catch (error) {
      console.error('Errore nell\'eliminazione:', error);
      // TODO: Mostrare errore all'utente
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('it-IT');
  };

  const isCalibrationExpired = (dataScadenza) => {
    if (!dataScadenza) return false;
    const today = new Date();
    const scadenza = new Date(dataScadenza);
    return scadenza < today;
  };

  const isCalibrationExpiringSoon = (dataScadenza) => {
    if (!dataScadenza) return false;
    const today = new Date();
    const scadenza = new Date(dataScadenza);
    const diffTime = scadenza - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 30 && diffDays > 0;
  };

  const getCalibrationStatus = (dataScadenza) => {
    if (isCalibrationExpired(dataScadenza)) {
      return { label: 'Scaduto', color: 'error' };
    }
    if (isCalibrationExpiringSoon(dataScadenza)) {
      return { label: 'In scadenza', color: 'warning' };
    }
    return { label: 'Valido', color: 'success' };
  };

  if (strumenti.length === 0) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          Nessuno strumento certificato trovato
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Clicca su "Nuovo Strumento" per aggiungere il primo strumento
        </Typography>
      </Paper>
    );
  }

  return (
    <>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell><strong>Nome</strong></TableCell>
              <TableCell><strong>Marca</strong></TableCell>
              <TableCell><strong>Modello</strong></TableCell>
              <TableCell><strong>N° Serie</strong></TableCell>
              <TableCell><strong>Data Calibrazione</strong></TableCell>
              <TableCell><strong>Scadenza Calibrazione</strong></TableCell>
              <TableCell><strong>Stato</strong></TableCell>
              <TableCell><strong>Azioni</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {strumenti.map((strumento) => {
              const calibrationStatus = getCalibrationStatus(strumento.data_scadenza_calibrazione);
              
              return (
                <TableRow key={strumento.id_strumento} hover>
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold">
                      {strumento.nome}
                    </Typography>
                  </TableCell>
                  <TableCell>{strumento.marca}</TableCell>
                  <TableCell>{strumento.modello}</TableCell>
                  <TableCell>
                    <Typography variant="body2" fontFamily="monospace">
                      {strumento.numero_serie}
                    </Typography>
                  </TableCell>
                  <TableCell>{formatDate(strumento.data_calibrazione)}</TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {formatDate(strumento.data_scadenza_calibrazione)}
                      {isCalibrationExpired(strumento.data_scadenza_calibrazione) && (
                        <WarningIcon color="error" fontSize="small" />
                      )}
                      {isCalibrationExpiringSoon(strumento.data_scadenza_calibrazione) && (
                        <WarningIcon color="warning" fontSize="small" />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={calibrationStatus.label}
                      color={calibrationStatus.color}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      <IconButton
                        size="small"
                        onClick={() => onEdit(strumento)}
                        title="Modifica"
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleDeleteClick(strumento)}
                        title="Elimina"
                        color="error"
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Dialog Conferma Eliminazione */}
      <Dialog
        open={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
      >
        <DialogTitle>Conferma Eliminazione</DialogTitle>
        <DialogContent>
          <Typography>
            Sei sicuro di voler eliminare lo strumento "{strumentoToDelete?.nome} {strumentoToDelete?.marca} {strumentoToDelete?.modello}"?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Questa operazione non può essere annullata. Lo strumento non potrà essere eliminato se è utilizzato in certificazioni esistenti.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDeleteDialog(false)}>
            Annulla
          </Button>
          <Button 
            onClick={handleDeleteConfirm} 
            color="error" 
            disabled={loading}
          >
            Elimina
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default StrumentiList;
