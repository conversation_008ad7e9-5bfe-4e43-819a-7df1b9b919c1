import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line
} from 'recharts';
import { Box, Typography, Grid, Paper } from '@mui/material';

const COLORS = {
  primary: '#2c3e50',
  secondary: '#34495e',
  success: '#27ae60',
  warning: '#f39c12',
  info: '#3498db',
  error: '#e74c3c',
  light: '#ecf0f1',
  dark: '#2c3e50',
  accent: '#9b59b6'
};

const ProgressChart = ({ data }) => {
  if (!data) return null;

  // Dati per il grafico a torta dell'avanzamento
  const progressData = [
    {
      name: 'Metri Posati',
      value: data.metri_posati,
      color: COLORS.success
    },
    {
      name: 'Metri Rimanenti',
      value: data.metri_da_posare,
      color: COLORS.warning
    }
  ];

  // Dati per il grafico a torta dei cavi
  const caviData = [
    {
      name: '<PERSON><PERSON>',
      value: data.cavi_posati,
      color: COLORS.success
    },
    {
      name: '<PERSON><PERSON>',
      value: data.cavi_rimanenti,
      color: COLORS.warning
    }
  ];

  // Dati per il grafico a barre delle metriche principali
  const metricsData = [
    {
      name: 'Metri',
      Totali: data.metri_totali,
      Posati: data.metri_posati,
      Rimanenti: data.metri_da_posare
    },
    {
      name: 'Cavi',
      Totali: data.totale_cavi,
      Posati: data.cavi_posati,
      Rimanenti: data.cavi_rimanenti
    }
  ];

  // Dati per il grafico temporale della posa recente
  const posaTrendData = data.posa_recente?.map(posa => ({
    data: posa.data,
    metri: posa.metri
  })) || [];

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>
          <Typography variant="body2">{`${label}`}</Typography>
          {payload.map((entry, index) => (
            <Typography key={index} variant="body2" style={{ color: entry.color }}>
              {`${entry.name}: ${entry.value}`}
            </Typography>
          ))}
        </Paper>
      );
    }
    return null;
  };

  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    if (percent < 0.05) return null; // Non mostrare etichette per fette troppo piccole

    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
        fontSize="12"
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <Paper sx={{ p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
      <Typography variant="subtitle1" sx={{ fontWeight: 500, mb: 2, color: '#2c3e50' }}>
        Analisi Avanzamento
      </Typography>

      <Grid container spacing={2}>
        {/* Tabella Metriche Principali */}
        <Grid item xs={12} md={8}>
          <Box sx={{
            border: '1px solid #e0e0e0',
            borderRadius: 1,
            overflow: 'hidden'
          }}>
            <Box sx={{
              bgcolor: '#f8f9fa',
              p: 1.5,
              borderBottom: '1px solid #e0e0e0'
            }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                Metriche di Avanzamento
              </Typography>
            </Box>
            <Box sx={{ p: 0 }}>
              <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                <thead>
                  <tr style={{ backgroundColor: '#f8f9fa' }}>
                    <th style={{
                      padding: '8px 12px',
                      textAlign: 'left',
                      fontSize: '12px',
                      fontWeight: 600,
                      color: '#2c3e50',
                      borderBottom: '1px solid #e0e0e0'
                    }}>Metrica</th>
                    <th style={{
                      padding: '8px 12px',
                      textAlign: 'right',
                      fontSize: '12px',
                      fontWeight: 600,
                      color: '#2c3e50',
                      borderBottom: '1px solid #e0e0e0'
                    }}>Valore</th>
                    <th style={{
                      padding: '8px 12px',
                      textAlign: 'right',
                      fontSize: '12px',
                      fontWeight: 600,
                      color: '#2c3e50',
                      borderBottom: '1px solid #e0e0e0'
                    }}>%</th>
                    <th style={{
                      padding: '8px 12px',
                      textAlign: 'center',
                      fontSize: '12px',
                      fontWeight: 600,
                      color: '#2c3e50',
                      borderBottom: '1px solid #e0e0e0'
                    }}>Progresso</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td style={{
                      padding: '8px 12px',
                      fontSize: '13px',
                      borderBottom: '1px solid #f0f0f0'
                    }}>Metri Totali</td>
                    <td style={{
                      padding: '8px 12px',
                      textAlign: 'right',
                      fontSize: '13px',
                      fontWeight: 600,
                      borderBottom: '1px solid #f0f0f0'
                    }}>{data.metri_totali}m</td>
                    <td style={{
                      padding: '8px 12px',
                      textAlign: 'right',
                      fontSize: '13px',
                      borderBottom: '1px solid #f0f0f0'
                    }}>100%</td>
                    <td style={{
                      padding: '8px 12px',
                      textAlign: 'center',
                      borderBottom: '1px solid #f0f0f0'
                    }}>
                      <Box sx={{
                        width: '60px',
                        height: '6px',
                        bgcolor: '#e0e0e0',
                        borderRadius: '3px',
                        position: 'relative',
                        margin: '0 auto'
                      }}>
                        <Box sx={{
                          width: '100%',
                          height: '100%',
                          bgcolor: COLORS.primary,
                          borderRadius: '3px'
                        }} />
                      </Box>
                    </td>
                  </tr>
                  <tr>
                    <td style={{
                      padding: '8px 12px',
                      fontSize: '13px',
                      borderBottom: '1px solid #f0f0f0'
                    }}>Metri Posati</td>
                    <td style={{
                      padding: '8px 12px',
                      textAlign: 'right',
                      fontSize: '13px',
                      fontWeight: 600,
                      color: COLORS.success,
                      borderBottom: '1px solid #f0f0f0'
                    }}>{data.metri_posati}m</td>
                    <td style={{
                      padding: '8px 12px',
                      textAlign: 'right',
                      fontSize: '13px',
                      color: COLORS.success,
                      borderBottom: '1px solid #f0f0f0'
                    }}>{data.percentuale_avanzamento}%</td>
                    <td style={{
                      padding: '8px 12px',
                      textAlign: 'center',
                      borderBottom: '1px solid #f0f0f0'
                    }}>
                      <Box sx={{
                        width: '60px',
                        height: '6px',
                        bgcolor: '#e0e0e0',
                        borderRadius: '3px',
                        position: 'relative',
                        margin: '0 auto'
                      }}>
                        <Box sx={{
                          width: `${data.percentuale_avanzamento}%`,
                          height: '100%',
                          bgcolor: COLORS.success,
                          borderRadius: '3px'
                        }} />
                      </Box>
                    </td>
                  </tr>
                  <tr>
                    <td style={{
                      padding: '8px 12px',
                      fontSize: '13px',
                      borderBottom: '1px solid #f0f0f0'
                    }}>Cavi Totali</td>
                    <td style={{
                      padding: '8px 12px',
                      textAlign: 'right',
                      fontSize: '13px',
                      fontWeight: 600,
                      borderBottom: '1px solid #f0f0f0'
                    }}>{data.totale_cavi}</td>
                    <td style={{
                      padding: '8px 12px',
                      textAlign: 'right',
                      fontSize: '13px',
                      borderBottom: '1px solid #f0f0f0'
                    }}>100%</td>
                    <td style={{
                      padding: '8px 12px',
                      textAlign: 'center',
                      borderBottom: '1px solid #f0f0f0'
                    }}>
                      <Box sx={{
                        width: '60px',
                        height: '6px',
                        bgcolor: '#e0e0e0',
                        borderRadius: '3px',
                        position: 'relative',
                        margin: '0 auto'
                      }}>
                        <Box sx={{
                          width: '100%',
                          height: '100%',
                          bgcolor: COLORS.primary,
                          borderRadius: '3px'
                        }} />
                      </Box>
                    </td>
                  </tr>
                  <tr>
                    <td style={{
                      padding: '8px 12px',
                      fontSize: '13px'
                    }}>Cavi Posati</td>
                    <td style={{
                      padding: '8px 12px',
                      textAlign: 'right',
                      fontSize: '13px',
                      fontWeight: 600,
                      color: COLORS.success
                    }}>{data.cavi_posati}</td>
                    <td style={{
                      padding: '8px 12px',
                      textAlign: 'right',
                      fontSize: '13px',
                      color: COLORS.success
                    }}>{data.percentuale_cavi}%</td>
                    <td style={{
                      padding: '8px 12px',
                      textAlign: 'center'
                    }}>
                      <Box sx={{
                        width: '60px',
                        height: '6px',
                        bgcolor: '#e0e0e0',
                        borderRadius: '3px',
                        position: 'relative',
                        margin: '0 auto'
                      }}>
                        <Box sx={{
                          width: `${data.percentuale_cavi}%`,
                          height: '100%',
                          bgcolor: COLORS.success,
                          borderRadius: '3px'
                        }} />
                      </Box>
                    </td>
                  </tr>
                </tbody>
              </table>
            </Box>
          </Box>
        </Grid>

        {/* Grafico Compatto */}
        <Grid item xs={12} md={4}>
          <Box sx={{
            border: '1px solid #e0e0e0',
            borderRadius: 1,
            overflow: 'hidden',
            height: '100%'
          }}>
            <Box sx={{
              bgcolor: '#f8f9fa',
              p: 1.5,
              borderBottom: '1px solid #e0e0e0'
            }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                Distribuzione Avanzamento
              </Typography>
            </Box>
            <Box sx={{ p: 2, height: 'calc(100% - 50px)', display: 'flex', alignItems: 'center' }}>
              <ResponsiveContainer width="100%" height={180}>
                <PieChart>
                  <Pie
                    data={progressData}
                    cx="50%"
                    cy="50%"
                    innerRadius={35}
                    outerRadius={65}
                    paddingAngle={2}
                    dataKey="value"
                    stroke="none"
                  >
                    {progressData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        return (
                          <Box sx={{
                            bgcolor: 'white',
                            p: 1,
                            border: '1px solid #e0e0e0',
                            borderRadius: 1,
                            fontSize: '12px'
                          }}>
                            <Typography variant="caption" sx={{ fontWeight: 600 }}>
                              {payload[0].name}: {payload[0].value}m
                            </Typography>
                          </Box>
                        );
                      }
                      return null;
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </Box>
          </Box>
        </Grid>

        {/* Performance e Trend (solo se ci sono dati) */}
        {data.media_giornaliera && (
          <Grid item xs={12}>
            <Box sx={{
              border: '1px solid #e0e0e0',
              borderRadius: 1,
              overflow: 'hidden'
            }}>
              <Box sx={{
                bgcolor: '#f8f9fa',
                p: 1.5,
                borderBottom: '1px solid #e0e0e0'
              }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                  Performance e Previsioni
                </Typography>
              </Box>
              <Box sx={{ p: 2 }}>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={3}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h6" sx={{ fontWeight: 600, color: COLORS.primary }}>
                        {data.media_giornaliera}m
                      </Typography>
                      <Typography variant="caption" sx={{ color: '#666' }}>
                        Media Giornaliera
                      </Typography>
                    </Box>
                  </Grid>
                  {data.giorni_stimati && (
                    <>
                      <Grid item xs={12} md={3}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="h6" sx={{ fontWeight: 600, color: COLORS.warning }}>
                            {data.giorni_stimati}
                          </Typography>
                          <Typography variant="caption" sx={{ color: '#666' }}>
                            Giorni Stimati
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="body2" sx={{ fontWeight: 600, color: COLORS.info }}>
                            {data.data_completamento}
                          </Typography>
                          <Typography variant="caption" sx={{ color: '#666' }}>
                            Data Completamento Prevista
                          </Typography>
                        </Box>
                      </Grid>
                    </>
                  )}
                </Grid>
              </Box>
            </Box>
          </Grid>
        )}
      </Grid>
    </Paper>
  );
};

export default ProgressChart;
