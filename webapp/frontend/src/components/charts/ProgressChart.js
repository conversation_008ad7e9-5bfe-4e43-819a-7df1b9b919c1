import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Toolt<PERSON>,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line
} from 'recharts';
import { Box, Typography, Grid, Paper } from '@mui/material';

const COLORS = {
  primary: '#1976d2',
  secondary: '#dc004e',
  success: '#2e7d32',
  warning: '#ed6c02',
  info: '#0288d1',
  error: '#d32f2f'
};

const ProgressChart = ({ data }) => {
  if (!data) return null;

  // Dati per il grafico a torta dell'avanzamento
  const progressData = [
    {
      name: 'Metri Posati',
      value: data.metri_posati,
      color: COLORS.success
    },
    {
      name: 'Metri Rimanenti',
      value: data.metri_da_posare,
      color: COLORS.warning
    }
  ];

  // Dati per il grafico a torta dei cavi
  const caviData = [
    {
      name: '<PERSON><PERSON>',
      value: data.cavi_posati,
      color: COLORS.success
    },
    {
      name: '<PERSON><PERSON>',
      value: data.cavi_rimanenti,
      color: COLORS.warning
    }
  ];

  // Dati per il grafico a barre delle metriche principali
  const metricsData = [
    {
      name: 'Metri',
      Totali: data.metri_totali,
      Posati: data.metri_posati,
      Rimanenti: data.metri_da_posare
    },
    {
      name: 'Cavi',
      Totali: data.totale_cavi,
      Posati: data.cavi_posati,
      Rimanenti: data.cavi_rimanenti
    }
  ];

  // Dati per il grafico temporale della posa recente
  const posaTrendData = data.posa_recente?.map(posa => ({
    data: posa.data,
    metri: posa.metri
  })) || [];

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>
          <Typography variant="body2">{`${label}`}</Typography>
          {payload.map((entry, index) => (
            <Typography key={index} variant="body2" style={{ color: entry.color }}>
              {`${entry.name}: ${entry.value}`}
            </Typography>
          ))}
        </Paper>
      );
    }
    return null;
  };

  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    if (percent < 0.05) return null; // Non mostrare etichette per fette troppo piccole

    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
        fontSize="12"
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, mb: 3 }}>
        Grafici di Avanzamento
      </Typography>

      <Grid container spacing={3}>
        {/* Grafici a torta - Avanzamento Metri e Cavi */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: 350, bgcolor: 'grey.50' }}>
            <Typography variant="h6" gutterBottom align="center" sx={{ fontWeight: 600, mb: 2 }}>
              Avanzamento Metri
            </Typography>
            <Typography variant="body2" align="center" sx={{ mb: 2, color: 'text.secondary' }}>
              {data.percentuale_avanzamento}% completato
            </Typography>
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={progressData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomizedLabel}
                  outerRadius={90}
                  innerRadius={40}
                  fill="#8884d8"
                  dataKey="value"
                  strokeWidth={2}
                >
                  {progressData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
                <Legend
                  verticalAlign="bottom"
                  height={36}
                  iconType="circle"
                  wrapperStyle={{ fontSize: '14px', fontWeight: 'bold' }}
                />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: 350, bgcolor: 'grey.50' }}>
            <Typography variant="h6" gutterBottom align="center" sx={{ fontWeight: 600, mb: 2 }}>
              Avanzamento Cavi
            </Typography>
            <Typography variant="body2" align="center" sx={{ mb: 2, color: 'text.secondary' }}>
              {data.percentuale_cavi}% completato
            </Typography>
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={caviData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomizedLabel}
                  outerRadius={90}
                  innerRadius={40}
                  fill="#8884d8"
                  dataKey="value"
                  strokeWidth={2}
                >
                  {caviData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
                <Legend
                  verticalAlign="bottom"
                  height={36}
                  iconType="circle"
                  wrapperStyle={{ fontSize: '14px', fontWeight: 'bold' }}
                />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Grafico a barre - Confronto Metriche */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: 350, bgcolor: 'grey.50' }}>
            <Typography variant="h6" gutterBottom align="center" sx={{ fontWeight: 600, mb: 2 }}>
              Confronto Metriche
            </Typography>
            <ResponsiveContainer width="100%" height={280}>
              <BarChart data={metricsData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
                <XAxis
                  dataKey="name"
                  tick={{ fontSize: 12, fontWeight: 'bold' }}
                  axisLine={{ stroke: '#666' }}
                />
                <YAxis
                  tick={{ fontSize: 12 }}
                  axisLine={{ stroke: '#666' }}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend
                  wrapperStyle={{ fontSize: '12px', fontWeight: 'bold' }}
                />
                <Bar dataKey="Totali" fill={COLORS.primary} radius={[4, 4, 0, 0]} />
                <Bar dataKey="Posati" fill={COLORS.success} radius={[4, 4, 0, 0]} />
                <Bar dataKey="Rimanenti" fill={COLORS.warning} radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Grafico temporale - Posa Recente (solo se ci sono dati) */}
        {posaTrendData.length > 0 && (
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3, height: 350, bgcolor: 'grey.50' }}>
              <Typography variant="h6" gutterBottom align="center" sx={{ fontWeight: 600, mb: 2 }}>
                Trend Posa Recente
              </Typography>
              <Typography variant="body2" align="center" sx={{ mb: 2, color: 'text.secondary' }}>
                Media: {data.media_giornaliera}m/giorno
              </Typography>
              <ResponsiveContainer width="100%" height={280}>
                <LineChart data={posaTrendData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
                  <XAxis
                    dataKey="data"
                    tick={{ fontSize: 10 }}
                    axisLine={{ stroke: '#666' }}
                  />
                  <YAxis
                    tick={{ fontSize: 12 }}
                    axisLine={{ stroke: '#666' }}
                    label={{ value: 'Metri', angle: -90, position: 'insideLeft' }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend wrapperStyle={{ fontSize: '12px', fontWeight: 'bold' }} />
                  <Line
                    type="monotone"
                    dataKey="metri"
                    stroke={COLORS.primary}
                    strokeWidth={3}
                    dot={{ fill: COLORS.primary, strokeWidth: 2, r: 5 }}
                    activeDot={{ r: 8, stroke: COLORS.primary, strokeWidth: 2 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default ProgressChart;
