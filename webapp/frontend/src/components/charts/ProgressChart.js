import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Toolt<PERSON>,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line
} from 'recharts';
import { Box, Typography, Grid, Paper } from '@mui/material';

const COLORS = {
  primary: '#1976d2',
  secondary: '#dc004e',
  success: '#2e7d32',
  warning: '#ed6c02',
  info: '#0288d1',
  error: '#d32f2f'
};

const ProgressChart = ({ data }) => {
  if (!data) return null;

  // Dati per il grafico a torta dell'avanzamento
  const progressData = [
    {
      name: 'Metri Posati',
      value: data.metri_posati,
      color: COLORS.success
    },
    {
      name: 'Metri Rimanenti',
      value: data.metri_da_posare,
      color: COLORS.warning
    }
  ];

  // Dati per il grafico a torta dei cavi
  const caviData = [
    {
      name: '<PERSON><PERSON>',
      value: data.cavi_posati,
      color: COLORS.success
    },
    {
      name: '<PERSON><PERSON>',
      value: data.cavi_rimanenti,
      color: COLORS.warning
    }
  ];

  // Dati per il grafico a barre delle metriche principali
  const metricsData = [
    {
      name: 'Metri',
      Totali: data.metri_totali,
      Posati: data.metri_posati,
      Rimanenti: data.metri_da_posare
    },
    {
      name: 'Cavi',
      Totali: data.totale_cavi,
      Posati: data.cavi_posati,
      Rimanenti: data.cavi_rimanenti
    }
  ];

  // Dati per il grafico temporale della posa recente
  const posaTrendData = data.posa_recente?.map(posa => ({
    data: posa.data,
    metri: posa.metri
  })) || [];

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>
          <Typography variant="body2">{`${label}`}</Typography>
          {payload.map((entry, index) => (
            <Typography key={index} variant="body2" style={{ color: entry.color }}>
              {`${entry.name}: ${entry.value}`}
            </Typography>
          ))}
        </Paper>
      );
    }
    return null;
  };

  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    if (percent < 0.05) return null; // Non mostrare etichette per fette troppo piccole

    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
        fontSize="12"
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        Grafici di Avanzamento
      </Typography>

      <Grid container spacing={2}>
        {/* Grafici a torta - Avanzamento Metri e Cavi */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: 250 }}>
            <Typography variant="subtitle1" gutterBottom align="center" sx={{ fontSize: '0.9rem' }}>
              Avanzamento Metri ({data.percentuale_avanzamento}%)
            </Typography>
            <ResponsiveContainer width="100%" height={180}>
              <PieChart>
                <Pie
                  data={progressData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomizedLabel}
                  outerRadius={60}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {progressData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: 250 }}>
            <Typography variant="subtitle1" gutterBottom align="center" sx={{ fontSize: '0.9rem' }}>
              Avanzamento Cavi ({data.percentuale_cavi}%)
            </Typography>
            <ResponsiveContainer width="100%" height={180}>
              <PieChart>
                <Pie
                  data={caviData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomizedLabel}
                  outerRadius={60}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {caviData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Grafico temporale - Posa Recente (solo se ci sono dati) */}
        {posaTrendData.length > 0 && (
          <Grid item xs={12}>
            <Paper sx={{ p: 2, height: 250 }}>
              <Typography variant="subtitle1" gutterBottom align="center" sx={{ fontSize: '0.9rem' }}>
                Trend Posa Recente (Media: {data.media_giornaliera}m/giorno)
              </Typography>
              <ResponsiveContainer width="100%" height={180}>
                <LineChart data={posaTrendData} margin={{ top: 10, right: 20, left: 10, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="data" />
                  <YAxis />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="metri"
                    stroke={COLORS.primary}
                    strokeWidth={2}
                    dot={{ fill: COLORS.primary, strokeWidth: 2, r: 3 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default ProgressChart;
