# Componenti Grafici per Report CMS

Questa directory contiene i componenti grafici sviluppati per migliorare la visualizzazione dei report nel sistema CMS.

## Componenti Disponibili

### 1. ProgressChart.js
**Scopo**: Visualizzazione grafica del report di avanzamento
**Grafici inclusi**:
- Grafico a torta per avanzamento metri (posati vs rimanenti)
- Grafico a torta per avanzamento cavi (posati vs rimanenti)
- Grafico a barre per confronto metriche principali
- Grafico temporale per trend posa recente

**Dati richiesti**:
```javascript
{
  metri_totali: number,
  metri_posati: number,
  metri_da_posare: number,
  percentuale_avanzamento: number,
  totale_cavi: number,
  cavi_posati: number,
  cavi_rimanenti: number,
  percentuale_cavi: number,
  media_giornaliera: number,
  posa_recente: [{ data: string, metri: number }]
}
```

### 2. BobineChart.js
**Scopo**: Analisi grafica dell'utilizzo delle bobine
**Grafici inclusi**:
- Grafico a torta per distribuzione bobine per stato
- Grafico a barre per utilizzo per tipologia
- Grafico a barre per conteggio bobine per stato
- Scatter plot per efficienza utilizzo

**Dati richiesti**:
```javascript
{
  totale_bobine: number,
  bobine: [{
    id_bobina: string,
    tipologia: string,
    sezione: string,
    metri_totali: number,
    metri_residui: number,
    stato: string,
    metri_utilizzati: number,
    percentuale_utilizzo: number
  }]
}
```

### 3. BoqChart.js
**Scopo**: Visualizzazione Bill of Quantities (Distinta Materiali)
**Grafici inclusi**:
- Grafico a torta per distribuzione metri totali
- Grafico a barre per metri per tipologia cavo
- Grafico a barre per bobine disponibili per tipologia
- Analisi deficit/surplus materiali

**Dati richiesti**:
```javascript
{
  cavi_per_tipo: [{
    tipologia: string,
    sezione: string,
    num_cavi: number,
    metri_teorici: number,
    metri_reali: number,
    metri_da_posare: number
  }],
  bobine_per_tipo: [{
    tipologia: string,
    sezione: string,
    num_bobine: number,
    metri_disponibili: number
  }]
}
```

### 4. TimelineChart.js
**Scopo**: Analisi temporale della posa
**Grafici inclusi**:
- Grafico temporale giornaliero con media mobile
- Grafico area cumulativo
- Grafico performance settimanale
- Analisi performance dettagliata

**Dati richiesti**:
```javascript
{
  data_inizio: string,
  data_fine: string,
  totale_metri_periodo: number,
  giorni_attivi: number,
  media_giornaliera: number,
  posa_giornaliera: [{ data: string, metri: number }]
}
```

### 5. CaviStatoChart.js
**Scopo**: Analisi cavi per stato di installazione
**Grafici inclusi**:
- Grafico a torta per distribuzione cavi per stato
- Grafico a barre per numero cavi per stato
- Confronto metri teorici vs reali
- Analisi efficienza installazione

**Dati richiesti**:
```javascript
{
  cavi_per_stato: [{
    stato: string,
    num_cavi: number,
    metri_teorici: number,
    metri_reali: number
  }]
}
```

## Librerie Utilizzate

- **Recharts**: Libreria principale per la generazione dei grafici
- **Material-UI**: Per componenti UI e styling
- **React**: Framework base

## Caratteristiche Comuni

### Colori
Tutti i componenti utilizzano una palette di colori consistente:
- Primary: #1976d2 (blu)
- Success: #2e7d32 (verde)
- Warning: #ed6c02 (arancione)
- Error: #d32f2f (rosso)
- Info: #0288d1 (azzurro)
- Secondary: #dc004e (magenta)

### Tooltip Personalizzati
Ogni grafico include tooltip personalizzati con informazioni dettagliate.

### Responsive Design
Tutti i grafici sono responsive e si adattano alle dimensioni del contenitore.

### Accessibilità
- Etichette leggibili
- Contrasti adeguati
- Supporto per screen reader

## Integrazione

I componenti sono integrati nella pagina `ReportCaviPageNew.js` e possono essere attivati/disattivati tramite un interruttore.

## Installazione Dipendenze

```bash
npm install recharts
```

## Utilizzo

```javascript
import ProgressChart from './components/charts/ProgressChart';

// Nel componente
<ProgressChart data={reportData} />
```

## Note Tecniche

- I grafici gestiscono automaticamente dati mancanti o nulli
- Le etichette vengono troncate se troppo lunghe
- I grafici a torta nascondono automaticamente le fette troppo piccole (<5%)
- Tutti i valori numerici vengono formattati con 2 decimali dove appropriato
