import React from 'react';
import { Button, Tooltip } from '@mui/material';
import { Home as HomeIcon } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

/**
 * Componente che mostra un pulsante per tornare al pannello admin
 * Visibile solo quando un amministratore sta impersonando un utente
 */
const AdminHomeButton = () => {
  const { isImpersonating } = useAuth();
  const navigate = useNavigate();

  // Se l'utente non è un amministratore che sta impersonando un utente, non mostrare il pulsante
  if (!isImpersonating) {
    return null;
  }

  // Naviga al pannello amministratore
  const handleBackToAdmin = () => {
    navigate('/dashboard/admin');
  };

  return (
    <Tooltip title="Torna al pannello amministratore">
      <Button
        variant="outlined"
        color="primary"
        startIcon={<HomeIcon />}
        onClick={handleBackToAdmin}
        size="small"
      >
        Pannello Admin
      </Button>
    </Tooltip>
  );
};

export default AdminHomeButton;
