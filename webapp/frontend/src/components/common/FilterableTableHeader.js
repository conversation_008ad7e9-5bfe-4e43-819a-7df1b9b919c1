import React from 'react';
import { Box, TableCell, TableSortLabel, Typography } from '@mui/material';
import ExcelLikeFilter from './ExcelLikeFilter';

/**
 * Componente per l'intestazione di una tabella con filtri in stile Excel
 * 
 * @param {Object} props - Proprietà del componente
 * @param {string} props.columnName - Nome della colonna
 * @param {string} props.label - Etichetta da visualizzare
 * @param {Array} props.data - Dati della tabella
 * @param {Function} props.onFilterChange - Funzione chiamata quando il filtro cambia
 * @param {string} props.dataType - Tipo di dati ('text', 'number', 'date')
 * @param {string} props.sortDirection - Direzione di ordinamento ('asc', 'desc', null)
 * @param {Function} props.onSortChange - Funzione chiamata quando l'ordinamento cambia
 * @param {boolean} props.disableFilter - Disabilita il filtro
 * @param {boolean} props.disableSort - Disabilita l'ordinamento
 */
const FilterableTableHeader = ({
  columnName,
  label,
  data,
  onFilterChange,
  dataType = 'text',
  sortDirection = null,
  onSortChange = null,
  disableFilter = false,
  disableSort = false,
  ...cellProps
}) => {
  const handleSortClick = () => {
    if (onSortChange && !disableSort) {
      const newDirection = sortDirection === 'asc' ? 'desc' : 'asc';
      onSortChange(columnName, newDirection);
    }
  };

  return (
    <TableCell {...cellProps}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        {!disableSort && onSortChange ? (
          <TableSortLabel
            active={Boolean(sortDirection)}
            direction={sortDirection || 'asc'}
            onClick={handleSortClick}
          >
            <Typography variant="subtitle2" component="span">
              {label}
            </Typography>
          </TableSortLabel>
        ) : (
          <Typography variant="subtitle2" component="span">
            {label}
          </Typography>
        )}
        
        {!disableFilter && (
          <ExcelLikeFilter
            data={data}
            columnName={columnName}
            onFilterChange={onFilterChange}
            dataType={dataType}
          />
        )}
      </Box>
    </TableCell>
  );
};

export default FilterableTableHeader;
