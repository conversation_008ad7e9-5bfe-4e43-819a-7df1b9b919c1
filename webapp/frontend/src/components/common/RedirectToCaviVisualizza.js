import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useGlobalContext } from '../../context/GlobalContext';
import { useAuth } from '../../context/AuthContext';

// Componente che reindirizza alla pagina di visualizzazione cavi
// e opzionalmente apre il dialog di modifica cavo
const RedirectToCaviVisualizza = ({ openModificaDialog = false }) => {
  const navigate = useNavigate();
  const { setOpenModificaCavoDialog } = useGlobalContext();
  const { user } = useAuth();

  // Verifica che il cantiereId sia presente nel localStorage
  const cantiereId = localStorage.getItem('selectedCantiereId');
  console.log('RedirectToCaviVisualizza - cantiereId dal localStorage:', cantiereId);

  // Se non c'è un cantiereId nel localStorage e l'utente è un utente cantiere,
  // prova a recuperare l'ID del cantiere dai dati utente
  if (!cantiereId && user?.role === 'cantieri_user' && user?.cantiere_id) {
    console.log('RedirectToCaviVisualizza - cantiereId dai dati utente:', user.cantiere_id);
    localStorage.setItem('selectedCantiereId', user.cantiere_id.toString());
    localStorage.setItem('selectedCantiereName', user.cantiere_name || `Cantiere ${user.cantiere_id}`);
  }

  useEffect(() => {
    // Prima reindirizza alla pagina di visualizzazione cavi
    navigate('/dashboard/cavi/visualizza');

    // Se richiesto, apre il dialog di modifica cavo dopo un breve ritardo
    // per assicurarsi che la navigazione sia completata
    if (openModificaDialog) {
      setTimeout(() => {
        setOpenModificaCavoDialog(true);
      }, 500);
    }
  }, [navigate, setOpenModificaCavoDialog, openModificaDialog]);

  return (
    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
      Reindirizzamento in corso...
    </div>
  );
};

export default RedirectToCaviVisualizza;
