import React from 'react';
import { Box, Typography, Chip } from '@mui/material';
import { Construction as ConstructionIcon } from '@mui/icons-material';

/**
 * Componente che mostra il cantiere selezionato
 */
const SelectedCantiereDisplay = () => {
  // Recupera l'ID e il nome del cantiere selezionato dal localStorage
  const selectedCantiereId = localStorage.getItem('selectedCantiereId');
  const selectedCantiereName = localStorage.getItem('selectedCantiereName');

  // Se non c'è un cantiere selezionato, non mostrare nulla
  if (!selectedCantiereId) {
    return null;
  }

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', mx: 2 }}>
      <Typography variant="body2" color="textSecondary" sx={{ mr: 1.5, fontSize: '1rem', fontWeight: 500 }}>
        Cantiere attivo:
      </Typography>
      <Chip
        icon={<ConstructionIcon fontSize="medium" />}
        label={selectedCantiereName || selectedCantiereId}
        color="secondary"
        variant="outlined"
        size="medium"
        sx={{
          fontWeight: 'bold',
          fontSize: '1rem',
          padding: '6px 0',
          height: '36px',
          '& .MuiChip-label': { padding: '0 14px' }
        }}
      />
    </Box>
  );
};

export default SelectedCantiereDisplay;
