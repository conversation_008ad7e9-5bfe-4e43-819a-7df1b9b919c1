import React, { createContext, useState, useContext, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import authService from '../services/authService';

// Crea il contesto di autenticazione
const AuthContext = createContext(null);

// Hook personalizzato per utilizzare il contesto di autenticazione
export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [isImpersonating, setIsImpersonating] = useState(localStorage.getItem('isImpersonating') === 'true');
  const [impersonatedUser, setImpersonatedUser] = useState(JSON.parse(localStorage.getItem('impersonatedUser') || 'null'));
  const [selectedCantiere, setSelectedCantiere] = useState(null);
  const navigate = useNavigate();

  // Carica il cantiere selezionato dal localStorage all'avvio
  useEffect(() => {
    const cantiereId = localStorage.getItem('selectedCantiereId');
    const cantiereName = localStorage.getItem('selectedCantiereName');

    if (cantiereId) {
      setSelectedCantiere({
        id_cantiere: parseInt(cantiereId, 10),
        nome: cantiereName || `Cantiere ${cantiereId}`
      });
      console.log('Cantiere caricato dal localStorage:', { id_cantiere: cantiereId, nome: cantiereName });
    }
  }, []);

  // Verifica se l'utente è già autenticato all'avvio dell'applicazione
  useEffect(() => {
    const checkAuth = async () => {
      try {
        console.log('Verificando autenticazione all\'avvio...');
        // Prima di tutto, imposta loading a true
        setLoading(true);

        // Pulisci eventuali token non validi o scaduti
        const token = localStorage.getItem('token');
        console.log('Token trovato nel localStorage:', token ? 'Sì' : 'No');

        if (token) {
          try {
            // Verifica la validità del token
            console.log('Tentativo di verifica token...');
            const userData = await authService.checkToken();
            console.log('Token valido, dati utente:', userData);
            setUser(userData);
            setIsAuthenticated(true);

            // Imposta lo stato di impersonificazione in base ai dati dell'utente
            // Questo valore viene ora impostato dal backend nel token JWT e recuperato da authService.checkToken
            const impersonatingState = userData.isImpersonated === true;
            console.log('Stato di impersonificazione recuperato dai dati utente:', impersonatingState);
            setIsImpersonating(impersonatingState);
          } catch (tokenError) {
            console.error('Errore durante la verifica del token:', tokenError);
            // Se il token non è valido, rimuovilo
            console.log('Rimozione token non valido dal localStorage');
            localStorage.removeItem('token');
            setUser(null);
            setIsAuthenticated(false);
            localStorage.removeItem('isImpersonating');
            setIsImpersonating(false);

            // Pulisci eventuali selezioni di cantiere precedenti
            localStorage.removeItem('selectedCantiereId');
            localStorage.removeItem('selectedCantiereName');
            console.log('Rimossi dati cantiere precedenti dal localStorage');
          }
        } else {
          console.log('Nessun token trovato, utente non autenticato');
          setUser(null);
          setIsAuthenticated(false);
          localStorage.removeItem('isImpersonating');
          setIsImpersonating(false);

          // Pulisci eventuali selezioni di cantiere precedenti
          localStorage.removeItem('selectedCantiereId');
          localStorage.removeItem('selectedCantiereName');
          console.log('Rimossi dati cantiere precedenti dal localStorage');
        }
      } catch (error) {
        console.error('Errore generale durante la verifica dell\'autenticazione:', error);
        // In caso di errore generale, assicurati che l'utente non sia autenticato
        localStorage.removeItem('token');
        setUser(null);
        setIsAuthenticated(false);

        // Pulisci eventuali selezioni di cantiere precedenti
        localStorage.removeItem('selectedCantiereId');
        localStorage.removeItem('selectedCantiereName');
        console.log('Rimossi dati cantiere precedenti dal localStorage');
      } finally {
        // Assicurati che loading sia impostato a false alla fine
        console.log('Completata verifica autenticazione, loading:', false);
        setTimeout(() => {
          setLoading(false);
        }, 500); // Aggiungi un piccolo ritardo per assicurarsi che lo stato sia aggiornato
      }
    };

    // Esegui la verifica dell'autenticazione
    checkAuth();
  }, []);

  // Funzione di login
  const login = async (credentials, loginType) => {
    try {
      console.log(`Tentativo di login come ${loginType}`, credentials);
      const response = await authService.login(credentials, loginType);
      console.log('Risposta login ricevuta:', response);

      const { access_token, user_id, username, role, cantiere_id, cantiere_name } = response;

      // Pulisci eventuali selezioni di cantiere precedenti
      // Questo risolve il problema del cantiere che rimane in memoria tra sessioni diverse
      localStorage.removeItem('selectedCantiereId');
      localStorage.removeItem('selectedCantiereName');
      console.log('Rimossi dati cantiere precedenti dal localStorage');

      // Se è un login cantiere, salva l'ID e il nome del cantiere nel localStorage
      if (loginType === 'cantiere' && cantiere_id) {
        console.log('Login cantiere: salvando ID cantiere nel localStorage:', cantiere_id);
        localStorage.setItem('selectedCantiereId', cantiere_id.toString());
        localStorage.setItem('selectedCantiereName', cantiere_name || `Cantiere ${cantiere_id}`);
      }

      // Salva il token nel localStorage
      console.log('Salvataggio token nel localStorage');
      localStorage.setItem('token', access_token);

      // Imposta i dati dell'utente
      const userData = { id: user_id, username, role };
      console.log('Impostazione dati utente:', userData);
      setUser(userData);
      setIsAuthenticated(true);

      // Se è un utente cantiere, salva l'ID e il nome del cantiere nel localStorage
      if (role === 'cantieri_user' && cantiere_id) {
        console.log('Utente cantiere, salvataggio dati cantiere:', { cantiere_id, cantiere_name });
        localStorage.setItem('selectedCantiereId', cantiere_id.toString());
        localStorage.setItem('selectedCantiereName', cantiere_name || `Cantiere ${cantiere_id}`);
      }

      // Resetta lo stato di impersonificazione a false quando si effettua un login normale
      if (isImpersonating) {
        console.log('Reset dello stato di impersonificazione durante login normale');
        localStorage.removeItem('isImpersonating');
        setIsImpersonating(false);
      }

      return userData;
    } catch (error) {
      console.error('Errore durante il login:', error);
      throw error;
    }
  };

  // Funzione di logout
  const logout = () => {
    // Se l'utente corrente è un amministratore che ha effettuato "login as user",
    // reindirizza alla dashboard amministratore invece che alla pagina di login
    if (isImpersonating) {
      console.log('Logout da utente impersonato, ritorno al menu amministratore');

      // Rimuovi i dati dell'utente impersonato
      localStorage.removeItem('impersonatedUser');
      localStorage.removeItem('isImpersonating');
      setImpersonatedUser(null);
      setIsImpersonating(false);

      // Reindirizza alla dashboard amministratore
      navigate('/dashboard/admin');
    } else {
      // Logout normale
      console.log('Logout normale, ritorno alla pagina di login');
      localStorage.removeItem('token');
      localStorage.removeItem('isImpersonating');
      localStorage.removeItem('impersonatedUser');
      localStorage.removeItem('selectedCantiereId');
      localStorage.removeItem('selectedCantiereName');
      setUser(null);
      setImpersonatedUser(null);
      setIsAuthenticated(false);
      navigate('/login');
    }
  };

  // Funzione per impostare il token
  const setToken = (token) => {
    localStorage.setItem('token', token);
  };

  // Funzione per impersonare un utente
  const impersonateUser = async (userId) => {
    try {
      // Chiama l'endpoint di impersonificazione
      const response = await authService.impersonateUser(userId);

      // Salva il token nel localStorage
      localStorage.setItem('token', response.access_token);

      // Salva i dati dell'utente impersonato
      const impersonatedUserData = {
        id: response.impersonated_id,
        username: response.impersonated_username,
        role: response.impersonated_role
      };

      // Salva i dati dell'utente impersonato nel localStorage
      localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData));
      setImpersonatedUser(impersonatedUserData);

      // Imposta lo stato di impersonificazione a true
      setIsImpersonating(true);
      localStorage.setItem('isImpersonating', 'true');

      // L'utente rimane l'amministratore, ma ora ha informazioni sull'utente impersonato
      // Non modifichiamo lo stato dell'utente perché rimane l'amministratore

      console.log('Impersonificazione attivata, logout riporterà al menu amministratore');
      console.log('Utente impersonato:', impersonatedUserData.username, 'Ruolo:', impersonatedUserData.role);

      return {
        ...user,  // Mantiene i dati dell'amministratore
        isImpersonated: true,
        impersonatedUser: impersonatedUserData  // Aggiunge i dati dell'utente impersonato
      };
    } catch (error) {
      console.error('Errore durante l\'impersonificazione:', error);
      throw error;
    }
  };

  // Funzione per selezionare un cantiere
  const selectCantiere = (cantiere) => {
    if (cantiere && cantiere.id_cantiere) {
      // Salva nel localStorage
      localStorage.setItem('selectedCantiereId', cantiere.id_cantiere.toString());
      localStorage.setItem('selectedCantiereName', cantiere.nome || `Cantiere ${cantiere.id_cantiere}`);

      // Aggiorna lo stato
      setSelectedCantiere(cantiere);
      console.log('Cantiere selezionato:', cantiere);
    }
  };

  // Valore del contesto
  const value = {
    user,
    setUser,
    isAuthenticated,
    loading,
    login,
    logout,
    setToken,
    impersonateUser,
    isImpersonating,
    impersonatedUser,
    selectedCantiere,
    selectCantiere
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
