import React, { createContext, useState, useContext } from 'react';

// Crea il contesto globale
const GlobalContext = createContext(null);

// Hook personalizzato per utilizzare il contesto globale
export const useGlobalContext = () => useContext(GlobalContext);

export const GlobalProvider = ({ children }) => {
  // Stato per il dialogo di eliminazione cavi
  const [openEliminaCavoDialog, setOpenEliminaCavoDialog] = useState(false);

  // Stato per il dialogo di modifica cavi
  const [openModificaCavoDialog, setOpenModificaCavoDialog] = useState(false);

  // Stato per il dialogo di aggiunta cavi
  const [openAggiungiCavoDialog, setOpenAggiungiCavoDialog] = useState(false);

  // Valore del contesto
  const value = {
    openEliminaCavoDialog,
    setOpenEliminaCavoDialog,
    openModificaCavoDialog,
    setOpenModificaCavoDialog,
    openAggiungiCavoDialog,
    setOpenAggiungiCavoDialog
  };

  return <GlobalContext.Provider value={value}>{children}</GlobalContext.Provider>;
};
