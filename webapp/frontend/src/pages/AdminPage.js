import React, { useState } from 'react';
import {
  Box,
  Tabs,
  Tab,
  Typography,
  Paper,
  Alert,
  Snackbar
} from '@mui/material';
import {
  People as PeopleIcon,
  Storage as StorageIcon,
  Add as AddIcon,
  Login as LoginIcon,
  DeleteForever as DeleteForeverIcon
} from '@mui/icons-material';

import UsersList from '../components/admin/UsersList';
import UserForm from '../components/admin/UserForm';
import DatabaseView from '../components/admin/DatabaseView';
import ImpersonateUser from '../components/admin/ImpersonateUser';
import ResetDatabase from '../components/admin/ResetDatabase';
import { useAuth } from '../context/AuthContext';

// Componente per il pannello delle tab
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`admin-tabpanel-${index}`}
      aria-labelledby={`admin-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const AdminPage = () => {
  const { user } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  // State for showing/hiding user form - currently managed by tab selection
  const [selectedUser, setSelectedUser] = useState(null);
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });

  // Verifica se l'utente è un amministratore
  if (user?.role !== 'owner') {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Non hai i permessi per accedere a questa pagina.
        </Alert>
      </Box>
    );
  }

  // Gestione del cambio di tab
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    // Resetta il form quando si cambia tab
    setSelectedUser(null);
  };

  // Gestione dell'apertura del form per la creazione di un nuovo utente
  // Handled directly by tab selection

  // Gestione dell'apertura del form per la modifica di un utente esistente
  const handleEditUser = (user) => {
    setSelectedUser(user);
    // Imposta la tab su 5 (modifica utente)
    setTabValue(5);
  };

  // Gestione del salvataggio di un utente
  const handleSaveUser = (user) => {
    // Form is closed after save
    setSelectedUser(null);

    // Mostra notifica di successo
    setNotification({
      open: true,
      message: `Utente ${user.username} ${selectedUser ? 'aggiornato' : 'creato'} con successo`,
      severity: 'success'
    });

    // Torna alla tab "Visualizza Utenti" dopo il salvataggio
    setTabValue(0);
  };

  // Gestione della chiusura del form
  const handleCancelForm = () => {
    // Form is closed after cancel
    setSelectedUser(null);

    // Torna alla tab "Visualizza Utenti" dopo l'annullamento
    setTabValue(0);
  };

  // Gestione della chiusura della notifica
  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Paper sx={{ width: '100%', mb: 2 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
        >
          <Tab icon={<PeopleIcon />} label="Visualizza Utenti" />
          <Tab icon={<AddIcon />} label="Crea Nuovo Utente" />
          <Tab icon={<LoginIcon />} label="Accedi come Utente" />
          <Tab icon={<StorageIcon />} label="Visualizza Database Raw" />
          <Tab icon={<DeleteForeverIcon />} label="Reset Database" />
        </Tabs>
      </Paper>

      {/* Tab Visualizza Utenti */}
      <TabPanel value={tabValue} index={0}>
        <Typography variant="h5" gutterBottom>
          Visualizza Utenti
        </Typography>
        <Typography variant="body1" gutterBottom>
          Questa sezione mostra la lista di tutti gli utenti del sistema.
        </Typography>
        <UsersList onEditUser={handleEditUser} />
      </TabPanel>

      {/* Tab Crea Nuovo Utente */}
      <TabPanel value={tabValue} index={1}>
        <Typography variant="h5" gutterBottom>
          Crea Nuovo Utente Standard
        </Typography>
        <Typography variant="body1" gutterBottom>
          Da qui puoi creare un nuovo utente standard nel sistema.
        </Typography>
        <UserForm
          user={null}
          onSave={handleSaveUser}
          onCancel={handleCancelForm}
        />
      </TabPanel>

      {/* Tab Modifica Utente - Visibile solo quando un utente è selezionato */}
      {selectedUser && (
        <TabPanel value={5} index={5}>
          <Typography variant="h5" gutterBottom>
            Modifica Utente: {selectedUser.username}
          </Typography>
          <Typography variant="body1" gutterBottom>
            Da qui puoi modificare i dati dell'utente selezionato.
          </Typography>
          <UserForm
            user={selectedUser}
            onSave={handleSaveUser}
            onCancel={handleCancelForm}
          />
        </TabPanel>
      )}

      {/* Tab Accedi come Utente */}
      <TabPanel value={tabValue} index={2}>
        <Typography variant="h5" gutterBottom>
          Accedi come Utente
        </Typography>
        <Typography variant="body1" gutterBottom>
          Da qui puoi accedere al sistema impersonando un altro utente.
        </Typography>
        <ImpersonateUser />
      </TabPanel>

      {/* Tab Visualizza Database Raw */}
      <TabPanel value={tabValue} index={3}>
        <Typography variant="h5" gutterBottom>
          Visualizzazione Database Raw
        </Typography>
        <Typography variant="body1" gutterBottom>
          Questa sezione mostra una visualizzazione raw del database. Puoi vedere i dati delle tabelle principali.
        </Typography>
        <DatabaseView />
      </TabPanel>

      {/* Tab Reset Database */}
      <TabPanel value={tabValue} index={4}>
        <Typography variant="h5" gutterBottom>
          Reset Database
        </Typography>
        <Typography variant="body1" gutterBottom>
          Da qui puoi resettare completamente il database, eliminando tutti i dati.
        </Typography>
        <ResetDatabase />
      </TabPanel>

      {/* Notifica */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default AdminPage;
