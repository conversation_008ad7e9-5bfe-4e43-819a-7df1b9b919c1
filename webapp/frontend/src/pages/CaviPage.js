import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const CaviPage = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  // Reindirizza alla pagina di visualizzazione cavi
  useEffect(() => {
    console.log('CaviPage - Inizializzazione');

    // Verifica se l'utente è un utente cantiere
    const isCantieresUser = user?.role === 'cantieri_user';
    console.log('Utente cantiere:', isCantieresUser);

    // Verifica se c'è un cantiere selezionato nel localStorage
    const selectedCantiereId = localStorage.getItem('selectedCantiereId');
    const selectedCantiereName = localStorage.getItem('selectedCantiereName');
    console.log('Cantiere selezionato:', { selectedCantiereId, selectedCantiereName });

    // Se l'utente è un utente cantiere ma non c'è un cantiere selezionato,
    // potrebbe essere necessario recuperare l'ID del cantiere dal token
    if (isCantieresUser && !selectedCantiereId && user?.id) {
      console.log('Utente cantiere senza cantiere selezionato, tentativo di recupero dal token');
      // Qui potremmo fare una chiamata API per ottenere i dettagli del cantiere
      // Per ora, reindirizza comunque alla pagina di visualizzazione cavi
    }

    // Recupera il tab index dal localStorage
    const savedTabIndex = localStorage.getItem('caviTabIndex');
    const tabIndex = savedTabIndex ? parseInt(savedTabIndex, 10) : 0;
    console.log('Tab index:', tabIndex);

    // Reindirizza alla pagina appropriata in base al tab index
    switch (tabIndex) {
      case 0:
        console.log('Reindirizzamento a /dashboard/cavi/visualizza');
        navigate('/dashboard/cavi/visualizza');
        break;
      case 1:
        navigate('/dashboard/cavi/posa');
        break;
      case 2:
        navigate('/dashboard/cavi/parco');
        break;
      case 3:
        navigate('/dashboard/cavi/excel');
        break;
      case 4:
        navigate('/dashboard/cavi/report');
        break;
      case 5:
        navigate('/dashboard/cavi/certificazione');
        break;
      case 6:
        navigate('/dashboard/cavi/comande');
        break;
      default:
        console.log('Tab index non valido, reindirizzamento a /dashboard/cavi/visualizza');
        navigate('/dashboard/cavi/visualizza');
        break;
    }
  }, [navigate, user]);

  // Rendering di un componente vuoto, poiché il reindirizzamento avviene nell'useEffect
  return null;
};

export default CaviPage;
