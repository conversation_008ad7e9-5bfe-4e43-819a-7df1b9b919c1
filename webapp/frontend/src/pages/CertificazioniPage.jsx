import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Button,
  TextField,
  Paper,
  Alert,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import { Add as AddIcon, Assignment as AssignmentIcon, Build as BuildIcon } from '@mui/icons-material';

import { useAuth } from '../context/AuthContext';
import { apiService } from '../services/apiService';
import CertificazioniList from '../components/certificazioni/CertificazioniList';
import CertificazioneForm from '../components/certificazioni/CertificazioneForm';
import StrumentiList from '../components/certificazioni/StrumentiList';
import StrumentoForm from '../components/certificazioni/StrumentoForm';

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`certificazioni-tabpanel-${index}`}
      aria-labelledby={`certificazioni-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function CertificazioniPage() {
  const { cantiereId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();

  const [tabValue, setTabValue] = useState(0);
  const [cantiere, setCantiere] = useState(null);
  const [certificazioni, setCertificazioni] = useState([]);
  const [strumenti, setStrumenti] = useState([]);
  const [loading, setLoading] = useState(true);
  const [formLoading, setFormLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Stati per i form
  const [showCertificazioneForm, setShowCertificazioneForm] = useState(false);
  const [showStrumentoForm, setShowStrumentoForm] = useState(false);
  const [editingCertificazione, setEditingCertificazione] = useState(null);
  const [editingStrumento, setEditingStrumento] = useState(null);

  // Filtro per le certificazioni
  const [filtroCavo, setFiltroCavo] = useState('');

  useEffect(() => {
    loadData();
  }, [cantiereId]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError('');

      // Carica informazioni del cantiere
      const cantiereData = await apiService.getCantiere(cantiereId);
      setCantiere(cantiereData);

      // Carica certificazioni e strumenti in parallelo
      const [certificazioniData, strumentiData] = await Promise.all([
        apiService.getCertificazioni(cantiereId, filtroCavo),
        apiService.getStrumenti(cantiereId)
      ]);

      setCertificazioni(certificazioniData);
      setStrumenti(strumentiData);
    } catch (err) {
      console.error('Errore nel caricamento dei dati:', err);
      setError('Errore nel caricamento dei dati: ' + (err.response?.data?.detail || err.message));
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    // Reset form states when changing tab
    setShowCertificazioneForm(false);
    setShowStrumentoForm(false);
    setEditingCertificazione(null);
    setEditingStrumento(null);
  };

  const handleFiltroCavoChange = (event) => {
    setFiltroCavo(event.target.value);
  };

  const handleFiltroCavoSubmit = () => {
    loadData();
  };

  const handleCreateCertificazione = () => {
    setEditingCertificazione(null);
    setShowCertificazioneForm(true);
  };

  const handleEditCertificazione = (certificazione) => {
    setEditingCertificazione(certificazione);
    setShowCertificazioneForm(true);
  };

  const handleCreateStrumento = () => {
    setEditingStrumento(null);
    setShowStrumentoForm(true);
  };

  const handleEditStrumento = (strumento) => {
    setEditingStrumento(strumento);
    setShowStrumentoForm(true);
  };

  const handleCertificazioneSuccess = (message) => {
    setSuccess(message);
    setShowCertificazioneForm(false);
    setEditingCertificazione(null);
    loadData();
    setTimeout(() => setSuccess(''), 3000);
  };

  const handleStrumentoSuccess = (message) => {
    setSuccess(message);
    setShowStrumentoForm(false);
    setEditingStrumento(null);
    setFormLoading(false);
    loadData();
    setTimeout(() => setSuccess(''), 3000);
  };

  const handleStrumentoSubmit = async () => {
    setFormLoading(true);
    // The actual submission is handled by the form's onSubmit handler
  };

  const handleFormCancel = () => {
    setShowCertificazioneForm(false);
    setShowStrumentoForm(false);
    setEditingCertificazione(null);
    setEditingStrumento(null);
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Typography>Caricamento...</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Certificazioni Cavi
        </Typography>
        {cantiere && (
          <Typography variant="h6" color="text.secondary">
            {cantiere.nome} - {cantiere.codice_univoco}
          </Typography>
        )}
      </Box>

      {/* Messaggi di stato */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}
      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
        >
          <Tab
            icon={<AssignmentIcon />}
            label="Certificazioni"
            id="certificazioni-tab-0"
            aria-controls="certificazioni-tabpanel-0"
          />
          <Tab
            icon={<BuildIcon />}
            label="Strumenti"
            id="certificazioni-tab-1"
            aria-controls="certificazioni-tabpanel-1"
          />
        </Tabs>
      </Paper>

      {/* Tab Panel Certificazioni */}
      <TabPanel value={tabValue} index={0}>
        {!showCertificazioneForm ? (
          <>
            {/* Controlli per le certificazioni */}
            <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
              <TextField
                label="Filtra per ID Cavo"
                value={filtroCavo}
                onChange={handleFiltroCavoChange}
                onKeyPress={(e) => e.key === 'Enter' && handleFiltroCavoSubmit()}
                size="small"
                sx={{ minWidth: 200 }}
              />
              <Button
                variant="outlined"
                onClick={handleFiltroCavoSubmit}
              >
                Filtra
              </Button>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleCreateCertificazione}
                sx={{ ml: 'auto' }}
              >
                Nuova Certificazione
              </Button>
            </Box>

            {/* Lista certificazioni */}
            <CertificazioniList
              certificazioni={certificazioni}
              onEdit={handleEditCertificazione}
              onDelete={loadData}
              cantiereId={cantiereId}
            />
          </>
        ) : (
          <CertificazioneForm
            cantiereId={cantiereId}
            certificazione={editingCertificazione}
            strumenti={strumenti}
            onSuccess={handleCertificazioneSuccess}
            onCancel={handleFormCancel}
          />
        )}
      </TabPanel>

      {/* Tab Panel Strumenti */}
      <TabPanel value={tabValue} index={1}>
        {/* Controlli per gli strumenti */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateStrumento}
          >
            Nuovo Strumento
          </Button>
        </Box>

        {/* Lista strumenti */}
        <StrumentiList
          strumenti={strumenti}
          onEdit={handleEditStrumento}
          onDelete={loadData}
          cantiereId={cantiereId}
        />

        {/* Dialog per il form degli strumenti */}
        <Dialog 
          open={showStrumentoForm} 
          onClose={handleFormCancel}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: { p: 0 }
          }}
        >
          <DialogTitle>
            {editingStrumento ? 'Modifica Strumento' : 'Nuovo Strumento'}
          </DialogTitle>
          <DialogContent>
            <StrumentoForm
              cantiereId={cantiereId}
              strumento={editingStrumento}
              onSuccess={handleStrumentoSuccess}
              onCancel={handleFormCancel}
            />
          </DialogContent>
        </Dialog>
      </TabPanel>
    </Container>
  );
}

export default CertificazioniPage;
