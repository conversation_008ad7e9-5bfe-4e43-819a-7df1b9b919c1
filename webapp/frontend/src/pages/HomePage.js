import React, { useEffect } from 'react';
import { Box, Typography, Grid, Card, CardContent, CardActionArea, Avatar, CircularProgress } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import {
  AdminPanelSettings as AdminIcon,
  Construction as ConstructionIcon,
  Cable as CableIcon,
  Description as ReportIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';

const HomePage = () => {
  const { user, isImpersonating } = useAuth();
  const navigate = useNavigate();

  // Reindirizza automaticamente in base al tipo di utente
  useEffect(() => {
    // Breve timeout per evitare reindirizzamenti troppo rapidi
    const redirectTimer = setTimeout(() => {
      // Se l'utente è un amministratore che sta impersonando un utente
      if (isImpersonating) {
        navigate('/dashboard/admin');
      }
      // Se l'utente è un amministratore normale
      else if (user?.role === 'owner') {
        navigate('/dashboard/admin');
      }
      // Se l'utente è un utente standard
      else if (user?.role === 'user') {
        navigate('/dashboard/cantieri');
      }
      // Se l'utente è un utente cantiere
      else if (user?.role === 'cantieri_user') {
        // Reindirizza direttamente alla pagina di visualizzazione cavi
        navigate('/dashboard/cavi/visualizza');
      }
    }, 300);

    return () => clearTimeout(redirectTimer);
  }, [user, isImpersonating, navigate]);

  // Naviga a un percorso
  const navigateTo = (path) => {
    navigate(path);
  };

  // Mostra un indicatore di caricamento durante il reindirizzamento
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', minHeight: '50vh' }}>
      <Typography variant="h4" gutterBottom>
        Benvenuto nel Sistema di Gestione Cantieri
      </Typography>

      <Typography variant="body1" paragraph>
        Reindirizzamento in corso...
      </Typography>

      <CircularProgress sx={{ mt: 3 }} />
    </Box>
  );
};

export default HomePage;
