import React from 'react';
import { Container, Typography, Box } from '@mui/material';
import TestBobineComponent from '../components/cavi/TestBobineComponent';
import { useParams } from 'react-router-dom';

/**
 * Pagina di test per visualizzare tutte le bobine e i cavi disponibili
 */
const TestBobinePage = () => {
  const { cantiereId } = useParams();
  
  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Test Bobine e Cavi - Cantiere {cantiereId}
        </Typography>
        
        <TestBobineComponent cantiereId={cantiereId} />
      </Box>
    </Container>
  );
};

export default TestBobinePage;
