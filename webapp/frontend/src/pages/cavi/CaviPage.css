/* Stile per le pagine di gestione cavi */
.cavi-page {
  padding: 20px;
}

.cavi-page .MuiTypography-h4 {
  font-size: 1.8rem;
  font-weight: 500;
  margin-bottom: 16px;
}

.cavi-page .MuiButton-root {
  text-transform: none;
  font-size: 1rem;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
}

.cavi-page .MuiButton-contained {
  background-color: #f8f9fa;
  color: #212529;
  border: 1px solid #dee2e6;
  box-shadow: none;
}

.cavi-page .MuiButton-contained:hover {
  background-color: #e9ecef;
  box-shadow: none;
}

.cavi-page .MuiIconButton-root {
  color: #6c757d;
}

.cavi-page .MuiIconButton-root:hover {
  background-color: #e9ecef;
}

.cavi-page .MuiPaper-root {
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cavi-page .MuiTabs-root {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.cavi-page .MuiTab-root {
  text-transform: none;
  font-size: 1rem;
  font-weight: 500;
  padding: 12px 16px;
}

.cavi-page .MuiTab-root.Mui-selected {
  color: #212529;
  font-weight: 600;
}

.cavi-page .MuiCard-root {
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.cavi-page .MuiCard-root:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.cavi-page .MuiCardContent-root {
  padding: 16px;
}

.cavi-page .MuiCardActions-root {
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
}

.cavi-page .cavo-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.cavi-page .cavo-header .MuiSvgIcon-root {
  font-size: 1.5rem;
  margin-right: 8px;
  color: #6c757d;
}

.cavi-page .cavo-header .MuiTypography-h6 {
  font-size: 1.2rem;
  font-weight: 500;
}

.cavi-page .MuiTableContainer-root {
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.cavi-page .MuiTable-root {
  border-collapse: separate;
  border-spacing: 0;
}

.cavi-page .MuiTableHead-root {
  background-color: #f8f9fa;
}

.cavi-page .MuiTableCell-head {
  font-weight: 600;
  color: #212529;
  padding: 12px 16px;
  border-bottom: 2px solid #dee2e6;
}

.cavi-page .MuiTableCell-body {
  padding: 12px 16px;
  border-bottom: 1px solid #dee2e6;
}

.cavi-page .MuiTableRow-root:hover {
  background-color: #f8f9fa;
}

.cavi-page .MuiAlert-root {
  border-radius: 4px;
  margin-bottom: 16px;
}

.cavi-page .MuiCircularProgress-root {
  color: #6c757d;
}
