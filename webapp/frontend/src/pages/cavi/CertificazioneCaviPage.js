import React, { useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  IconButton,
  Alert
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Refresh as RefreshIcon,
  Home as HomeIcon
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import AdminHomeButton from '../../components/common/AdminHomeButton';
import CertificazioneCavi from '../../components/cavi/CertificazioneCavi';

const CertificazioneCaviPage = () => {
  const { isImpersonating } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const certificazioneRef = useRef();

  // Recupera l'ID del cantiere dal localStorage
  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);
  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;

  // Effetto per gestire le diverse route
  useEffect(() => {
    if (certificazioneRef.current && location.pathname) {
      const path = location.pathname;

      // Mappa le route alle opzioni del componente CertificazioneCavi
      if (path.includes('/visualizza')) {
        certificazioneRef.current.handleOptionSelect('visualizzaCertificazioni');
      } else if (path.includes('/filtra')) {
        certificazioneRef.current.handleOptionSelect('filtraCertificazioni');
      } else if (path.includes('/crea')) {
        certificazioneRef.current.handleOptionSelect('creaCertificazione');
      } else if (path.includes('/dettagli')) {
        certificazioneRef.current.handleOptionSelect('dettagliCertificazione');
      } else if (path.includes('/pdf')) {
        certificazioneRef.current.handleOptionSelect('generaPdf');
      } else if (path.includes('/elimina')) {
        certificazioneRef.current.handleOptionSelect('eliminaCertificazione');
      } else if (path.includes('/strumenti')) {
        certificazioneRef.current.handleOptionSelect('gestioneStrumenti');
      } else {
        // Route base - mostra visualizza certificazioni
        certificazioneRef.current.handleOptionSelect('visualizzaCertificazioni');
      }
    }
  }, [location.pathname]);

  // Torna alla lista dei cantieri
  const handleBackToCantieri = () => {
    navigate('/dashboard/cantieri');
  };



  // Gestisce le notifiche
  const handleSuccess = (message) => {
    // Qui puoi implementare una notifica di successo se necessario
    console.log('Successo:', message);
  };

  const handleError = (message) => {
    // Qui puoi implementare una notifica di errore se necessario
    console.error('Errore:', message);
  };

  if (!cantiereId || isNaN(cantiereId)) {
    return (
      <Box>
        <Alert severity="error" sx={{ mb: 2 }}>
          Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.
        </Alert>
        <Button
          variant="contained"
          startIcon={<ArrowBackIcon />}
          onClick={handleBackToCantieri}
        >
          Torna ai Cantieri
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h5">
            Certificazione Cavi
          </Typography>
          <IconButton
            onClick={() => window.location.reload()}
            sx={{ ml: 2 }}
            color="primary"
            title="Ricarica la pagina"
          >
            <RefreshIcon />
          </IconButton>
        </Box>
        <AdminHomeButton />
      </Box>



      <CertificazioneCavi
        ref={certificazioneRef}
        cantiereId={cantiereId}
        onSuccess={handleSuccess}
        onError={handleError}
      />
    </Box>
  );
};

export default CertificazioneCaviPage;
