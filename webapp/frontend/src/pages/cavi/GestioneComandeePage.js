import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  IconButton,
  Alert
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Refresh as RefreshIcon,
  Home as HomeIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import AdminHomeButton from '../../components/common/AdminHomeButton';
import GestioneComande from '../../components/cavi/GestioneComande';

const GestioneComandeePage = () => {
  const { isImpersonating } = useAuth();
  const navigate = useNavigate();

  // Recupera l'ID del cantiere dal localStorage
  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);
  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;

  // Torna alla lista dei cantieri
  const handleBackToCantieri = () => {
    navigate('/dashboard/cantieri');
  };

  

  // Gestisce le notifiche
  const handleSuccess = (message) => {
    // Qui puoi implementare una notifica di successo se necessario
    console.log('Successo:', message);
  };

  const handleError = (message) => {
    // Qui puoi implementare una notifica di errore se necessario
    console.error('Errore:', message);
  };

  if (!cantiereId || isNaN(cantiereId)) {
    return (
      <Box>
        <Alert severity="error" sx={{ mb: 2 }}>
          Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.
        </Alert>
        <Button
          variant="contained"
          startIcon={<ArrowBackIcon />}
          onClick={handleBackToCantieri}
        >
          Torna ai Cantieri
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h4">
            Gestione Comande
          </Typography>
          <IconButton
            onClick={() => window.location.reload()}
            sx={{ ml: 2 }}
            color="primary"
            title="Ricarica la pagina"
          >
            <RefreshIcon />
          </IconButton>
        </Box>
        <AdminHomeButton />
      </Box>

      <Paper sx={{ mb: 3, p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            Cantiere: {cantiereName} (ID: {cantiereId})
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<ArrowBackIcon />}
            onClick={handleBackToCantieri}
          >
            Torna ai Cantieri
          </Button>
        </Box>
      </Paper>

      <GestioneComande
        cantiereId={cantiereId}
        onSuccess={handleSuccess}
        onError={handleError}
      />
    </Box>
  );
};

export default GestioneComandeePage;
