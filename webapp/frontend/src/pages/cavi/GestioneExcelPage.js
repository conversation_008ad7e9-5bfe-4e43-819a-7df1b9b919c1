import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  IconButton,
  Alert,
  Snackbar
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Refresh as RefreshIcon,
  Home as HomeIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import AdminHomeButton from '../../components/common/AdminHomeButton';
import GestioneExcel from '../../components/cavi/GestioneExcel';

const GestioneExcelPage = () => {
  const { isImpersonating } = useAuth();
  const navigate = useNavigate();
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Recupera l'ID del cantiere dal localStorage
  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);
  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;

  // Torna alla lista dei cantieri
  const handleBackToCantieri = () => {
    navigate('/dashboard/cantieri');
  };

  // Gestisce la chiusura dello snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Gestisce le notifiche
  const handleSuccess = (message) => {
    setSnackbar({
      open: true,
      message,
      severity: 'success'
    });
    console.log('Successo:', message);
  };

  const handleError = (message) => {
    setSnackbar({
      open: true,
      message,
      severity: 'error'
    });
    console.error('Errore:', message);
  };

  if (!cantiereId || isNaN(cantiereId)) {
    return (
      <Box>
        <Alert severity="error" sx={{ mb: 2 }}>
          Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.
        </Alert>
        <Button
          variant="contained"
          startIcon={<ArrowBackIcon />}
          onClick={handleBackToCantieri}
        >
          Torna ai Cantieri
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h4">
            Gestione Excel
          </Typography>
          <IconButton
            onClick={() => window.location.reload()}
            sx={{ ml: 2 }}
            color="primary"
            title="Ricarica la pagina"
          >
            <RefreshIcon />
          </IconButton>
        </Box>
        <AdminHomeButton />
      </Box>

      <Paper sx={{ mb: 3, p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            Cantiere: {cantiereName} (ID: {cantiereId})
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<ArrowBackIcon />}
            onClick={handleBackToCantieri}
          >
            Torna ai Cantieri
          </Button>
        </Box>
      </Paper>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Importazione ed Esportazione Excel
        </Typography>
        <Typography variant="body2" paragraph>
          Questa pagina consente di importare ed esportare dati in formato Excel. 
          È possibile importare cavi e bobine da file Excel, creare template per l'importazione, 
          ed esportare i dati esistenti in formato Excel.
        </Typography>
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            <strong>Nota:</strong> Per l'importazione di cavi, assicurati che il file Excel sia nel formato corretto. 
            Puoi scaricare un template vuoto utilizzando l'opzione "Template Cavi".
          </Typography>
        </Alert>
      </Paper>

      <GestioneExcel
        cantiereId={cantiereId}
        onSuccess={handleSuccess}
        onError={handleError}
      />

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default GestioneExcelPage;
