import React, { useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  IconButton,
  Alert,
  Card,
  CardContent,
  CardActions,
  Grid,
  Divider
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Refresh as RefreshIcon,
  Home as HomeIcon,
  ViewList as ViewListIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  History as HistoryIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import AdminHomeButton from '../../components/common/AdminHomeButton';
import ParcoCavi from '../../components/cavi/ParcoCavi';

const ParcoCaviPage = () => {
  const { isImpersonating } = useAuth();
  const navigate = useNavigate();

  // Recupera l'ID del cantiere dal localStorage
  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);
  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;

  // Reindirizza automaticamente alla pagina di visualizzazione bobine
  useEffect(() => {
    navigate('/dashboard/cavi/parco/visualizza');
  }, [navigate]);

  // Torna alla lista dei cantieri
  const handleBackToCantieri = () => {
    navigate('/dashboard/cavi');
  };



  // Gestisce le notifiche
  const handleSuccess = (message) => {
    // Qui puoi implementare una notifica di successo se necessario
    console.log('Successo:', message);
  };

  const handleError = (message) => {
    // Qui puoi implementare una notifica di errore se necessario
    console.error('Errore:', message);
  };

  if (!cantiereId || isNaN(cantiereId)) {
    return (
      <Box>
        <Alert severity="error" sx={{ mb: 2 }}>
          Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.
        </Alert>
        <Button
          variant="contained"
          startIcon={<ArrowBackIcon />}
          onClick={handleBackToCantieri}
        >
          Torna ai Cantieri
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h4">
            Parco Cavi
          </Typography>
          <IconButton
            onClick={() => window.location.reload()}
            sx={{ ml: 2 }}
            color="primary"
            title="Ricarica la pagina"
          >
            <RefreshIcon />
          </IconButton>
        </Box>
        <AdminHomeButton />
      </Box>

      <Paper sx={{ mb: 3, p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            Cantiere: {cantiereName} (ID: {cantiereId})
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<ArrowBackIcon />}
            onClick={handleBackToCantieri}
          >
            Torna al Menu Cavi
          </Button>
        </Box>
      </Paper>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Parco Cavi - Gestione Bobine
        </Typography>
        <Typography variant="body1">
          Seleziona un'opzione dal menu principale nella barra di navigazione in alto per gestire le bobine del parco cavi.
        </Typography>
        <Box sx={{ mt: 2 }}>
          <Typography variant="body2" color="textSecondary">
            Opzioni disponibili:
          </Typography>
          <ul>
            <li>Visualizza Bobine Disponibili</li>
            <li>Crea Nuova Bobina</li>
            <li>Modifica Bobina</li>
            <li>Elimina Bobina</li>
            <li>Visualizza Storico Utilizzo</li>
          </ul>
        </Box>
      </Paper>
    </Box>
  );
};

export default ParcoCaviPage;
