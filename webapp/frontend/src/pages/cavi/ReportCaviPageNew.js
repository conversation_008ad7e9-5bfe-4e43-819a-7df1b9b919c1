import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Alert,
  CircularProgress,
  Divider,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Assessment as AssessmentIcon,
  BarChart as BarChartIcon,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Timeline as TimelineIcon,
  List as ListIcon,
  Download as DownloadIcon,
  Visibility as VisibilityIcon,
  Refresh as RefreshIcon,
  ArrowBack as ArrowBackIcon,
  DateRange as DateRangeIcon,
  Cable as CableIcon,
  Inventory as InventoryIcon,
  ExpandMore as ExpandMoreIcon,
  ShowChart as ShowChartIcon
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import AdminHomeButton from '../../components/common/AdminHomeButton';
import reportService from '../../services/reportService';
import FilterableTable from '../../components/common/FilterableTable';

// Import dei componenti grafici
import ProgressChart from '../../components/charts/ProgressChart';
import BobineChart from '../../components/charts/BobineChart';
import BoqChart from '../../components/charts/BoqChart';
import TimelineChart from '../../components/charts/TimelineChart';
import CaviStatoChart from '../../components/charts/CaviStatoChart';

const ReportCaviPageNew = () => {
  const navigate = useNavigate();
  const { cantiereId } = useParams();
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [reportData, setReportData] = useState(null);
  const [selectedReport, setSelectedReport] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [selectedReportType, setSelectedReportType] = useState('progress');
  const [formData, setFormData] = useState({
    formato: 'video',
    data_inizio: '',
    data_fine: '',
    id_bobina: ''
  });

  // New state to store all report data
  const [reportsData, setReportsData] = useState({
    progress: null,
    boq: null,
    bobine: null,
    caviStato: null,
    bobinaSpecifica: null,
    posaPeriodo: null
  });

  // State per controllo visualizzazione grafici
  const [showCharts, setShowCharts] = useState(true);

  // Load all basic reports on component mount
  useEffect(() => {
    const loadAllReports = async () => {
      setLoading(true);
      try {
        // Create individual promises that handle their own errors
        const progressPromise = reportService.getProgressReport(cantiereId, 'video')
          .catch(err => {
            console.error('Error loading progress report:', err);
            return { content: null };
          });

        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video')
          .catch(err => {
            console.error('Error loading BOQ report:', err);
            return { content: null };
          });

        const bobinePromise = reportService.getBobineReport(cantiereId, 'video')
          .catch(err => {
            console.error('Error loading bobine report:', err);
            return { content: null };
          });

        const caviStatoPromise = reportService.getCaviStatoReport(cantiereId, 'video')
          .catch(err => {
            console.error('Error loading cavi stato report:', err);
            return { content: null };
          });

        // Wait for all promises to resolve (they won't reject due to the catch handlers)
        const [progressData, boqData, bobineData, caviStatoData] = await Promise.all([
          progressPromise,
          boqPromise,
          bobinePromise,
          caviStatoPromise
        ]);

        // Set the data for each report, even if some are null
        setReportsData({
          progress: progressData.content,
          boq: boqData.content,
          bobine: bobineData.content,
          caviStato: caviStatoData.content,
          bobinaSpecifica: null,
          posaPeriodo: null
        });

        // Only set error to null if we successfully loaded at least one report
        if (progressData.content || boqData.content || bobineData.content || caviStatoData.content) {
          setError(null);
        } else {
          setError('Errore nel caricamento dei report. Riprova più tardi.');
        }
      } catch (err) {
        // This catch block should rarely be hit due to the individual error handling above
        console.error('Unexpected error loading reports:', err);
        setError('Errore nel caricamento dei report. Riprova più tardi.');
      } finally {
        setLoading(false);
      }
    };

    if (cantiereId) {
      loadAllReports();
    }
  }, [cantiereId]);

  // Configurazione dei report disponibili
  const reportTypes = [
    {
      id: 'progress',
      title: 'Report Avanzamento',
      description: 'Panoramica completa dell\'avanzamento dei lavori con metriche di performance e previsioni',
      icon: <AssessmentIcon />,
      color: 'primary',
      features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']
    },
    {
      id: 'boq',
      title: 'Bill of Quantities',
      description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',
      icon: <ListIcon />,
      color: 'secondary',
      features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']
    },
    {
      id: 'bobine',
      title: 'Report Utilizzo Bobine',
      description: 'Analisi completa dell\'utilizzo delle bobine con efficienza e sprechi',
      icon: <InventoryIcon />,
      color: 'success',
      features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']
    },
    {
      id: 'bobina-specifica',
      title: 'Report Bobina Specifica',
      description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',
      icon: <CableIcon />,
      color: 'info',
      features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']
    },
    {
      id: 'posa-periodo',
      title: 'Report Posa per Periodo',
      description: 'Analisi temporale della posa con trend e pattern di lavoro',
      icon: <TimelineIcon />,
      color: 'warning',
      features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']
    },
    {
      id: 'cavi-stato',
      title: 'Report Cavi per Stato',
      description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',
      icon: <BarChartIcon />,
      color: 'error',
      features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']
    }
  ];

  // Nuova funzione per generare report con formato specificato
  const generateReportWithFormat = async (reportType, format) => {
    try {
      setLoading(true);
      setError(null);

      let response;

      switch (reportType) {
        case 'progress':
          response = await reportService.getProgressReport(cantiereId, format);
          break;
        case 'boq':
          response = await reportService.getBillOfQuantities(cantiereId, format);
          break;
        case 'bobine':
          response = await reportService.getBobineReport(cantiereId, format);
          break;
        case 'cavi-stato':
          response = await reportService.getCaviStatoReport(cantiereId, format);
          break;
        case 'bobina-specifica':
          if (!formData.id_bobina) {
            setError('Inserisci l\'ID della bobina');
            return;
          }
          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, format);
          break;
        case 'posa-periodo':
          if (!formData.data_inizio || !formData.data_fine) {
            setError('Seleziona le date di inizio e fine periodo');
            return;
          }
          response = await reportService.getPosaPerPeriodoReport(
            cantiereId,
            formData.data_inizio,
            formData.data_fine,
            format
          );
          break;
        default:
          throw new Error('Tipo di report non riconosciuto');
      }

      if (format === 'video') {
        // For special reports, update the specific report data
        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {
          setReportsData(prev => ({
            ...prev,
            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content
          }));
        }
        setReportData(response.content);
      } else {
        // Per PDF/Excel, apri il link di download
        if (response.file_url) {
          window.open(response.file_url, '_blank');
        }
      }
    } catch (err) {
      console.error('Errore nella generazione del report:', err);
      setError(err.detail || err.message || 'Errore durante la generazione del report');
    } finally {
      setLoading(false);
    }
  };

  const handleReportSelect = (reportType) => {
    setSelectedReport(reportType);
    setDialogType(reportType.id);

    // Per report che necessitano di parametri aggiuntivi, mostra il dialog
    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {
      // Imposta valori di default per alcuni report
      if (reportType.id === 'posa-periodo') {
        const today = new Date();
        const lastMonth = new Date();
        lastMonth.setMonth(today.getMonth() - 1);

        setFormData({
          ...formData,
          data_inizio: lastMonth.toISOString().split('T')[0],
          data_fine: today.toISOString().split('T')[0]
        });
      }

      setOpenDialog(true);
    } else {
      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'
      generateReportWithFormat(reportType.id, 'video');
    }
  };

  const handleGenerateReport = async () => {
    await generateReportWithFormat(dialogType, formData.formato);
    setOpenDialog(false);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setError(null);
    setFormData({
      formato: 'video',
      data_inizio: '',
      data_fine: '',
      id_bobina: ''
    });
  };

  const renderReportContent = () => {
    if (!reportData) return null;

    return (
      <Paper sx={{ p: 3, mt: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            {selectedReport?.title} - {reportData.nome_cantiere}
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            {/* Export buttons */}
            <Button
              startIcon={<DownloadIcon />}
              onClick={() => generateReportWithFormat(dialogType, 'pdf')}
              variant="outlined"
              size="small"
              color="primary"
            >
              PDF
            </Button>
            <Button
              startIcon={<DownloadIcon />}
              onClick={() => generateReportWithFormat(dialogType, 'excel')}
              variant="outlined"
              size="small"
              color="success"
            >
              Excel
            </Button>
            <Button
              startIcon={<RefreshIcon />}
              onClick={() => setReportData(null)}
              variant="outlined"
              size="small"
            >
              Nuovo Report
            </Button>
          </Box>
        </Box>

        <Divider sx={{ mb: 3 }} />

        {/* Renderizza il contenuto specifico del report */}
        {dialogType === 'progress' && renderProgressReport(reportData)}
        {dialogType === 'boq' && renderBoqReport(reportData)}
        {dialogType === 'bobine' && renderBobineReport(reportData)}
        {dialogType === 'bobina-specifica' && renderBobinaSpecificaReport(reportData)}
        {dialogType === 'posa-periodo' && renderPosaPeriodoReport(reportData)}
        {dialogType === 'cavi-stato' && renderCaviStatoReport(reportData)}
      </Paper>
    );
  };

  const renderProgressReport = (data) => (
    <Box>
      {/* Header con controlli */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, color: 'primary.main' }}>
          Report Avanzamento - {data.nome_cantiere || 'Cantiere'}
        </Typography>
        <FormControlLabel
          control={
            <Switch
              checked={showCharts}
              onChange={(e) => setShowCharts(e.target.checked)}
              color="primary"
            />
          }
          label={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <ShowChartIcon sx={{ mr: 1 }} />
              Grafici
            </Box>
          }
        />
      </Box>

      {/* Metriche Principali - Layout Orizzontale Compatto */}
      <Paper sx={{ p: 2, mb: 3, border: '1px solid #e0e0e0' }}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={3}>
            <Box sx={{ textAlign: 'center', p: 1 }}>
              <Typography variant="h5" sx={{ fontWeight: 600, color: '#2c3e50', mb: 0.5 }}>
                {data.metri_totali}m
              </Typography>
              <Typography variant="caption" sx={{ color: '#666', textTransform: 'uppercase', letterSpacing: 0.5 }}>
                Metri Totali
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={3}>
            <Box sx={{ textAlign: 'center', p: 1 }}>
              <Typography variant="h5" sx={{ fontWeight: 600, color: '#27ae60', mb: 0.5 }}>
                {data.metri_posati}m
              </Typography>
              <Typography variant="caption" sx={{ color: '#666', textTransform: 'uppercase', letterSpacing: 0.5 }}>
                Metri Posati ({data.percentuale_avanzamento}%)
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={3}>
            <Box sx={{ textAlign: 'center', p: 1 }}>
              <Typography variant="h5" sx={{ fontWeight: 600, color: '#f39c12', mb: 0.5 }}>
                {data.metri_da_posare}m
              </Typography>
              <Typography variant="caption" sx={{ color: '#666', textTransform: 'uppercase', letterSpacing: 0.5 }}>
                Metri Rimanenti ({100 - data.percentuale_avanzamento}%)
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={3}>
            <Box sx={{ textAlign: 'center', p: 1 }}>
              <Typography variant="h5" sx={{ fontWeight: 600, color: '#3498db', mb: 0.5 }}>
                {data.media_giornaliera}m
              </Typography>
              <Typography variant="caption" sx={{ color: '#666', textTransform: 'uppercase', letterSpacing: 0.5 }}>
                Media/Giorno
                {data.giorni_stimati && ` (${data.giorni_stimati} giorni rimasti)`}
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Grafici */}
      {showCharts && (
        <Box sx={{ mb: 4 }}>
          <ProgressChart data={data} />
        </Box>
      )}

      {/* Dettagli Cavi e Performance */}
      <Grid container spacing={3}>
        {/* Dettagli Cavi */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Dettagli Cavi
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="body1">Totale Cavi:</Typography>
              <Typography variant="body1" sx={{ fontWeight: 600 }}>{data.totale_cavi}</Typography>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="body1">Cavi Posati:</Typography>
              <Typography variant="body1" sx={{ fontWeight: 600, color: 'success.main' }}>
                {data.cavi_posati} ({data.percentuale_cavi}%)
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body1">Cavi Rimanenti:</Typography>
              <Typography variant="body1" sx={{ fontWeight: 600, color: 'warning.main' }}>
                {data.cavi_rimanenti} ({100 - data.percentuale_cavi}%)
              </Typography>
            </Box>
          </Paper>
        </Grid>

        {/* Performance e Previsioni */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Performance e Previsioni
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="body1">Media Giornaliera:</Typography>
              <Typography variant="body1" sx={{ fontWeight: 600 }}>{data.media_giornaliera}m/giorno</Typography>
            </Box>
            {data.giorni_stimati && (
              <>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body1">Giorni Stimati:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>{data.giorni_stimati} giorni</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body1">Data Completamento:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>{data.data_completamento}</Typography>
                </Box>
              </>
            )}
          </Paper>
        </Grid>

        {/* Posa Recente */}
        {data.posa_recente && data.posa_recente.length > 0 && (
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                Attività Recente
              </Typography>
              <FilterableTable
                data={data.posa_recente.map(posa => ({
                  data: posa.data,
                  metri: `${posa.metri}m`
                }))}
                columns={[
                  { field: 'data', headerName: 'Data', width: 200 },
                  { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right' }
                ]}
                pagination={data.posa_recente.length > 6}
                pageSize={6}
              />
            </Paper>
          </Grid>
        )}
      </Grid>
    </Box>
  );

  const renderBoqReport = (data) => (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, color: 'secondary.main' }}>
          Bill of Quantities - Distinta Materiali
        </Typography>
        <FormControlLabel
          control={
            <Switch
              checked={showCharts}
              onChange={(e) => setShowCharts(e.target.checked)}
              color="primary"
            />
          }
          label={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <ShowChartIcon sx={{ mr: 1 }} />
              Grafici
            </Box>
          }
        />
      </Box>

      {/* Grafici */}
      {showCharts && (
        <Box sx={{ mb: 4 }}>
          <BoqChart data={data} />
        </Box>
      )}

      {/* Cavi per Tipologia */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          Cavi per Tipologia
        </Typography>
        <FilterableTable
          data={data.cavi_per_tipo || []}
          columns={[
            { field: 'tipologia', headerName: 'Tipologia', width: 150 },
            { field: 'sezione', headerName: 'Sezione', width: 100 },
            { field: 'num_cavi', headerName: 'Cavi', width: 80, align: 'right', dataType: 'number' },
            { field: 'metri_teorici', headerName: 'Metri Teorici', width: 120, align: 'right', dataType: 'number',
              renderCell: (row) => `${row.metri_teorici}m` },
            { field: 'metri_reali', headerName: 'Metri Reali', width: 120, align: 'right', dataType: 'number',
              renderCell: (row) => `${row.metri_reali}m` },
            { field: 'metri_da_posare', headerName: 'Da Posare', width: 120, align: 'right', dataType: 'number',
              renderCell: (row) => `${row.metri_da_posare}m` }
          ]}
          pageSize={10}
        />
      </Paper>

      {/* Bobine Disponibili */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          Bobine Disponibili
        </Typography>
        <FilterableTable
          data={data.bobine_per_tipo || []}
          columns={[
            { field: 'tipologia', headerName: 'Tipologia', width: 150 },
            { field: 'sezione', headerName: 'Sezione', width: 100 },
            { field: 'num_bobine', headerName: 'Bobine', width: 100, align: 'right', dataType: 'number' },
            { field: 'metri_disponibili', headerName: 'Metri Disponibili', width: 150, align: 'right', dataType: 'number',
              renderCell: (row) => `${row.metri_disponibili}m` }
          ]}
          pageSize={10}
        />
      </Paper>
    </Box>
  );

  const renderBobineReport = (data) => (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, color: 'success.main' }}>
          Report Utilizzo Bobine ({data.totale_bobine} totali)
        </Typography>
        <FormControlLabel
          control={
            <Switch
              checked={showCharts}
              onChange={(e) => setShowCharts(e.target.checked)}
              color="primary"
            />
          }
          label={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <ShowChartIcon sx={{ mr: 1 }} />
              Grafici
            </Box>
          }
        />
      </Box>

      {/* Grafici */}
      {showCharts && (
        <Box sx={{ mb: 4 }}>
          <BobineChart data={data} />
        </Box>
      )}

      {/* Bobine del Cantiere */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          Dettaglio Bobine del Cantiere
        </Typography>
        <FilterableTable
          data={data.bobine || []}
          columns={[
            { field: 'id_bobina', headerName: 'ID Bobina', width: 120 },
            { field: 'tipologia', headerName: 'Tipologia', width: 150 },
            { field: 'sezione', headerName: 'Sezione', width: 100 },
            { field: 'stato', headerName: 'Stato', width: 120,
              renderCell: (row) => (
                <Chip
                  label={row.stato}
                  color={row.stato === 'DISPONIBILE' ? 'success' : 'warning'}
                  size="small"
                />
              )
            },
            { field: 'metri_totali', headerName: 'Metri Totali', width: 120, align: 'right', dataType: 'number',
              renderCell: (row) => `${row.metri_totali}m` },
            { field: 'metri_residui', headerName: 'Metri Residui', width: 120, align: 'right', dataType: 'number',
              renderCell: (row) => `${row.metri_residui}m` },
            { field: 'metri_utilizzati', headerName: 'Metri Utilizzati', width: 140, align: 'right', dataType: 'number',
              renderCell: (row) => `${row.metri_utilizzati}m` },
            { field: 'percentuale_utilizzo', headerName: 'Utilizzo', width: 100, align: 'right', dataType: 'number',
              renderCell: (row) => `${row.percentuale_utilizzo}%` }
          ]}
          pageSize={10}
        />
      </Paper>
    </Box>
  );

  const renderBobinaSpecificaReport = (data) => (
    <Box>
      {/* Header */}
      <Typography variant="h5" sx={{ fontWeight: 600, color: 'info.main', mb: 3 }}>
        Report Bobina Specifica - {data.bobina?.id_bobina}
      </Typography>

      <Grid container spacing={3}>
        {/* Dettagli Bobina */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Dettagli Bobina
            </Typography>
            {data.bobina && (
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">ID Bobina:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>{data.bobina.id_bobina}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">Tipologia:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>{data.bobina.tipologia}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">Sezione:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>{data.bobina.sezione}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">Stato:</Typography>
                  <Chip
                    label={data.bobina.stato}
                    color={data.bobina.stato === 'DISPONIBILE' ? 'success' : 'warning'}
                    size="small"
                  />
                </Box>
              </Box>
            )}
          </Paper>
        </Grid>

        {/* Metriche Utilizzo */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Metriche Utilizzo
            </Typography>
            {data.bobina && (
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">Metri Totali:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>{data.bobina.metri_totali}m</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">Metri Utilizzati:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600, color: 'success.main' }}>
                    {data.bobina.metri_utilizzati}m
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">Metri Residui:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600, color: 'warning.main' }}>
                    {data.bobina.metri_residui}m
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body1">Percentuale Utilizzo:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>{data.bobina.percentuale_utilizzo}%</Typography>
                </Box>
              </Box>
            )}
          </Paper>
        </Grid>

        {/* Cavi Associati */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Cavi Associati ({data.totale_cavi})
            </Typography>
            <FilterableTable
              data={data.cavi_associati || []}
              columns={[
                { field: 'id_cavo', headerName: 'ID Cavo', width: 120 },
                { field: 'sistema', headerName: 'Sistema', width: 120 },
                { field: 'utility', headerName: 'Utility', width: 120 },
                { field: 'tipologia', headerName: 'Tipologia', width: 150 },
                { field: 'metri_teorici', headerName: 'Metri Teorici', width: 120, align: 'right', dataType: 'number',
                  renderCell: (row) => `${row.metri_teorici}m` },
                { field: 'metri_reali', headerName: 'Metri Reali', width: 120, align: 'right', dataType: 'number',
                  renderCell: (row) => `${row.metri_reali}m` },
                { field: 'stato', headerName: 'Stato', width: 120,
                  renderCell: (row) => (
                    <Chip
                      label={row.stato}
                      color={row.stato === 'POSATO' ? 'success' : 'warning'}
                      size="small"
                    />
                  )
                }
              ]}
              pageSize={10}
            />
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );

  const renderPosaPeriodoReport = (data) => (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, color: 'warning.main' }}>
          Report Posa per Periodo
        </Typography>
        <FormControlLabel
          control={
            <Switch
              checked={showCharts}
              onChange={(e) => setShowCharts(e.target.checked)}
              color="primary"
            />
          }
          label={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <ShowChartIcon sx={{ mr: 1 }} />
              Grafici
            </Box>
          }
        />
      </Box>

      {/* Statistiche Periodo - Layout Orizzontale */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'warning.main', color: 'white' }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              {data.totale_metri_periodo}m
            </Typography>
            <Typography variant="body1">Metri Totali</Typography>
            <Typography variant="caption">{data.data_inizio} - {data.data_fine}</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'info.main', color: 'white' }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              {data.giorni_attivi}
            </Typography>
            <Typography variant="body1">Giorni Attivi</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'success.main', color: 'white' }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              {data.media_giornaliera}m
            </Typography>
            <Typography variant="body1">Media/Giorno</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'primary.main', color: 'white' }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              {Math.round(data.totale_metri_periodo / data.giorni_attivi * 7)}m
            </Typography>
            <Typography variant="body1">Media/Settimana</Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Grafici */}
      {showCharts && (
        <Box sx={{ mb: 4 }}>
          <TimelineChart data={data} />
        </Box>
      )}

      {/* Posa Giornaliera */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          Dettaglio Posa Giornaliera
        </Typography>
        <FilterableTable
          data={data.posa_giornaliera || []}
          columns={[
            { field: 'data', headerName: 'Data', width: 200 },
            { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right', dataType: 'number',
              renderCell: (row) => `${row.metri}m` }
          ]}
          pageSize={10}
        />
      </Paper>
    </Box>
  );

  const renderCaviStatoReport = (data) => (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, color: 'error.main' }}>
          Report Cavi per Stato
        </Typography>
        <FormControlLabel
          control={
            <Switch
              checked={showCharts}
              onChange={(e) => setShowCharts(e.target.checked)}
              color="primary"
            />
          }
          label={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <ShowChartIcon sx={{ mr: 1 }} />
              Grafici
            </Box>
          }
        />
      </Box>

      {/* Grafici */}
      {showCharts && (
        <Box sx={{ mb: 4 }}>
          <CaviStatoChart data={data} />
        </Box>
      )}

      {/* Cavi per Stato di Installazione */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          Distribuzione Cavi per Stato di Installazione
        </Typography>
        <FilterableTable
          data={data.cavi_per_stato || []}
          columns={[
            { field: 'stato', headerName: 'Stato', width: 150,
              renderCell: (row) => (
                <Chip
                  label={row.stato}
                  color={row.stato === 'Installato' ? 'success' : 'warning'}
                  size="small"
                />
              )
            },
            { field: 'num_cavi', headerName: 'Numero Cavi', width: 120, align: 'right', dataType: 'number' },
            { field: 'metri_teorici', headerName: 'Metri Teorici', width: 150, align: 'right', dataType: 'number',
              renderCell: (row) => `${row.metri_teorici}m` },
            { field: 'metri_reali', headerName: 'Metri Reali', width: 150, align: 'right', dataType: 'number',
              renderCell: (row) => `${row.metri_reali}m` }
          ]}
          pagination={false}
        />
      </Paper>
    </Box>
  );

  const renderDialog = () => (
    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
      <DialogTitle>
        {selectedReport?.title}
      </DialogTitle>
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Formato</InputLabel>
              <Select
                value={formData.formato}
                label="Formato"
                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}
              >
                <MenuItem value="video">Visualizza a schermo</MenuItem>
                <MenuItem value="pdf">Download PDF</MenuItem>
                <MenuItem value="excel">Download Excel</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {dialogType === 'bobina-specifica' && (
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="ID Bobina"
                value={formData.id_bobina}
                onChange={(e) => setFormData({ ...formData, id_bobina: e.target.value })}
                placeholder="Es: 1, 2, A, B..."
                helperText="Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)"
              />
            </Grid>
          )}

          {dialogType === 'posa-periodo' && (
            <>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  type="date"
                  label="Data Inizio"
                  value={formData.data_inizio}
                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  type="date"
                  label="Data Fine"
                  value={formData.data_fine}
                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
            </>
          )}
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCloseDialog}>Annulla</Button>
        <Button
          onClick={handleGenerateReport}
          variant="contained"
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}
        >
          {loading ? 'Generazione...' : 'Genera Report'}
        </Button>
      </DialogActions>
    </Dialog>
  );

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton onClick={() => navigate(-1)} color="primary">
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h4" component="h1">
            Report e Analytics
          </Typography>
        </Box>
        <AdminHomeButton />
      </Box>

      {/* Loading indicator */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Reports Navigation */}
      <Box sx={{ mt: 3 }}>
        {/* Report Tabs */}
        <Paper sx={{ mb: 3 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, p: 2 }}>
              <Button
                variant={selectedReportType === 'progress' ? 'contained' : 'outlined'}
                startIcon={<AssessmentIcon />}
                onClick={() => setSelectedReportType('progress')}
                color="primary"
              >
                Avanzamento
              </Button>
              <Button
                variant={selectedReportType === 'boq' ? 'contained' : 'outlined'}
                startIcon={<ListIcon />}
                onClick={() => setSelectedReportType('boq')}
                color="secondary"
              >
                Bill of Quantities
              </Button>
              <Button
                variant={selectedReportType === 'bobine' ? 'contained' : 'outlined'}
                startIcon={<InventoryIcon />}
                onClick={() => setSelectedReportType('bobine')}
                color="success"
              >
                Bobine
              </Button>
              <Button
                variant={selectedReportType === 'cavi-stato' ? 'contained' : 'outlined'}
                startIcon={<BarChartIcon />}
                onClick={() => setSelectedReportType('cavi-stato')}
                color="error"
              >
                Cavi per Stato
              </Button>
              <Button
                variant={selectedReportType === 'bobina-specifica' ? 'contained' : 'outlined'}
                startIcon={<CableIcon />}
                onClick={() => setSelectedReportType('bobina-specifica')}
                color="info"
              >
                Bobina Specifica
              </Button>
              <Button
                variant={selectedReportType === 'posa-periodo' ? 'contained' : 'outlined'}
                startIcon={<TimelineIcon />}
                onClick={() => setSelectedReportType('posa-periodo')}
                color="warning"
              >
                Posa per Periodo
              </Button>
            </Box>
          </Box>
        </Paper>

        {/* Report Content */}
        <Box sx={{ minHeight: '400px' }}>
          {/* Progress Report */}
          {selectedReportType === 'progress' && (
            <Paper sx={{ p: 3 }}>
              {reportsData.progress ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('progress', 'pdf')}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      PDF
                    </Button>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('progress', 'excel')}
                      variant="outlined"
                      size="small"
                      color="success"
                    >
                      Excel
                    </Button>
                  </Box>
                  {renderProgressReport(reportsData.progress)}
                </Box>
              ) : loading ? (
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 4 }}>
                  <CircularProgress size={24} />
                  <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', my: 4 }}>
                  <Alert severity="error" sx={{ mb: 2 }}>
                    Impossibile caricare il report. Riprova più tardi.
                  </Alert>
                  <Button
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={() => {
                      setLoading(true);
                      reportService.getProgressReport(cantiereId, 'video')
                        .then(data => {
                          setReportsData(prev => ({
                            ...prev,
                            progress: data.content
                          }));
                        })
                        .catch(err => {
                          console.error('Error retrying progress report:', err);
                        })
                        .finally(() => {
                          setLoading(false);
                        });
                    }}
                  >
                    Riprova
                  </Button>
                </Box>
              )}
            </Paper>
          )}

          {/* Bill of Quantities */}
          {selectedReportType === 'boq' && (
            <Paper sx={{ p: 3 }}>
              {reportsData.boq ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('boq', 'pdf')}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      PDF
                    </Button>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('boq', 'excel')}
                      variant="outlined"
                      size="small"
                      color="success"
                    >
                      Excel
                    </Button>
                  </Box>
                  {renderBoqReport(reportsData.boq)}
                </Box>
              ) : loading ? (
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 4 }}>
                  <CircularProgress size={24} />
                  <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', my: 4 }}>
                  <Alert severity="error" sx={{ mb: 2 }}>
                    Impossibile caricare il report. Riprova più tardi.
                  </Alert>
                  <Button
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={() => {
                      setLoading(true);
                      reportService.getBillOfQuantities(cantiereId, 'video')
                        .then(data => {
                          setReportsData(prev => ({
                            ...prev,
                            boq: data.content
                          }));
                        })
                        .catch(err => {
                          console.error('Error retrying BOQ report:', err);
                        })
                        .finally(() => {
                          setLoading(false);
                        });
                    }}
                  >
                    Riprova
                  </Button>
                </Box>
              )}
            </Paper>
          )}

          {/* Bobine Report */}
          {selectedReportType === 'bobine' && (
            <Paper sx={{ p: 3 }}>
              {reportsData.bobine ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('bobine', 'pdf')}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      PDF
                    </Button>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('bobine', 'excel')}
                      variant="outlined"
                      size="small"
                      color="success"
                    >
                      Excel
                    </Button>
                  </Box>
                  {renderBobineReport(reportsData.bobine)}
                </Box>
              ) : loading ? (
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 4 }}>
                  <CircularProgress size={24} />
                  <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', my: 4 }}>
                  <Alert severity="error" sx={{ mb: 2 }}>
                    Impossibile caricare il report. Riprova più tardi.
                  </Alert>
                  <Button
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={() => {
                      setLoading(true);
                      reportService.getBobineReport(cantiereId, 'video')
                        .then(data => {
                          setReportsData(prev => ({
                            ...prev,
                            bobine: data.content
                          }));
                        })
                        .catch(err => {
                          console.error('Error retrying bobine report:', err);
                        })
                        .finally(() => {
                          setLoading(false);
                        });
                    }}
                  >
                    Riprova
                  </Button>
                </Box>
              )}
            </Paper>
          )}

          {/* Cavi Stato Report */}
          {selectedReportType === 'cavi-stato' && (
            <Paper sx={{ p: 3 }}>
              {reportsData.caviStato ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('cavi-stato', 'pdf')}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      PDF
                    </Button>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('cavi-stato', 'excel')}
                      variant="outlined"
                      size="small"
                      color="success"
                    >
                      Excel
                    </Button>
                  </Box>
                  {renderCaviStatoReport(reportsData.caviStato)}
                </Box>
              ) : loading ? (
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 4 }}>
                  <CircularProgress size={24} />
                  <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', my: 4 }}>
                  <Alert severity="error" sx={{ mb: 2 }}>
                    Impossibile caricare il report. Riprova più tardi.
                  </Alert>
                  <Button
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={() => {
                      setLoading(true);
                      reportService.getCaviStatoReport(cantiereId, 'video')
                        .then(data => {
                          setReportsData(prev => ({
                            ...prev,
                            caviStato: data.content
                          }));
                        })
                        .catch(err => {
                          console.error('Error retrying cavi stato report:', err);
                        })
                        .finally(() => {
                          setLoading(false);
                        });
                    }}
                  >
                    Riprova
                  </Button>
                </Box>
              )}
            </Paper>
          )}

          {/* Bobina Specifica Report */}
          {selectedReportType === 'bobina-specifica' && (
            <Paper sx={{ p: 3 }}>
              {reportsData.bobinaSpecifica ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('bobina-specifica', 'pdf')}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      PDF
                    </Button>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('bobina-specifica', 'excel')}
                      variant="outlined"
                      size="small"
                      color="success"
                    >
                      Excel
                    </Button>
                  </Box>
                  {renderBobinaSpecificaReport(reportsData.bobinaSpecifica)}
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', my: 4 }}>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Report Bobina Specifica
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Seleziona una bobina per generare il report dettagliato con tutti i cavi associati.
                  </Typography>
                  <Button
                    variant="contained"
                    color="info"
                    startIcon={<CableIcon />}
                    onClick={() => {
                      setDialogType('bobina-specifica');
                      setOpenDialog(true);
                    }}
                  >
                    Seleziona Bobina
                  </Button>
                </Box>
              )}
            </Paper>
          )}

          {/* Posa per Periodo Report */}
          {selectedReportType === 'posa-periodo' && (
            <Paper sx={{ p: 3 }}>
              {reportsData.posaPeriodo ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('posa-periodo', 'pdf')}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      PDF
                    </Button>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('posa-periodo', 'excel')}
                      variant="outlined"
                      size="small"
                      color="success"
                    >
                      Excel
                    </Button>
                  </Box>
                  {renderPosaPeriodoReport(reportsData.posaPeriodo)}
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', my: 4 }}>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Report Posa per Periodo
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Seleziona un periodo per analizzare i trend e pattern di posa dei cavi.
                  </Typography>
                  <Button
                    variant="contained"
                    color="warning"
                    startIcon={<TimelineIcon />}
                    onClick={() => {
                      setDialogType('posa-periodo');
                      // Set default date range (last month to today)
                      const today = new Date();
                      const lastMonth = new Date();
                      lastMonth.setMonth(today.getMonth() - 1);

                      setFormData({
                        ...formData,
                        data_inizio: lastMonth.toISOString().split('T')[0],
                        data_fine: today.toISOString().split('T')[0]
                      });
                      setOpenDialog(true);
                    }}
                  >
                    Seleziona Periodo
                  </Button>
                </Box>
              )}
            </Paper>
          )}
        </Box>
      </Box>

      {/* Dialog per configurazione report */}
      {renderDialog()}
    </Box>
  );
};

export default ReportCaviPageNew;
