import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  CircularProgress,
  Alert,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import axios from 'axios';
import config from '../../config';

const API_URL = config.API_URL;

const TestCaviPage = () => {
  const [cantiereId, setCantiereId] = useState('2');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [result, setResult] = useState(null);

  const testDebugEndpoint = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/cavi/debug/${cantiereId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('Risposta debug endpoint:', response.data);
      setResult(response.data);
    } catch (err) {
      console.error('Errore nel test debug endpoint:', err);
      setError(err.message || 'Errore sconosciuto');
    } finally {
      setLoading(false);
    }
  };

  const testRegularEndpoint = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/cavi/${cantiereId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('Risposta endpoint regolare:', response.data);
      setResult({
        total_cavi: response.data.length,
        cavi: response.data.slice(0, 10)
      });
    } catch (err) {
      console.error('Errore nel test endpoint regolare:', err);
      setError(err.message || 'Errore sconosciuto');
    } finally {
      setLoading(false);
    }
  };

  const testActiveCablesEndpoint = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/cavi/${cantiereId}?tipo_cavo=0`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('Risposta endpoint cavi attivi:', response.data);
      setResult({
        total_cavi: response.data.length,
        cavi: response.data.slice(0, 10)
      });
    } catch (err) {
      console.error('Errore nel test endpoint cavi attivi:', err);
      setError(err.message || 'Errore sconosciuto');
    } finally {
      setLoading(false);
    }
  };

  const testSpareCablesEndpoint = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/cavi/${cantiereId}?tipo_cavo=3`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('Risposta endpoint cavi spare:', response.data);
      setResult({
        total_cavi: response.data.length,
        cavi: response.data.slice(0, 10)
      });
    } catch (err) {
      console.error('Errore nel test endpoint cavi spare:', err);
      setError(err.message || 'Errore sconosciuto');
    } finally {
      setLoading(false);
    }
  };

  const testDirectSQL = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // Questa è una simulazione di una query SQL diretta
      // In un'applicazione reale, dovresti avere un endpoint dedicato per questo
      const sql = `
        SELECT * FROM cavi
        WHERE id_cantiere = ${cantiereId}
        LIMIT 10
      `;

      setResult({
        sql: sql,
        message: "Questa è solo una simulazione. In un'applicazione reale, dovresti avere un endpoint dedicato per eseguire query SQL dirette."
      });
    } catch (err) {
      console.error('Errore nella simulazione SQL:', err);
      setError(err.message || 'Errore sconosciuto');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Test API Cavi
      </Typography>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Configurazione
        </Typography>

        <TextField
          label="ID Cantiere"
          value={cantiereId}
          onChange={(e) => setCantiereId(e.target.value)}
          sx={{ mr: 2, mb: 2 }}
        />

        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 2 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={testDebugEndpoint}
            disabled={loading}
          >
            Test Debug Endpoint
          </Button>

          <Button
            variant="contained"
            color="secondary"
            onClick={testRegularEndpoint}
            disabled={loading}
          >
            Test Endpoint Regolare
          </Button>

          <Button
            variant="contained"
            color="success"
            onClick={testActiveCablesEndpoint}
            disabled={loading}
          >
            Test Cavi Attivi
          </Button>

          <Button
            variant="contained"
            color="info"
            onClick={testSpareCablesEndpoint}
            disabled={loading}
          >
            Test Cavi Spare
          </Button>

          <Button
            variant="contained"
            color="warning"
            onClick={testDirectSQL}
            disabled={loading}
          >
            Simula Query SQL
          </Button>
        </Box>
      </Paper>

      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {result && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Risultato
          </Typography>

          {result.sql ? (
            <Box>
              <Typography variant="subtitle1">Query SQL:</Typography>
              <pre>{result.sql}</pre>
              <Alert severity="info">{result.message}</Alert>
            </Box>
          ) : (
            <Box>
              <Typography variant="subtitle1">
                Totale cavi: {result.total_cavi || 0}
                {result.cavi_attivi && ` (Attivi: ${result.cavi_attivi}, Spare: ${result.cavi_spare})`}
              </Typography>

              {result.cavi && result.cavi.length > 0 ? (
                <TableContainer component={Paper} sx={{ mt: 2, maxHeight: 400, overflow: 'auto' }}>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>ID Cavo</TableCell>
                        <TableCell>Utility</TableCell>
                        <TableCell>Tipologia</TableCell>
                        <TableCell>N.Cond</TableCell>
                        <TableCell>Sezione</TableCell>
                        <TableCell>Ubicaz.Part.</TableCell>
                        <TableCell>Ubicaz.Arr.</TableCell>
                        <TableCell>Metri T.</TableCell>
                        <TableCell>Stato</TableCell>
                        <TableCell>Mod. Man.</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {result.cavi.map((cavo) => (
                        <TableRow key={cavo.id_cavo}>
                          <TableCell>{cavo.id_cavo}</TableCell>
                          <TableCell>{cavo.utility || '-'}</TableCell>
                          <TableCell>{cavo.tipologia || '-'}</TableCell>
                          <TableCell>{cavo.n_conduttori || '-'}</TableCell>
                          <TableCell>{cavo.sezione || '-'}</TableCell>
                          <TableCell>{cavo.ubicazione_partenza || '-'}</TableCell>
                          <TableCell>{cavo.ubicazione_arrivo || '-'}</TableCell>
                          <TableCell>{cavo.metri_teorici || '-'}</TableCell>
                          <TableCell>{cavo.stato_installazione || '-'}</TableCell>
                          <TableCell>{cavo.modificato_manualmente}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Alert severity="info" sx={{ mt: 2 }}>
                  Nessun cavo trovato
                </Alert>
              )}

              <Typography variant="subtitle1" sx={{ mt: 3 }}>
                Dati completi:
              </Typography>
              <pre style={{ maxHeight: '300px', overflow: 'auto', backgroundColor: '#f5f5f5', padding: '10px' }}>
                {JSON.stringify(result, null, 2)}
              </pre>
            </Box>
          )}
        </Paper>
      )}
    </Box>
  );
};

export default TestCaviPage;
