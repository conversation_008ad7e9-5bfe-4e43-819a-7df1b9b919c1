import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  IconButton,
  Alert,
  Snackbar
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../context/AuthContext';
import AdminHomeButton from '../../../components/common/AdminHomeButton';
import ParcoCavi from '../../../components/cavi/ParcoCavi';
import { useState } from 'react';

const ModificaBobinaPage = () => {
  const navigate = useNavigate();
  const { selectedCantiere } = useAuth();
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [openSuccess, setOpenSuccess] = useState(false);
  const [openError, setOpenError] = useState(false);

  // Recupera l'ID del cantiere dal localStorage se non è disponibile nel contesto
  const cantiereId = selectedCantiere ? selectedCantiere.id_cantiere : parseInt(localStorage.getItem('selectedCantiereId'), 10);
  const cantiereName = selectedCantiere ? selectedCantiere.nome : localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;

  // Verifica se un cantiere è selezionato
  if (!cantiereId || isNaN(cantiereId)) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning">
          Nessun cantiere selezionato. Seleziona un cantiere per gestire il parco cavi.
        </Alert>
        <Button
          variant="contained"
          color="primary"
          onClick={() => navigate('/dashboard/cantieri')}
          sx={{ mt: 2 }}
        >
          Vai alla lista cantieri
        </Button>
      </Box>
    );
  }

  // Gestisce il ritorno alla pagina del parco cavi
  const handleBackToCantieri = () => {
    navigate('/dashboard/cavi/parco');
  };

  // Gestisce i messaggi di successo
  const handleSuccess = (message) => {
    setSuccessMessage(message);
    setOpenSuccess(true);
  };

  // Gestisce i messaggi di errore
  const handleError = (message) => {
    setErrorMessage(message);
    setOpenError(true);
  };

  // Chiude i messaggi
  const handleCloseSuccess = () => {
    setOpenSuccess(false);
  };

  const handleCloseError = () => {
    setOpenError(false);
  };

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h4">
            Modifica Bobina
          </Typography>
          <IconButton
            onClick={() => window.location.reload()}
            sx={{ ml: 2 }}
            color="primary"
            title="Ricarica la pagina"
          >
            <RefreshIcon />
          </IconButton>
        </Box>
        <AdminHomeButton />
      </Box>

      {cantiereId && (
        <ParcoCavi
          cantiereId={cantiereId}
          onSuccess={handleSuccess}
          onError={handleError}
          initialOption="modificaBobina"
        />
      )}

      {/* Snackbar per i messaggi di successo */}
      <Snackbar
        open={openSuccess}
        autoHideDuration={6000}
        onClose={handleCloseSuccess}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSuccess} severity="success" sx={{ width: '100%' }}>
          {successMessage}
        </Alert>
      </Snackbar>

      {/* Snackbar per i messaggi di errore */}
      <Snackbar
        open={openError}
        autoHideDuration={6000}
        onClose={handleCloseError}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseError} severity="error" sx={{ width: '100%' }}>
          {errorMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ModificaBobinaPage;
