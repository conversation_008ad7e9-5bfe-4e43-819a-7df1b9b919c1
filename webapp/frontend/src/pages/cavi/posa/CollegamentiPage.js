import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  IconButton,
  Alert,
  Snackbar
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Refresh as RefreshIcon,
  Home as HomeIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../context/AuthContext';
import AdminHomeButton from '../../../components/common/AdminHomeButton';
import CollegamentiCavo from '../../../components/cavi/CollegamentiCavo';

const CollegamentiPage = () => {
  const navigate = useNavigate();
  const { isImpersonating } = useAuth();
  const [alertMessage, setAlertMessage] = useState(null);
  const [alertSeverity, setAlertSeverity] = useState('success');
  const [openSnackbar, setOpenSnackbar] = useState(false);

  // Recupera l'ID del cantiere selezionato dal localStorage
  const cantiereId = localStorage.getItem('selectedCantiereId');
  const cantiereName = localStorage.getItem('selectedCantiereName');

  // Gestisce il ritorno alla pagina dei cantieri
  const handleBackToCantieri = () => {
    navigate('/dashboard/cantieri');
  };

  // Gestisce il ritorno al menu admin (per admin che impersonano utenti)
  const handleBackToAdmin = () => {
    navigate('/dashboard/admin');
  };

  // Gestisce il ritorno alla pagina principale di posa cavi
  const handleBackToPosa = () => {
    navigate('/dashboard/cavi/posa');
  };

  // Gestisce il successo di un'operazione
  const handleSuccess = (message) => {
    setAlertMessage(message);
    setAlertSeverity('success');
    setOpenSnackbar(true);
  };

  // Gestisce l'errore di un'operazione
  const handleError = (message) => {
    setAlertMessage(message);
    setAlertSeverity('error');
    setOpenSnackbar(true);
  };

  // Chiude lo snackbar
  const handleCloseSnackbar = () => {
    setOpenSnackbar(false);
  };

  // Se non c'è un cantiere selezionato, reindirizza alla pagina dei cantieri
  if (!cantiereId) {
    navigate('/dashboard/cantieri');
    return null;
  }

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={handleBackToPosa} sx={{ mr: 1 }}>
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h6">
            Gestisci Collegamenti Cavo
          </Typography>
          <IconButton
            onClick={() => window.location.reload()}
            sx={{ ml: 2 }}
            color="primary"
            title="Ricarica la pagina"
          >
            <RefreshIcon />
          </IconButton>
        </Box>
        <AdminHomeButton />
      </Box>

      {/* Componente per la gestione dei collegamenti */}
      <CollegamentiCavo
        cantiereId={cantiereId}
        onSuccess={handleSuccess}
        onError={handleError}
      />

      <Snackbar
        open={openSnackbar}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={alertSeverity} sx={{ width: '100%' }}>
          {alertMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CollegamentiPage;
