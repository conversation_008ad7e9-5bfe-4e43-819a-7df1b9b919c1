import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  IconButton,
  Alert,
  Snackbar
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import AdminHomeButton from '../../../components/common/AdminHomeButton';
import InserisciMetriForm from '../../../components/cavi/InserisciMetriForm';

const InserisciMetriPage = () => {
  const navigate = useNavigate();
  const [alertMessage, setAlertMessage] = useState(null);
  const [alertSeverity, setAlertSeverity] = useState('success');
  const [openSnackbar, setOpenSnackbar] = useState(false);

  // Recupera l'ID del cantiere selezionato dal localStorage
  const cantiereId = localStorage.getItem('selectedCantiereId');

  // Gestisce il ritorno alla pagina principale di posa cavi
  const handleBackToPosa = () => {
    navigate('/dashboard/cavi/posa');
  };

  // Gestisce il successo di un'operazione
  const handleSuccess = (message) => {
    setAlertMessage(message);
    setAlertSeverity('success');
    setOpenSnackbar(true);
  };

  // Gestisce l'errore di un'operazione
  const handleError = (message) => {
    setAlertMessage(message);
    setAlertSeverity('error');
    setOpenSnackbar(true);
  };

  // Chiude lo snackbar
  const handleCloseSnackbar = () => {
    setOpenSnackbar(false);
  };

  // Se non c'è un cantiere selezionato, reindirizza alla pagina dei cantieri
  if (!cantiereId) {
    navigate('/dashboard/cantieri');
    return null;
  }

  return (
    <Box>
      <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={handleBackToPosa} sx={{ mr: 0.5 }} size="small">
            <ArrowBackIcon fontSize="small" />
          </IconButton>
          <Typography variant="h6" sx={{ fontSize: '1.1rem' }}>
            Inserimento Metri Posati
          </Typography>
          <IconButton
            onClick={() => window.location.reload()}
            sx={{ ml: 1 }}
            color="primary"
            title="Ricarica la pagina"
            size="small"
          >
            <RefreshIcon fontSize="small" />
          </IconButton>
        </Box>
        <AdminHomeButton />
      </Box>

      <Paper sx={{ p: 3 }}>
        <InserisciMetriForm
          cantiereId={cantiereId}
          onSuccess={handleSuccess}
          onError={handleError}
        />
      </Paper>

      <Snackbar
        open={openSnackbar}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={alertSeverity} sx={{ width: '100%' }}>
          {alertMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default InserisciMetriPage;
