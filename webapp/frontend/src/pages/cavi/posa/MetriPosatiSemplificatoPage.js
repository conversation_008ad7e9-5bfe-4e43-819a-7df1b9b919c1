import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  IconButton,
  Alert,
  Snackbar
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import AdminHomeButton from '../../../components/common/AdminHomeButton';
import MetriPosatiSemplificatoForm from '../../../components/cavi/MetriPosatiSemplificatoForm';
import SelectedCantiereDisplay from '../../../components/common/SelectedCantiereDisplay';

const MetriPosatiSemplificatoPage = () => {
  const navigate = useNavigate();
  const [alertMessage, setAlertMessage] = useState(null);
  const [alertSeverity, setAlertSeverity] = useState('success');
  const [openSnackbar, setOpenSnackbar] = useState(false);

  // Recupera l'ID del cantiere selezionato dal localStorage
  const cantiereId = localStorage.getItem('selectedCantiereId');
  const cantiereName = localStorage.getItem('selectedCantiereName');

  // Gestisce il ritorno alla pagina principale di posa cavi
  const handleBackToPosa = () => {
    navigate('/dashboard/cavi/posa');
  };

  // Gestisce il successo di un'operazione
  const handleSuccess = (message) => {
    setAlertMessage(message);
    setAlertSeverity('success');
    setOpenSnackbar(true);
  };

  // Gestisce un errore
  const handleError = (message) => {
    setAlertMessage(message);
    setAlertSeverity('error');
    setOpenSnackbar(true);
  };

  // Gestisce la chiusura dello snackbar
  const handleCloseSnackbar = () => {
    setOpenSnackbar(false);
  };

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={handleBackToPosa} sx={{ mr: 1 }}>
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h4">
            Inserimento Metri Posati (Semplificato)
          </Typography>
          <IconButton
            onClick={() => window.location.reload()}
            sx={{ ml: 2 }}
            color="primary"
            title="Ricarica la pagina"
          >
            <RefreshIcon />
          </IconButton>
        </Box>
        <AdminHomeButton />
      </Box>

      <Paper sx={{ mb: 3, p: 2 }}>
        <SelectedCantiereDisplay />
      </Paper>

      <Paper sx={{ p: 3 }}>
        <MetriPosatiSemplificatoForm
          cantiereId={cantiereId}
          onSuccess={handleSuccess}
          onError={handleError}
        />
      </Paper>

      <Snackbar
        open={openSnackbar}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={alertSeverity} sx={{ width: '100%' }}>
          {alertMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default MetriPosatiSemplificatoPage;
