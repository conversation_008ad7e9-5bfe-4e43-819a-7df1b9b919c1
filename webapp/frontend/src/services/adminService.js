import axios from 'axios';
import config from '../config';
import axiosInstance from './axiosConfig';

const API_URL = config.API_URL;

const adminService = {
  // Reset del database
  resetDatabase: async () => {
    try {
      const response = await axiosInstance.post('/admin/reset-database');
      return response.data;
    } catch (error) {
      console.error('Reset database error:', error);
      throw error.response ? error.response.data : error;
    }
  }
};

export default adminService;
