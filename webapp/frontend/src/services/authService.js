import axios from 'axios';
import config from '../config';
import axiosInstance from './axiosConfig';

const API_URL = config.API_URL;

// Gestione degli errori di autenticazione
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('Errore nella risposta API:', error);
    if (error.response && error.response.status === 401) {
      // Se la risposta è 401 Unauthorized, effettua il logout
      console.log('Errore 401 rilevato, rimozione token');
      localStorage.removeItem('token');

      // Pulisci eventuali selezioni di cantiere precedenti
      localStorage.removeItem('selectedCantiereId');
      localStorage.removeItem('selectedCantiereName');
      console.log('Rimossi dati cantiere precedenti dal localStorage');

      // Non reindirizzare automaticamente, lascia che sia il componente React a gestire il reindirizzamento
      // Questo evita loop di reindirizzamento
    }
    return Promise.reject(error);
  }
);

const authService = {
  // Login standard (admin o utente standard)
  login: async (credentials, loginType) => {
    try {
      console.log(`Tentativo di login ${loginType} con API_URL: ${API_URL}`);

      if (loginType === 'standard') {
        // Converti le credenziali nel formato richiesto da OAuth2
        const formData = new FormData();
        formData.append('username', credentials.username);
        formData.append('password', credentials.password);

        console.log(`Invio richiesta POST a ${API_URL}/auth/login`);
        // Usa axios direttamente per il login perché richiede FormData
        const response = await axios.post(`${API_URL}/auth/login`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
        console.log('Risposta ricevuta:', response);
        return response.data;
      } else if (loginType === 'cantiere') {
        // Login cantiere
        console.log(`Invio richiesta POST a ${API_URL}/auth/login/cantiere`);
        const response = await axiosInstance.post('/auth/login/cantiere', {
          codice_univoco: credentials.codice_univoco,
          password: credentials.password
        });
        console.log('Risposta ricevuta:', response);

        // Salva l'ID e il nome del cantiere nel localStorage
        if (response.data.cantiere_id) {
          console.log('Salvando ID cantiere nel localStorage:', response.data.cantiere_id);
          localStorage.setItem('selectedCantiereId', response.data.cantiere_id.toString());
          localStorage.setItem('selectedCantiereName', response.data.cantiere_name || `Cantiere ${response.data.cantiere_id}`);
        } else {
          console.warn('Risposta login cantiere non contiene cantiere_id:', response.data);
        }

        return response.data;
      } else {
        throw new Error('Tipo di login non valido');
      }
    } catch (error) {
      console.error('Login error:', error);
      if (error.response) {
        console.error('Dettagli errore:', error.response.status, error.response.data);
        // Miglioramento della gestione degli errori per mostrare messaggi più chiari
        if (error.response.status === 401) {
          throw { detail: error.response.data.detail || 'Credenziali non valide. Verifica username e password.' };
        } else if (error.response.status === 403) {
          throw { detail: error.response.data.detail || 'Accesso negato. Non hai i permessi necessari.' };
        } else {
          throw error.response.data;
        }
      } else if (error.request) {
        console.error('Nessuna risposta ricevuta:', error.request);
        throw { detail: 'Errore di connessione al server. Verifica che il backend sia in esecuzione.' };
      } else {
        console.error('Errore durante la configurazione della richiesta:', error.message);
        throw { detail: error.message };
      }
    }
  },



  // Verifica la validità del token
  checkToken: async () => {
    try {
      console.log('Verifica token in corso...');
      const response = await axiosInstance.post('/auth/test-token');
      console.log('Risposta verifica token:', response.data);

      // Controlla se l'utente è impersonato da un admin
      // Questo valore viene ora impostato dal backend nel token JWT
      const isImpersonated = response.data.is_impersonated === true;

      // Se l'utente è impersonato, salva lo stato nel localStorage
      if (isImpersonated) {
        console.log('Utente impersonato da admin, salvataggio stato nel localStorage');
        localStorage.setItem('isImpersonating', 'true');
      } else {
        // Altrimenti, assicurati che non ci sia uno stato di impersonificazione salvato
        localStorage.removeItem('isImpersonating');
      }

      // Costruisci l'oggetto utente con i dati dal token
      const userData = {
        id: response.data.user_id,
        username: response.data.username,
        role: response.data.role,
        isImpersonated: isImpersonated
      };

      // Se l'utente è un utente cantiere, aggiungi i dati del cantiere
      if (response.data.role === 'cantieri_user' && response.data.cantiere_id) {
        userData.cantiere_id = response.data.cantiere_id;
        userData.cantiere_name = response.data.cantiere_name || `Cantiere ${response.data.cantiere_id}`;

        // Salva l'ID e il nome del cantiere nel localStorage
        localStorage.setItem('selectedCantiereId', response.data.cantiere_id.toString());
        localStorage.setItem('selectedCantiereName', userData.cantiere_name);
        console.log('Salvati dati cantiere nel localStorage durante checkToken:', {
          cantiere_id: response.data.cantiere_id,
          cantiere_name: userData.cantiere_name
        });
      }

      // Se l'utente è impersonato, aggiungi i dati dell'utente impersonato
      if (isImpersonated && response.data.impersonated_id) {
        // Salva i dati dell'utente impersonato nel localStorage
        const impersonatedUserData = {
          id: response.data.impersonated_id,
          username: response.data.impersonated_username,
          role: response.data.impersonated_role
        };

        localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData));
      }

      return userData;
    } catch (error) {
      console.error('Check token error:', error);
      // Pulisci il localStorage per evitare loop
      localStorage.removeItem('token');
      localStorage.removeItem('isImpersonating');

      // Pulisci eventuali selezioni di cantiere precedenti
      localStorage.removeItem('selectedCantiereId');
      localStorage.removeItem('selectedCantiereName');
      console.log('Rimossi dati cantiere precedenti dal localStorage');
      throw error.response ? error.response.data : error;
    }
  },

  // Impersona un altro utente (solo per admin)
  impersonateUser: async (userId) => {
    try {
      const response = await axiosInstance.post('/auth/impersonate', {
        user_id: userId
      });
      return response.data;
    } catch (error) {
      console.error('Impersonate user error:', error);
      throw error.response ? error.response.data : error;
    }
  }
};

export default authService;
