import axios from 'axios';
import config from '../config';

// Crea un'istanza di axios con configurazione migliorata
const axiosInstance = axios.create({
  baseURL: config.API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 60000, // 60 secondi
  withCredentials: false, // Disabilita l'invio di credenziali per evitare problemi CORS
});

// Configura axios per includere il token in tutte le richieste
axiosInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    console.error('Errore nella configurazione della richiesta:', error);
    return Promise.reject(error);
  }
);

// Gestione globale degli errori
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Log dettagliato dell'errore (solo in console, non mostrato all'utente)
    console.error('Errore nella risposta:', error);

    // Gestione specifica per errori di rete
    if (error.message && (error.message.includes('Network Error') || error.message.includes('Failed to fetch'))) {
      console.error('Errore di rete:', error);
      error.isNetworkError = true;
      error.customMessage = 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.';
      // Sostituisci il messaggio di errore originale per evitare alert da localhost
      error.message = error.customMessage;
    }

    // Gestione errori di timeout
    if (error.code === 'ECONNABORTED') {
      console.error('Timeout della richiesta:', error);
      error.isTimeoutError = true;
      error.customMessage = 'La richiesta ha impiegato troppo tempo. Riprova più tardi.';
      // Sostituisci il messaggio di errore originale per evitare alert da localhost
      error.message = error.customMessage;
    }

    // Gestione errori CORS
    if (error.message && error.message.includes('CORS')) {
      error.customMessage = 'Errore di comunicazione con il server. Riprova più tardi.';
      error.message = error.customMessage;
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;
