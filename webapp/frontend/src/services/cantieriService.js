import axios from 'axios';
import config from '../config';
import axiosInstance from './axiosConfig';

const API_URL = config.API_URL;

const cantieriService = {
  // Ottiene la lista di tutti i cantieri dell'utente corrente
  getMyCantieri: async () => {
    try {
      const response = await axiosInstance.get('/cantieri');
      return response.data;
    } catch (error) {
      console.error('Get cantieri error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Crea un nuovo cantiere
  createCantiere: async (cantiereData) => {
    try {
      const response = await axiosInstance.post('/cantieri', cantiereData);
      return response.data;
    } catch (error) {
      console.error('Create cantiere error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Elimina un cantiere
  deleteCantiere: async (cantiereId) => {
    try {
      const response = await axiosInstance.delete(`/cantieri/${cantiereId}`);
      return response.data;
    } catch (error) {
      console.error('Delete cantiere error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene i dettagli di un cantiere specifico
  getCantiere: async (cantiereId) => {
    try {
      const response = await axiosInstance.get(`/cantieri/${cantiereId}`);
      return response.data;
    } catch (error) {
      console.error('Get cantiere error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene i cantieri di un utente specifico (per amministratori che impersonano utenti)
  getUserCantieri: async (userId) => {
    try {
      const response = await axiosInstance.get(`/cantieri/user/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Get user cantieri error:', error);
      throw error.response ? error.response.data : error;
    }
  }
};

export default cantieriService;
