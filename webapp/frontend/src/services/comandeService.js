import axios from 'axios';
import config from '../config';
import axiosInstance from './axiosConfig';

const API_URL = config.API_URL;

const comandeService = {
  // Ottiene la lista delle comande di un cantiere
  getComande: async (cantiereId) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/comande/${cantiereIdNum}`);
      return response.data;
    } catch (error) {
      console.error('Get comande error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Crea una nuova comanda
  createComanda: async (cantiereId, comandaData) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.post(`/comande/${cantiereIdNum}`, comandaData);
      return response.data;
    } catch (error) {
      console.error('Create comanda error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Aggiorna una comanda esistente
  updateComanda: async (cantiereId, idComanda, comandaData) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.put(`/comande/${cantiereIdNum}/${idComanda}`, comandaData);
      return response.data;
    } catch (error) {
      console.error('Update comanda error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Elimina una comanda
  deleteComanda: async (cantiereId, idComanda) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.delete(`/comande/${cantiereIdNum}/${idComanda}`);
      return response.data;
    } catch (error) {
      console.error('Delete comanda error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Assegna una comanda a un cavo
  assignComandaToCavo: async (cantiereId, idComanda, idCavo) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.post(`/comande/${cantiereIdNum}/${idComanda}/assign`, {
        id_cavo: idCavo
      });
      return response.data;
    } catch (error) {
      console.error('Assign comanda error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Genera PDF di una comanda
  printComanda: async (cantiereId, idComanda) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/comande/${cantiereIdNum}/${idComanda}/pdf`);
      return response.data;
    } catch (error) {
      console.error('Print comanda error:', error);
      throw error.response ? error.response.data : error;
    }
  }
};

export default comandeService;
