import axios from 'axios';
import config from '../config';
import axiosInstance from './axiosConfig';

const API_URL = config.API_URL;

const parcoCaviService = {
  // Ottiene la lista delle bobine di un cantiere
  getBobine: async (cantiereId) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/parco-cavi/${cantiereIdNum}`);
      return response.data;
    } catch (error) {
      console.error('Get bobine error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Crea una nuova bobina
  createBobina: async (cantiereId, bobinaData) => {
    // Variabile per memorizzare i dati inviati per il logging degli errori
    let sentData;

    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      // Log dei dati originali
      console.log('Dati originali della bobina:', JSON.stringify(bobinaData, null, 2));

      // Validazione dei campi obbligatori
      if (!bobinaData.utility || bobinaData.utility.trim() === '') {
        throw new Error("L'utility è obbligatoria");
      }

      if (!bobinaData.tipologia || bobinaData.tipologia.trim() === '') {
        throw new Error("La tipologia è obbligatoria");
      }

      if (!bobinaData.n_conduttori || bobinaData.n_conduttori.trim() === '') {
        throw new Error("Il numero di conduttori è obbligatorio");
      }

      if (!bobinaData.sezione || bobinaData.sezione.trim() === '') {
        throw new Error("La sezione è obbligatoria");
      }

      // Assicurati che i campi numerici siano numeri e che i campi stringa siano stringhe
      const processedData = {
        ...bobinaData,
        numero_bobina: String(bobinaData.numero_bobina || ''),
        utility: String(bobinaData.utility || ''),
        tipologia: String(bobinaData.tipologia || ''),
        n_conduttori: String(bobinaData.n_conduttori || '0'),
        sezione: String(bobinaData.sezione || '0'),
        metri_totali: parseFloat(bobinaData.metri_totali) || 0,
        ubicazione_bobina: String(bobinaData.ubicazione_bobina || 'TBD'),
        fornitore: String(bobinaData.fornitore || 'TBD'),
        n_DDT: String(bobinaData.n_DDT || 'TBD'),
        data_DDT: bobinaData.data_DDT || null,
        configurazione: String(bobinaData.configurazione || 's')
        // Non inviare metri_residui, verranno impostati dal backend
      };

      // Validazione metri_totali
      if (processedData.metri_totali <= 0) {
        throw new Error("I metri totali devono essere maggiori di zero");
      }

      // Log dei dati processati prima dell'invio
      console.log('Dati processati prima dell\'invio:', JSON.stringify(processedData, null, 2));

      // Salva una copia dei dati per il log degli errori
      const sentData = { ...processedData };

      // Assicurati che numero_bobina sia impostato correttamente
      if (processedData.configurazione === 'n' && (!processedData.numero_bobina || processedData.numero_bobina.trim() === '')) {
        throw new Error('L\'ID della bobina è obbligatorio');
      }

      // Log dei dati processati
      console.log('Dati processati della bobina:', JSON.stringify(processedData, null, 2));

      // Log della richiesta
      console.log(`Invio richiesta POST a: /parco-cavi/${cantiereIdNum}`);

      const response = await axiosInstance.post(`/parco-cavi/${cantiereIdNum}`, processedData);
      console.log('Risposta dal server:', response.data);
      return response.data;
    } catch (error) {
      console.error('Create bobina error:', error);
      // Log dettagliato dei dati inviati che hanno causato l'errore
      if (sentData) {
        console.error('Dati inviati che hanno causato errore:', JSON.stringify(sentData, null, 2));
      }

      // Gestione specifica per errori di rete
      if (error.isNetworkError || error.isTimeoutError) {
        console.error('Errore di rete o timeout:', error.customMessage || error.message);
        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };
      }

      if (error.response) {
        console.error('Dettagli errore:', error.response.data);
        console.error('Status errore:', error.response.status);
        console.error('Headers errore:', error.response.headers);

        // Formatta il messaggio di errore in modo più leggibile
        let errorDetail = error.response.data.detail || 'Errore sconosciuto';
        // Se errorDetail è un oggetto, convertilo in stringa
        if (typeof errorDetail === 'object') {
          errorDetail = JSON.stringify(errorDetail);
        }
        throw { detail: errorDetail, status: error.response.status };
      }
      // Se è un errore di validazione locale, formatta il messaggio
      if (error instanceof Error) {
        throw { detail: error.message, status: 400 };
      }
      throw error;
    }
  },

  // Aggiorna una bobina esistente
  updateBobina: async (cantiereId, numeroBobina, bobinaData) => {
    // Variabile per memorizzare i dati inviati per il logging degli errori
    let sentData;

    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      // Estrai solo il numero della bobina dalla stringa completa (se necessario)
      // Il formato dell'ID bobina è C{id_cantiere}_B{numero_bobina}
      let bobinaPart = numeroBobina;
      if (numeroBobina.includes('_B')) {
        bobinaPart = numeroBobina.split('_B')[1];
      }

      // Validazione dei campi obbligatori
      if (!bobinaData.utility || bobinaData.utility.trim() === '') {
        throw new Error("L'utility è obbligatoria");
      }

      if (!bobinaData.tipologia || bobinaData.tipologia.trim() === '') {
        throw new Error("La tipologia è obbligatoria");
      }

      if (!bobinaData.n_conduttori || String(bobinaData.n_conduttori).trim() === '') {
        throw new Error("Il numero di conduttori è obbligatorio");
      }

      if (!bobinaData.sezione || String(bobinaData.sezione).trim() === '') {
        throw new Error("La sezione è obbligatoria");
      }

      // Assicurati che i campi numerici siano numeri e che i campi stringa siano stringhe
      const processedData = {
        ...bobinaData,
        utility: String(bobinaData.utility || ''),
        tipologia: String(bobinaData.tipologia || ''),
        n_conduttori: String(bobinaData.n_conduttori || '0'),
        sezione: String(bobinaData.sezione || '0'),
        metri_totali: parseFloat(bobinaData.metri_totali) || 0,
        ubicazione_bobina: String(bobinaData.ubicazione_bobina || 'TBD'),
        fornitore: String(bobinaData.fornitore || 'TBD'),
        n_DDT: String(bobinaData.n_DDT || 'TBD'),
        data_DDT: bobinaData.data_DDT || null,
        configurazione: String(bobinaData.configurazione || 's')
      };

      // Validazione metri_totali
      if (processedData.metri_totali <= 0) {
        throw new Error("I metri totali devono essere maggiori di zero");
      }

      // Log dei dati processati
      console.log('Dati processati per aggiornamento bobina:', JSON.stringify(processedData, null, 2));

      // Salva una copia dei dati per il log degli errori
      sentData = { ...processedData };

      // Log della richiesta
      console.log(`Invio richiesta PUT a: /parco-cavi/${cantiereIdNum}/${bobinaPart}`);

      const response = await axiosInstance.put(`/parco-cavi/${cantiereIdNum}/${bobinaPart}`, processedData);
      console.log('Risposta dal server:', response.data);
      return response.data;
    } catch (error) {
      console.error('Update bobina error:', error);

      // Log dettagliato dei dati inviati che hanno causato l'errore
      if (sentData) {
        console.error('Dati inviati che hanno causato errore:', JSON.stringify(sentData, null, 2));
      }

      // Gestione specifica per errori di rete
      if (error.isNetworkError || error.isTimeoutError) {
        console.error('Errore di rete o timeout:', error.customMessage || error.message);
        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };
      }

      if (error.response) {
        console.error('Dettagli errore:', error.response.data);
        console.error('Status errore:', error.response.status);
        console.error('Headers errore:', error.response.headers);

        // Formatta il messaggio di errore in modo più leggibile
        let errorDetail = error.response.data.detail || 'Errore sconosciuto';
        // Se errorDetail è un oggetto, convertilo in stringa
        if (typeof errorDetail === 'object') {
          errorDetail = JSON.stringify(errorDetail);
        }
        throw { detail: errorDetail, status: error.response.status };
      }
      // Se è un errore di validazione locale, formatta il messaggio
      if (error instanceof Error) {
        throw { detail: error.message, status: 400 };
      }
      throw error;
    }
  },

  // Elimina una bobina
  deleteBobina: async (cantiereId, numeroBobina) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      // Estrai solo il numero della bobina dalla stringa completa (se necessario)
      // Il formato dell'ID bobina è C{id_cantiere}_B{numero_bobina}
      let bobinaPart = numeroBobina;
      if (numeroBobina.includes('_B')) {
        bobinaPart = numeroBobina.split('_B')[1];
      }

      const response = await axiosInstance.delete(`/parco-cavi/${cantiereIdNum}/${bobinaPart}`);

      // Log per debug
      console.log('Risposta eliminazione bobina:', response.data);

      // Verifica se è l'ultima bobina
      if (response.data.is_last_bobina) {
        console.log(`Eliminata l'ultima bobina del cantiere ${cantiereId}. Il parco cavi è ora vuoto.`);
        // Rimuovi la configurazione salvata in localStorage
        localStorage.removeItem(`cantiere_${cantiereId}_config`);
      }

      return response.data;
    } catch (error) {
      console.error('Delete bobina error:', error);

      // Gestione specifica per errori di rete
      if (error.isNetworkError || error.isTimeoutError) {
        console.error('Errore di rete o timeout:', error.customMessage || error.message);
        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };
      }

      if (error.response && error.response.data) {
        let errorDetail = error.response.data.detail || 'Errore sconosciuto';
        // Se errorDetail è un oggetto, convertilo in stringa
        if (typeof errorDetail === 'object') {
          errorDetail = JSON.stringify(errorDetail);
        }
        throw { detail: errorDetail, status: error.response.status };
      }
      // Se è un errore di validazione locale, formatta il messaggio
      if (error instanceof Error) {
        throw { detail: error.message, status: 400 };
      }
      throw error;
    }
  },

  // Verifica se è il primo inserimento di una bobina per un cantiere e recupera la configurazione esistente
  isFirstBobinaInsertion: async (cantiereId) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      // Usa l'API per verificare se è il primo inserimento
      const response = await axiosInstance.get(`/parco-cavi/${cantiereIdNum}/is-first-insertion`);
      console.log('Risposta API is-first-insertion:', response.data);
      return response.data;
    } catch (error) {
      console.error('Check first insertion error:', error);

      // Gestione specifica per errori di rete
      if (error.isNetworkError || error.isTimeoutError) {
        console.error('Errore di rete o timeout:', error.customMessage || error.message);
        // Per questo metodo, in caso di errore di rete, restituiamo un valore di default
        // invece di propagare l'errore, per evitare che l'utente non possa procedere
        return { is_first_insertion: false, configurazione: 's' };
      }

      if (error.response && error.response.data) {
        let errorDetail = error.response.data.detail || 'Errore sconosciuto';
        // Se errorDetail è un oggetto, convertilo in stringa
        if (typeof errorDetail === 'object') {
          errorDetail = JSON.stringify(errorDetail);
        }
        throw { detail: errorDetail, status: error.response.status };
      }
      // Se è un errore di validazione locale, formatta il messaggio
      if (error instanceof Error) {
        throw { detail: error.message, status: 400 };
      }
      throw error;
    }
  },

  // Recupera la configurazione esistente per un cantiere
  getBobinaConfig: async (cantiereId) => {
    try {
      // Usa l'API per verificare se è il primo inserimento e ottenere la configurazione
      const response = await parcoCaviService.isFirstBobinaInsertion(cantiereId);
      return { configurazione: response.configurazione || 's' };
    } catch (error) {
      console.error('Get bobina config error:', error);
      // In caso di errore, restituisci la configurazione di default
      return { configurazione: 's' };
    }
  },

  // Ottiene lo storico utilizzo delle bobine
  getStoricoUtilizzo: async (cantiereId) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/parco-cavi/${cantiereIdNum}/storico`);
      return response.data;
    } catch (error) {
      console.error('Get storico utilizzo error:', error);

      // Gestione specifica per errori di rete
      if (error.isNetworkError || error.isTimeoutError) {
        console.error('Errore di rete o timeout:', error.customMessage || error.message);
        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };
      }

      if (error.response && error.response.data) {
        let errorDetail = error.response.data.detail || 'Errore sconosciuto';
        // Se errorDetail è un oggetto, convertilo in stringa
        if (typeof errorDetail === 'object') {
          errorDetail = JSON.stringify(errorDetail);
        }
        throw { detail: errorDetail, status: error.response.status };
      }
      // Se è un errore di validazione locale, formatta il messaggio
      if (error instanceof Error) {
        throw { detail: error.message, status: 400 };
      }
      throw error;
    }
  },

  // Ottiene le bobine compatibili con un cavo specifico
  getBobineCompatibili: async (cantiereId, tipologia, n_conduttori, sezione) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      // Costruisci i parametri di query
      const params = {};

      // Gestisci tutti i parametri in modo più robusto
      // Converti esplicitamente in stringhe per garantire la compatibilità con il backend
      // Normalizza i valori rimuovendo spazi extra e convertendo in minuscolo
      params.tipologia = tipologia !== undefined && tipologia !== null ? String(tipologia).trim() : '';

      // Nota: n_conduttori non è più utilizzato per la compatibilità
      // Non inviamo più questo parametro al backend

      params.sezione = sezione !== undefined && sezione !== null ? String(sezione).trim() : '0';

      // Log dettagliato dei parametri
      console.log('SERVIZIO API - Parametri originali:', { tipologia, sezione });
      console.log('SERVIZIO API - Tipi dei parametri originali:', {
        tipologia: typeof tipologia,
        sezione: typeof sezione
      });
      console.log('SERVIZIO API - Parametri inviati all\'API:', params);
      console.log(`SERVIZIO API - URL chiamata: /parco-cavi/${cantiereIdNum}/compatibili`);

      // Log per debug - Ottieni tutte le bobine per confronto
      console.log('Ottenendo tutte le bobine per confronto...');
      const allBobineResponse = await axiosInstance.get(`/parco-cavi/${cantiereIdNum}`);
      console.log('Tutte le bobine disponibili:', allBobineResponse.data);

      // Verifica manuale della compatibilità
      console.log('Verifica manuale della compatibilità:');
      allBobineResponse.data.forEach(bobina => {
        const bobinaTipologia = String(bobina.tipologia || '').trim();
        const bobinaSezione = String(bobina.sezione || '').trim();
        const paramTipologia = params.tipologia;
        const paramSezione = params.sezione;

        console.log(`Bobina ${bobina.id_bobina}:`, {
          'Tipologia bobina': bobinaTipologia,
          'Tipologia parametro': paramTipologia,
          'Tipologie uguali?': bobinaTipologia === paramTipologia,
          'Sezione bobina': bobinaSezione,
          'Sezione parametro': paramSezione,
          'Sezioni uguali?': bobinaSezione === paramSezione,
          'Compatibile?': bobinaTipologia === paramTipologia && bobinaSezione === paramSezione
        });
      });

      // Esegui la richiesta API
      const response = await axiosInstance.get(`/parco-cavi/${cantiereIdNum}/compatibili`, { params });

      // Log dei risultati
      console.log('Bobine compatibili trovate:', response.data.length);
      if (response.data.length > 0) {
        console.log('Prima bobina compatibile:', response.data[0]);
      }

      return response.data;
    } catch (error) {
      console.error('Get bobine compatibili error:', error);

      // Gestione specifica per errori di rete
      if (error.isNetworkError || error.isTimeoutError) {
        console.error('Errore di rete o timeout:', error.customMessage || error.message);
        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };
      }

      if (error.response && error.response.data) {
        let errorDetail = error.response.data.detail || 'Errore sconosciuto';
        // Se errorDetail è un oggetto, convertilo in stringa
        if (typeof errorDetail === 'object') {
          errorDetail = JSON.stringify(errorDetail);
        }
        throw { detail: errorDetail, status: error.response.status };
      }
      // Se è un errore di validazione locale, formatta il messaggio
      if (error instanceof Error) {
        throw { detail: error.message, status: 400 };
      }
      throw error;
    }
  }
};

export default parcoCaviService;
