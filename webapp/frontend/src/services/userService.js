import axios from 'axios';
import config from '../config';
import axiosInstance from './axiosConfig';

const API_URL = config.API_URL;

const userService = {
  // Ottiene la lista di tutti gli utenti
  getUsers: async () => {
    try {
      const response = await axiosInstance.get('/users');
      return response.data;
    } catch (error) {
      console.error('Get users error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Crea un nuovo utente
  createUser: async (userData) => {
    try {
      const response = await axiosInstance.post('/users', userData);
      return response.data;
    } catch (error) {
      console.error('Create user error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Aggiorna un utente esistente
  updateUser: async (userId, userData) => {
    try {
      const response = await axiosInstance.put(`/users/${userId}`, userData);
      return response.data;
    } catch (error) {
      console.error('Update user error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Elimina un utente
  deleteUser: async (userId) => {
    try {
      const response = await axiosInstance.delete(`/users/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Delete user error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Abilita/disabilita un utente
  toggleUserStatus: async (userId) => {
    try {
      const response = await axiosInstance.get(`/users/toggle/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Toggle user status error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene una visualizzazione raw del database
  getDbRaw: async () => {
    try {
      console.log('Chiamata API a /users/db-raw');
      const response = await axiosInstance.get('/users/db-raw');
      console.log('Risposta API ricevuta:', response);
      return response.data;
    } catch (error) {
      console.error('Get DB raw error:', error);
      console.error('Dettagli errore:', error.response ? error.response.data : 'Nessun dettaglio disponibile');
      throw error.response ? error.response.data : error;
    }
  },

  // Verifica e disabilita gli utenti scaduti
  checkExpiredUsers: async () => {
    try {
      const response = await axiosInstance.post('/users/check-expired');
      return response.data;
    } catch (error) {
      console.error('Check expired users error:', error);
      throw error.response ? error.response.data : error;
    }
  }
};

export default userService;
