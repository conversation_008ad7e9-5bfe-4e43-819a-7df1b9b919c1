/**
 * Utility per la validazione dei campi delle bobine
 * Implementa le stesse regole di validazione della CLI
 */

// Costanti
export const TBD = "TBD";

/**
 * Verifica se un valore è vuoto
 * @param {string|number|null} value - Valore da verificare
 * @returns {boolean} - True se il valore è vuoto, false altrimenti
 */
export const isEmpty = (value) => {
  return value === null || value === undefined ||
         (typeof value === 'string' && value.trim() === '') ||
         (typeof value === 'number' && isNaN(value));
};

/**
 * Converte un valore in float
 * @param {string|number|null} value - Valore da convertire
 * @returns {number} - Valore convertito in float
 */
export const convertToFloat = (value) => {
  if (isEmpty(value)) return 0;

  if (typeof value === 'number') return value;

  if (typeof value === 'string') {
    const normalized = value.trim().replace(',', '.');
    try {
      return parseFloat(normalized);
    } catch (e) {
      console.warn(`Errore conversione float: '${value}' non è un numero valido.`);
      return 0;
    }
  }

  console.warn(`Tipo di valore non supportato: ${typeof value}`);
  return 0;
};

/**
 * Valida un campo numerico
 * @param {string|number} value - Valore da validare
 * @param {string} fieldName - Nome del campo
 * @returns {Object} - Risultato della validazione
 */
export const validateNumber = (value, fieldName) => {
  try {
    // Gestione campi vuoti
    if (isEmpty(value)) {
      if (fieldName === "Numero conduttori") {
        // Per numero conduttori, accetta vuoto e imposta a 0
        return { valid: true, message: "", value: "0" };
      }
      if (fieldName === "Formazione") {
        // Per formazione, accetta vuoto e imposta a 0
        return { valid: true, message: "", value: "0" };
      }
      return { valid: false, message: `${fieldName} non può essere vuoto`, value: null };
    }

    // Normalizzazione input se è stringa
    let normalizedValue = value;
    if (typeof value === 'string') {
      normalizedValue = value.trim().replace(',', '.');
      if (normalizedValue === '.') {
        return { valid: false, message: `${fieldName} non valido`, value: null };
      }
    }

    // Per Numero conduttori e Formazione, accetta anche valori non numerici
    if (fieldName === "Numero conduttori" || fieldName === "Formazione") {
      // Accetta qualsiasi valore come stringa
      const stringValue = normalizedValue.toString();

      // Se è un numero, verifica i limiti
      if (!isNaN(parseFloat(normalizedValue))) {
        const numero = parseFloat(normalizedValue);

        // Validazione numero negativo
        if (numero < 0) {
          return { valid: false, message: `${fieldName} non può essere negativo`, value: null };
        }

        // Validazione limiti specifici
        if (fieldName === "Numero conduttori" && numero > 24) {
          return {
            valid: true,
            message: `ATTENZIONE: Il numero di conduttori (${numero}) supera il limite standard di 24`,
            value: stringValue,
            warning: true
          };
        }

        if (fieldName === "Formazione" && numero > 1000) {
          return {
            valid: true,
            message: `ATTENZIONE: La formazione (${numero}) supera il limite standard di 1000`,
            value: stringValue,
            warning: true
          };
        }
      }

      return { valid: true, message: "", value: stringValue };
    } else {
      // Per altri campi numerici, mantieni la validazione originale
      const numero = parseFloat(normalizedValue);

      // Validazione numero negativo
      if (numero < 0) {
        return { valid: false, message: `${fieldName} non può essere negativo`, value: null };
      }

      return { valid: true, message: "", value: numero.toString() };
    }
  } catch (e) {
    return { valid: false, message: `Il valore inserito per ${fieldName} non è un numero valido`, value: null };
  }
};

/**
 * Valida i metri totali
 * @param {string|number} value - Valore da validare
 * @returns {Object} - Risultato della validazione
 */
export const validateMetriTotali = (value) => {
  try {
    if (isEmpty(value)) {
      return { valid: false, message: "I metri totali sono obbligatori", value: null };
    }

    const val = convertToFloat(value);

    if (val <= 0) {
      return { valid: false, message: "I metri totali devono essere maggiori di zero", value: null };
    }

    if (val > 100000) {  // 100km come limite ragionevole
      return { valid: false, message: "I metri totali non possono superare 100.000", value: null };
    }

    return { valid: true, message: "Valore valido", value: val };
  } catch (e) {
    return { valid: false, message: "Il valore deve essere un numero valido", value: null };
  }
};

/**
 * Valida un campo di testo base
 * @param {string} value - Valore da validare
 * @returns {Object} - Risultato della validazione
 */
export const validateBaseField = (value) => {
  if (isEmpty(value)) {
    return { valid: true, message: "Campo vuoto", value: TBD };
  }
  return { valid: true, message: "Campo valido", value: value.trim() };
};

/**
 * Valida un campo di testo obbligatorio
 * @param {string} value - Valore da validare
 * @returns {Object} - Risultato della validazione
 */
export const validateRequiredTextField = (value) => {
  if (isEmpty(value)) {
    return { valid: false, message: "Il campo non può essere vuoto", value: null };
  }
  return { valid: true, message: "Campo valido", value: value.trim() };
};

/**
 * Valida un campo in base al suo tipo
 * @param {string} fieldName - Nome del campo
 * @param {string|number} value - Valore da validare
 * @returns {Object} - Risultato della validazione
 */
export const validateBobinaField = (fieldName, value) => {
  // Campi che richiedono validazione speciale
  const specialValidations = {
    'metri_totali': () => validateMetriTotali(value),
    'n_conduttori': () => validateNumber(value, "Numero conduttori"),
    'sezione': () => validateNumber(value, "Formazione"),
  };

  // Campi obbligatori
  const requiredFields = [
    'utility', 'tipologia', 'sezione', 'metri_totali'
  ];

  // Campi che possono avere "TBD" come valore predefinito quando vuoti
  const tbdFields = [
    'ubicazione_bobina', 'fornitore', 'n_DDT'
  ];

  // Se il campo richiede validazione speciale, usala
  if (fieldName in specialValidations) {
    return specialValidations[fieldName]();
  }

  // Se il campo è obbligatorio
  if (requiredFields.includes(fieldName)) {
    return validateRequiredTextField(value);
  }

  // Se il campo può avere TBD come valore predefinito
  if (tbdFields.includes(fieldName)) {
    return validateBaseField(value);
  }

  // Per tutti gli altri campi, usa la validazione base
  return validateBaseField(value);
};

/**
 * Valida l'ID della bobina
 * @param {string} value - ID della bobina
 * @returns {Object} - Risultato della validazione
 */
export const validateBobinaId = (value) => {
  // Gestione dei valori nulli o vuoti
  if (isEmpty(value)) {
    return { valid: false, message: "L'ID della bobina è obbligatorio", value: null };
  }

  // Converti sempre in stringa e rimuovi spazi iniziali e finali
  const stringValue = String(value).trim();

  // Verifica che l'ID non sia vuoto dopo il trim
  if (stringValue === '') {
    return { valid: false, message: "L'ID della bobina è obbligatorio", value: null };
  }

  // Verifica che l'ID non contenga caratteri speciali non consentiti
  const invalidChars = /[\s\\/:*?"<>|]/;
  if (invalidChars.test(stringValue)) {
    return {
      valid: false,
      message: "L'ID della bobina non può contenere spazi o caratteri speciali come \\ / : * ? \" < > |",
      value: null
    };
  }

  // Verifica che l'ID non sia troppo lungo
  if (stringValue.length > 50) {
    return {
      valid: false,
      message: "L'ID della bobina non può superare i 50 caratteri",
      value: null
    };
  }

  return { valid: true, message: "", value: stringValue };
};

/**
 * Valida tutti i campi di una bobina
 * @param {Object} bobinaData - Dati della bobina
 * @returns {Object} - Risultato della validazione
 */
export const validateBobinaData = (bobinaData) => {
  const errors = {};
  const warnings = {};
  const validatedData = { ...bobinaData };

  // Assicurati che configurazione sia impostata
  validatedData.configurazione = validatedData.configurazione || 's';

  // Campi da validare
  const fieldsToValidate = [
    'utility', 'tipologia',
    'sezione', 'metri_totali', 'ubicazione_bobina', 'fornitore', 'n_DDT'
  ];

  // Validazione speciale per numero_bobina (parte dell'ID_BOBINA)
  if (validatedData.configurazione === 'n') {
    // In modalità manuale, verifica che il campo numero_bobina sia presente e non vuoto
    if (!validatedData.numero_bobina || String(validatedData.numero_bobina).trim() === '') {
      errors.numero_bobina = "L'ID della bobina è obbligatorio";
    } else {
      const idResult = validateBobinaId(validatedData.numero_bobina);
      if (!idResult.valid) {
        errors.numero_bobina = idResult.message;
      } else {
        validatedData.numero_bobina = idResult.value;
      }
    }
  } else if (validatedData.configurazione === 's') {
    // In modalità automatica, il numero_bobina dovrebbe essere già impostato
    if (!validatedData.numero_bobina || String(validatedData.numero_bobina).trim() === '') {
      // Se per qualche motivo non è impostato, imposta un valore di default
      validatedData.numero_bobina = '1';
    } else {
      // Assicurati che sia una stringa
      validatedData.numero_bobina = String(validatedData.numero_bobina).trim();
    }
  }

  // Validazione degli altri campi
  for (const field of fieldsToValidate) {
    // Assicurati che il campo esista nell'oggetto
    if (validatedData[field] === undefined) {
      if (field === 'metri_totali') {
        errors[field] = "I metri totali sono obbligatori";
        continue;
      } else if (['utility', 'tipologia'].includes(field)) {
        errors[field] = `Il campo ${field} è obbligatorio`;
        continue;
      } else if (field === 'n_conduttori') {
        // Imposta un valore di default per n_conduttori
        validatedData[field] = '0';
        continue;
      } else if (field === 'sezione') {
        // Imposta un valore di default per sezione
        validatedData[field] = '0';
        continue;
      } else {
        // Per i campi non obbligatori, imposta un valore di default
        validatedData[field] = field === 'data_DDT' ? null : 'TBD';
        continue;
      }
    }

    const result = validateBobinaField(field, validatedData[field]);

    if (!result.valid) {
      errors[field] = result.message;
    } else {
      validatedData[field] = result.value;
      if (result.warning) {
        warnings[field] = result.message;
      }
    }
  }

  // Assicurati che i campi numerici siano numeri
  if (!errors.metri_totali) {
    validatedData.metri_totali = parseFloat(validatedData.metri_totali) || 0;
    if (validatedData.metri_totali <= 0) {
      errors.metri_totali = "I metri totali devono essere maggiori di zero";
    }
  }

  // Assicurati che n_conduttori sia una stringa
  validatedData.n_conduttori = validatedData.n_conduttori !== undefined && validatedData.n_conduttori !== null ? validatedData.n_conduttori.toString() : "0";
  // Se è vuoto, imposta a "0"
  if (validatedData.n_conduttori === "") validatedData.n_conduttori = "0";

  // Assicurati che sezione sia una stringa
  validatedData.sezione = validatedData.sezione !== undefined && validatedData.sezione !== null ? validatedData.sezione.toString() : "0";
  // Se è vuoto, imposta a "0"
  if (validatedData.sezione === "") validatedData.sezione = "0";

  // Log dei dati validati
  console.log('Dati validati:', validatedData);
  console.log('Errori di validazione:', errors);
  console.log('Avvisi di validazione:', warnings);

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    warnings,
    validatedData
  };
};
