/**
 * Formatta una data in formato leggibile
 * @param {string|Date} date - Data da formattare
 * @param {boolean} includeTime - Se includere l'ora nella formattazione
 * @returns {string} - Data formattata
 */
export const formatDate = (date, includeTime = true) => {
  if (!date) return '';
  
  try {
    const dateObj = new Date(date);
    
    // Verifica se la data è valida
    if (isNaN(dateObj.getTime())) {
      return date;
    }
    
    // Formatta la data
    const options = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      ...(includeTime ? { hour: '2-digit', minute: '2-digit' } : {})
    };
    
    return dateObj.toLocaleDateString('it-IT', options);
  } catch (error) {
    console.error('Errore nella formattazione della data:', error);
    return date;
  }
};

/**
 * Converte una data in formato ISO
 * @param {string|Date} date - Data da convertire
 * @returns {string} - Data in formato ISO
 */
export const toISODate = (date) => {
  if (!date) return '';
  
  try {
    const dateObj = new Date(date);
    
    // Verifica se la data è valida
    if (isNaN(dateObj.getTime())) {
      return '';
    }
    
    return dateObj.toISOString().split('T')[0];
  } catch (error) {
    console.error('Errore nella conversione della data in ISO:', error);
    return '';
  }
};

/**
 * Verifica se una data è valida
 * @param {string|Date} date - Data da verificare
 * @returns {boolean} - True se la data è valida, false altrimenti
 */
export const isValidDate = (date) => {
  if (!date) return false;
  
  try {
    const dateObj = new Date(date);
    return !isNaN(dateObj.getTime());
  } catch (error) {
    return false;
  }
};
