/**
 * Utility per la navigazione nell'applicazione
 */

/**
 * Reindirizza alla pagina di visualizzazione cavi con un ritardo opzionale
 * @param {function} navigate - Funzione di navigazione di React Router
 * @param {number} delay - Ritardo in millisecondi prima del reindirizzamento (default: 0)
 */
export const redirectToVisualizzaCavi = (navigate, delay = 0) => {
  console.log('Tentativo di reindirizzamento a /dashboard/cavi/visualizza');

  // Funzione di reindirizzamento
  const doRedirect = () => {
    try {
      // Prima prova con navigate
      navigate('/dashboard/cavi/visualizza');
      console.log('Reindirizzamento con navigate eseguito');
    } catch (error) {
      console.error('Errore durante il reindirizzamento con navigate:', error);
      // Se fallisce, usa window.location come fallback
      try {
        window.location.href = '/dashboard/cavi/visualizza';
        console.log('Reindirizzamento con window.location eseguito');
      } catch (locationError) {
        console.error('Errore anche con window.location:', locationError);
        // Ultimo tentativo: ricarica la pagina
        window.location.reload();
      }
    }
  };

  // Esegui con o senza ritardo
  if (delay > 0) {
    console.log(`Reindirizzamento programmato con ritardo di ${delay}ms`);
    setTimeout(doRedirect, delay);
  } else {
    doRedirect();
  }
};

/**
 * Ricarica la pagina corrente con un ritardo opzionale
 * @param {number} delay - Ritardo in millisecondi prima del ricaricamento (default: 0)
 */
export const reloadPage = (delay = 0) => {
  if (delay > 0) {
    setTimeout(() => {
      window.location.reload();
    }, delay);
  } else {
    window.location.reload();
  }
};
