/**
 * Utility per la validazione dei campi dei cavi
 * Implementa le stesse regole di validazione della CLI
 */

// Costanti
export const TBD = "TBD";
export const STATI_INSTALLAZIONE = ["Da installare", "In corso", "Installato", "SPARE"];

/**
 * Verifica se un valore è vuoto
 * @param {string|number|null} value - Valore da verificare
 * @returns {boolean} - True se il valore è vuoto, false altrimenti
 */
export const isEmpty = (value) => {
  return value === null || value === undefined ||
         (typeof value === 'string' && value.trim() === '') ||
         (typeof value === 'number' && isNaN(value));
};

/**
 * Converte un valore in float
 * @param {string|number|null} value - Valore da convertire
 * @returns {number} - Valore convertito in float
 */
export const convertToFloat = (value) => {
  if (isEmpty(value)) return 0;

  if (typeof value === 'number') return value;

  if (typeof value === 'string') {
    const normalized = value.trim().replace(',', '.');
    try {
      return parseFloat(normalized);
    } catch (e) {
      console.warn(`Errore conversione float: '${value}' non è un numero valido.`);
      return 0;
    }
  }

  console.warn(`Tipo di valore non supportato: ${typeof value}`);
  return 0;
};

/**
 * Valida un campo numerico
 * @param {string|number} value - Valore da validare
 * @param {string} fieldName - Nome del campo
 * @returns {Object} - Risultato della validazione
 */
export const validateNumber = (value, fieldName) => {
  try {
    // Gestione campi vuoti
    if (isEmpty(value)) {
      if (fieldName === "Numero conduttori" || fieldName === "Sezione" || fieldName === "Metri teorici") {
        return { valid: true, message: "", value: "0" };
      }
      return { valid: false, message: `${fieldName} non può essere vuoto`, value: null };
    }

    // Normalizzazione input se è stringa
    let normalizedValue = value;
    if (typeof value === 'string') {
      normalizedValue = value.trim().replace(',', '.');
      if (normalizedValue === '.') {
        return { valid: false, message: `${fieldName} non valido`, value: null };
      }
    }

    // Conversione
    const numero = parseFloat(normalizedValue);

    // Validazione numero negativo
    if (numero < 0) {
      return { valid: false, message: `${fieldName} non può essere negativo`, value: null };
    }

    // Validazione limiti specifici
    if (fieldName === "Numero conduttori" && numero > 24) {
      return {
        valid: true,
        message: `ATTENZIONE: Il numero di conduttori (${numero}) supera il limite standard di 24`,
        value: numero.toString(),
        warning: true
      };
    }

    if (fieldName === "Sezione" && numero > 1000) {
      return {
        valid: true,
        message: `ATTENZIONE: La sezione (${numero}) supera il limite standard di 1000`,
        value: numero.toString(),
        warning: true
      };
    }

    return { valid: true, message: "", value: numero.toString() };
  } catch (e) {
    return { valid: false, message: `Il valore inserito per ${fieldName} non è un numero valido`, value: null };
  }
};

/**
 * Normalizza lo stato di installazione per utilizzare i valori dell'enum StatoInstallazione
 * @param {string} stato - Stato di installazione da normalizzare
 * @returns {string} - Stato normalizzato
 */
export const normalizeInstallationStatus = (stato) => {
  if (isEmpty(stato)) return 'Da installare'; // Valore predefinito dall'enum

  // Mappa gli stati usati nel backend agli stati dell'enum
  const statoLower = stato.toLowerCase();

  if (statoLower.includes('posato') || statoLower.includes('installato')) {
    return 'Installato';
  } else if (statoLower.includes('da posare') || statoLower.includes('da installare')) {
    return 'Da installare';
  } else if (statoLower.includes('in corso') || statoLower.includes('in posa')) {
    return 'In corso';
  }

  return stato; // Ritorna lo stato originale se non corrisponde a nessuno dei casi
};

/**
 * Valida lo stato di installazione
 * @param {string} value - Valore da validare
 * @returns {Object} - Risultato della validazione
 */
export const validateStatoInstallazione = (value) => {
  if (isEmpty(value)) {
    return { valid: true, message: "", value: TBD };
  }

  // Normalizza lo stato prima di validarlo
  const normalizedValue = normalizeInstallationStatus(value);
  if (STATI_INSTALLAZIONE.includes(normalizedValue)) {
    return { valid: true, message: "", value: normalizedValue };
  }

  return {
    valid: false,
    message: `Stato di installazione non valido. Stati ammessi: ${STATI_INSTALLAZIONE.join(', ')}`,
    value: null
  };
};

/**
 * Valida il campo SH (schermato)
 * @param {string} value - Valore da validare
 * @returns {Object} - Risultato della validazione
 */
export const validateSH = (value) => {
  // Se il valore è vuoto, restituisci 'N' come valore di default
  if (isEmpty(value)) {
    return { valid: true, message: "", value: "N" };
  }

  const normalizedValue = value.trim().toUpperCase();

  // Valori positivi
  if (['S', 'SI', 'Y', 'YES'].includes(normalizedValue)) {
    return { valid: true, message: "", value: "S" };
  }

  // Valori negativi
  if (['N', 'NO'].includes(normalizedValue)) {
    return { valid: true, message: "", value: "N" };
  }

  return { valid: false, message: "Valore non valido per SH. Inserire 'S' o 'N'.", value: null };
};

/**
 * Valida un campo di testo base
 * @param {string} value - Valore da validare
 * @returns {Object} - Risultato della validazione
 */
export const validateBaseField = (value) => {
  if (isEmpty(value)) {
    return { valid: true, message: "Campo vuoto", value: TBD };
  }
  return { valid: true, message: "Campo valido", value: value.trim() };
};

/**
 * Valida un campo di testo obbligatorio
 * @param {string} value - Valore da validare
 * @returns {Object} - Risultato della validazione
 */
export const validateRequiredTextField = (value) => {
  if (isEmpty(value)) {
    return { valid: false, message: "Il campo non può essere vuoto", value: null };
  }
  return { valid: true, message: "Campo valido", value: value.trim() };
};

/**
 * Valida i metri teorici
 * @param {string|number} value - Valore da validare
 * @returns {Object} - Risultato della validazione
 */
export const validateMetriTeorici = (value) => {
  try {
    if (isEmpty(value)) {
      return { valid: false, message: "I metri teorici sono obbligatori", value: null };
    }

    const val = convertToFloat(value);

    if (val <= 0) {
      return { valid: false, message: "I metri teorici devono essere maggiori di zero", value: null };
    }

    if (val > 100000) {  // 100km come limite ragionevole
      return { valid: false, message: "I metri teorici non possono superare 100.000", value: null };
    }

    return { valid: true, message: "Valore valido", value: val };
  } catch (e) {
    return { valid: false, message: "Il valore deve essere un numero valido", value: null };
  }
};

/**
 * Valida la metratura reale
 * @param {string|number} value - Valore da validare
 * @param {number} metriTeorici - Metri teorici di riferimento
 * @returns {Object} - Risultato della validazione
 */
export const validateMetraturaReale = (value, metriTeorici) => {
  try {
    if (isEmpty(value)) {
      return { valid: true, message: "", value: 0 };
    }

    const val = convertToFloat(value);

    if (val < 0) {
      return { valid: false, message: "La metratura reale non può essere negativa", value: null };
    }

    if (val > metriTeorici * 1.1) {  // 10% di tolleranza
      return {
        valid: true,
        message: `ATTENZIONE: La metratura reale (${val}m) supera del ${((val / metriTeorici) - 1) * 100}% i metri teorici (${metriTeorici}m)`,
        value: val,
        warning: true
      };
    }

    return { valid: true, message: "", value: val };
  } catch (e) {
    return { valid: false, message: "Inserire un numero valido per la metratura reale", value: null };
  }
};

/**
 * Valida un campo in base al suo tipo
 * @param {string} fieldName - Nome del campo
 * @param {string|number} value - Valore da validare
 * @param {Object} additionalParams - Parametri aggiuntivi per la validazione
 * @returns {Object} - Risultato della validazione
 */
export const validateField = (fieldName, value, additionalParams = {}) => {
  // Campi che richiedono validazione speciale
  const specialValidations = {
    'metri_teorici': () => validateMetriTeorici(value),
    'metratura_reale': () => validateMetraturaReale(value, additionalParams.metriTeorici || 0),
    'stato_installazione': () => validateStatoInstallazione(value),
    'n_conduttori': () => validateNumber(value, "Numero conduttori"),
    'sezione': () => validateNumber(value, "Sezione"),
    'sh': () => validateSH(value),
  };

  // Campi che devono avere "TBD" come valore predefinito quando vuoti
  const tbdFields = [
    'sistema', 'utility', 'colore_cavo', 'tipologia',
    'ubicazione_partenza', 'utenza_partenza', 'ubicazione_arrivo', 'utenza_arrivo',
    'descrizione_utenza_partenza', 'descrizione_utenza_arrivo'
  ];

  // Se il campo richiede validazione speciale, usala
  if (fieldName in specialValidations) {
    return specialValidations[fieldName]();
  }

  // Se il campo deve avere TBD come valore predefinito
  if (tbdFields.includes(fieldName)) {
    if (isEmpty(value)) {
      return { valid: true, message: "", value: TBD };
    }
    return { valid: true, message: "", value: value.trim() };
  }

  // Per tutti gli altri campi, usa la validazione base
  return validateBaseField(value);
};

/**
 * Valida tutti i campi di un cavo
 * @param {Object} cavoData - Dati del cavo
 * @returns {Object} - Risultato della validazione
 */
export const validateCavoData = (cavoData) => {
  const errors = {};
  const warnings = {};
  const validatedData = { ...cavoData };

  // Campi da validare
  const fieldsToValidate = [
    'id_cavo', 'revisione_ufficiale', 'sistema', 'utility', 'colore_cavo',
    'tipologia', 'n_conduttori', 'sezione', 'sh', 'ubicazione_partenza',
    'utenza_partenza', 'descrizione_utenza_partenza', 'ubicazione_arrivo',
    'utenza_arrivo', 'descrizione_utenza_arrivo', 'metri_teorici'
  ];

  // Validazione speciale per id_cavo
  if (isEmpty(cavoData.id_cavo)) {
    errors.id_cavo = "L'ID cavo è obbligatorio";
  }

  // Validazione degli altri campi
  for (const field of fieldsToValidate) {
    if (field === 'id_cavo') continue; // Già validato

    const additionalParams = {};
    if (field === 'metratura_reale') {
      additionalParams.metriTeorici = convertToFloat(cavoData.metri_teorici);
    }

    const result = validateField(field, cavoData[field], additionalParams);

    if (!result.valid) {
      errors[field] = result.message;
    } else {
      validatedData[field] = result.value;
      if (result.warning) {
        warnings[field] = result.message;
      }
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    warnings,
    validatedData
  };
};
