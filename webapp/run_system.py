# run_system.py
import subprocess
import sys
import os
import time
import signal
import socket
try:
    import requests
except ImportError:
    print("Error: The 'requests' module is not installed. Please install it using 'pip install requests'")
    sys.exit(1)
from threading import Thread
from pathlib import Path


def is_port_in_use(port):
    """Verifica se una porta è già in uso"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0


def kill_process_on_port(port):
    """Termina il processo che occupa una porta specifica"""
    try:
        if sys.platform == "win32":
            # Versione migliorata per Windows che ottiene il PID corretto
            result = subprocess.run(
                f"netstat -ano | findstr :{port} | findstr LISTENING",
                shell=True,
                capture_output=True,
                text=True
            )

            if result.stdout:
                # Estrai i PID dalle righe di output
                for line in result.stdout.strip().split('\n'):
                    if f":{port}" in line and "LISTENING" in line:
                        parts = line.strip().split()
                        if len(parts) > 4:
                            pid = parts[-1]
                            if pid != "0":
                                print(f"Terminazione processo con PID {pid} sulla porta {port}")
                                try:
                                    kill_result = subprocess.run(f"taskkill /F /PID {pid}", shell=True, capture_output=True, text=True)
                                    if kill_result.returncode == 0:
                                        print(f"Processo con PID {pid} terminato con successo")
                                    else:
                                        print(f"Errore durante la terminazione del processo con PID {pid}: {kill_result.stderr}")
                                except Exception as e:
                                    print(f"Eccezione durante la terminazione del processo con PID {pid}: {e}")
            else:
                print(f"Nessun processo LISTENING trovato sulla porta {port}")
        else:
            subprocess.run(f"lsof -ti:{port} | xargs kill -9", shell=True)

        # Attendi che la porta venga liberata
        for _ in range(10):  # Aumentato a 10 secondi
            if not is_port_in_use(port):
                print(f"Porta {port} liberata con successo")
                return True
            time.sleep(1)

        # Se non siamo riusciti a liberare la porta, proviamo a usare una porta diversa
        if is_port_in_use(port):
            print(f"Impossibile liberare la porta {port} dopo diversi tentativi")
            return False
        return True
    except Exception as e:
        print(f"Errore durante la terminazione del processo sulla porta {port}: {e}")
        return False


def run_fastapi(port=8001, max_port_attempts=5):
    """Avvia il server FastAPI (backend)"""
    # Salva la directory corrente
    original_dir = os.getcwd()
    backend_dir = Path(__file__).parent / "backend"

    # Verifica che la directory del backend esista
    if not backend_dir.exists():
        print(f"Errore: La directory del backend non esiste: {backend_dir}")
        return None, port

    # Verifica che il file main.py esista
    main_py = backend_dir / "main.py"
    if not main_py.exists():
        print(f"Errore: Il file main.py non esiste: {main_py}")
        return None, port

    os.chdir(backend_dir)
    print(f"Avvio del server FastAPI (backend) sulla porta {port}...")
    print(f"Directory: {os.getcwd()}")

    # Prova porte consecutive fino a trovarne una libera
    current_port = port
    port_attempts = 0

    while port_attempts < max_port_attempts:
        # Verifica se la porta è già in uso
        if is_port_in_use(current_port):
            print(f"La porta {current_port} è già in uso. Tentativo di liberare la porta...")
            if kill_process_on_port(current_port):
                # Porta liberata con successo
                break
            else:
                # Se non possiamo liberare la porta, proviamo con la porta successiva
                port_attempts += 1
                current_port = port + port_attempts
                print(f"Impossibile liberare la porta {current_port-1}. Provo con la porta {current_port}")
        else:
            # Porta libera trovata
            break

    # Se abbiamo esaurito tutti i tentativi
    if port_attempts >= max_port_attempts:
        print(f"Impossibile trovare una porta libera dopo {max_port_attempts} tentativi.")
        print("Verifica manualmente i processi in esecuzione.")
        os.chdir(original_dir)  # Ripristina la directory originale
        return None, port

    # Utilizziamo Popen invece di run per non bloccare il thread
    try:
        # Crea un file di log per il backend
        log_file = open("backend_log.txt", "w")

        # Modifica il comando per specificare la porta e aumentare il livello di log
        cmd = [sys.executable, "-m", "uvicorn", "main:app", "--host", "0.0.0.0", f"--port={current_port}", "--log-level=debug"]

        print(f"Esecuzione comando: {' '.join(cmd)}")

        if sys.platform == "win32":
            process = subprocess.Popen(
                cmd,
                stdout=log_file,
                stderr=subprocess.STDOUT
            )
        else:
            process = subprocess.Popen(
                cmd,
                stdout=log_file,
                stderr=subprocess.STDOUT
            )

        print(f"Backend avviato sulla porta {current_port}. Log disponibile in backend_log.txt")

        # Attendi un po' per assicurarsi che il server si avvii
        print("Attesa avvio del server backend...")
        server_started = False
        for i in range(15):  # Aumentato a 15 secondi
            time.sleep(1)
            print(f"Attesa: {i+1}/15", end="\r")
            # Verifica se il server è in ascolto sulla porta specificata
            if is_port_in_use(current_port):
                print(f"\nServer backend avviato con successo sulla porta {current_port}!")
                server_started = True
                break

        if not server_started:
            print("\nAttenzione: Il server backend potrebbe non essere stato avviato correttamente.")
            print("Verifica il file di log backend_log.txt per maggiori dettagli.")
            # Leggi e mostra le ultime righe del log
            try:
                with open("backend_log.txt", "r") as f:
                    log_lines = f.readlines()
                    if log_lines:
                        print("\nUltime righe del log:")
                        for line in log_lines[-10:]:  # Mostra le ultime 10 righe
                            print(line.strip())
            except Exception as e:
                print(f"Errore durante la lettura del log: {e}")

        # Ripristina la directory originale
        os.chdir(original_dir)
        return process, current_port
    except Exception as e:
        print(f"Errore durante l'avvio del backend: {e}")
        os.chdir(original_dir)  # Ripristina la directory originale
        return None, port


def run_react(backend_port=8001):
    """Avvia il server di sviluppo React (frontend)"""
    # Salva la directory corrente
    original_dir = os.getcwd()
    frontend_dir = Path(__file__).parent / "frontend"

    # Verifica che la directory del frontend esista
    if not frontend_dir.exists():
        print(f"Errore: La directory del frontend non esiste: {frontend_dir}")
        return None

    # Verifica che il file package.json esista
    package_json = frontend_dir / "package.json"
    if not package_json.exists():
        print(f"Errore: Il file package.json non esiste: {package_json}")
        return None

    os.chdir(frontend_dir)
    print("Avvio del server React (frontend) sulla porta 3000...")
    print(f"Directory: {os.getcwd()}")

    # Verifica se npm è installato
    try:
        npm_version = subprocess.run(
            "npm --version",
            shell=True,
            capture_output=True,
            text=True
        )
        if npm_version.returncode != 0:
            print("Errore: npm non è installato o non è disponibile nel PATH.")
            os.chdir(original_dir)  # Ripristina la directory originale
            return None
        print(f"Versione npm: {npm_version.stdout.strip()}")
    except Exception as e:
        print(f"Errore durante la verifica di npm: {e}")
        os.chdir(original_dir)  # Ripristina la directory originale
        return None

    # Verifica se la porta è già in uso
    if is_port_in_use(3000):
        print("La porta 3000 è già in uso. Tentativo di liberare la porta...")
        if not kill_process_on_port(3000):
            print("Impossibile liberare la porta 3000. Verifica manualmente i processi in esecuzione.")
            os.chdir(original_dir)  # Ripristina la directory originale
            return None

    # Aggiorna il file di configurazione del frontend
    try:
        config_file = frontend_dir / "src" / "config.js"
        if config_file.exists():
            print(f"Aggiornamento del file di configurazione {config_file} con la porta del backend {backend_port}...")
            with open(config_file, "r") as f:
                config_content = f.read()

            # Aggiorna la porta del backend nel file di configurazione
            import re
            updated_content = re.sub(
                r"API_URL: 'http://localhost:\d+/api'",
                f"API_URL: 'http://localhost:{backend_port}/api'",
                config_content
            )

            with open(config_file, "w") as f:
                f.write(updated_content)

            print(f"File di configurazione aggiornato con successo.")
    except Exception as e:
        print(f"Errore durante l'aggiornamento del file di configurazione: {e}")
        # Continuiamo comunque, utilizzeremo le variabili d'ambiente

    # Utilizziamo Popen invece di run per non bloccare il thread
    try:
        # Crea un file di log per il frontend
        log_file = open("frontend_log.txt", "w")

        # Imposta la variabile d'ambiente per la porta del backend
        env = os.environ.copy()
        env["REACT_APP_API_URL"] = f"http://localhost:{backend_port}/api"

        if sys.platform == "win32":
            # Utilizziamo cmd.exe esplicitamente per evitare problemi con PowerShell
            cmd = f"cmd.exe /c set REACT_APP_API_URL=http://localhost:{backend_port}/api && npm start"
            process = subprocess.Popen(
                cmd,
                shell=True,
                stdout=log_file,
                stderr=subprocess.STDOUT,
                env=env
            )
        else:
            process = subprocess.Popen(
                ["npm", "start"],
                stdout=log_file,
                stderr=subprocess.STDOUT,
                env=env
            )

        print(f"Frontend avviato con API URL: http://localhost:{backend_port}/api")
        print("Log disponibile in frontend_log.txt")

        # Attendi un po' per assicurarsi che il server si avvii
        print("Attesa avvio del server frontend...")
        server_started = False
        for i in range(30):  # Aumentato a 30 secondi, il frontend potrebbe richiedere più tempo
            time.sleep(1)
            print(f"Attesa: {i+1}/30", end="\r")
            # Verifica se il server è in ascolto sulla porta 3000
            if is_port_in_use(3000):
                print("\nServer frontend avviato con successo!")
                server_started = True
                break

        if not server_started:
            print("\nAttenzione: Il server frontend potrebbe non essere stato avviato correttamente.")
            print("Verifica il file di log frontend_log.txt per maggiori dettagli.")
            # Leggi e mostra le ultime righe del log
            try:
                with open("frontend_log.txt", "r") as f:
                    log_lines = f.readlines()
                    if log_lines:
                        print("\nUltime righe del log:")
                        for line in log_lines[-10:]:  # Mostra le ultime 10 righe
                            print(line.strip())
            except Exception as e:
                print(f"Errore durante la lettura del log: {e}")

        # Ripristina la directory originale
        os.chdir(original_dir)
        return process
    except Exception as e:
        print(f"Errore durante l'avvio del frontend: {e}")
        os.chdir(original_dir)  # Ripristina la directory originale
        return None


def check_backend_connection(port=8001, max_attempts=3, timeout=2):
    """Verifica la connessione al backend"""
    for attempt in range(1, max_attempts + 1):
        try:
            print(f"Tentativo {attempt}/{max_attempts} di connessione al backend sulla porta {port}...")
            response = requests.get(f"http://localhost:{port}/api/health", timeout=timeout)
            if response.status_code == 200:
                print(f"Connessione al backend sulla porta {port} verificata con successo!")
                return True
            else:
                print(f"Errore nella connessione al backend sulla porta {port}: Status code {response.status_code}")
                # Proviamo anche altri endpoint comuni
                try:
                    response = requests.get(f"http://localhost:{port}/docs", timeout=timeout)
                    if response.status_code == 200:
                        print(f"Connessione al backend sulla porta {port} verificata tramite /docs!")
                        return True
                except:
                    pass  # Ignoriamo errori qui, continuiamo con i tentativi
        except requests.exceptions.ConnectionError:
            print(f"Errore: Impossibile connettersi al backend sulla porta {port}.")
        except requests.exceptions.Timeout:
            print(f"Timeout durante la connessione al backend sulla porta {port}.")
        except Exception as e:
            print(f"Errore durante la verifica della connessione al backend sulla porta {port}: {e}")

        if attempt < max_attempts:
            print(f"Attesa prima del prossimo tentativo...")
            time.sleep(2)  # Attendi 2 secondi prima del prossimo tentativo

    print(f"Impossibile connettersi al backend dopo {max_attempts} tentativi.")
    return False


def signal_handler(sig, frame, processes=None):
    """Gestisce il segnale di interruzione (Ctrl+C)"""
    print("\nTerminazione dei server in corso...")

    # Termina i processi avviati
    if processes:
        for process in processes:
            if process and process.poll() is None:  # Se il processo è ancora in esecuzione
                try:
                    # Invia SIGTERM per una chiusura pulita
                    process.terminate()
                    # Attendi che il processo termini, ma con timeout
                    for _ in range(10):  # Attendi fino a 5 secondi
                        if process.poll() is not None:
                            break
                        time.sleep(0.5)

                    # Se il processo è ancora in esecuzione, forzane la chiusura
                    if process.poll() is None:
                        print("Forzatura della chiusura del processo...")
                        process.kill()
                except Exception as e:
                    print(f"Errore durante la terminazione del processo: {e}")

    # Assicurati che le porte siano liberate
    if is_port_in_use(8001):
        kill_process_on_port(8001)
    if is_port_in_use(3000):
        kill_process_on_port(3000)

    sys.exit(0)


if __name__ == "__main__":
    # Configura il gestore del segnale per Ctrl+C
    signal.signal(signal.SIGINT, lambda sig, frame: signal_handler(sig, frame, []))

    print("Percorso dello script:", Path(__file__).parent)
    print("Avvio del sistema CMS...")
    print("Backend (FastAPI): http://localhost:8001 (o porta alternativa se occupata)")
    print("Frontend (React): http://localhost:3000")
    print("Premi Ctrl+C per terminare entrambi i server\n")

    # Avvia il backend con un massimo di 5 tentativi di porte diverse
    backend_process, backend_port = run_fastapi(port=8001, max_port_attempts=5)
    if not backend_process:
        print("Errore: Impossibile avviare il backend. Verifica i log per maggiori dettagli.")
        sys.exit(1)

    # Verifica la connessione al backend con 5 tentativi e timeout di 3 secondi
    print(f"\nVerifica della connessione al backend sulla porta {backend_port}...")
    if not check_backend_connection(backend_port, max_attempts=5, timeout=3):
        print("Avviso: Impossibile verificare la connessione al backend.")
        print("Il sistema continuerà comunque l'avvio del frontend, ma potrebbero verificarsi errori.")
        print("Verifica il file di log backend_log.txt per maggiori dettagli.")

    # Avvia il frontend
    frontend_process = run_react(backend_port)
    if not frontend_process:
        print("Errore: Impossibile avviare il frontend. Verifica i log per maggiori dettagli.")
        # Termina il backend se il frontend non può essere avviato
        if backend_process:
            print("Terminazione del backend in corso...")
            signal_handler(None, None, [backend_process])
        sys.exit(1)

    # Aggiorna il gestore del segnale con i processi avviati
    signal.signal(signal.SIGINT, lambda sig, frame: signal_handler(sig, frame, [backend_process, frontend_process]))

    print("\nSistema CMS avviato con successo!")
    print(f"Backend: http://localhost:{backend_port}")
    print("Frontend: http://localhost:3000")
    print("Premi Ctrl+C per terminare entrambi i server")

    # Mantiene il programma in esecuzione
    try:
        while True:
            # Verifica se i processi sono ancora in esecuzione
            backend_running = backend_process and backend_process.poll() is None
            frontend_running = frontend_process and frontend_process.poll() is None

            if not backend_running and not frontend_running:
                print("Entrambi i processi sono terminati. Uscita in corso...")
                break
            elif not backend_running:
                print("Il processo backend è terminato. Terminazione del frontend in corso...")
                if frontend_process:
                    frontend_process.terminate()
                break
            elif not frontend_running:
                print("Il processo frontend è terminato. Terminazione del backend in corso...")
                if backend_process:
                    backend_process.terminate()
                break

            time.sleep(1)
    except KeyboardInterrupt:
        print("\nInterruzione richiesta dall'utente. Terminazione dei processi in corso...")
        signal_handler(None, None, [backend_process, frontend_process])
    finally:
        # Assicurati che i processi vengano terminati anche in caso di altri errori
        signal_handler(None, None, [backend_process, frontend_process])