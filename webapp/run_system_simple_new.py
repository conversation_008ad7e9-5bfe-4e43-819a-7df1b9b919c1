#!/usr/bin/env python3
# run_system_simple.py - Script di avvio semplice per il sistema CMS
import subprocess
import sys
import os
import time
import signal
from pathlib import Path

def run_backend():
    """Avvia il server FastAPI (backend)"""
    print("🚀 Avvio del backend...")

    # Ottieni il percorso assoluto della directory backend
    backend_dir = Path(__file__).resolve().parent / "backend"

    # Verifica che la directory esista
    if not backend_dir.exists():
        print(f"❌ La directory del backend non esiste: {backend_dir}")
        return None

    # Cambia directory
    original_dir = os.getcwd()
    os.chdir(backend_dir)

    # Comando per avviare il backend
    cmd = [sys.executable, "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8001", "--reload"]
    print(f"📝 Esecuzione comando: {' '.join(cmd)}")

    try:
        # Avvia il processo
        process = subprocess.Popen(cmd)

        # Attendi un po' per assicurarsi che il server si avvii
        time.sleep(5)
        
        print("✅ Backend avviato con successo!")
        return process
    except Exception as e:
        print(f"❌ Errore durante l'avvio del backend: {e}")
        return None
    finally:
        # Torna alla directory originale
        os.chdir(original_dir)

def run_frontend():
    """Avvia il server React (frontend)"""
    print("🚀 Avvio del frontend...")

    # Ottieni il percorso assoluto della directory frontend
    frontend_dir = Path(__file__).resolve().parent / "frontend"

    # Verifica che la directory esista
    if not frontend_dir.exists():
        print(f"❌ La directory del frontend non esiste: {frontend_dir}")
        return None

    # Cambia directory
    original_dir = os.getcwd()
    os.chdir(frontend_dir)

    # Comando per avviare il frontend
    if os.name == 'nt':  # Windows
        cmd = ["cmd", "/c", "npm", "start"]
    else:  # Linux/Mac
        cmd = ["npm", "start"]
    
    print(f"📝 Esecuzione comando: {' '.join(cmd)}")

    try:
        # Imposta variabili d'ambiente per React
        env = os.environ.copy()
        env['REACT_APP_API_URL'] = 'http://localhost:8001'
        env['BROWSER'] = 'none'  # Non aprire automaticamente il browser
        
        # Avvia il processo
        process = subprocess.Popen(cmd, env=env)

        # Attendi un po' per assicurarsi che il server si avvii
        time.sleep(8)
        
        print("✅ Frontend avviato con successo!")
        return process
    except Exception as e:
        print(f"❌ Errore durante l'avvio del frontend: {e}")
        return None
    finally:
        # Torna alla directory originale
        os.chdir(original_dir)

def main():
    """Funzione principale"""
    print("\n🚀 === AVVIO SISTEMA CMS ===\n")

    # Salva la directory corrente
    original_dir = os.getcwd()

    try:
        # Avvia il backend
        backend_process = run_backend()
        if not backend_process:
            print("❌ Impossibile avviare il backend.")
            return False

        # Avvia il frontend
        frontend_process = run_frontend()
        if not frontend_process:
            print("❌ Impossibile avviare il frontend.")
            # Termina il backend
            backend_process.terminate()
            return False

        # Sistema avviato con successo
        print("\n🎉 === SISTEMA CMS AVVIATO CON SUCCESSO! ===")
        print("🌐 Backend API: http://localhost:8001")
        print("🌐 Frontend: http://localhost:3000")
        print("📚 Documentazione API: http://localhost:8001/docs")
        print("⚡ Premi Ctrl+C per terminare entrambi i server\n")

        # Gestione del segnale di interruzione
        def signal_handler(sig, frame):
            print("\n🛑 Terminazione dei server in corso...")
            try:
                if frontend_process and frontend_process.poll() is None:
                    frontend_process.terminate()
                    print("✅ Frontend terminato")
                if backend_process and backend_process.poll() is None:
                    backend_process.terminate()
                    print("✅ Backend terminato")
            except Exception as e:
                print(f"❌ Errore durante la terminazione: {e}")
            
            print("👋 Sistema CMS terminato. Arrivederci!")
            sys.exit(0)

        # Registra il gestore del segnale
        signal.signal(signal.SIGINT, signal_handler)

        # Mantiene il programma in esecuzione
        try:
            while True:
                # Verifica che i processi siano ancora in esecuzione
                if backend_process.poll() is not None:
                    print("❌ Il backend si è chiuso inaspettatamente")
                    break
                if frontend_process.poll() is not None:
                    print("❌ Il frontend si è chiuso inaspettatamente")
                    break
                time.sleep(2)
        except KeyboardInterrupt:
            # Questo blocco non dovrebbe essere mai raggiunto grazie al signal_handler
            pass

    finally:
        # Torna alla directory originale
        os.chdir(original_dir)

    return True

if __name__ == "__main__":
    main()
