/**
 * Benefits Page Specific Styles
 * For CMS Gestione Cantieri e Certificazioni
 */

/* Page Hero - Similar to features page but with different background */
.page-hero {
    background-color: var(--primary-dark);
    color: var(--white);
    padding: 6rem 0 3rem;
    text-align: center;
    margin-top: 70px; /* Height of the header */
}

.page-hero h1 {
    color: var(--white);
    margin-bottom: var(--spacing-md);
    font-size: 2.8rem;
    animation: fadeInUp 0.8s ease;
}

.page-hero p {
    max-width: 800px;
    margin: 0 auto;
    font-size: 1.2rem;
    opacity: 0.9;
    animation: fadeInUp 0.8s ease 0.2s;
    animation-fill-mode: both;
}

/* Benefits Overview Section */
.benefits-overview {
    padding: var(--spacing-xl) 0;
    background-color: var(--white);
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

/* Benefit Cards */
.benefit-card {
    background-color: var(--background-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.benefit-icon {
    width: 80px;
    height: 80px;
    background-color: rgba(44, 82, 130, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-md);
}

.benefit-icon i {
    font-size: 2.5rem;
    color: var(--primary-color);
}

.benefit-card h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--primary-dark);
    font-size: 1.5rem;
}

.benefit-card > p {
    color: var(--text-dark);
    margin-bottom: var(--spacing-md);
    font-size: 1.1rem;
}

.benefit-list {
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-md);
}

.benefit-list li {
    margin-bottom: var(--spacing-sm);
    position: relative;
    padding-left: var(--spacing-md);
    list-style-type: none;
}

.benefit-list li::before {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    left: 0;
    color: var(--primary-color);
}

.benefit-testimonial {
    background-color: rgba(44, 82, 130, 0.05);
    border-left: 3px solid var(--primary-color);
    padding: var(--spacing-md);
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
    margin-top: auto;
}

.benefit-testimonial p {
    font-style: italic;
    color: var(--text-dark);
    margin-bottom: var(--spacing-xs);
    font-size: 0.95rem;
}

.benefit-testimonial cite {
    font-style: normal;
    font-weight: 600;
    color: var(--primary-color);
    font-size: 0.9rem;
}

/* ROI Section */
.roi-section {
    padding: var(--spacing-xl) 0;
    background-color: var(--background-light);
}

.roi-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
    margin-top: var(--spacing-lg);
}

.roi-text p {
    font-size: 1.1rem;
    margin-bottom: var(--spacing-lg);
}

.roi-metrics {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    margin: var(--spacing-lg) 0;
}

.roi-metric {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    text-align: center;
    box-shadow: var(--shadow-sm);
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.metric-label {
    color: var(--text-dark);
    font-size: 0.9rem;
}

.roi-chart {
    position: relative;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.roi-chart img {
    width: 100%;
    height: auto;
    display: block;
}

.chart-caption {
    background-color: rgba(0, 0, 0, 0.7);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.9rem;
    text-align: center;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
}

/* Comparison Section */
.comparison-section {
    padding: var(--spacing-xl) 0;
    background-color: var(--white);
}

.comparison-table-container {
    overflow-x: auto;
    margin-top: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    border-radius: var(--border-radius-md);
}

.comparison-table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--white);
}

.comparison-table th,
.comparison-table td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.comparison-table th {
    background-color: var(--primary-color);
    color: var(--white);
    font-weight: 600;
}

.comparison-table th:first-child {
    border-top-left-radius: var(--border-radius-md);
}

.comparison-table th:last-child {
    border-top-right-radius: var(--border-radius-md);
}

.comparison-table tr:last-child td {
    border-bottom: none;
}

.comparison-table tr:nth-child(even) {
    background-color: rgba(44, 82, 130, 0.05);
}

.comparison-table td:first-child {
    font-weight: 600;
    color: var(--primary-dark);
}

.comparison-table td:nth-child(2) {
    color: var(--text-light);
}

.comparison-table td:nth-child(3) {
    color: var(--primary-color);
    font-weight: 500;
}

/* CTA Buttons */
.cta-buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    margin-top: var(--spacing-lg);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .page-hero h1 {
        font-size: 2.4rem;
    }
    
    .page-hero p {
        font-size: 1.1rem;
    }
    
    .benefits-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
    
    .roi-content {
        gap: var(--spacing-lg);
    }
    
    .metric-value {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .benefits-grid {
        grid-template-columns: 1fr;
    }
    
    .roi-content {
        grid-template-columns: 1fr;
    }
    
    .roi-metrics {
        grid-template-columns: 1fr 1fr;
    }
    
    .cta-buttons {
        flex-direction: column;
        max-width: 300px;
        margin-left: auto;
        margin-right: auto;
    }
}

@media (max-width: 576px) {
    .roi-metrics {
        grid-template-columns: 1fr;
    }
    
    .comparison-table th,
    .comparison-table td {
        padding: var(--spacing-sm);
        font-size: 0.9rem;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Add animation to elements */
.benefit-card {
    opacity: 0;
    animation: fadeInUp 0.8s ease forwards;
}

.benefit-card:nth-child(2) {
    animation-delay: 0.2s;
}

.benefit-card:nth-child(3) {
    animation-delay: 0.4s;
}

.benefit-card:nth-child(4) {
    animation-delay: 0.6s;
}

.benefit-card:nth-child(5) {
    animation-delay: 0.8s;
}

.benefit-card:nth-child(6) {
    animation-delay: 1s;
}

.roi-content {
    opacity: 0;
    animation: fadeInUp 0.8s ease 0.2s forwards;
}

.comparison-table-container {
    opacity: 0;
    animation: fadeInUp 0.8s ease 0.4s forwards;
}