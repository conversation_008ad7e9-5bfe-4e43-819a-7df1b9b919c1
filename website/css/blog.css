/* Blog Page Styles */

/* Blog Content Section */
.blog-content {
    padding: 80px 0;
    background-color: #fff;
}

.blog-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
}

/* Main Content */
.blog-main {
    width: 100%;
}

/* Featured Article */
.blog-post.featured {
    margin-bottom: 50px;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.blog-post.featured:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.blog-post .post-image {
    position: relative;
    height: 400px;
    overflow: hidden;
}

.blog-post.featured .post-image {
    height: 450px;
}

.blog-post .post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.blog-post:hover .post-image img {
    transform: scale(1.05);
}

.blog-post .post-category {
    position: absolute;
    top: 20px;
    left: 20px;
    background-color: #4299e1;
    color: #fff;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    z-index: 1;
}

.blog-post .post-content {
    padding: 30px;
}

.blog-post .post-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    color: #718096;
    font-size: 14px;
}

.blog-post .post-title {
    margin: 0 0 15px 0;
    color: #2c5282;
    font-size: 24px;
    line-height: 1.4;
}

.blog-post.featured .post-title {
    font-size: 28px;
}

.blog-post .post-excerpt {
    margin: 0 0 20px 0;
    color: #555;
    line-height: 1.8;
}

.blog-post .read-more {
    display: inline-flex;
    align-items: center;
    color: #4299e1;
    font-weight: 600;
    text-decoration: none;
    transition: color 0.3s ease;
}

.blog-post .read-more i {
    margin-left: 5px;
    transition: transform 0.3s ease;
}

.blog-post .read-more:hover {
    color: #2c5282;
}

.blog-post .read-more:hover i {
    transform: translateX(5px);
}

/* Blog Posts Grid */
.blog-posts-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-bottom: 50px;
}

.blog-post {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.blog-post:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

.pagination a, .pagination span {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    padding: 0 10px;
    border-radius: 4px;
    background-color: #f1f5f9;
    color: #4a5568;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.pagination a.active {
    background-color: #4299e1;
    color: #fff;
}

.pagination a:hover:not(.active) {
    background-color: #e2e8f0;
}

.pagination a.next {
    padding: 0 15px;
}

.pagination a.next i {
    margin-left: 5px;
}

/* Sidebar */
.blog-sidebar {
    width: 100%;
}

.sidebar-widget {
    background-color: #fff;
    border-radius: 8px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.sidebar-widget h3 {
    margin: 0 0 20px 0;
    color: #2c5282;
    font-size: 20px;
    position: relative;
    padding-bottom: 10px;
}

.sidebar-widget h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: #4299e1;
}

/* Search Widget */
.search-form {
    display: flex;
    position: relative;
}

.search-form input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 16px;
    color: #2d3748;
}

.search-form button {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 50px;
    background-color: #4299e1;
    color: #fff;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.search-form button:hover {
    background-color: #2c5282;
}

/* Categories Widget */
.categories-widget ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.categories-widget li {
    border-bottom: 1px solid #e2e8f0;
}

.categories-widget li:last-child {
    border-bottom: none;
}

.categories-widget a {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    color: #4a5568;
    text-decoration: none;
    transition: color 0.3s ease;
}

.categories-widget a:hover {
    color: #4299e1;
}

.categories-widget span {
    background-color: #f1f5f9;
    color: #4a5568;
    padding: 2px 8px;
    border-radius: 20px;
    font-size: 12px;
}

/* Popular Posts Widget */
.popular-posts {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.popular-post {
    display: flex;
    gap: 15px;
}

.popular-post .post-image {
    width: 80px;
    height: 80px;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0;
}

.popular-post .post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.popular-post .post-info {
    flex: 1;
}

.popular-post h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
    line-height: 1.4;
}

.popular-post h4 a {
    color: #2d3748;
    text-decoration: none;
    transition: color 0.3s ease;
}

.popular-post h4 a:hover {
    color: #4299e1;
}

.popular-post .post-date {
    color: #718096;
    font-size: 14px;
}

/* Tags Widget */
.tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.tags a {
    display: inline-block;
    padding: 5px 12px;
    background-color: #f1f5f9;
    color: #4a5568;
    border-radius: 4px;
    font-size: 14px;
    text-decoration: none;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.tags a:hover {
    background-color: #4299e1;
    color: #fff;
}

/* Newsletter Widget */
.newsletter-widget p {
    margin: 0 0 15px 0;
    color: #555;
    line-height: 1.6;
}

.newsletter-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.newsletter-form input {
    padding: 12px 15px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 16px;
    color: #2d3748;
}

.newsletter-form button {
    padding: 12px 15px;
    font-size: 16px;
    font-weight: 600;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .blog-grid {
        grid-template-columns: 1fr;
    }
    
    .blog-sidebar {
        margin-top: 50px;
    }
}

@media (max-width: 768px) {
    .blog-posts-grid {
        grid-template-columns: 1fr;
    }
    
    .blog-post .post-image {
        height: 300px;
    }
    
    .blog-post.featured .post-image {
        height: 350px;
    }
}

@media (max-width: 480px) {
    .blog-post .post-image {
        height: 250px;
    }
    
    .blog-post.featured .post-image {
        height: 300px;
    }
    
    .blog-post .post-content {
        padding: 20px;
    }
    
    .blog-post .post-title {
        font-size: 20px;
    }
    
    .blog-post.featured .post-title {
        font-size: 24px;
    }
    
    .pagination {
        flex-wrap: wrap;
    }
}