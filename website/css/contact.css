/* Contact Page Styles */

/* Contact Information Section */
.contact-info-section {
    padding: 80px 0;
    background-color: #fff;
}

.contact-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.contact-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.contact-icon {
    width: 70px;
    height: 70px;
    background-color: #ebf8ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.contact-icon i {
    font-size: 28px;
    color: #4299e1;
}

.contact-card h3 {
    margin: 0 0 15px 0;
    color: #2c5282;
    font-size: 20px;
}

.contact-card p {
    margin: 0 0 10px 0;
    color: #555;
    line-height: 1.6;
}

.contact-card a {
    color: #4299e1;
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-card a:hover {
    color: #2c5282;
    text-decoration: underline;
}

.contact-card .social-links {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 15px;
}

.contact-card .social-links a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: #f1f5f9;
    color: #4299e1;
    border-radius: 50%;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.contact-card .social-links a:hover {
    background-color: #4299e1;
    color: #fff;
    text-decoration: none;
}

/* Contact Form Section */
.contact-form-section {
    padding: 80px 0;
    background-color: #f8f9fa;
}

.contact-form-container {
    max-width: 800px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 40px;
}

.form-header {
    text-align: center;
    margin-bottom: 30px;
}

.form-header h2 {
    margin: 0 0 10px 0;
    color: #2c5282;
    font-size: 28px;
}

.form-header p {
    color: #555;
    margin: 0;
}

.contact-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group.full-width {
    grid-column: span 2;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #2d3748;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 16px;
    color: #2d3748;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
    outline: none;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin-top: 3px;
}

.checkbox-group label {
    margin-bottom: 0;
    font-weight: normal;
}

.form-group button {
    padding: 12px 25px;
    font-size: 16px;
    font-weight: 600;
}

.form-note {
    grid-column: span 2;
    color: #718096;
    font-size: 14px;
    margin-top: 10px;
}

/* Map Section */
.map-section {
    padding: 80px 0;
    background-color: #fff;
}

.map-container {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    margin-top: 40px;
}

.map-placeholder {
    flex: 2;
    min-width: 300px;
    height: 400px;
    background-color: #f1f5f9;
    border-radius: 8px;
    overflow: hidden;
}

.map-placeholder img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.directions {
    flex: 1;
    min-width: 300px;
}

.directions h3 {
    margin: 0 0 25px 0;
    color: #2c5282;
    font-size: 24px;
}

.direction-item {
    display: flex;
    margin-bottom: 20px;
}

.direction-item i {
    font-size: 24px;
    color: #4299e1;
    margin-right: 15px;
    margin-top: 3px;
}

.direction-item div {
    flex: 1;
}

.direction-item h4 {
    margin: 0 0 5px 0;
    color: #2d3748;
    font-size: 18px;
}

.direction-item p {
    margin: 0;
    color: #555;
    line-height: 1.6;
}

/* FAQ Section */
.faq-section {
    padding: 80px 0;
    background-color: #f8f9fa;
}

.faq-container {
    max-width: 800px;
    margin: 40px auto 0;
}

.faq-item {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.faq-question {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.faq-question:hover {
    background-color: #f1f5f9;
}

.faq-question h3 {
    margin: 0;
    color: #2c5282;
    font-size: 18px;
    font-weight: 600;
}

.faq-question i {
    color: #4299e1;
    font-size: 16px;
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 20px 20px;
    display: none;
}

.faq-item.active .faq-answer {
    display: block;
}

.faq-answer p {
    margin: 0;
    color: #555;
    line-height: 1.8;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .contact-form {
        grid-template-columns: 1fr;
    }
    
    .form-group.full-width {
        grid-column: span 1;
    }
    
    .form-note {
        grid-column: span 1;
    }
    
    .map-container {
        flex-direction: column;
    }
    
    .map-placeholder {
        height: 300px;
    }
}

@media (max-width: 480px) {
    .contact-form-container {
        padding: 30px 20px;
    }
    
    .contact-info-grid {
        grid-template-columns: 1fr;
    }
}