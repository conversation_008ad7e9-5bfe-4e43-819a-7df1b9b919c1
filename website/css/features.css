/**
 * Features Page Specific Styles
 * For CMS Gestione Cantieri e Certificazioni
 */

/* Page Hero */
.page-hero {
    background-color: var(--primary-dark);
    color: var(--white);
    padding: 6rem 0 3rem;
    text-align: center;
    margin-top: 70px; /* Height of the header */
}

.page-hero h1 {
    color: var(--white);
    margin-bottom: var(--spacing-md);
    font-size: 2.8rem;
    animation: fadeInUp 0.8s ease;
}

.page-hero p {
    max-width: 800px;
    margin: 0 auto;
    font-size: 1.2rem;
    opacity: 0.9;
    animation: fadeInUp 0.8s ease 0.2s;
    animation-fill-mode: both;
}

/* Features Overview */
.features-overview {
    padding: var(--spacing-xl) 0;
    background-color: var(--white);
}

.features-nav {
    margin-top: var(--spacing-xl);
}

.features-nav ul {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.features-nav a {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    transition: all var(--transition-normal);
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.features-nav a:hover,
.features-nav a.active {
    background-color: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Feature Detail Sections */
.feature-detail {
    padding: var(--spacing-xl) 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-detail.alternate {
    background-color: var(--background-light);
}

.feature-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
    margin-bottom: var(--spacing-xl);
}

.feature-detail.alternate .feature-content {
    grid-template-columns: 1fr 1fr;
}

.feature-text h2 {
    margin-bottom: var(--spacing-sm);
    color: var(--primary-dark);
}

.feature-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    margin-bottom: var(--spacing-lg);
}

.feature-description p {
    margin-bottom: var(--spacing-lg);
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.feature-list li {
    display: flex;
    gap: var(--spacing-md);
}

.feature-list i {
    color: var(--primary-color);
    font-size: 1.5rem;
    flex-shrink: 0;
    margin-top: 0.25rem;
}

.feature-list h4 {
    margin-bottom: var(--spacing-xs);
    color: var(--text-dark);
}

.feature-list p {
    margin-bottom: 0;
    color: var(--text-light);
}

.feature-image {
    position: relative;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.feature-image img {
    width: 100%;
    height: auto;
    display: block;
}

.feature-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.9rem;
    text-align: center;
}

/* Feature Screenshots */
.feature-screenshots {
    margin-top: var(--spacing-xl);
}

.feature-screenshots h3 {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.screenshots-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.screenshot {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.screenshot:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.screenshot img {
    width: 100%;
    height: auto;
    display: block;
}

.screenshot p {
    padding: var(--spacing-md);
    margin: 0;
    text-align: center;
    font-size: 0.9rem;
    color: var(--text-light);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .page-hero h1 {
        font-size: 2.4rem;
    }
    
    .page-hero p {
        font-size: 1.1rem;
    }
    
    .feature-content {
        gap: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .feature-content {
        grid-template-columns: 1fr !important;
    }
    
    .feature-detail.alternate .feature-content .feature-image {
        order: -1;
    }
    
    .features-nav ul {
        flex-direction: column;
        align-items: center;
    }
    
    .features-nav a {
        width: 100%;
        text-align: center;
    }
    
    .screenshots-grid {
        grid-template-columns: 1fr;
    }
}

/* Animation for scrolling to sections */
html {
    scroll-behavior: smooth;
}

/* Smooth scrolling adjustment for fixed header */
:target {
    scroll-margin-top: 80px;
}

/* Active section highlighting */
.features-nav a {
    position: relative;
}

.features-nav a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    width: 0;
    height: 3px;
    background-color: var(--secondary-color);
    transition: width 0.3s ease, left 0.3s ease;
    transform: translateX(-50%);
}

.features-nav a:hover::after,
.features-nav a.active::after {
    width: 50%;
}