/**
 * How It Works Page Specific Styles
 * For CMS Gestione Cantieri e Certificazioni
 */

/* Page Hero - Similar to other pages but with different background */
.page-hero {
    background-color: var(--primary-dark);
    color: var(--white);
    padding: 6rem 0 3rem;
    text-align: center;
    margin-top: 70px; /* Height of the header */
}

.page-hero h1 {
    color: var(--white);
    margin-bottom: var(--spacing-md);
    font-size: 2.8rem;
    animation: fadeInUp 0.8s ease;
}

.page-hero p {
    max-width: 800px;
    margin: 0 auto;
    font-size: 1.2rem;
    opacity: 0.9;
    animation: fadeInUp 0.8s ease 0.2s;
    animation-fill-mode: both;
}

/* Process Flow Section */
.process-flow {
    padding: var(--spacing-xl) 0;
    background-color: var(--white);
}

.flow-diagram {
    max-width: 800px;
    margin: var(--spacing-xl) auto 0;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.flow-step {
    display: flex;
    align-items: flex-start;
    width: 100%;
    position: relative;
    z-index: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
    margin-right: var(--spacing-md);
    flex-shrink: 0;
}

.step-content {
    background-color: var(--background-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.step-icon {
    width: 60px;
    height: 60px;
    background-color: rgba(44, 82, 130, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-sm);
}

.step-icon i {
    font-size: 1.8rem;
    color: var(--primary-color);
}

.step-content h3 {
    margin-bottom: var(--spacing-xs);
    color: var(--primary-dark);
    font-size: 1.3rem;
}

.step-content p {
    color: var(--text-light);
    margin-bottom: 0;
}

.flow-connector {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.5rem;
    margin: var(--spacing-sm) 0;
}

/* Add animation to flow steps */
.flow-step {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.8s ease forwards;
}

.flow-step:nth-child(3) {
    animation-delay: 0.2s;
}

.flow-step:nth-child(5) {
    animation-delay: 0.4s;
}

.flow-step:nth-child(7) {
    animation-delay: 0.6s;
}

.flow-step:nth-child(9) {
    animation-delay: 0.8s;
}

.flow-step:nth-child(11) {
    animation-delay: 1s;
}

.flow-connector {
    opacity: 0;
    animation: fadeIn 0.8s ease forwards;
}

.flow-connector:nth-child(2) {
    animation-delay: 0.1s;
}

.flow-connector:nth-child(4) {
    animation-delay: 0.3s;
}

.flow-connector:nth-child(6) {
    animation-delay: 0.5s;
}

.flow-connector:nth-child(8) {
    animation-delay: 0.7s;
}

.flow-connector:nth-child(10) {
    animation-delay: 0.9s;
}

/* Video Tutorials Section */
.video-tutorials {
    padding: var(--spacing-xl) 0;
    background-color: var(--background-light);
}

.videos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.video-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.video-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.video-thumbnail {
    position: relative;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform var(--transition-normal);
}

.video-card:hover .video-thumbnail img {
    transform: scale(1.05);
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background-color: rgba(44, 82, 130, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.5rem;
    transition: all var(--transition-normal);
}

.video-card:hover .play-button {
    background-color: var(--primary-color);
    transform: translate(-50%, -50%) scale(1.1);
}

.video-info {
    padding: var(--spacing-md);
}

.video-info h3 {
    margin-bottom: var(--spacing-xs);
    font-size: 1.2rem;
    color: var(--primary-dark);
}

.video-info p {
    color: var(--text-light);
    margin-bottom: var(--spacing-sm);
    font-size: 0.9rem;
}

.video-duration {
    display: inline-block;
    background-color: rgba(44, 82, 130, 0.1);
    color: var(--primary-color);
    padding: 0.2rem 0.5rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
}

.videos-cta {
    text-align: center;
    margin-top: var(--spacing-xl);
}

/* Use Cases Section */
.use-cases {
    padding: var(--spacing-xl) 0;
    background-color: var(--white);
}

.use-cases-tabs {
    margin-top: var(--spacing-lg);
}

.tabs-nav {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.tab-button {
    padding: 0.75rem 1.5rem;
    background-color: var(--background-light);
    border: none;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    color: var(--text-dark);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.tab-button:hover,
.tab-button.active {
    background-color: var(--primary-color);
    color: var(--white);
    box-shadow: var(--shadow-sm);
}

.tabs-content {
    position: relative;
}

.tab-pane {
    display: none;
    animation: fadeIn 0.5s ease;
}

.tab-pane.active {
    display: block;
}

.use-case-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
}

.use-case-text h3 {
    margin-bottom: var(--spacing-md);
    color: var(--primary-dark);
    font-size: 1.8rem;
}

.use-case-text p {
    margin-bottom: var(--spacing-md);
    color: var(--text-dark);
}

.use-case-text h4 {
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
    font-size: 1.2rem;
}

.use-case-text ul {
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-md);
}

.use-case-text li {
    margin-bottom: var(--spacing-xs);
    position: relative;
    padding-left: var(--spacing-md);
    list-style-type: none;
}

.use-case-text li::before {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    left: 0;
    color: var(--primary-color);
}

.use-case-quote {
    background-color: rgba(44, 82, 130, 0.05);
    border-left: 3px solid var(--primary-color);
    padding: var(--spacing-md);
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
    margin-top: var(--spacing-md);
}

.use-case-quote p {
    font-style: italic;
    color: var(--text-dark);
    margin-bottom: var(--spacing-xs);
    font-size: 0.95rem;
}

.use-case-quote cite {
    font-style: normal;
    font-weight: 600;
    color: var(--primary-color);
    font-size: 0.9rem;
}

.use-case-image {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.use-case-image img {
    width: 100%;
    height: auto;
    display: block;
}

/* FAQ Section */
.faq-section {
    padding: var(--spacing-xl) 0;
    background-color: var(--background-light);
}

.faq-container {
    max-width: 800px;
    margin: var(--spacing-lg) auto 0;
}

.faq-item {
    margin-bottom: var(--spacing-md);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    background-color: var(--white);
}

.faq-question {
    padding: var(--spacing-md);
    background-color: var(--white);
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: background-color var(--transition-normal);
}

.faq-question:hover {
    background-color: rgba(44, 82, 130, 0.05);
}

.faq-question h3 {
    margin: 0;
    font-size: 1.1rem;
    color: var(--primary-dark);
}

.faq-toggle {
    color: var(--primary-color);
    font-size: 1rem;
    transition: transform var(--transition-normal);
}

.faq-item.active .faq-toggle {
    transform: rotate(45deg);
}

.faq-answer {
    padding: 0 var(--spacing-md);
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition-normal), padding var(--transition-normal);
}

.faq-item.active .faq-answer {
    padding: 0 var(--spacing-md) var(--spacing-md);
    max-height: 500px;
}

.faq-answer p {
    margin: 0;
    color: var(--text-light);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Styles */
@media (max-width: 992px) {
    .page-hero h1 {
        font-size: 2.4rem;
    }
    
    .page-hero p {
        font-size: 1.1rem;
    }
    
    .use-case-content {
        gap: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .use-case-content {
        grid-template-columns: 1fr;
    }
    
    .use-case-image {
        order: -1;
        margin-bottom: var(--spacing-md);
    }
    
    .tabs-nav {
        flex-direction: column;
        align-items: center;
    }
    
    .tab-button {
        width: 100%;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .step-content {
        padding: var(--spacing-md);
    }
    
    .step-icon {
        width: 50px;
        height: 50px;
    }
    
    .step-icon i {
        font-size: 1.5rem;
    }
    
    .faq-question h3 {
        font-size: 1rem;
    }
}