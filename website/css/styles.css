/* 
 * Main Stylesheet for CMS Gestione Cantieri e Certificazioni
 * Author: CMS Development Team
 * Version: 1.0
 */

/* ===== Base Styles ===== */
:root {
    /* Color Palette */
    --primary-color: #2c5282; /* Deep Blue */
    --primary-light: #4299e1; /* Light Blue */
    --primary-dark: #1a365d; /* Dark Blue */
    --secondary-color: #ed8936; /* Orange */
    --secondary-light: #f6ad55; /* Light Orange */
    --secondary-dark: #c05621; /* Dark Orange */
    --text-dark: #2d3748; /* Dark Gray for text */
    --text-light: #a0aec0; /* Light Gray for secondary text */
    --background-light: #f7fafc; /* Very Light Gray for backgrounds */
    --background-dark: #1a202c; /* Very Dark Gray for dark sections */
    --success-color: #48bb78; /* Green */
    --warning-color: #ecc94b; /* Yellow */
    --danger-color: #e53e3e; /* Red */
    --white: #ffffff;
    --black: #000000;

    /* Typography */
    --heading-font: 'Roboto', sans-serif;
    --body-font: 'Open Sans', sans-serif;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 2rem;
    --spacing-xl: 4rem;
    
    /* Border Radius */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 1rem;
    
    /* Box Shadow */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--body-font);
    color: var(--text-dark);
    line-height: 1.6;
    background-color: var(--background-light);
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--primary-dark);
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.5rem;
}

h4 {
    font-size: 1.25rem;
}

p {
    margin-bottom: var(--spacing-md);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-light);
}

img {
    max-width: 100%;
    height: auto;
}

ul {
    list-style: none;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.section-header h2 {
    position: relative;
    display: inline-block;
    margin-bottom: var(--spacing-md);
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background-color: var(--secondary-color);
}

.section-header p {
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== Buttons ===== */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    border: none;
    font-family: var(--body-font);
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* ===== Header ===== */
.header {
    background-color: var(--white);
    box-shadow: var(--shadow-sm);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    padding: var(--spacing-md) 0;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo img {
    height: 40px;
}

.main-nav ul {
    display: flex;
}

.main-nav li {
    margin-left: var(--spacing-lg);
}

.main-nav a {
    color: var(--text-dark);
    font-weight: 600;
    position: relative;
}

.main-nav a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width var(--transition-normal);
}

.main-nav a:hover::after,
.main-nav a.active::after {
    width: 100%;
}

.mobile-menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--primary-color);
}

/* ===== Hero Section ===== */
.hero {
    height: 100vh;
    min-height: 600px;
    background-image: url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    position: relative;
    display: flex;
    align-items: center;
    color: var(--white);
    margin-top: 70px; /* Height of the header */
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(26, 54, 93, 0.8), rgba(26, 54, 93, 0.6));
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
}

.hero h1 {
    font-size: 3.5rem;
    margin-bottom: var(--spacing-lg);
    color: var(--white);
    animation: fadeInUp 1s ease;
}

.hero p {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-xl);
    animation: fadeInUp 1s ease 0.2s;
    animation-fill-mode: both;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-md);
    animation: fadeInUp 1s ease 0.4s;
    animation-fill-mode: both;
}

/* ===== Features Section ===== */
.features {
    padding: var(--spacing-xl) 0;
    background-color: var(--white);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.feature-card {
    background-color: var(--background-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    text-align: center;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-md);
}

.feature-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.feature-card h3 {
    margin-bottom: var(--spacing-sm);
}

/* ===== Testimonials Section ===== */
.testimonials {
    padding: var(--spacing-xl) 0;
    background-color: var(--background-light);
}

.testimonials-slider {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.testimonial {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.testimonial-content {
    position: relative;
    padding: var(--spacing-md) 0;
}

.testimonial-content::before {
    content: '\201C';
    font-size: 4rem;
    position: absolute;
    top: -20px;
    left: -10px;
    color: var(--primary-light);
    opacity: 0.3;
}

.testimonial-author {
    display: flex;
    align-items: center;
    margin-top: var(--spacing-md);
}

.testimonial-author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: var(--spacing-md);
    object-fit: cover;
}

.author-info h4 {
    margin-bottom: 0;
}

.author-info p {
    color: var(--text-light);
    margin-bottom: 0;
    font-size: 0.875rem;
}

/* ===== Call to Action ===== */
.cta {
    padding: var(--spacing-xl) 0;
    background-color: var(--primary-color);
    color: var(--white);
    text-align: center;
}

.cta h2 {
    color: var(--white);
    margin-bottom: var(--spacing-md);
}

.cta p {
    max-width: 600px;
    margin: 0 auto var(--spacing-lg);
}

.cta .btn-primary {
    background-color: var(--secondary-color);
}

.cta .btn-primary:hover {
    background-color: var(--secondary-dark);
}

/* ===== Footer ===== */
.footer {
    background-color: var(--background-dark);
    color: var(--white);
    padding: var(--spacing-xl) 0 var(--spacing-md);
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-logo img {
    height: 40px;
    margin-bottom: var(--spacing-md);
}

.footer-col h4 {
    color: var(--white);
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.footer-col h4::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: var(--secondary-color);
}

.footer-col ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-col a {
    color: var(--text-light);
    transition: color var(--transition-fast);
}

.footer-col a:hover {
    color: var(--white);
}

.social-links {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--white);
    transition: all var(--transition-normal);
}

.social-links a:hover {
    background-color: var(--primary-light);
    transform: translateY(-3px);
}

.contact-info li {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.contact-info i {
    margin-right: var(--spacing-sm);
    color: var(--primary-light);
}

.newsletter-form {
    display: flex;
    margin-top: var(--spacing-md);
}

.newsletter-form input {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
}

.newsletter-form button {
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
    padding: 0 var(--spacing-md);
    background-color: var(--secondary-color);
}

.newsletter-form button:hover {
    background-color: var(--secondary-dark);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-bottom p {
    margin-bottom: 0;
}

.footer-bottom ul {
    display: flex;
    gap: var(--spacing-lg);
}

/* ===== Animations ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== Responsive Styles ===== */
@media (max-width: 992px) {
    h1 {
        font-size: 2.2rem;
    }
    
    h2 {
        font-size: 1.8rem;
    }
    
    .hero h1 {
        font-size: 2.8rem;
    }
    
    .hero p {
        font-size: 1.1rem;
    }
}

@media (max-width: 768px) {
    .main-nav {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .hero {
        min-height: 500px;
    }
    
    .hero h1 {
        font-size: 2.2rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        width: 100%;
        max-width: 300px;
    }
    
    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }
    
    .footer-bottom ul {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .section-header h2 {
        font-size: 1.5rem;
    }
    
    .feature-card {
        padding: var(--spacing-md);
    }
    
    .testimonials-slider {
        grid-template-columns: 1fr;
    }
    
    .footer-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
}