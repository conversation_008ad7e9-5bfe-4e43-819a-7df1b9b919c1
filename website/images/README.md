# Images for CMS Gestione Cantieri e Certificazioni Website

This directory contains all the images used in the CMS Gestione Cantieri e Certificazioni website.

## Required Images

Please add the following images to this directory:

### Logo Images
- `logo.png` - Main logo for the header (dimensions: 180px × 40px)
- `logo-white.png` - White version of the logo for the footer (dimensions: 180px × 40px)

### Hero Section
- `hero-bg.jpg` - Background image for the hero section (dimensions: 1920px × 1080px)
  - This should be a high-quality image of a construction site or cable installation
  - The image should be dark enough or have enough empty space to allow white text to be readable

### Testimonials
- `testimonial-1.jpg` - Photo of the first testimonial person (dimensions: 120px × 120px)
- `testimonial-2.jpg` - Photo of the second testimonial person (dimensions: 120px × 120px)

### Feature Section Images
- `feature-cantieri.jpg` - Image representing construction site management (dimensions: 600px × 400px)
- `feature-certificazioni.jpg` - Image representing cable certification (dimensions: 600px × 400px)
- `feature-strumenti.jpg` - Image representing certified tools (dimensions: 600px × 400px)
- `feature-reports.jpg` - Image representing reports and analytics (dimensions: 600px × 400px)

## Image Guidelines

1. All images should be optimized for web (compressed without losing quality)
2. Use professional, high-quality images that represent the construction and certification industry
3. Maintain a consistent style and color palette across all images
4. Ensure all images are properly licensed for commercial use

## Placeholder Images

Until you have the final images, you can use placeholder services like:
- https://placeholder.com/
- https://picsum.photos/

Example usage:
```html
<!-- For the logo -->
<img src="https://via.placeholder.com/180x40/2c5282/ffffff?text=CMS+Logo" alt="CMS Logo">

<!-- For the hero background -->
<img src="https://picsum.photos/1920/1080" alt="Hero Background">
```

## Image Optimization

Before adding images to the website, optimize them using tools like:
- [TinyPNG](https://tinypng.com/)
- [Squoosh](https://squoosh.app/)
- [ImageOptim](https://imageoptim.com/)

This will ensure fast loading times and better user experience.