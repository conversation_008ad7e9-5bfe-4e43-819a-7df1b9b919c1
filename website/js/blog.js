/**
 * Blog Page JavaScript
 * Handles search functionality, category filtering, and other blog-specific interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize blog search functionality
    initBlogSearch();
    
    // Initialize category filtering
    initCategoryFiltering();
    
    // Initialize tag filtering
    initTagFiltering();
    
    // Initialize newsletter form
    initNewsletterForm();
});

/**
 * Initialize blog search functionality
 */
function initBlogSearch() {
    const searchForm = document.querySelector('.search-form');
    
    if (!searchForm) return;
    
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const searchInput = this.querySelector('input');
        const searchTerm = searchInput.value.trim().toLowerCase();
        
        if (searchTerm === '') {
            // Show error for empty search
            showSearchMessage('error', 'Inserisci un termine di ricerca');
            return;
        }
        
        // In a real implementation, this would redirect to a search results page or filter posts via AJAX
        // For this demo, we'll just show a message
        showSearchMessage('success', `Ricerca in corso per: "${searchTerm}"`);
        
        // Simulate search results by highlighting matching content
        simulateSearch(searchTerm);
    });
}

/**
 * Show a search message
 * @param {string} type - The type of message ('success' or 'error')
 * @param {string} message - The message text
 */
function showSearchMessage(type, message) {
    const searchForm = document.querySelector('.search-form');
    
    // Remove any existing message
    const existingMessage = document.querySelector('.search-message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    // Create message element
    const messageElement = document.createElement('div');
    messageElement.className = `search-message ${type}`;
    messageElement.textContent = message;
    
    // Style based on type
    if (type === 'success') {
        messageElement.style.backgroundColor = '#c6f6d5';
        messageElement.style.color = '#2f855a';
    } else {
        messageElement.style.backgroundColor = '#fed7d7';
        messageElement.style.color = '#c53030';
    }
    
    // Common styles
    messageElement.style.padding = '10px';
    messageElement.style.borderRadius = '4px';
    messageElement.style.marginTop = '10px';
    messageElement.style.fontSize = '14px';
    
    // Insert after the search form
    searchForm.parentNode.insertBefore(messageElement, searchForm.nextSibling);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
        messageElement.remove();
    }, 3000);
}

/**
 * Simulate search by highlighting matching content
 * @param {string} searchTerm - The search term to highlight
 */
function simulateSearch(searchTerm) {
    // Get all blog post titles and excerpts
    const postTitles = document.querySelectorAll('.post-title');
    const postExcerpts = document.querySelectorAll('.post-excerpt');
    
    // Reset any previous highlighting
    resetHighlighting();
    
    // Highlight matching text in titles
    postTitles.forEach(title => {
        const titleText = title.textContent;
        if (titleText.toLowerCase().includes(searchTerm)) {
            highlightText(title, searchTerm);
        }
    });
    
    // Highlight matching text in excerpts
    postExcerpts.forEach(excerpt => {
        const excerptText = excerpt.textContent;
        if (excerptText.toLowerCase().includes(searchTerm)) {
            highlightText(excerpt, searchTerm);
        }
    });
}

/**
 * Highlight occurrences of a search term in an element
 * @param {HTMLElement} element - The element containing text to highlight
 * @param {string} searchTerm - The search term to highlight
 */
function highlightText(element, searchTerm) {
    const html = element.innerHTML;
    const regex = new RegExp(searchTerm, 'gi');
    const newHtml = html.replace(regex, match => `<span class="highlight">${match}</span>`);
    
    element.innerHTML = newHtml;
    
    // Add style for highlighted text
    const highlights = document.querySelectorAll('.highlight');
    highlights.forEach(highlight => {
        highlight.style.backgroundColor = '#fef3c7';
        highlight.style.padding = '0 2px';
        highlight.style.borderRadius = '2px';
    });
}

/**
 * Reset any search highlighting
 */
function resetHighlighting() {
    const highlights = document.querySelectorAll('.highlight');
    
    highlights.forEach(highlight => {
        const parent = highlight.parentNode;
        parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
        parent.normalize();
    });
}

/**
 * Initialize category filtering
 */
function initCategoryFiltering() {
    const categoryLinks = document.querySelectorAll('.categories-widget a');
    
    categoryLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const category = this.textContent.split(' ')[0]; // Get category name without count
            
            // In a real implementation, this would filter posts by category via AJAX or redirect
            // For this demo, we'll just show a message
            alert(`Filtro per categoria: ${category}`);
        });
    });
}

/**
 * Initialize tag filtering
 */
function initTagFiltering() {
    const tagLinks = document.querySelectorAll('.tags a');
    
    tagLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const tag = this.textContent;
            
            // In a real implementation, this would filter posts by tag via AJAX or redirect
            // For this demo, we'll just show a message
            alert(`Filtro per tag: ${tag}`);
        });
    });
}

/**
 * Initialize newsletter form
 */
function initNewsletterForm() {
    const newsletterForms = document.querySelectorAll('.newsletter-form');
    
    newsletterForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const emailInput = this.querySelector('input[type="email"]');
            const email = emailInput.value.trim();
            
            if (email === '') {
                // Show error for empty email
                alert('Inserisci un indirizzo email valido');
                return;
            }
            
            if (!isValidEmail(email)) {
                // Show error for invalid email
                alert('Inserisci un indirizzo email valido');
                return;
            }
            
            // In a real implementation, this would submit the form via AJAX
            // For this demo, we'll just show a message
            alert(`Grazie per esserti iscritto alla newsletter con l'email: ${email}`);
            
            // Reset form
            this.reset();
        });
    });
}

/**
 * Check if an email is valid
 * @param {string} email - The email to validate
 * @returns {boolean} - Whether the email is valid
 */
function isValidEmail(email) {
    const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
}

/**
 * Initialize pagination (for future implementation)
 */
function initPagination() {
    const paginationLinks = document.querySelectorAll('.pagination a');
    
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Only prevent default for links that aren't meant to navigate to a real page
            if (this.getAttribute('href') === '#') {
                e.preventDefault();
                
                const page = this.textContent;
                
                // In a real implementation, this would load the selected page via AJAX or redirect
                // For this demo, we'll just show a message
                alert(`Navigazione alla pagina: ${page}`);
            }
        });
    });
}