/**
 * Contact Page JavaScript
 * Handles form validation and FAQ accordion functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize contact form validation
    initContactForm();
    
    // Initialize FAQ accordion
    initFaqAccordion();
});

/**
 * Initialize contact form validation and submission
 */
function initContactForm() {
    const contactForm = document.getElementById('contactForm');
    
    if (!contactForm) return;
    
    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Validate form
        if (validateForm(contactForm)) {
            // Show success message (in a real implementation, this would submit the form via AJAX)
            showFormMessage('success', 'Grazie per averci contattato! Ti risponderemo al più presto.');
            
            // Reset form after successful submission
            contactForm.reset();
        }
    });
    
    // Add input event listeners for real-time validation
    const requiredInputs = contactForm.querySelectorAll('[required]');
    requiredInputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateInput(this);
        });
    });
}

/**
 * Validate the entire form
 * @param {HTMLFormElement} form - The form element to validate
 * @returns {boolean} - Whether the form is valid
 */
function validateForm(form) {
    const requiredInputs = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredInputs.forEach(input => {
        if (!validateInput(input)) {
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * Validate a single input field
 * @param {HTMLInputElement|HTMLTextAreaElement} input - The input to validate
 * @returns {boolean} - Whether the input is valid
 */
function validateInput(input) {
    let isValid = true;
    let errorMessage = '';
    
    // Remove any existing error message
    removeInputError(input);
    
    // Check if empty
    if (input.value.trim() === '') {
        isValid = false;
        errorMessage = 'Questo campo è obbligatorio';
    } 
    // Email validation
    else if (input.type === 'email' && !isValidEmail(input.value)) {
        isValid = false;
        errorMessage = 'Inserisci un indirizzo email valido';
    }
    
    // If invalid, show error message
    if (!isValid) {
        showInputError(input, errorMessage);
    }
    
    return isValid;
}

/**
 * Check if an email is valid
 * @param {string} email - The email to validate
 * @returns {boolean} - Whether the email is valid
 */
function isValidEmail(email) {
    const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
}

/**
 * Show an error message for an input
 * @param {HTMLInputElement|HTMLTextAreaElement} input - The input with the error
 * @param {string} message - The error message
 */
function showInputError(input, message) {
    const errorElement = document.createElement('div');
    errorElement.className = 'input-error';
    errorElement.textContent = message;
    errorElement.style.color = '#e53e3e';
    errorElement.style.fontSize = '14px';
    errorElement.style.marginTop = '5px';
    
    // Add error class to input
    input.classList.add('input-error');
    input.style.borderColor = '#e53e3e';
    
    // Insert error message after input
    input.parentNode.appendChild(errorElement);
}

/**
 * Remove error message from an input
 * @param {HTMLInputElement|HTMLTextAreaElement} input - The input to remove error from
 */
function removeInputError(input) {
    // Remove error class from input
    input.classList.remove('input-error');
    input.style.borderColor = '';
    
    // Remove error message if it exists
    const errorElement = input.parentNode.querySelector('.input-error');
    if (errorElement) {
        errorElement.remove();
    }
}

/**
 * Show a form-wide message (success or error)
 * @param {string} type - The type of message ('success' or 'error')
 * @param {string} message - The message text
 */
function showFormMessage(type, message) {
    const contactForm = document.getElementById('contactForm');
    
    // Remove any existing message
    const existingMessage = document.querySelector('.form-message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    // Create message element
    const messageElement = document.createElement('div');
    messageElement.className = `form-message ${type}`;
    messageElement.textContent = message;
    
    // Style based on type
    if (type === 'success') {
        messageElement.style.backgroundColor = '#c6f6d5';
        messageElement.style.color = '#2f855a';
    } else {
        messageElement.style.backgroundColor = '#fed7d7';
        messageElement.style.color = '#c53030';
    }
    
    // Common styles
    messageElement.style.padding = '15px';
    messageElement.style.borderRadius = '4px';
    messageElement.style.marginBottom = '20px';
    
    // Insert at the top of the form
    contactForm.parentNode.insertBefore(messageElement, contactForm);
    
    // Scroll to message
    messageElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    
    // Auto-remove after 5 seconds if it's a success message
    if (type === 'success') {
        setTimeout(() => {
            messageElement.remove();
        }, 5000);
    }
}

/**
 * Initialize FAQ accordion functionality
 */
function initFaqAccordion() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        
        question.addEventListener('click', function() {
            // Toggle active class on the clicked item
            item.classList.toggle('active');
            
            // Close other items
            faqItems.forEach(otherItem => {
                if (otherItem !== item && otherItem.classList.contains('active')) {
                    otherItem.classList.remove('active');
                }
            });
        });
    });
}