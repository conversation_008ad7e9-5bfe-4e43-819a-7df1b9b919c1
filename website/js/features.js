/**
 * Features Page Specific JavaScript
 * For CMS Gestione Cantieri e Certificazioni
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize scroll spy functionality
    initScrollSpy();
    
    // Initialize smooth scrolling for navigation links
    initSmoothScroll();
});

/**
 * Scroll Spy Functionality
 * Updates the active navigation link based on the current scroll position
 */
function initScrollSpy() {
    const sections = document.querySelectorAll('.feature-detail');
    const navLinks = document.querySelectorAll('.features-nav a');
    
    if (!sections.length || !navLinks.length) return;
    
    function updateActiveLink() {
        let currentSection = '';
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            
            // Check if we've scrolled to this section (with some offset for better UX)
            if (window.pageYOffset >= sectionTop - 150) {
                currentSection = section.getAttribute('id');
            }
        });
        
        // Update active class on navigation links
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${currentSection}`) {
                link.classList.add('active');
            }
        });
    }
    
    // Update active link on scroll
    window.addEventListener('scroll', updateActiveLink);
    
    // Initialize on page load
    updateActiveLink();
}

/**
 * Smooth Scrolling for Navigation Links
 * Adds smooth scrolling behavior to navigation links
 */
function initSmoothScroll() {
    const navLinks = document.querySelectorAll('.features-nav a');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            if (!targetId || targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (!targetElement) return;
            
            // Get header height for offset
            const headerHeight = document.querySelector('.header').offsetHeight;
            
            // Calculate position to scroll to
            const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - headerHeight - 20;
            
            // Smooth scroll to target
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        });
    });
}