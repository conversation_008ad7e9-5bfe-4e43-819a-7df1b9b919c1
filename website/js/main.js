/**
 * Main JavaScript file for CMS Gestione Cantieri e Certificazioni website
 * Author: CMS Development Team
 * Version: 1.0
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initMobileMenu();
    initSmoothScroll();
    initAnimations();
    initTestimonialSlider();
    initNewsletterForm();
});

/**
 * Mobile Menu Toggle
 */
function initMobileMenu() {
    const menuToggle = document.querySelector('.mobile-menu-toggle');
    const mainNav = document.querySelector('.main-nav');
    
    if (!menuToggle || !mainNav) return;
    
    menuToggle.addEventListener('click', function() {
        mainNav.classList.toggle('active');
        
        // Change icon based on menu state
        const icon = menuToggle.querySelector('i');
        if (icon) {
            if (mainNav.classList.contains('active')) {
                icon.classList.remove('fa-bars');
                icon.classList.add('fa-times');
            } else {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        }
    });
    
    // Close menu when clicking outside
    document.addEventListener('click', function(event) {
        if (!mainNav.contains(event.target) && !menuToggle.contains(event.target) && mainNav.classList.contains('active')) {
            mainNav.classList.remove('active');
            const icon = menuToggle.querySelector('i');
            if (icon) {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        }
    });
    
    // Add styles for mobile menu when active
    const style = document.createElement('style');
    style.textContent = `
        @media (max-width: 768px) {
            .main-nav.active {
                display: block;
                position: absolute;
                top: 100%;
                left: 0;
                width: 100%;
                background-color: var(--white);
                box-shadow: var(--shadow-md);
                padding: 1rem;
                z-index: 1000;
            }
            
            .main-nav.active ul {
                flex-direction: column;
                gap: 1rem;
            }
            
            .main-nav.active li {
                margin-left: 0;
            }
        }
    `;
    document.head.appendChild(style);
}

/**
 * Smooth Scrolling for Anchor Links
 */
function initSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (!targetElement) return;
            
            const headerHeight = document.querySelector('.header').offsetHeight;
            const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - headerHeight;
            
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
            
            // Close mobile menu if open
            const mainNav = document.querySelector('.main-nav');
            if (mainNav && mainNav.classList.contains('active')) {
                mainNav.classList.remove('active');
                const menuToggle = document.querySelector('.mobile-menu-toggle');
                if (menuToggle) {
                    const icon = menuToggle.querySelector('i');
                    if (icon) {
                        icon.classList.remove('fa-times');
                        icon.classList.add('fa-bars');
                    }
                }
            }
        });
    });
}

/**
 * Scroll Animations
 */
function initAnimations() {
    // Add animation to elements when they come into view
    const animatedElements = document.querySelectorAll('.feature-card, .testimonial, .section-header');
    
    if ('IntersectionObserver' in window) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });
        
        animatedElements.forEach(element => {
            observer.observe(element);
        });
        
        // Add animation classes
        const style = document.createElement('style');
        style.textContent = `
            .feature-card, .testimonial, .section-header {
                opacity: 0;
                transform: translateY(20px);
                transition: opacity 0.6s ease, transform 0.6s ease;
            }
            
            .feature-card.animated, .testimonial.animated, .section-header.animated {
                opacity: 1;
                transform: translateY(0);
            }
            
            .feature-card:nth-child(2) {
                transition-delay: 0.2s;
            }
            
            .feature-card:nth-child(3) {
                transition-delay: 0.4s;
            }
            
            .feature-card:nth-child(4) {
                transition-delay: 0.6s;
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * Testimonial Slider
 */
function initTestimonialSlider() {
    const testimonials = document.querySelectorAll('.testimonial');
    if (testimonials.length <= 2) return; // No need for slider if 2 or fewer testimonials
    
    let currentIndex = 0;
    const slider = document.querySelector('.testimonials-slider');
    
    if (!slider) return;
    
    // Create navigation dots
    const dotsContainer = document.createElement('div');
    dotsContainer.className = 'slider-dots';
    
    for (let i = 0; i < testimonials.length; i++) {
        const dot = document.createElement('button');
        dot.className = i === 0 ? 'dot active' : 'dot';
        dot.setAttribute('aria-label', `Testimonial ${i + 1}`);
        dot.addEventListener('click', () => goToSlide(i));
        dotsContainer.appendChild(dot);
    }
    
    slider.parentNode.appendChild(dotsContainer);
    
    // Add navigation arrows for larger screens
    const prevButton = document.createElement('button');
    prevButton.className = 'slider-nav prev';
    prevButton.innerHTML = '<i class="fas fa-chevron-left"></i>';
    prevButton.setAttribute('aria-label', 'Previous testimonial');
    prevButton.addEventListener('click', () => goToSlide(currentIndex - 1));
    
    const nextButton = document.createElement('button');
    nextButton.className = 'slider-nav next';
    nextButton.innerHTML = '<i class="fas fa-chevron-right"></i>';
    nextButton.setAttribute('aria-label', 'Next testimonial');
    nextButton.addEventListener('click', () => goToSlide(currentIndex + 1));
    
    slider.parentNode.appendChild(prevButton);
    slider.parentNode.appendChild(nextButton);
    
    // Add styles for slider
    const style = document.createElement('style');
    style.textContent = `
        .testimonials-slider {
            position: relative;
            overflow: hidden;
        }
        
        @media (min-width: 769px) {
            .testimonials-slider {
                display: flex;
                transition: transform 0.5s ease;
            }
            
            .testimonial {
                flex: 0 0 calc(50% - 1rem);
                margin-right: 2rem;
            }
        }
        
        .slider-dots {
            display: flex;
            justify-content: center;
            margin-top: 2rem;
            gap: 0.5rem;
        }
        
        .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: var(--text-light);
            border: none;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        
        .dot.active {
            background-color: var(--primary-color);
        }
        
        .slider-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background-color: var(--white);
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: var(--shadow-md);
            z-index: 10;
            transition: all 0.3s ease;
        }
        
        .slider-nav:hover {
            background-color: var(--primary-color);
            color: var(--white);
        }
        
        .slider-nav.prev {
            left: -20px;
        }
        
        .slider-nav.next {
            right: -20px;
        }
        
        @media (max-width: 768px) {
            .slider-nav {
                display: none;
            }
        }
    `;
    document.head.appendChild(style);
    
    function goToSlide(index) {
        // Handle wrapping
        if (index < 0) {
            index = testimonials.length - 1;
        } else if (index >= testimonials.length) {
            index = 0;
        }
        
        currentIndex = index;
        
        // Update dots
        const dots = document.querySelectorAll('.dot');
        dots.forEach((dot, i) => {
            dot.classList.toggle('active', i === currentIndex);
        });
        
        // Move slider
        if (window.innerWidth > 768) {
            // On desktop, show 2 testimonials at once
            const slideWidth = testimonials[0].offsetWidth + 32; // width + margin
            slider.style.transform = `translateX(-${currentIndex * slideWidth}px)`;
        } else {
            // On mobile, show 1 testimonial at a time
            slider.style.transform = `translateX(-${currentIndex * 100}%)`;
        }
    }
    
    // Auto-advance the slider
    let slideInterval = setInterval(() => {
        goToSlide(currentIndex + 1);
    }, 5000);
    
    // Pause auto-advance on hover
    slider.addEventListener('mouseenter', () => {
        clearInterval(slideInterval);
    });
    
    slider.addEventListener('mouseleave', () => {
        slideInterval = setInterval(() => {
            goToSlide(currentIndex + 1);
        }, 5000);
    });
    
    // Initialize slider
    goToSlide(0);
    
    // Update slider on window resize
    window.addEventListener('resize', () => {
        goToSlide(currentIndex);
    });
}

/**
 * Newsletter Form Submission
 */
function initNewsletterForm() {
    const form = document.querySelector('.newsletter-form');
    if (!form) return;
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const emailInput = this.querySelector('input[type="email"]');
        if (!emailInput) return;
        
        const email = emailInput.value.trim();
        if (!email) {
            showFormMessage(form, 'Inserisci un indirizzo email valido', 'error');
            return;
        }
        
        if (!validateEmail(email)) {
            showFormMessage(form, 'Indirizzo email non valido', 'error');
            return;
        }
        
        // Simulate form submission
        const submitButton = this.querySelector('button[type="submit"]');
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = 'Invio in corso...';
        }
        
        // Simulate API call
        setTimeout(() => {
            showFormMessage(form, 'Iscrizione completata con successo!', 'success');
            emailInput.value = '';
            
            if (submitButton) {
                submitButton.disabled = false;
                submitButton.textContent = 'Iscriviti';
            }
        }, 1500);
    });
    
    function validateEmail(email) {
        const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(email.toLowerCase());
    }
    
    function showFormMessage(form, message, type) {
        // Remove any existing message
        const existingMessage = form.querySelector('.form-message');
        if (existingMessage) {
            existingMessage.remove();
        }
        
        // Create message element
        const messageElement = document.createElement('div');
        messageElement.className = `form-message ${type}`;
        messageElement.textContent = message;
        
        // Add to form
        form.appendChild(messageElement);
        
        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            .form-message {
                margin-top: 0.5rem;
                padding: 0.5rem;
                border-radius: var(--border-radius-sm);
                font-size: 0.875rem;
            }
            
            .form-message.success {
                background-color: rgba(72, 187, 120, 0.1);
                color: var(--success-color);
            }
            
            .form-message.error {
                background-color: rgba(229, 62, 62, 0.1);
                color: var(--danger-color);
            }
        `;
        
        if (!document.querySelector('style[data-form-message]')) {
            style.setAttribute('data-form-message', 'true');
            document.head.appendChild(style);
        }
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            messageElement.remove();
        }, 5000);
    }
}